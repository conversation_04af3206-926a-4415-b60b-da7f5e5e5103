package com.lc.billion.icefire.web.bus.server.entity;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.longtech.ls.zookeeper.GameServerConfig;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @sine 2015年9月30日 下午3:14:29
 * 
 */
public class Server {

	private Long id;

	private String name;

	private String serverIp;

	/**
	 * 域名
	 */
	private String proxyHost;
	private int proxyPort;

	/**
	 * 域名
	 */
	private String protectionHost;
	private int protectionPort;

	/**
	 * 域名
	 */
	private String serverHost;

	private int serverPort;

	private String serverInnerIp;

	private int wsPort;

	private int serverStatus;

	private int serverHotState;

	private String diversionConfigurationByApp;

	private String diversionConfigurationByCountry;

	private int diversionConfigurationByWeight;

	private Date createTime;

	private Date openTime;

	/**
	 * 在线数量
	 */
	private int onlineCount;
	private boolean isOnlineOverflow;

	private int remainCityAmount;
	private boolean isMemoryOverflow;

	/**
	 * 剩余内存
	 */
	private int freeMemoryG;

	/**
	 * 注册速度 每秒注册量
	 */
	private double registerSpeed;
	private boolean isRegisterPerOverflow;

	/**
	 * 当前角色总数数量
	 */
	private int roleCount;

	private boolean isRegisterOverflow;

//	private List<RecommendPolicy> policies = new LinkedList<>();

//	private Map<Integer, Long> zoneGridCityNums = new HashMap<>();

	/** 服可见性 */
	private boolean visible = false;
	/** 在迁服列表中 */
	private boolean inServerList = false;
	/** 服务器在多角色列表中显示 */
	private boolean multiRoleServerShow = false;

	/**
	 * 是否开服
	 */
	private boolean open = false;

	/**
	 * 导量类型
	 * 对应 GameServerConfig.DiversionSwitch
	 */
	private int diversionSwitch;

	
	public static final int DIVERSION_STATE_STOP = 0;
	public static final int DIVERSION_STATE_RUN = 1;
	/**
	 * 导量状态
	 * 0导量停止 1导量运行
	 */
	private int diversionState;
	/**
	 * 服务器版本:灰度版本为canary
	 */
	private String version;

	/**
	 * 是否是新手服务器:散列游戏服压力
	 */
	private boolean isGuideServer;

	/**
	 * 付费人数
	 */
	@Setter
	@Getter
	private int payCount;

	/**
	 * 渠道 -> 人数
	 */
	@Setter
	@Getter
	private Map<String,String> roleByPlace = new HashMap<>();
	public Server() {

	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getServerIp() {
		return serverIp;
	}

	public void setServerIp(String serverIp) {
		this.serverIp = serverIp;
	}

	public String getServerInnerIp() {
		return serverInnerIp;
	}

	public void setServerInnerIp(String serverInnerIp) {
		this.serverInnerIp = serverInnerIp;
	}

	public String getProxyHost() {
		return proxyHost;
	}

	public void setProxyHost(String proxyHost) {
		this.proxyHost = proxyHost;
	}

	public String getProtectionHost() {
		return protectionHost;
	}

	public int getProxyPort() {
		return proxyPort;
	}

	public void setProxyPort(int proxyPort) {
		this.proxyPort = proxyPort;
	}

	public int getProtectionPort() {
		return protectionPort;
	}

	public void setProtectionPort(int protectionPort) {
		this.protectionPort = protectionPort;
	}

	public void setProtectionHost(String protectionHost) {
		this.protectionHost = protectionHost;
	}

	public String getServerHost() {
		return serverHost;
	}

	public void setServerHost(String serverHost) {
		this.serverHost = serverHost;
	}

	public int getServerPort() {
		return serverPort;
	}

	public void setServerPort(int serverPort) {
		this.serverPort = serverPort;
	}

	public int getServerStatus() {
		return serverStatus;
	}

	public void setServerStatus(int serverStatus) {
		this.serverStatus = serverStatus;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getDiversionConfigurationByApp() {
		return diversionConfigurationByApp;
	}

	public void setDiversionConfigurationByApp(String diversionConfigurationByApp) {
		this.diversionConfigurationByApp = diversionConfigurationByApp;
	}

	public String getDiversionConfigurationByCountry() {
		return diversionConfigurationByCountry;
	}

	public void setDiversionConfigurationByCountry(String diversionConfigurationByCountry) {
		this.diversionConfigurationByCountry = diversionConfigurationByCountry;
	}

	public int getDiversionConfigurationByWeight() {
		return diversionConfigurationByWeight;
	}


	public void setDiversionConfigurationByWeight(int diversionConfigurationByWeight) {
		this.diversionConfigurationByWeight = diversionConfigurationByWeight;
	}

	public int getWsPort() {
		return wsPort;
	}

	public void setWsPort(int wsPort) {
		this.wsPort = wsPort;
	}

	public Date getOpenTime() {
		return openTime;
	}

	public void setOpenTime(Date openTime) {
		this.openTime = openTime;
	}

	public boolean isInServerList() {
		return inServerList;
	}

	public void setInServerList(boolean inServerList) {
		this.inServerList = inServerList;
	}

	public int getOnlineCount() {
		return onlineCount;
	}

	public void setOnlineCount(int onlineCount) {
		this.onlineCount = onlineCount;
	}

	public int getRemainCityAmount() {
		return remainCityAmount;
	}

	public void setRemainCityAmount(int remainCityAmount) {
		this.remainCityAmount = remainCityAmount;
	}

	public int getRoleCount() {
		return roleCount;
	}

	public void setRoleCount(int roleCount) {
		this.roleCount = roleCount;
	}

	public int getServerHotState() {
		return serverHotState;
	}

	public void setServerHotState(int serverHotState) {
		this.serverHotState = serverHotState;
	}

	public boolean isVisible() {
		return visible;
	}

	public void setVisible(boolean visible) {
		this.visible = visible;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}
	public boolean isGuideServer() {
		return isGuideServer;
	}
	public void setIsGuideServer(boolean isGuideServer) {
		this.isGuideServer = isGuideServer;
	}

	public boolean isMultiRoleServerShow() {
		return multiRoleServerShow;
	}

	public void setMultiRoleServerShow(boolean multiRoleServerShow) {
		this.multiRoleServerShow = multiRoleServerShow;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Server other = (Server) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

//	public String getParams() {
//		JSONObject jsonObject = new JSONObject();
//		JSONArray p = new JSONArray();
//		for (RecommendPolicy rp : policies) {
//			p.add(rp.toJson());
//		}
//		jsonObject.put("p", p);
//		return jsonObject.toJSONString();
//	}

//	public void setParams(String params) {
//		if (Utils.isEmpty(params)) {
//			return;
//		}
//		policies.clear();
//		JSONObject jsonObject = JSON.parseObject(params);
//		if (jsonObject.containsKey("p")) {
//			JSONArray p = jsonObject.getJSONArray("p");
//			for (Object o : p) {
//				JSONObject j = (JSONObject) o;
//				int typeId = j.getIntValue("t");
//				RecommendPolicyType type = RecommendPolicyType.getType(typeId);
//				if (type == null) {
//					continue;
//				}
//				RecommendPolicy instance = type.createInstance();
//				instance.fromJson(j);
//				policies.add(instance);
//			}
//		}
//	}

//	public List<RecommendPolicy> getPolicies() {
//		return policies;
//	}

	@Override
	public String toString() {
		ObjectMapper mapper = new ObjectMapper();
		String json = "";
		try {
			json = mapper.writeValueAsString(this);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		return json;
	}

	public int getFreeMemoryG() {
		return freeMemoryG;
	}

	public void setFreeMemoryG(int freeMemoryG) {
		this.freeMemoryG = freeMemoryG;
	}

	public double getRegisterSpeed() {
		return registerSpeed;
	}

	public void setRegisterSpeed(double registerSpeed) {
		this.registerSpeed = registerSpeed;
	}

	public boolean isOpen() {
		return open;
	}

	public void setOpen(boolean open) {
		this.open = open;
	}

	public int getDiversionSwitch() {
		return diversionSwitch;
	}

	public void setDiversionSwitch(GameServerConfig.DiversionSwitch diversionSwitch) {
		this.diversionSwitch = diversionSwitch.ordinal();
	}

	public int getDiversionState() {
		return diversionState;
	}

	public void setDiversionState(int diversionState) {
		this.diversionState = diversionState;
	}

	public boolean getIsRegisterOverflow() {
		return isRegisterOverflow;
	}

	public void setIsRegisterOverflow(boolean registerOverflow) {
		isRegisterOverflow = registerOverflow;
	}

	public boolean getIsOnlineOverflow() {
		return isOnlineOverflow;
	}

	public void setIsOnlineOverflow(boolean onlineOverflow) {
		isOnlineOverflow = onlineOverflow;
	}

	public boolean getIsMemoryOverflow() {
		return isMemoryOverflow;
	}

	public void setIsMemoryOverflow(boolean memoryOverflow) {
		isMemoryOverflow = memoryOverflow;
	}

	public boolean getIsRegisterPerOverflow() {
		return isRegisterPerOverflow;
	}

	public void setIsRegisterPerOverflow(boolean registerPerOverflow) {
		isRegisterPerOverflow = registerPerOverflow;
	}

//	public Map<Integer, Long> getZoneGridCityNums() {
//		return zoneGridCityNums;
//	}
//
//	private static final String ZONEGRIDCITYNUMS_KEY = "pzcn";
//
//	@SuppressWarnings({ "rawtypes", "unchecked" })
//	public void zoneGridCityNumsFromJson(String params) {
//		if (Utils.isEmpty(params)) {
//			return;
//		}
//		zoneGridCityNums.clear();
//		JSONObject jsonObject = JSON.parseObject(params);
//
//		if (jsonObject.containsKey(ZONEGRIDCITYNUMS_KEY)) {
//			JSONObject object = jsonObject.getJSONObject(ZONEGRIDCITYNUMS_KEY);
//			Map m = object;
//			zoneGridCityNums.putAll(m);
//		}
//	}
	
	/**
	 * 此游戏服类型相关信息。
	 */
	private String serverDesc;

	public void setServerDesc(String serverDesc) {
		this.serverDesc = serverDesc;
	}

	public String getServerDesc() {
		return this.serverDesc;
	}

}
