package com.lc.billion.icefire.web.bus.server.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.common.JsonUtils;
import com.lc.billion.icefire.core.support.Utils;
import com.lc.billion.icefire.web.bus.server.entity.Server;
import com.lc.billion.icefire.web.bus.server.service.IServerService;
import com.lc.billion.icefire.web.response.ErrorCode;
import com.lc.billion.icefire.web.response.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @sine 2015年10月24日 下午5:03:41
 *
 */
@Controller
@RequestMapping(value = "/console")
public class ServerController {

	private Logger LOG = LoggerFactory.getLogger(getClass());

	@Autowired
	private IServerService serverService;

	@RequestMapping("/server")
	public ModelAndView list() {
		ModelAndView view = new ModelAndView("console/server");

		List<Server> servers = serverService.getAll();
		for(Server server : servers){
			if(server.getServerHost() == null){
				server.setServerHost("");
			}
//			if(server.getDiversionConfigurationByCountry() == null){
//				server.setDiversionConfigurationByCountry("");
//			}
//			if(server.getDiversionConfigurationByApp() == null){
//				server.setDiversionConfigurationByApp("");
//			}
		}
		view.addObject("servers", servers);
		//2020-9-24 去掉白名单
//		view.addObject("whiteList", WhiteListConst.getWhiteDevices());
		view.addObject("whiteList", Collections.emptyList());
		view.addObject("autoDiversionParams",serverService.getAutoDiversionParams());
		view.addObject("isDiversionGuaranteeAlgorithm",serverService.isDiversionGuaranteeAlgorithm());
		return view;
	}

	@RequestMapping(value = "/server/update", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String update(String params) {
		LOG.info("server update: {}", params);

		Server server = JsonUtils.readValue(params, Server.class);
		serverService.save(server);
		return JsonUtils.toJson(Response.success(server));
	}

	@RequestMapping(value = "/server/updateServerParams", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String updateServerParams(Long id, String params) {
		if (id == null) {
			LOG.info("server params update: {}", params);
			return JsonUtils.toJson(Response.error(-1, "服务器id 不能为null"));
		}
		LOG.info("server params update; serverId:{}, params:{}", id, params);
		Server server = serverService.getById(id);
//		server.setParams(params);
		serverService.save(server);
		return JsonUtils.toJson(Response.success(server));
	}

	@RequestMapping(value = "/server/setAutoDiversionParams", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String setAutoDiversionParams(String params) {
		String rs =serverService.setAutoDiversionParams(params);
		return JsonUtils.toJson(Response.success(rs));
	}

	@RequestMapping(value = "/server/resetServerBatch", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String resetServerBatch(String params) {
		int[] ids = JsonUtils.readValue(params, int[].class);
		LOG.info("start batch server. ids:{}", Arrays.toString(ids));
		for (int id : ids) {
			boolean result = serverService.checkAndResetGameServer(id);
			if (!result) {
				return JsonUtils.toJson(Response.error(ErrorCode.UNKNOW, id + "重置失败"));
			}
		}
		return JsonUtils.toJson(Response.success(ids));
	}

	@RequestMapping(value = "/server/getWhiteList", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String getWhiteList() {
		List<String> ips = serverService.getLoginIpinfoWhiteList();
		JSONObject result = new JSONObject();
		result.put("data", getWhiteListInfo(ips));
		result.put("ret",0);
		return result.toJSONString();
	}

	@RequestMapping(value = "/server/getUuidWhiteList", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String getUuidWhiteList() {
		List<String> uuidInfos = serverService.getLoginUuidInfoWhiteList();
		JSONObject result = new JSONObject();
		result.put("data", getWhiteListInfo(uuidInfos));
		result.put("ret",0);
		return result.toJSONString();
	}

	private String getWhiteListInfo(List<String> ips) {
		JSONArray retList = new JSONArray();
		int index = 1;
		for (String ip : ips) {
			JSONObject ipInfo = new JSONObject();
			String[] split = ip.split(":");
			ipInfo.put("id", index);
			ipInfo.put("ip", split.length >= 1 ? split[0] : "");
			ipInfo.put("desc", split.length >= 2 ? split[1] : "");
			retList.add(ipInfo);
			index++;
		}
		return retList.toJSONString();
	}

	@RequestMapping(value = "/server/updateWhiteList", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String updateWhiteList(String params) {
		LOG.info("server whiteIp update: {}", params);
		if (params == null) {
			return JsonUtils.toJson(Response.error(-1, "params is null"));
		}

		// 更新zookeep
		List<String> ips = new ArrayList<>();
		JSONArray jsonArray = Utils.toJSONArray(params);
		for (Object p : jsonArray) {
			ips.add(p.toString());
		}
		serverService.updateLoginIpWhiteList(ips);

		return JsonUtils.toJson(Response.success(ips));
	}

	@RequestMapping(value = "/server/updateUuidWhiteList", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String updateUuidWhiteList(String params) {
		LOG.info("server whiteUuid update: {}", params);
		if (params == null) {
			return JsonUtils.toJson(Response.error(-1, "params is null"));
		}

		// 更新zookeep
		List<String> uuids = new ArrayList<>();
		JSONArray jsonArray = Utils.toJSONArray(params);
		for (Object p : jsonArray) {
			uuids.add(p.toString());
		}
		serverService.updateLoginUuidWhiteList(uuids);

		return JsonUtils.toJson(Response.success(uuids));
	}

	@RequestMapping(value = "/server/checkid", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String checkId(long id) {

		if (serverService.getById(id) != null) {
			return JsonUtils.toJson(Response.error(-1, "server id is already exists"));
		}

		return JsonUtils.toJson(Response.SUCCESS);
	}

	@RequestMapping(value = "/server/remove", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String remove(long id) {
		serverService.remove(id);

		return JsonUtils.toJson(Response.SUCCESS);
	}

	@RequestMapping(value = "/server/startall", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String startAll(int serverStatus) {
		LOG.info("start all server.");
		serverService.startAll();

		return JsonUtils.toJson(Response.SUCCESS);
	}

	@RequestMapping(value = "/server/stopall", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String stopAll(int serverStatus) {
		LOG.info("stop all server.");
		serverService.stopAll();

		return JsonUtils.toJson(Response.SUCCESS);
	}

	@RequestMapping(value = "/server/startBatch", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String startBatch(String params) {
		long[] ids = JsonUtils.readValue(params, long[].class);
		LOG.info("start batch server. ids:{}", Arrays.toString(ids));
		serverService.startBatch(ids);

		return JsonUtils.toJson(Response.success(ids));
	}

	@RequestMapping(value = "/server/stopBatch", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String stopBatch(String params) {
		long[] ids = JsonUtils.readValue(params, long[].class);
		LOG.info("stop batch server. ids:{}", Arrays.toString(ids));
		serverService.stopBatch(ids);

		return JsonUtils.toJson(Response.success(ids));
	}

//	@RequestMapping(value = "/server/startMigrateBatch", produces = "text/plain; charset=UTF-8")
//	public @ResponseBody String startMigrateBatch(String params) {
//		long[] ids = JsonUtils.readValue(params, long[].class);
//		LOG.info("start batch migrate server. ids:{}", Arrays.toString(ids));
//		serverService.startMigrateBatch(ids);
//
//		return JsonUtils.toJson(Response.success(ids));
//	}
//
//	@RequestMapping(value = "/server/stopMigrateBatch", produces = "text/plain; charset=UTF-8")
//	public @ResponseBody String stopMigrateBatch(String params) {
//		long[] ids = JsonUtils.readValue(params, long[].class);
//		LOG.info("stop batch migrate server. ids:{}", Arrays.toString(ids));
//		serverService.stopMigrateBatch(ids);
//
//		return JsonUtils.toJson(Response.success(ids));
//	}

	@RequestMapping(value = "/server/batchAddInServerList", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String batchAddInServerList(String params) {
		long[] ids = JsonUtils.readValue(params, long[].class);
		LOG.info("start batch migrate list server. ids:{}", Arrays.toString(ids));
		serverService.batchAddInServerList(ids);

		return JsonUtils.toJson(Response.success(ids));
	}

	@RequestMapping(value = "/server/batchRemoveInServerList", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String batchRemoveInServerList(String params) {
		long[] ids = JsonUtils.readValue(params, long[].class);
		LOG.info("stop batch migrate list server. ids:{}", Arrays.toString(ids));
		serverService.batchRemoveInServerList(ids);

		return JsonUtils.toJson(Response.success(ids));
	}

	//白名单编辑功能目前不使用，暂时现占用为刷新服务器列表方法，省去flyway操作，如果需要之后再增加refreshList方法
	@RequestMapping(value = "/server/updateWhiteL", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String refresh() {
		LOG.info("server refresh");
		serverService.refreshServerList();
		List<Server> servers = serverService.getAll();
		for(Server server : servers){
			if(server.getServerHost() == null){
				server.setServerHost("");
			}
//			if(server.getDiversionConfigurationByCountry() == null){
//				server.setDiversionConfigurationByCountry("");
//			}
//			if(server.getDiversionConfigurationByApp() == null){
//				server.setDiversionConfigurationByApp("");
//			}
		}
		return JsonUtils.toJson(Response.success(servers));
	}

	@RequestMapping(value = "/server/batchAddMultiServerList", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String batchAddMultiServerList(String params) {
		long[] ids = JsonUtils.readValue(params, long[].class);
		LOG.info("batch open multi role server show. server ids:{}", Arrays.toString(ids));
		serverService.openMultiRoleServerShowBatch(ids);

		return JsonUtils.toJson(Response.success(ids));
	}

	@RequestMapping(value = "/server/batchRemoveMultiServerList", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String batchRemoveMultiServerList(String params) {
		long[] ids = JsonUtils.readValue(params, long[].class);
		LOG.info("batch close multi role server show. server ids:{}", Arrays.toString(ids));
		serverService.closeMultiRoleServerShowBatch(ids);

		return JsonUtils.toJson(Response.success(ids));
	}

	@RequestMapping(value = "/server/diversionInfo", produces = "text/plain; charset=UTF-8")
	public @ResponseBody String diversionInfo(String params){
		int serverId = Utils.toJSONObject(params).getIntValue("serverId");
		LOG.info("diversionInfo show. server serverId:{}", serverId);
		String str = serverService.getDiversionInfo();
		return JsonUtils.toJson(Response.success(str));
	}
}
