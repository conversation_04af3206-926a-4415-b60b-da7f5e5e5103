package com.lc.billion.icefire.web.bus.api.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.common.PlatformType;
import com.lc.billion.icefire.core.support.HttpUtil;
import com.lc.billion.icefire.web.bus.api.service.IAuthContext;
import com.lc.billion.icefire.web.bus.api.service.IAuthService;
import com.lc.billion.icefire.web.bus.api.service.impl.context.CommonAuthContext;
import com.lc.billion.icefire.web.bus.user.entity.User;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName TuyooSDKAuthServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/12/4 15:38
 * @Version 1.0
 */
@Service
public class TuyooSDKAuthServiceImpl implements IAuthService {

    private static final Logger logger = LoggerFactory.getLogger(TuyooSDKAuthServiceImpl.class);

    /**
     * sdk请求地址
     */
    @Value("${tuyoo.host}")
    public String HOST;

    /**
     * 登录校验
     */
    public String VERIFY_AUTHOR_CODE_URL = "/open/v4/user/verifyAuthorCode";

    @Override
    public PlatformType getPlatform() {
        return PlatformType.TUYOO;
    }

    @Override
    public IAuthContext auth(String id, String token) {
        CommonAuthContext ctx = new CommonAuthContext();
        try {
            String url = HOST + VERIFY_AUTHOR_CODE_URL;
            Map<String, String> paramMap = new HashMap<>(2);
            paramMap.put("userId", id);
            paramMap.put("authorCode", token);
            logger.info("auth verify url: {}, params: {}", url, paramMap);
//            String json = Utils.httpGet(url, paramMap, false);
            String json = HttpUtil.post(url, paramMap);
            JSONObject result = JSON.parseObject(json);
            if (null == result) {
                logger.error("auth failed. platform: {}, token: {}, return null.", getPlatform(), token);
            } else {
                JSONObject data = result.getJSONObject("result");
                String verify = data.getString("verify");
                if (verify != null && "ok".equals(verify)) {
                    ctx.setUserId(data.getString("userId"));
                    ctx.setSuccess(true);
                } else {
                    logger.error("auth failed. platform: {}, token: {}, return error: {}", getPlatform(), token, data);
                }
            }
            ctx.setOriginalData(json);
            ctx.setUserToken(token);

            // 解码token
            Base64.Decoder decoder = Base64.getDecoder();
            byte[] decodedData = decoder.decode(token);
            String tokenJsonStr = new String(decodedData);

            JSONObject tokenJson = JSON.parseObject(tokenJsonStr);
            JSONObject extData = new JSONObject();
            extData.put("token", tokenJson);
            ctx.setExtData(extData);

        } catch (Exception e) {
            logger.error("auth failed. platform: {}, token: {}", getPlatform(), token, e);
        }
        return ctx;
    }

    @Override
    public IAuthContext bindAuth(String id, String token) {
        return null;
    }

    @Override
    public String order(User user, int platform, HttpServletRequest request) {
        return null;
    }

    @Override
    public String pay(User user, HttpServletRequest request) {
        return null;
    }

    @Override
    public String getUserId(String id, String userToken) {
        return null;
    }

    @Override
    public int notify(JSONObject jsonObject) {
        return 0;
    }
}
