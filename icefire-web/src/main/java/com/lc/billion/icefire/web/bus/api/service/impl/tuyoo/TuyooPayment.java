package com.lc.billion.icefire.web.bus.api.service.impl.tuyoo;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.VersionManager;
import com.lc.billion.icefire.core.common.PlatformType;
import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.pay.TuConstants;
import com.lc.billion.icefire.core.support.HttpUtil;
import com.lc.billion.icefire.core.support.Utils;
import com.lc.billion.icefire.web.bus.api.account.entity.AccountBind;
import com.lc.billion.icefire.web.bus.api.account.service.IAccountBindService;
import com.lc.billion.icefire.web.bus.api.entity.Recharge;
import com.lc.billion.icefire.web.bus.api.entity.RechargeState;
import com.lc.billion.icefire.web.bus.api.entity.Subscription;
import com.lc.billion.icefire.web.bus.api.service.IOrderService;
import com.lc.billion.icefire.web.bus.api.service.impl.PaymentAdapter;
import com.lc.billion.icefire.web.bus.api.service.impl.PaymentErrorCode;
import com.lc.billion.icefire.web.bus.api.service.impl.RechargeServiceImpl;
import com.lc.billion.icefire.web.bus.config.LibaoConfig;
import com.lc.billion.icefire.web.bus.config.LibaoPriceConfig;
import com.lc.billion.icefire.web.bus.config.LibaoWebConfigManager;
import com.lc.billion.icefire.web.bus.config.WebSettingConfig;
import com.lc.billion.icefire.web.bus.gm.service.IEmailEndpointService;
import com.lc.billion.icefire.web.bus.gm.service.impl.VirtualThreadService;
import com.lc.billion.icefire.web.bus.gm.service.impl.WebRPCService;
import com.lc.billion.icefire.web.bus.user.entity.User;
import com.lc.billion.icefire.web.bus.user.service.IUserService;
import com.lc.billion.icefire.web.utils.HttpUtils;
import com.simfun.sgf.utils.JavaUtils;
import com.simfun.sgf.utils.MessageDigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName TuyooPayment
 * @Description
 * <AUTHOR>
 * @Date 2023/12/5 11:37
 * @Version 1.0
 */
@Service("tuyooPayment")
public class TuyooPayment extends PaymentAdapter {

    private final Logger logger = LoggerFactory.getLogger(TuyooPayment.class);

    @Value("${TUYOO_APPKEY}")
    private String appkey;

    @Autowired
    private LibaoWebConfigManager libaoWebConfigManager;

    @Autowired
    private ConfigServiceImpl configService;

    @Autowired
    private RechargeServiceImpl rechargeService;

    @Autowired
    private IUserService userService;

    @Autowired
    private WebRPCService rpcService;

    @Autowired
    private IOrderService iOrderService;
    @Autowired
    private IAccountBindService accountBindService;
    @Autowired
    private VirtualThreadService virtualThreadService;
    @Autowired
    private IEmailEndpointService emailEndpointService;
    @Autowired
    private VersionManager versionManager;

    private static final String MAIL_SUBJECT = "充值掉单补发";
    private static final String MAIL_CONTENT = "亲爱的指挥官，\n\n" +
            "经查询您充值的订单出现了重复充值且未到账的情况。\n\n" +
            "我们根据您掉单的额度为你补发对应数量的莲花金币。\n\n" +
            "莲花金币可以购买游戏内任意的礼包，1个莲花金币对应1元，30个莲花金币可购买30元的任意礼包。\n\n" +
            "若您有任何疑问，欢迎随时联系客服，感谢您的理解与支持，祝您游戏愉快！\n\n" +
            "《三国：冰河时代》 运营团队";

    public static final String SUCCESS = "success";

    public static final String FAIL = "fail";

    public static final String CODE = "code";

    public static final String DINGDING_BUDAN_URL = "https://oapi.dingtalk.com/robot/send?access_token=19dcff7e43d6fb26ca5dbd63dab22133593eb1dc4a26aa05e285be145004db1c";

    /**
     * 处理订单
     *
     * @param user
     * @param platform
     * @param productId
     * @param currencyName
     * @param localCurrency
     * @param extData       根据不同的礼包类型参数数据不同
     * @param appsFlyerId
     * @param season
     * @return
     */
    @Override
    protected String orderImpl(User user, int platform, String productId, String currencyName, String localCurrency, String extData, String appsFlyerId, int season) {
        if (user == null) {
            logger.error("user is null.");
            return error("user is null");
        }
        long startTime = System.currentTimeMillis();
        // 拼接订单信息
        String orderInfo = "user [" + user.getId() + "] [roleId:" + user.getRoleId() + "], platform [" + platform + "], productId [" + productId + "]";
        // 获取购买的礼包配置
        LibaoConfig.LibaoMeta libaoMeta = libaoWebConfigManager.getById(productId);
        if (libaoMeta == null) {
            logger.error("user: {}, productId: {} not found.", user.getRoleId(), productId);
            return error("product not found productId: " + productId);
        }

        LibaoPriceConfig libaoPriceConfig = configService.getConfig(LibaoPriceConfig.class);
        LibaoPriceConfig.LibaoPriceMeta priceMeta = libaoPriceConfig.getById(libaoMeta.getPriceId());
        if (priceMeta == null) {
            logger.error("user: {}, productId: {}, priceId: {} not found.", user.getRoleId(), productId, libaoMeta.getPriceId());
            return error("product not found price config, price id: " + libaoMeta.getPriceId());
        }

        // 检查是否可以购买，是否被限购
        if (canNotBuy(user, libaoMeta, extData)) {
            return error("Only one purchase allowed:" + productId, PaymentErrorCode.LIMITED_PURCHASE_ERROR);
        }

        logger.info("[order] orderCheck info :: {}, use time {}", orderInfo, System.currentTimeMillis() - startTime);
        boolean isDiscount = false;
        /** 新礼包打折逻辑 **/
        if (libaoMeta.getPricePercent() == 1 && !Utils.isEmpty(libaoMeta.getPercentLiBaoPrice())) {
            LibaoPriceConfig.LibaoPriceMeta discountPriceMeta = libaoPriceConfig.getById(libaoMeta.getPercentLiBaoPrice());
            if (discountPriceMeta != null) {
                priceMeta = discountPriceMeta;
                isDiscount = true;
            }
            logger.info("TuyooPayment,打折充值, roleId:{}, productId:{}, discountPriceId:{}", user.getRoleId(), productId, libaoMeta.getPercentLiBaoPrice());
        }

        if (priceMeta == null) {
            logger.error("User: {}, priceId: {} not found.", user.getId(), libaoMeta.getPriceId());
            return error("priceId not found productId:" + libaoMeta.getPriceId());
        }
        double localCurrencyPrice = priceMeta.getDollar();
        if (StringUtils.isBlank(currencyName) || StringUtils.isBlank(localCurrency)) {
            currencyName = "USD";
        } else {
            localCurrencyPrice = Double.parseDouble(localCurrency);
        }

        // 订阅不走福利账号
        // 福利账号,福利订单 0-正常订单 1-福利订单
        int welfare = 0;
        if (priceMeta.getSubscribe() != 1 && isEffectiveWelfare(user, priceMeta.getDollar(), true)) {
            welfare = 1;
        }

        logger.info("[order] welfareCheck info :: {}, welfare: {}, use time {}", orderInfo, welfare, System.currentTimeMillis() - startTime);
        JSONObject json = new JSONObject();
        if (welfare == 1) {
            // 处理福利订单
            logger.info("[order] welfareBuy begin; orderInfo {}, use time {}", orderInfo, System.currentTimeMillis() - startTime);
            rpcService.getProxy(user.getCurrentServerId()).welfareBuy(user.getRoleId(), libaoMeta.getId(), extData);
            json.put("ret", 888);
            json.put("orderId", 0);
            logger.info("[order] welfareBuy end; orderInfo {}, use time {}", orderInfo, System.currentTimeMillis() - startTime);
            return json.toJSONString();
        }


        int combinationIndex = checkAndGetCombinationIndex(libaoMeta, extData);

        // 生成订单 $ 美元 $
        Recharge recharge = rechargeService.create(user, platform, productId, priceMeta.getDollar(), currencyName, localCurrencyPrice, libaoMeta.getTotalDiamond(), isDiscount,
                welfare, combinationIndex, appsFlyerId, extData, priceMeta.getId(), season);

        logger.info("[order] rechageCreate info :: {}, use time {}", orderInfo, System.currentTimeMillis() - startTime);
        json.put("ret", 0);
        json.put("orderId", recharge.getId());
        json.put("priceId", priceMeta.getId());
        json.put("productId", productId);
        json.put("amount", priceMeta.getDollar());
        json.put("welfare", welfare); // 0-正常订单 1-福利订单
        json.put("isSub", priceMeta.getSubscribe() == 1); // 是否订阅
        logger.info("TuyooPayment order, User: {}, roleId={} order json: {}.", orderInfo, user.getRoleId(), json.toJSONString());
        return json.toJSONString();
    }


    @Override
    public int notify(JSONObject jsonObject) {
        return HttpServletResponse.SC_INTERNAL_SERVER_ERROR;
    }

    public String pay(HttpServletRequest request) {
        Map<String, String> jsonObject = new HashMap<>();
        for (String param : request.getParameterMap().keySet()) {
            jsonObject.put(param, request.getParameter(param));
        }
        String sign = jsonObject.remove(CODE);
        String data = appkey + HttpUtils.sortAndformatParameters(jsonObject) + appkey;
        String mysign = MessageDigestUtils.md5(data);
        String orderId = jsonObject.get("orderId");

        if (!mysign.equals(sign)) {
            logger.info("tuyoo sign error, recharge id {} signdata={}  ", orderId, data);
            return sendResp(TuConstants.CODE_SIGN_ERROR,"check sign fail");
        }
        Recharge recharge = rechargeService.get(orderId);
        Double chargedRmbs = Double.parseDouble(jsonObject.get("chargedRmbs"));
        String platformUserId = jsonObject.get("userId"); //SDKuserid
        String prodId = jsonObject.get("prodId"); //计费点ID
        String platformOrder = jsonObject.get("platformOrder");
        if (recharge == null) {
            logger.info("tuyoo Notify error, recharge id {} not exist", orderId);
            return sendResp(TuConstants.CODE_ORDER_NOT_EXIST,"recharge id not exist");
        }
        if (!Objects.equals(recharge.getPlat_productId(), prodId)) {
            logger.info("tuyoo Notify error, product id {} has been modified old", recharge.getPlat_productId(), prodId);
            return sendResp(TuConstants.CODE_PRODUCT_ID_NOT_MATCH,"product id has been modified old" + recharge.getPlat_productId() + "notify" + prodId);
        }
        if (iOrderService.containsProcessingOrder(recharge)) {
            logger.error("tuyoo notify User: {}, orderId: {} already in processed.", recharge.getRoleId(), recharge.getId());
            return sendResp(TuConstants.CODE_ORDER_PROCESSING,"recharge id processing");
        }
        if (recharge.isPayed()) {
            // 首次订阅，如果谷歌通知先于客户端发来的，recharge状态为未完成
            // 通知不处理，依赖客户端下发订阅请求(1.入库记录信息客户端下发的数据更为齐全 2.避免了并发问题)
            logger.info("tuyoo, payed state, orderId:{}.", recharge.getId());
            return sendResp(TuConstants.CODE_ALREADY_PROCESSED,"already notified");
        }

        // 检查 userId 是否相同，判断是否为同一人
        AccountBind accountBind = accountBindService.findByPlatformIdAndPlatform(platformUserId, recharge.getPlatform());
        var user = userService.findOne(recharge.getUserId());
        if (accountBind == null || user == null || user.getAccId() != accountBind.getAccId()){
            logger.warn("tuyoo, pay error, user: {}, recharge: {}", user, recharge);
            //先注掉，上线后没问题再打开
            return sendResp(TuConstants.CODE_USER_ID_NOT_MATCH,"userId not equal");
        }

        // 加入到正在处理订单列表
        iOrderService.addProcessingOrder(recharge);
        recharge.setCurrency(chargedRmbs);
        recharge.setPlat_orderId(platformOrder);
        recharge.setPlat_productId(prodId);

        LibaoPriceConfig libaoPriceConfig = configService.getConfig(LibaoPriceConfig.class);

        LibaoConfig.LibaoMeta libaoMeta = libaoWebConfigManager.getById(recharge.getProductId());
        LibaoPriceConfig.LibaoPriceMeta priceMeta = libaoPriceConfig.getById(libaoMeta.getPriceId());

//        Subscription sub = null;
        if (recharge.getWelfare() != 1) {
            //是不是订阅商品
            boolean isSub = priceMeta.getSubscribe() == 1;
//            if (isSub) {
//                sub = createSubInfo(recharge, user, payJson, isTest);
//            }
        } else {
            // 0-正常订单 1-福利订单
            String result = checkWelfareOrder(user, recharge, orderId);
            if (result != null) {
                logger.warn("pay error checkWelfareOrder fail, result={} orderId:{}", result, orderId);
                return sendResp(TuConstants.CODE_WELFARE_ERROR,"pay error fail");
            }
        }
        JSONObject json = new JSONObject();
        boolean suc = doPay(json, recharge, null, false, user.getCurrentServerId());
        if (!suc) {
            var ret = json.getInteger(TuConstants.KEY_CODE_FROM_GAME);
            if(ret == null){
                return sendResp(TuConstants.CODE_GAME_PROCESS_ERROR, "notify gameserver failed");
            }
            var newCode = TuConstants.CODE_GAME_PROCESS_BASE - ret;
            //异步去执行补单邮件逻辑
            if(newCode == TuConstants.CODE_LIBAO_RECORD_NOT_EXIST || newCode == TuConstants.CODE_LIBAO_OVER_BUY_COUNT || newCode == TuConstants.CODE_LIBAO_DISCOUNT_ITEM_NOT_EXIST){
                //执行补单
                virtualThreadService.execute(()->{
                    recharge.setState(RechargeState.GIVE_TOKEN);
                    rechargeService.update(recharge);
                    logger.info("触发限购补单,{} {} {} {}",user.getRoleId(),recharge.getId(),recharge.getProductId(),recharge.getCurrency());
                    //走邮件补发莲花金币
                    sendTokenEmailByUser(user,(int)priceMeta.getRmb());

                    //先临时搞一个告警
                    if(versionManager.isOnlineEnv()){
                        HttpUtil.postDingDing(DINGDING_BUDAN_URL, String.format("%d服用户[%d]\n订单号:%s\n商品id:%s\n补发莲花金币:%d",
                                user.getServerId(), user.getRoleId(), recharge.getId(), recharge.getProductId(), (int)priceMeta.getRmb()
                        ));
                    }
                });
            }
            return sendResp(newCode, json.getString(TuConstants.KEY_INFO_FROM_GAME));
        }
        return SUCCESS;
    }

    /***
     * 发送邮件给指定的玩家
     * @param user 玩家
     * @return 成功返回true，否则返回false。
     */
    private boolean sendTokenEmailByUser(User user,int num) {
        if (user == null) {
            return false;
        }
        String rewardItems = "2|"+num;
        try {
            var rs = emailEndpointService.roleEmail(String.valueOf(user.getRoleId()), user.getCurrentServerId(), MAIL_SUBJECT, MAIL_CONTENT, rewardItems, null, "");
            logger.debug("roleEmail return str:{}", rs);
        } catch (Exception e) {
            logger.error("限购补发莲花金币 failed roleId={},items={}", user.getRoleId(), rewardItems, e);
            throw new RuntimeException("奖励邮件发送失败");
        }
        return true;
    }

    private String sendResp(int code,String msg){
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(TuConstants.KEY_CODE, code);
        resultMap.put(TuConstants.KEY_INFO, msg);
        return JSONObject.toJSONString(resultMap);
    }

//    private Subscription createSubInfo(Recharge recharge, User user, JSONObject payJson, boolean isTest) {
//        recharge.setOriginId(recharge.getId());
//        int orderType = RechargeState.PURCHASED;
//        if (payJson.getBoolean("is_trial_period")) {
//            // 试用情况下，为了统计准确，把金额改为0
//            recharge.setCurrency(0);
//            recharge.setLocalCurrency(0); // 免费订阅 前端af 打点需要
//            orderType = RechargeState.FREE_TRIAL;
//        }
//        recharge.setOrderType(orderType);
//        return rechargeService.createSubscription(user, recharge.getId(), recharge.getId(), recharge.getProductId(), payJson.getString("transaction_id"), payJson.getString("original_transaction_id"),
//                payJson.getLong("purchase_date_ms"), payJson.getLong("expires_date_ms"), isTest ? 0 : -1, orderType);
//    }

    /**
     * 处理福利订单
     *
     * @param user
     * @param recharge
     * @param orderId
     * @return
     */
    private String checkWelfareOrder(User user, Recharge recharge, String orderId) {
        if (!isEffectiveWelfare(user, recharge.getCurrency(), true)) {
            logger.error("User: {}, orderId: {} ,welfare lose effectiveness.", user.getId(), orderId);
            return error("welfare lose effectiveness.");
        }

        if (iOrderService.containsProcessingOrder(recharge)) {
            logger.error("User: {}, orderId: {} already in processed.", user.getId(), orderId);
            return error("order already in processed.");
        }

        if (recharge.getState() != RechargeState.READY) {
            logger.error("User: {}, orderId: {} already pay. productId: {}", user.getId(), orderId, recharge.getProductId());
            return error("order already pay");
        }
        // 加入到正在处理订单列表
        iOrderService.addProcessingOrder(recharge);
        return null;
    }

    /**
     * 实际进行支付操作
     *
     * @param json
     * @param recharge
     */
    private boolean doPay(JSONObject json, Recharge recharge, Subscription subscription, boolean isTest, long serverId) {
        boolean pay = rechargeService.pay(serverId, recharge, subscription, json);
        if (pay) {
            // 修改充值状态
            recharge.setState(RechargeState.PAY);
            rechargeService.update(recharge);
            if (subscription != null) {
                subscription.setState(RechargeState.SUB_PAY);
                rechargeService.update(subscription);
                logger.info("[pay] updateSubscription after pay~ info :: {}", json.toString());
            }
            json.put("orderId", recharge.getPlat_orderId());
            json.put("roleId", recharge.getRoleId());
            json.put("productid", recharge.getProductId());
            json.put("price", recharge.getCurrency());
            json.put("currencyType", recharge.getCurrencyName());
            json.put("priceTax", recharge.getLocalCurrency());
            json.put("currencyTypeTax", recharge.getCurrencyName());
        } else if (subscription != null) {
            subscription.setState(RechargeState.SUB_ERROR);
            rechargeService.update(subscription);
            logger.info("[pay] updateSubscription when error~ info :: {}", json.toString());
        }

        // 处理完成在正在处理列表中删除
        iOrderService.removeProcessingOrder(recharge);

        json.put("ret", pay ? 0 : -1);
        // 无效订单:-2 正式订单:-1 测试订单:0
        int testOrder = isTest ? 0 : -1;
        // 福利订单特殊处理，给前端传0
        if (recharge.getWelfare() == 1) {
            testOrder = 0;
            recharge.setIsTest(testOrder);
            rechargeService.update(recharge);
        }
        json.put("isTest", testOrder);
        return pay;
    }

    @Override
    public boolean queryStock(User user, String productId) {
        if (user == null) {
            logger.error("queryStock user is null.");
            return false;
        }

        if(!JavaUtils.bool(productId)){
            return false;
        }
        String orderId = null;
        if(productId.contains("|")){
            var arr = MetaUtils.parse(productId,'|');
            if(arr == null){
                return false;
            }
            productId = arr[0];
            orderId = arr[1];
        }

        // 拼接订单信息
        // 获取购买的礼包配置
        LibaoConfig.LibaoMeta libaoMeta = libaoWebConfigManager.getById(productId);
        if (libaoMeta == null) {
            logger.error("queryStock user: {}, productId: {} not found.", user.getRoleId(), productId);
            return false;
        }

        LibaoPriceConfig libaoPriceConfig = configService.getConfig(LibaoPriceConfig.class);
        LibaoPriceConfig.LibaoPriceMeta priceMeta = libaoPriceConfig.getById(libaoMeta.getPriceId());
        if (priceMeta == null) {
            logger.error("queryStock user: {}, productId: {}, priceId: {} not found.", user.getRoleId(), productId, libaoMeta.getPriceId());
            return false;
        }

        // 检查是否可以购买，是否被限购
        if (canNotBuy(user, libaoMeta, null)) {
            logger.error("queryStock can not buy user: {}, productId: {}, priceId: {} not found.", user.getRoleId(), productId, libaoMeta.getPriceId());
            return false;
        }

        //检查订单是否已经支付
        if(JavaUtils.bool(orderId)){
            Recharge recharge = rechargeService.get(orderId);
            if(recharge == null){
                logger.error("queryStock can not buy user: {}, productId: {}, order not exist : {} not found.", user.getRoleId(), productId, orderId);
                return false;
            }
            if(recharge.isPayed()){
                logger.error("queryStock can not buy user: {}, productId: {}, order already payed : {} not found.", user.getRoleId(), productId, orderId);
                return false;
            }
        }
        return true;
    }

    public String rolePlatform(HttpServletRequest request) {
        var roleId = Long.parseLong(request.getParameter("roleId"));
        var user = userService.findByRoleId(roleId);
        var platforms = accountBindService.findByAccId(user.getAccId());
        var tuyooPlatform = platforms.stream().filter(p->p.getPlatform() == PlatformType.TUYOO.getId()).findAny();
        if(tuyooPlatform.isPresent()){
            return tuyooPlatform.get().getPlatformId();
        }
        var id = platforms.get(0).getPlatformId();
        var platform = accountBindService.createAccountBind(id,PlatformType.TUYOO.getId(),user.getAccId());
        return platform.getPlatformId();
    }


    public ResponseEntity<String> iosRefund(HttpServletRequest request) {
        Map<String, String> jsonObject = new HashMap<>();
        for (String param : request.getParameterMap().keySet()) {
            jsonObject.put(param, request.getParameter(param));
        }

        logger.info("Receive tuyoo ios refund request :{}",jsonObject);

        String sign = jsonObject.remove("sign");
        String data = HttpUtils.sortAndformatParameters(jsonObject);
        String mysign = MessageDigestUtils.sha256(data+":"+ appkey).toLowerCase();
        String orderId = jsonObject.get("game_order_id");
        if (!mysign.equals(sign)) {
            logger.info("tuyoo sign error, ios refund id {} signdata={}  ", orderId, data);
            return ResponseEntity.status(451).body("sign error");
        }
        Recharge recharge = rechargeService.get(orderId);
        if(recharge == null){
            logger.info("tuyoo ios refund order not exist id {} signdata={} ", orderId, data);
            return ResponseEntity.status(451).body("order not exist");
        }
        if(recharge.isPayed()){
            //已经付款了,记录下日志
            logger.info("Tuyoo ios refund recharge :{} {} {} {}",recharge.getId(),recharge.getRoleId(),recharge.getState(),recharge.getLocalCurrency());
        }
        return ResponseEntity.status(200).body("ok");
    }

}
