import com.lc.billion.icefire.core.support.HttpUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

public class LoginConcurrentTest {
	public static void main(String[] args) {
		String webUrl = "";

		AtomicInteger SEND_CNT = new AtomicInteger(0);
		int total = 10000;
		ExecutorService executor = Executors.newScheduledThreadPool(300);
		long start = System.currentTimeMillis();
		for(int i= 0;i<total;i++) {
			try {
				Map<String, String> loginParams = new HashMap<>();
				loginParams.put("country", "CN");
				loginParams.put("gaid", "");
				loginParams.put("channel", "");
				loginParams.put("bundleid", "com.lc.gos");
				loginParams.put("appversion", "10.4.0");
				loginParams.put("id", "");
				loginParams.put("lang", "English");
				loginParams.put("deviceid", String.valueOf(i));
				loginParams.put("serverId", "-1");
				loginParams.put("platform", "11");
				loginParams.put("token", String.valueOf(i));

				executor.execute(new Runnable() {
					@Override public void run() {
						try {

							HttpUtil.post(webUrl, loginParams);
							int currCnt = SEND_CNT.incrementAndGet();

							if(currCnt == total) {
								System.out.println("total cost " + (System.currentTimeMillis() -start));
								System.exit(1);
							}
						} catch (Exception e) {
							System.out.println(e);
						}
					}
				});

				System.out.println(i + " send~");
				Thread.sleep(2L);
			} catch (Exception e) {
				System.out.println(e);
			}
		}
	}
}
