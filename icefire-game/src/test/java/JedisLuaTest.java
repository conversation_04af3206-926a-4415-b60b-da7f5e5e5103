import com.lc.billion.icefire.game.biz.redis.RedisDataType;

import java.util.List;

public class JedisLuaTest {
    String strVval = """
                local isExist = redis.call('exists', 'test') 
                local ret = {} 
                if isExist == 0 then 
                    redis.call('hmset', 'test', 'server1', 1, 'server2', 2) 
                    return 1 
                else 
                    local count1 = redis.call('hget', 'test', 'server1') 
                    local n1 = tonumber(count1) 
                    if n1 > tonumber(ARGV[1]) then 
                        return 2 
                    else 
                        redis.call('hset', 'test', 'server1', count1 + 1) 
                        return 3 
                    end 
                end 
                """;
//    var obj = webJedisClient.eval(strVval, List.of(), List.of("2"), RedisDataType.GAME_SERVER_INSTANCE);
}
