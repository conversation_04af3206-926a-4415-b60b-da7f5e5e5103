import com.lc.billion.icefire.game.Application
import com.lc.billion.icefire.game.biz.dao.mongo.roles.ArmyDao
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleEnlistmentDao
import com.lc.billion.icefire.game.biz.dao.mongo.roles.SoldierDao
import com.lc.billion.icefire.game.biz.manager.WorkProgressManager
import com.lc.billion.icefire.game.biz.model.work.WorkContext
import com.lc.billion.icefire.game.biz.model.work.WorkState
import com.lc.billion.icefire.game.biz.service.impl.soldier.SoldierCureWorkContext
import com.lc.billion.icefire.game.biz.service.impl.soldier.SoldierUpdateWorkContext

var soldierDao = Application.getBean(SoldierDao.class);
var roleDao = Application.getBean(RoleDao.class);
var workProgressManager = Application.getBean(WorkProgressManager.class);
var roleEnlistmentDao = Application.getBean(RoleEnlistmentDao.class);
var armyDao = Application.getBean(ArmyDao.class);

var role = roleDao.findById(15700003876043);
var soldierInfo = soldierDao.findById(role.getPersistKey());

// 获得城市内士兵
Map<String, Integer> city = Collections.emptyMap();
// 获得医院内士兵
Map<String, Integer> hospital = Collections.emptyMap();
if (null != soldierInfo) {
    city = soldierInfo.getSoliderMap();
    hospital = soldierInfo.getWoundedMap();
}

// 正在治疗的
Map<String, Integer> cure = Collections.emptyMap();
// 正在晋升的
Map<String, Integer> update = Collections.emptyMap();
var workProgresses = workProgressManager.getWorkProgressByRoleId(role.getRoleId());
for (var progress : workProgresses) {
    if (progress.getWorkState() != WorkState.RUNNING) {
        continue;
    }

    WorkContext context = progress.getContext();
    if (context instanceof SoldierCureWorkContext) {
        var soldierCureWork = (SoldierCureWorkContext) context;
        cure = soldierCureWork.getCureMap();
    } else if (context instanceof SoldierUpdateWorkContext) {
        var soldierUpdateWork = (SoldierUpdateWorkContext) context;
        update = new HashMap<>();
        update.put(soldierUpdateWork.getOldSoldierMetaId(), soldierUpdateWork.getCount());
    }
}

var roleEnlistment = roleEnlistmentDao.findById(role.getRoleId());
// 获得后备营士兵
Map<String, Integer> camp = Collections.emptyMap();
// 获得预备役士兵
Map<String, Integer> reserve = Collections.emptyMap();
if (null != roleEnlistment) {
    camp = roleEnlistment.getEnlistSoliderMap();
    reserve = roleEnlistment.getEnlistNowSoliderMap();
}

// 获得部队士兵
Map<Long, Map<String, Integer>> worldAlive = new HashMap<>();
Map<Long, Map<String, Integer>> worldHurt = new HashMap<>();
var armies = armyDao.findByRoleId(role.getRoleId());
for (var army : armies) {
    Map<String, Integer> armyAlive = worldAlive.computeIfAbsent(army.getPersistKey(), o -> new HashMap<>());
    Map<String, Integer> armyHurt = worldHurt.computeIfAbsent(army.getPersistKey(), o -> new HashMap<>());
    for (var soldier : army.getArmySoldiers().values()) {
        if (soldier.getCount() > 0 ) {
            armyAlive.put(soldier.getSoldierMetaId(), soldier.getCount());
        }
        if (soldier.getHurt() > 0) {
            armyHurt.put(soldier.getSoldierMetaId(), soldier.getHurt());
        }
    }
}

// 总兵力=city+update+hospital+cure+worldAlive+worldHurt
Map<String, Integer> all = new HashMap<>(city);
update.forEach((key, value) -> all.merge(key, value, Integer::sum));
hospital.forEach((key, value) -> all.merge(key, value, Integer::sum));
cure.forEach((key, value) -> all.merge(key, value, Integer::sum));
worldAlive.forEach((key, value) -> value.forEach((k, v) -> all.merge(k, v, Integer::sum)));
worldHurt.forEach((key, value) -> value.forEach((k, v) -> all.merge(k, v, Integer::sum)));
int allCount = all.values().stream().mapToInt(Integer::intValue).sum();

Map<String, Object> ret = new HashMap<>();
ret.put("all", allCount);
ret.put("city", city);
ret.put("update", update);
ret.put("hospital", hospital);
ret.put("cure", cure);
ret.put("camp", camp);
ret.put("reserve", reserve);
ret.put("worldAlive", worldAlive);
ret.put("worldHurt", worldHurt);
return ret;
