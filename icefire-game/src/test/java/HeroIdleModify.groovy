import com.lc.billion.icefire.game.Application
import com.lc.billion.icefire.game.biz.dao.mongo.roles.ArmyDao
import com.lc.billion.icefire.game.biz.dao.mongo.roles.PlayerHeroDao
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao
import com.lc.billion.icefire.game.biz.model.hero.HeroState
import com.lc.billion.icefire.game.msg.handler.impl.chat.CgChatRoomSendMessageHandler
import com.simfun.sgf.utils.JavaUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

// 修改玩家没有出征的英雄,但状态确实非Idle的
Logger logger = LoggerFactory.getLogger(CgChatRoomSendMessageHandler.class);

var armyDao = Application.getBean(ArmyDao.class);
var heroDao = Application.getBean(PlayerHeroDao.class);
var roleDao = Application.getBean(RoleDao.class);
var roles = roleDao.findAll();
List<Object> ret = new ArrayList<>();
for (var role : roles) {
    var heros = heroDao.findByRoleId(role.getPersistKey());
    if (!JavaUtils.bool(heros)) {
        continue;
    }

    // 出征武将
    Set<String> armyHeros = new HashSet<>();
    var armies = armyDao.findByRoleId(role.getPersistKey());
    for (var army : armies) {
        armyHeros.addAll(army.getHeros());
    }

    // 是否有不空闲英雄
    for (var hero : heros) {
        // 不在出征队列中
        if (armyHeros.contains(hero.getMetaId())) {
            continue;
        }

        // 且空闲
        if (hero.isIdle()) {
            continue;
        }

        // 不空闲,需要处理
        hero.setState(HeroState.IDLE);
        ret.add(role.getPersistKey());
        ret.add(hero.getMetaId());
        logger.info("heroIdle check state error and deal role:{} hero:{}", role.getPersistKey(), hero.getMetaId());
        heroDao.save(hero);
    }
}

return ret;