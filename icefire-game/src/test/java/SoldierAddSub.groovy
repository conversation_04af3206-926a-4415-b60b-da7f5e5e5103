import com.lc.billion.icefire.game.Application
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao
import com.lc.billion.icefire.game.biz.manager.SoldierManager
import com.lc.billion.icefire.game.msg.handler.impl.chat.CgChatRoomSendMessageHandler
import org.slf4j.Logger
import org.slf4j.LoggerFactory

Logger logger = LoggerFactory.getLogger(CgChatRoomSendMessageHandler.class);

var roleDao = Application.getBean(RoleDao.class);
var soldierManager = Application.getBean(SoldierManager.class);
var role = roleDao.findById(15700003876043);
soldierManager.sub(role, "11008", 18052);
soldierManager.sub(role, "21008", 27086);
soldierManager.sub(role, "31008", 30878);
logger.info("groovy add soldier role:{}", role.getRoleId());