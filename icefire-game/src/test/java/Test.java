import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.simfun.sgf.utils.JavaUtils;

import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @author: 周会勤
 * @create: 2019-09-25 10:59
 **/
public class Test {
    private AtomicInteger status = new AtomicInteger(0);
    public static void main(String[] args) throws ParseException {
//        Map<String, Integer> sMap = Maps.of("21006",10819,"21005",2986,"21004",1798,"21003",5635,"21002",1815,"21001",3435,"21008",3160,"21007",16191,"31003",4447,"31002",2320,"11008",5990,"31005",5627,"31004",3283,"31001",3149,"11001",2791,"11003",4428,"11002",1406,"11005",2255,"31007",16956,"31006",3561,"11004",949,"11007",13457,"11006",3609,"31008",6648 );
        long roleId = 15700003823608L;
        String citySoldier = "{\"body\":\"{\\\"citySoldiers\\\":[{\\\"cityId\\\":\\\"15700003823608\\\",\\\"soldiers\\\":[{\\\"soldierMetaId\\\":\\\"21006\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21005\\\",\\\"count\\\":263,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21004\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21003\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21002\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21001\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21009\\\",\\\"count\\\":57058,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21008\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21007\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11009\\\",\\\"count\\\":71461,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11008\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11001\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11003\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11002\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11005\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11004\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11007\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11006\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31003\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31002\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31005\\\",\\\"count\\\":263,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31004\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31001\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31007\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31006\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31009\\\",\\\"count\\\":56717,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31008\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1}]}],\\\"woundedSoldiers\\\":[{\\\"soldierMetaId\\\":\\\"21006\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21005\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21004\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21003\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21002\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21001\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21009\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21008\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"21007\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11009\\\",\\\"count\\\":10,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11008\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11001\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11003\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11002\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11005\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11004\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11007\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"11006\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31003\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31002\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31005\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31004\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31001\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31007\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31006\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31009\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1},{\\\"soldierMetaId\\\":\\\"31008\\\",\\\"count\\\":0,\\\"power\\\":0.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":1}],\\\"dailyCureCount\\\":15334,\\\"hospitalCapacity\\\":156020,\\\"isRallyHideHero\\\":false,\\\"__isset_bitfield\\\":7}\",\"clusterId\":\"138-sanguo2-local\",\"cost\":\"0\",\"op\":\"GcSoldierList\",\"opNum\":\"401\",\"roleId\":\"15700003823608\",\"seq\":\"32767\",\"serverId\":\"20014\",\"time\":\"1734968951717\",\"traceId\":\"7821390743295178889\",\"type\":\"GC\",\"waitCost\":\"0\",\"__pack_meta__\":\"33|MTczNDI2NzYyMzE2NzAzNjg3NA==|301|155\",\"__topic__\":\"\",\"__source__\":\"**************\",\"__tag__:__hostname__\":\"138-sanguo2-game-10-172-222-117\",\"__tag__:__path__\":\"/home/<USER>/GAME_SERVER/logs/game_msg.log\",\"__tag__:__user_defined_id__\":\"138-sanguo2-online\",\"__tag__:__pack_id__\":\"485AE9370C01A646-ADC3\",\"__tag__:__receive_time__\":\"1734968951\",\"__time__\":\"1734968951\"}";
        String armyProgess = "{\"body\":\"{\\\"progresses\\\":[{\\\"id\\\":2001400000589972,\\\"cityId\\\":15700003823608,\\\"ownerId\\\":15700003823608,\\\"startTime\\\":1734951551392,\\\"updateTime\\\":1734951716386,\\\"remainTime\\\":51692323,\\\"originalTotalTime\\\":51693000,\\\"type\\\":\\\"GATHER_NEW_RES\\\",\\\"workType\\\":\\\"EXPLOITING\\\",\\\"startX\\\":1062,\\\"startY\\\":550,\\\"endX\\\":1012,\\\"endY\\\":513,\\\"targetX\\\":0,\\\"targetY\\\":0,\\\"head\\\":\\\"https://thirdwx.qlogo.cn/mmopen/vi_32/oySibBXd0M0owjOOnHBpLLnmlH3sspf5E0U1mZsZ7XnujGgpmvRQqDLyFuuW9uHuyLJicy4DR7Yia7vI1gGR39bmOvSQsPZ9wGAttBibhcGjIpo/132\\\",\\\"currExploitAmount\\\":0,\\\"maxExploitAmount\\\":8400000,\\\"currExploitSpeed\\\":162.49995,\\\"speedRatio\\\":1.0,\\\"parentArmyownerId\\\":0,\\\"resMetaId\\\":10902,\\\"targetNodeType\\\":\\\"NEW_RES\\\",\\\"exploreHouseGroupId\\\":null,\\\"targetName\\\":null,\\\"rallyArmyWorkType\\\":null,\\\"soldiers\\\":[{\\\"soldierMetaId\\\":\\\"11009\\\",\\\"count\\\":8481,\\\"power\\\":407088.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23},{\\\"soldierMetaId\\\":\\\"21005\\\",\\\"count\\\":100,\\\"power\\\":1400.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23},{\\\"soldierMetaId\\\":\\\"31005\\\",\\\"count\\\":100,\\\"power\\\":1400.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23},{\\\"soldierMetaId\\\":\\\"11005\\\",\\\"count\\\":100,\\\"power\\\":1400.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23},{\\\"soldierMetaId\\\":\\\"21009\\\",\\\"count\\\":8480,\\\"power\\\":407040.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23},{\\\"soldierMetaId\\\":\\\"31009\\\",\\\"count\\\":8480,\\\"power\\\":407040.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23}],\\\"heros\\\":[\\\"2303\\\"],\\\"startPointSize\\\":{\\\"x\\\":1062,\\\"y\\\":550,\\\"size\\\":1,\\\"__isset_bitfield\\\":7},\\\"endPointSize\\\":{\\\"x\\\":1012,\\\"y\\\":513,\\\"size\\\":1,\\\"__isset_bitfield\\\":7},\\\"roleInfo\\\":{\\\"bitMap\\\":2,\\\"bitMapEx\\\":0,\\\"id\\\":0,\\\"name\\\":null,\\\"head\\\":null,\\\"level\\\":0,\\\"x\\\":0,\\\"y\\\":0,\\\"serverPos\\\":0,\\\"allianceAliasName\\\":null,\\\"power\\\":0,\\\"serverId\\\":0,\\\"allianceName\\\":null,\\\"officialMetaId\\\":null,\\\"currentServerId\\\":0,\\\"headFrame\\\":\\\"2003\\\",\\\"oServerId\\\":157,\\\"__isset_bitfield\\\":1025},\\\"isRallyHideHero\\\":false,\\\"currency\\\":null,\\\"building\\\":false,\\\"__isset_bitfield\\\":911359},{\\\"id\\\":2001400000853656,\\\"cityId\\\":15700003823608,\\\"ownerId\\\":15700003823608,\\\"startTime\\\":1734967654777,\\\"updateTime\\\":1734967859328,\\\"remainTime\\\":51692323,\\\"originalTotalTime\\\":51693000,\\\"type\\\":\\\"GATHER_NEW_RES\\\",\\\"workType\\\":\\\"EXPLOITING\\\",\\\"startX\\\":1062,\\\"startY\\\":550,\\\"endX\\\":1004,\\\"endY\\\":603,\\\"targetX\\\":0,\\\"targetY\\\":0,\\\"head\\\":\\\"https://thirdwx.qlogo.cn/mmopen/vi_32/oySibBXd0M0owjOOnHBpLLnmlH3sspf5E0U1mZsZ7XnujGgpmvRQqDLyFuuW9uHuyLJicy4DR7Yia7vI1gGR39bmOvSQsPZ9wGAttBibhcGjIpo/132\\\",\\\"currExploitAmount\\\":0,\\\"maxExploitAmount\\\":8400000,\\\"currExploitSpeed\\\":162.49995,\\\"speedRatio\\\":1.0,\\\"parentArmyownerId\\\":0,\\\"resMetaId\\\":10904,\\\"targetNodeType\\\":\\\"NEW_RES\\\",\\\"exploreHouseGroupId\\\":null,\\\"targetName\\\":null,\\\"rallyArmyWorkType\\\":null,\\\"soldiers\\\":[{\\\"soldierMetaId\\\":\\\"11009\\\",\\\"count\\\":8504,\\\"power\\\":408192.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23},{\\\"soldierMetaId\\\":\\\"21005\\\",\\\"count\\\":87,\\\"power\\\":1218.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23},{\\\"soldierMetaId\\\":\\\"31005\\\",\\\"count\\\":87,\\\"power\\\":1218.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23},{\\\"soldierMetaId\\\":\\\"21009\\\",\\\"count\\\":8504,\\\"power\\\":408192.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23},{\\\"soldierMetaId\\\":\\\"31009\\\",\\\"count\\\":8504,\\\"power\\\":408192.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23}],\\\"heros\\\":[\\\"4301\\\",\\\"3301\\\",\\\"3302\\\"],\\\"startPointSize\\\":{\\\"x\\\":1062,\\\"y\\\":550,\\\"size\\\":1,\\\"__isset_bitfield\\\":7},\\\"endPointSize\\\":{\\\"x\\\":1004,\\\"y\\\":603,\\\"size\\\":1,\\\"__isset_bitfield\\\":7},\\\"roleInfo\\\":{\\\"bitMap\\\":2,\\\"bitMapEx\\\":0,\\\"id\\\":0,\\\"name\\\":null,\\\"head\\\":null,\\\"level\\\":0,\\\"x\\\":0,\\\"y\\\":0,\\\"serverPos\\\":0,\\\"allianceAliasName\\\":null,\\\"power\\\":0,\\\"serverId\\\":0,\\\"allianceName\\\":null,\\\"officialMetaId\\\":null,\\\"currentServerId\\\":0,\\\"headFrame\\\":\\\"2003\\\",\\\"oServerId\\\":157,\\\"__isset_bitfield\\\":1025},\\\"isRallyHideHero\\\":false,\\\"currency\\\":null,\\\"building\\\":false,\\\"__isset_bitfield\\\":911359},{\\\"id\\\":2001400000853690,\\\"cityId\\\":15700003823608,\\\"ownerId\\\":15700003823608,\\\"startTime\\\":1734967657498,\\\"updateTime\\\":1734967798383,\\\"remainTime\\\":51692323,\\\"originalTotalTime\\\":51693000,\\\"type\\\":\\\"GATHER_NEW_RES\\\",\\\"workType\\\":\\\"EXPLOITING\\\",\\\"startX\\\":1062,\\\"startY\\\":550,\\\"endX\\\":1109,\\\"endY\\\":574,\\\"targetX\\\":0,\\\"targetY\\\":0,\\\"head\\\":\\\"https://thirdwx.qlogo.cn/mmopen/vi_32/oySibBXd0M0owjOOnHBpLLnmlH3sspf5E0U1mZsZ7XnujGgpmvRQqDLyFuuW9uHuyLJicy4DR7Yia7vI1gGR39bmOvSQsPZ9wGAttBibhcGjIpo/132\\\",\\\"currExploitAmount\\\":0,\\\"maxExploitAmount\\\":8400000,\\\"currExploitSpeed\\\":162.49995,\\\"speedRatio\\\":1.0,\\\"parentArmyownerId\\\":0,\\\"resMetaId\\\":10904,\\\"targetNodeType\\\":\\\"NEW_RES\\\",\\\"exploreHouseGroupId\\\":null,\\\"targetName\\\":null,\\\"rallyArmyWorkType\\\":null,\\\"soldiers\\\":[{\\\"soldierMetaId\\\":\\\"11009\\\",\\\"count\\\":8537,\\\"power\\\":409776.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23},{\\\"soldierMetaId\\\":\\\"21009\\\",\\\"count\\\":8537,\\\"power\\\":409776.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23},{\\\"soldierMetaId\\\":\\\"31009\\\",\\\"count\\\":8536,\\\"power\\\":409728.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23}],\\\"heros\\\":[\\\"4101\\\",\\\"2201\\\"],\\\"startPointSize\\\":{\\\"x\\\":1062,\\\"y\\\":550,\\\"size\\\":1,\\\"__isset_bitfield\\\":7},\\\"endPointSize\\\":{\\\"x\\\":1109,\\\"y\\\":574,\\\"size\\\":1,\\\"__isset_bitfield\\\":7},\\\"roleInfo\\\":{\\\"bitMap\\\":2,\\\"bitMapEx\\\":0,\\\"id\\\":0,\\\"name\\\":null,\\\"head\\\":null,\\\"level\\\":0,\\\"x\\\":0,\\\"y\\\":0,\\\"serverPos\\\":0,\\\"allianceAliasName\\\":null,\\\"power\\\":0,\\\"serverId\\\":0,\\\"allianceName\\\":null,\\\"officialMetaId\\\":null,\\\"currentServerId\\\":0,\\\"headFrame\\\":\\\"2003\\\",\\\"oServerId\\\":157,\\\"__isset_bitfield\\\":1025},\\\"isRallyHideHero\\\":false,\\\"currency\\\":null,\\\"building\\\":false,\\\"__isset_bitfield\\\":911359},{\\\"id\\\":2001400000853716,\\\"cityId\\\":15700003823608,\\\"ownerId\\\":15700003823608,\\\"startTime\\\":1734967660462,\\\"updateTime\\\":1734967747417,\\\"remainTime\\\":51692323,\\\"originalTotalTime\\\":51693000,\\\"type\\\":\\\"GATHER_NEW_RES\\\",\\\"workType\\\":\\\"EXPLOITING\\\",\\\"startX\\\":1062,\\\"startY\\\":550,\\\"endX\\\":1081,\\\"endY\\\":575,\\\"targetX\\\":0,\\\"targetY\\\":0,\\\"head\\\":\\\"https://thirdwx.qlogo.cn/mmopen/vi_32/oySibBXd0M0owjOOnHBpLLnmlH3sspf5E0U1mZsZ7XnujGgpmvRQqDLyFuuW9uHuyLJicy4DR7Yia7vI1gGR39bmOvSQsPZ9wGAttBibhcGjIpo/132\\\",\\\"currExploitAmount\\\":0,\\\"maxExploitAmount\\\":8400000,\\\"currExploitSpeed\\\":162.49995,\\\"speedRatio\\\":1.0,\\\"parentArmyownerId\\\":0,\\\"resMetaId\\\":10904,\\\"targetNodeType\\\":\\\"NEW_RES\\\",\\\"exploreHouseGroupId\\\":null,\\\"targetName\\\":null,\\\"rallyArmyWorkType\\\":null,\\\"soldiers\\\":[{\\\"soldierMetaId\\\":\\\"11009\\\",\\\"count\\\":8537,\\\"power\\\":409776.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23},{\\\"soldierMetaId\\\":\\\"21009\\\",\\\"count\\\":8537,\\\"power\\\":409776.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23},{\\\"soldierMetaId\\\":\\\"31009\\\",\\\"count\\\":8536,\\\"power\\\":409728.0,\\\"hurt\\\":0,\\\"badlyInjured\\\":0,\\\"dead\\\":0,\\\"__isset_bitfield\\\":23}],\\\"heros\\\":[\\\"2202\\\"],\\\"startPointSize\\\":{\\\"x\\\":1062,\\\"y\\\":550,\\\"size\\\":1,\\\"__isset_bitfield\\\":7},\\\"endPointSize\\\":{\\\"x\\\":1081,\\\"y\\\":575,\\\"size\\\":1,\\\"__isset_bitfield\\\":7},\\\"roleInfo\\\":{\\\"bitMap\\\":2,\\\"bitMapEx\\\":0,\\\"id\\\":0,\\\"name\\\":null,\\\"head\\\":null,\\\"level\\\":0,\\\"x\\\":0,\\\"y\\\":0,\\\"serverPos\\\":0,\\\"allianceAliasName\\\":null,\\\"power\\\":0,\\\"serverId\\\":0,\\\"allianceName\\\":null,\\\"officialMetaId\\\":null,\\\"currentServerId\\\":0,\\\"headFrame\\\":\\\"2003\\\",\\\"oServerId\\\":157,\\\"__isset_bitfield\\\":1025},\\\"isRallyHideHero\\\":false,\\\"currency\\\":null,\\\"building\\\":false,\\\"__isset_bitfield\\\":911359}]}\",\"clusterId\":\"138-sanguo2-local\",\"cost\":\"1\",\"op\":\"GcArmyProgress\",\"opNum\":\"500\",\"roleId\":\"15700003823608\",\"seq\":\"-32751\",\"serverId\":\"20014\",\"time\":\"1734968951718\",\"traceId\":\"7821390743295178889\",\"type\":\"GC\",\"waitCost\":\"0\",\"__pack_meta__\":\"33|MTczNDI2NzYyMzE2NzAzNjg3NA==|301|172\",\"__topic__\":\"\",\"__source__\":\"**************\",\"__tag__:__hostname__\":\"138-sanguo2-game-10-172-222-117\",\"__tag__:__path__\":\"/home/<USER>/GAME_SERVER/logs/game_msg.log\",\"__tag__:__user_defined_id__\":\"138-sanguo2-online\",\"__tag__:__pack_id__\":\"485AE9370C01A646-ADC3\",\"__tag__:__receive_time__\":\"1734968951\",\"__time__\":\"1734968951\"}";
        Map<String, Integer> citySoldierMap = parseSoldierList(citySoldier, roleId);
        parseArmyList(armyProgess, roleId, citySoldierMap);
//        String jsonBefore = "{\"project_id\":\"20562\",\"client_id\":\"H5_5.2_weixin.weixin.0-hall20562.weixin.sanguo2\",\"event_time\":1735205409912,\"event\":\"soldier_detail\",\"type\":\"game\",\"properties\":{\"proj_atk_point\":\"\",\"proj_server_id\":\"20014\",\"proj_reason\":\"\",\"proj_all\":\"305866\",\"proj_trace_id\":\"\",\"proj_reserve\":\"{}\",\"proj_world_alive\":\"{}\",\"proj_city_inner\":\"{21006=5925, 11009=101029, 21005=1905, 11005=100, 21009=33587, 21008=46397, 31009=93608, 21007=9322, 31008=13993}\",\"proj_reason_type\":\"GM_GIVE\",\"proj_level\":\"27\",\"proj_level_id\":\"1029\",\"proj_user_id_odd\":\"0\",\"proj_update\":\"{}\",\"proj_season\":\"2\",\"proj_hospital\":\"{}\",\"proj_cure\":\"{}\",\"proj_camp\":\"{}\",\"proj_def_point\":\"\",\"proj_oserver_id\":\"157\",\"proj_total_online_time\":\"1359860\",\"proj_vip_level\":\"7\",\"proj_world_hurt\":\"{}\",\"proj_role_id\":\"15700003850203\"},\"lib\":{},\"device_id\":\"\",\"user_id\":\"103677934\"}";
        String jsonAfter = "{\"client_id\":\"H5_5.2_weixin.weixin.0-hall20562.weixin.sanguo2\",\"device_id\":\"\",\"event\":\"soldier_detail\",\"event_time\":\"1735092214710\",\"lib\":\"{}\",\"project_id\":\"20562\",\"properties\":\"{\\\"proj_atk_point\\\":\\\"\\\",\\\"proj_server_id\\\":\\\"20014\\\",\\\"proj_reason\\\":\\\"\\\",\\\"proj_all\\\":\\\"187701\\\",\\\"proj_trace_id\\\":\\\"4438613063329669097\\\",\\\"proj_reserve\\\":\\\"{}\\\",\\\"proj_world_alive\\\":\\\"{}\\\",\\\"proj_city_inner\\\":\\\"{11009=54598, 21005=450, 31005=450, 11005=100, 21009=71808, 31009=59890}\\\",\\\"proj_reason_type\\\":\\\"SOLDIER_UPGRADE_PICK\\\",\\\"proj_level\\\":\\\"26\\\",\\\"proj_level_id\\\":\\\"766\\\",\\\"proj_user_id_odd\\\":\\\"1\\\",\\\"proj_update\\\":\\\"{}\\\",\\\"proj_season\\\":\\\"2\\\",\\\"proj_hospital\\\":\\\"{11009=332, 31009=73}\\\",\\\"proj_cure\\\":\\\"{}\\\",\\\"proj_camp\\\":\\\"{}\\\",\\\"proj_def_point\\\":\\\"\\\",\\\"proj_oserver_id\\\":\\\"157\\\",\\\"proj_total_online_time\\\":\\\"1210597\\\",\\\"proj_vip_level\\\":\\\"6\\\",\\\"proj_world_hurt\\\":\\\"{}\\\",\\\"proj_role_id\\\":\\\"15700003823608\\\"}\",\"type\":\"game\",\"user_id\":\"103819511\",\"__pack_meta__\":\"2|MTczMTc3NTU2MDA3MjYyNjQ1NA==|330|106\",\"__topic__\":\"\",\"__source__\":\"**************\",\"__tag__:__hostname__\":\"138-sanguo2-game-10-172-222-117\",\"__tag__:__path__\":\"/home/<USER>/ga_log/Ga_138-sanguo2-game-10-172-222-117_20241225_10.log\",\"__tag__:__user_defined_id__\":\"138-sanguo2-online\",\"__tag__:__pack_id__\":\"C44DFF0478417765-B5E\",\"__tag__:__receive_time__\":\"1735092214\",\"__time__\":\"1735092214\"}";
//        var resultBefore = pareSoldier(jsonBefore, roleId);
        var resultAfter = pareSoldier(jsonAfter, roleId);
        for (var entry : citySoldierMap.entrySet()) {
            var id = entry.getKey();
            int num = entry.getValue();
            var after = resultAfter.getOrDefault(id, 0);
            if (Math.abs(num - after) > 1000) {
                System.out.println("soldierManager.add(role, \""+ id + "\", " + (num - after) + ");");
            }
        }
//        System.out.println(resultBefore + "before");
//        System.out.println(resultAfter + "after");
    }
    private static Map<String, Integer> parseArmyList(String json, long roleId, Map<String, Integer> map) {
        if (!JavaUtils.bool(json)) {
            return map;
        }
        var jsonData = JSON.parseObject(json);
        var body = jsonData.getJSONObject("body");
        if (jsonData.getLong("roleId") != roleId) {
            throw new RuntimeException("GCSoldierList与" + roleId + "不一致");
        }
        var progresses = body.getJSONArray("progresses");
        for (var s : progresses) {
            var data = (JSONObject)s;
            var soldiers = data.getJSONArray("soldiers");
            if (soldiers == null || soldiers.isEmpty()) {
                continue;
            }
            for (var soldier : soldiers) {
                var metaId = ((JSONObject)soldier).getString("soldierMetaId");
                var count = ((JSONObject)soldier).getIntValue("count");
                map.put(metaId, map.getOrDefault(metaId, 0) + count);
            }
        }
        return map;
    }
    private static Map<String, Integer> parseSoldierList(String json, long roleId) {
        Map<String, Integer> map = new HashMap<>();
        var jsonData = JSON.parseObject(json);
        var body = jsonData.getJSONObject("body");
        if (jsonData.getLong("roleId") != roleId) {
            throw new RuntimeException("GCSoldierList与" + roleId + "不一致");
        }
        var citySoldiers = (JSONObject)body.getJSONArray("citySoldiers").get(0);
        var soldiers = citySoldiers.getJSONArray("soldiers");
        var woundedSoldiers = body.getJSONArray("woundedSoldiers");
        for (var s : soldiers) {
            var data = (JSONObject)s;
            String metaId = data.getString("soldierMetaId");
            var count = data.getIntValue("count");
            if (count > 0) {
                map.put(metaId, map.getOrDefault(metaId, 0) + count);
            }
        }
        for (var s : woundedSoldiers) {
            var data = (JSONObject)s;
            String metaId = data.getString("soldierMetaId");
            var count = data.getIntValue("count");
            if (count > 0) {
                map.put(metaId, map.getOrDefault(metaId, 0) + count);
            }
        }
        return map;
    }
    private static Map<String, Integer> pareSoldier(String json1, long roleId) {
        Map<String, Integer> map = new HashMap<>();
        JSONObject jsonData = JSON.parseObject(json1.replace("=", ":"));
        var prop = jsonData.getJSONObject("properties");
        var proj_world_alive = prop.getJSONObject("proj_world_alive");
//        parese(prop, map, "proj_city_inner");
        var jsonRole = prop.getLong("proj_role_id");
        if (roleId != jsonRole) {
            throw new RuntimeException("角色id不同，不能对比");
        }
        var proj_city_inner = prop.getJSONObject("proj_city_inner");
        for (var entry : proj_city_inner.entrySet()) {
            var id = entry.getKey();
            var num = (Integer)entry.getValue();
            map.put(id, map.getOrDefault(id, 0) + num);
        }
        if (proj_world_alive != null) {
            for (Object alive : proj_world_alive.values()) {
                var aliveMap = (Map<String, Integer>) alive;
                for (var dd : aliveMap.entrySet()) {
                    map.put(dd.getKey(), map.getOrDefault(dd.getKey(), 0) + dd.getValue());
                }
            }
        }

        var world_hurt = prop.getJSONObject("proj_world_hurt");
        if (world_hurt != null) {
            for (Object alive : world_hurt.values()) {
                var aliveMap = (Map<String, Integer>) alive;
                for (var dd : aliveMap.entrySet()) {
                    map.put(dd.getKey(), map.getOrDefault(dd.getKey(), 0) + dd.getValue());
                }
            }
        }
        parese(prop, map, "proj_cure");
//        parese(prop, map, "proj_camp");
//        parese(prop, map, "proj_reserve");
        parese(prop, map, "proj_update");
        parese(prop, map, "proj_hospital");
        Integer all = prop.getInteger("proj_all");
        var calcValue = map.values().stream().mapToInt(Integer::intValue).sum();
        if (all != calcValue) {
            throw new RuntimeException("all=" + all + "calcValue=" + calcValue);
        }
        return map;
    }
    static void parese(JSONObject prop, Map<String, Integer> map, String fieldName) {
        var data = prop.getJSONObject(fieldName);
        for (var entry : data.entrySet()) {
            map.put(entry.getKey(), map.getOrDefault(entry.getKey(), 0) + (Integer)entry.getValue());
        }
    }
}
