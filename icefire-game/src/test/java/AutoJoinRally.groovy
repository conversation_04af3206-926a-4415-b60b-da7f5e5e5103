import com.google.firebase.database.utilities.Pair
import com.lc.billion.icefire.core.common.TimeUtil
import com.lc.billion.icefire.game.Application
import com.lc.billion.icefire.game.biz.dao.mongo.alliances.AllianceMemberDao
import com.lc.billion.icefire.game.biz.manager.RoleExtraManager
import com.lc.billion.icefire.game.biz.model.alliance.AllianceMember
import com.simfun.sgf.utils.JavaUtils

AllianceMemberDao allianceMemberDao = Application.getBean(AllianceMemberDao.class);
RoleExtraManager roleExtraManager = Application.getBean(RoleExtraManager.class);

Long allianceId = 10000000000034L;
int serverId = 30000;

var memberList = allianceMemberDao.findByAllianceId(allianceId);
if (!JavaUtils.bool(memberList)) {
    return;
}

long now = TimeUtil.getNow();
// 开启自动集结
List<AllianceMember> activeMembers = new ArrayList<>();
// 离线自动集结
List<AllianceMember> unactiveMembers = new ArrayList<>();
for (var member : memberList) {
    var memberExtra = roleExtraManager.getRoleExtra(member.getPersistKey());
    if (null == memberExtra) {
        continue;
    }

    // 不在一个服，过滤
    if (memberExtra.getCurrentServerId() != serverId) {
        continue;
    }

    if (member.isOnline()) {
        if (memberExtra.getAutoRallyPveEndTime() <= 0 || memberExtra.getAutoRallyPveEndTime() < now) {
            continue;
        }

        activeMembers.add(member);
    } else {
        if (memberExtra.isDisableOfflineAutoRally()) {
            continue;
        }

        unactiveMembers.add(member);
    }
}

// 分别排序
var activeSort = activeMembers.stream().sorted(Comparator.comparing(AllianceMember::getLastAutoJoinRally)).toList();
var unactiveSort = unactiveMembers.stream().sorted(Comparator.comparing(AllianceMember::getLastAutoJoinRally)).toList();

List<Object> ret = new ArrayList<>();
for (var entry : activeSort) {
    ret.add(new Pair<Long, Long>(entry.getPersistKey(), entry.getLastAutoJoinRally()));
}
ret.add("-------");
for (var entry : unactiveSort) {
    ret.add(new Pair<Long, Long>(entry.getPersistKey(), entry.getLastAutoJoinRally()));
}

Long roleId = 10000000240376L;
var memberExtra = roleExtraManager.getRoleExtra(roleId);
ret.add(memberExtra.getAutoRallyArmyType());

return ret;