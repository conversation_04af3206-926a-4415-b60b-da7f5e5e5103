import com.lc.billion.icefire.game.Application
import com.lc.billion.icefire.game.biz.dao.AbstractMemoryDao
import com.lc.billion.icefire.game.biz.dao.RolesEntityDao
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao
import com.lc.billion.icefire.game.biz.dao.mongo.root.RoleServerInfoDao
import com.lc.billion.icefire.game.biz.model.AbstractEntity
import com.lc.billion.icefire.game.biz.model.IRolesEntity
import com.lc.billion.icefire.game.biz.model.role.Role
import com.lc.billion.icefire.game.biz.model.role.RoleServerInfo
import com.lc.billion.icefire.game.biz.service.impl.DaoService
import com.lc.billion.icefire.game.msg.handler.impl.chat.CgChatRoomSendMessageHandler
import com.longtech.ls.zookeeper.ConfigCenter
import org.jongo.MongoCursor
import org.slf4j.Logger
import org.slf4j.LoggerFactory

// 新手服迁服失败,之迁了部分数据到目标服时,可以使用该接口将新手服未处理完的数据迁到目标服
// 需要确认web中的指向是否正确
// 之后需要调用MigrateTargetFix处理目标服数据

Logger logger = LoggerFactory.getLogger(CgChatRoomSendMessageHandler.class);
ConfigCenter configCenter = Application.getBean(ConfigCenter.class);

// 非新手服不执行
if (!configCenter.getCurrentGameServerConfig().isGuideServer()) {
    return 1;
}

int srcServerId = 9313;
int targetServerId = 249;
long roleId = 24900014624823;

DaoService daoService = Application.getBean(DaoService.class);
RoleDao roleDao = Application.getBean(RoleDao.class);
RoleServerInfoDao roleServerInfoDao = Application.getBean(RoleServerInfoDao.class);

Role role = roleDao.findById(roleId);
if (null != role) {
    // 角色在内存中,是完整的数据,请走正常迁服流程
    return 2;
}

if(null != role && role.isClearing()) {
    return 3;
}

for (var dao : daoService.daosByDaoName.values()) {
    // 先跳过用户
    if (dao instanceof RoleDao) {
        continue;
    }

    if (!(dao instanceof RolesEntityDao)) {
        continue;
    }

    RolesEntityDao roleD = (RolesEntityDao) dao;
    var memData = roleD.findByRoleId(roleId);
    if (null != memData && !memData.isEmpty()) {
        // 在内存中存在,则先删除
        for (var entry : memData) {
            if (null == entry) {
                continue;
            }

            AbstractEntity dbEntry = (AbstractEntity) entry;
            var _entry = roleD.findById(dbEntry.getPersistKey());
            if (null == _entry) {
                continue;
            }

            IRolesEntity roleEntry = (IRolesEntity) entry;
            if (null != roleEntry) {
                logger.info("MigrateGuideFix memData roleId:{} clsName:{} {}", roleEntry.getRoleId(), roleEntry.getClass().getSimpleName(), dbEntry.getPersistKey())
            } else {
                logger.info("MigrateGuideFix memData dao:{} clsName:{}", roleD.getClass(), entry.getClass())
            }

            // 从内存中删除
            var method = AbstractMemoryDao.class.getDeclaredMethod("removeMemory",Long.class);
            method.setAccessible(true)
            method.invoke(roleD, dbEntry.getPersistKey())
        }
    }

    var entities = roleD.transform(roleD.doFindByPlayerId(srcServerId, roleId));
    for (var entry : entities) {
        AbstractEntity dbEntry = (AbstractEntity) entry;
        IRolesEntity roleEntry = (IRolesEntity) entry;
        if (null != roleEntry) {
            logger.info("MigrateGuideFix dbData roleId:{} clsName:{} {}", roleEntry.getRoleId(), roleEntry.getClass().getSimpleName(), dbEntry.getPersistKey())
        } else {
            logger.info("MigrateGuideFix dbData notRole clsName:{}", dbEntry.getClass().getSimpleName())
        }
        // 保存到目标服务
        roleD.dbSave(targetServerId, dbEntry);

        // 删除新手服
        roleD.deleteFromDB(srcServerId, dbEntry.getPersistKey());
    }
}

MongoCursor<RoleServerInfo> dbEntries = roleServerInfoDao.dbFind(srcServerId, Arrays.asList("_id"), Arrays.asList(roleId));
if (null == dbEntries || dbEntries.isEmpty()) {
    return 4;
}
Collection<RoleServerInfo> roleServerInfos = roleServerInfoDao.transform(dbEntries);
RoleServerInfo roleServerInfo = roleServerInfos[0];
roleServerInfo.setRegisterServerId(targetServerId);
roleServerInfo.setMigrateServerId(targetServerId);
logger.info("MigrateGuideFix dbData RoleServerInfo roleId:{}", roleServerInfo.getRoleId());
// 保存到目标服务
roleServerInfoDao.dbSave(targetServerId, roleServerInfo);
// 删除新手服
roleServerInfoDao.deleteFromDB(srcServerId, roleServerInfo.getPersistKey());

return 0;
