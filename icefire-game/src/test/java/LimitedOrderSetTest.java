import com.lc.billion.icefire.core.common.RandomUtils;
import com.lc.billion.icefire.core.utils.LimitedOrderSet;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankConstants;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
public class LimitedOrderSetTest {

	private static abstract class RunnableTask implements Runnable {
		final int i;

		public RunnableTask(int i) {
			this.i = i;
		}
	}

	public static void t0() {
		System.out.println("t0");
		int limit = 5;
		LimitedOrderSet orderSet = new LimitedOrderSet(limit, true);
		for (int i = 0; i < limit; i++) {
			String memberId = "t" + (i + 1);
			double score = 100 * (i + 1);
			orderSet.update(memberId, score);
		}
		System.out.println(orderSet.print());

		for (int i = 0; i < limit; i++) {
			String memberId = "t" + (limit + i + 1);
			double score = 100 * (i + 1);
			orderSet.update(memberId, score);
		}
		System.out.println(orderSet.print());

		for (int i = 0; i < limit; i++) {
			LimitedOrderSet.OrderItem item = orderSet.getFirst();
			orderSet.update(item.getKey(), 10.0);
		}
		System.out.println(orderSet.print());

		orderSet.clear();
		for (int i = 0; i < 2 * limit; i++) {
			String memberId = "t" + (i + 1);
			double score = 100 * (i + 1);
			orderSet.update(memberId, score);
		}
		System.out.println(orderSet.print());
	}

	public static void t1_cover() {

		System.out.println("t1");

		LimitedOrderSet orderSet = new LimitedOrderSet(20, true);
		System.out.println("limit=" + orderSet.getLimit());

		long t = System.currentTimeMillis();

		for (int i = 0; i < 16; i++) {
			String memberId = "t" + (i + 1);
			long score = 100 * (i + 1);
			double saveScore = BigDecimal.valueOf(Double.parseDouble(score + "." + (RankConstants.TIME_MAX_VALUE - System.currentTimeMillis()))).doubleValue();
			orderSet.update(memberId, saveScore);
		}
		System.out.println(orderSet.print());

		LimitedOrderSet.OrderItem t5 = orderSet.getItem("t5");
		orderSet.update(t5.getKey(), 10000.0);
		System.out.println("update t5 10000.0");
		System.out.println(orderSet.print());

		orderSet.update("t15", 15000.0);
		System.out.println("update t15 15000.0");
		System.out.println(orderSet.print());

		LimitedOrderSet.OrderItem no_10 = orderSet.getItem(10);
		orderSet.update(no_10.getKey(), 280.0);
		System.out.println("update no.10 280.0");
		System.out.println(orderSet.print());

		LimitedOrderSet.OrderItem no_6 = orderSet.getItem(6);
		System.out.println("no_6=" + no_6);

		List<LimitedOrderSet.OrderItem> l1 = orderSet.getRange(0, 10);
		System.out.println("l1(0, 10)=" + l1.size() + " : " + l1);

		List<LimitedOrderSet.OrderItem> l2 = orderSet.getRange(5, 8);
		System.out.println("l2(5, 8)=" + l2.size() + " : " + l2);

		List<LimitedOrderSet.OrderItem> l3 = orderSet.getRange(18, 28);
		System.out.println("l3(18, 28)=" + l3.size() + " : " + l3);

		List<LimitedOrderSet.OrderItem> l4 = orderSet.getRange(-10, Integer.MAX_VALUE);
		System.out.println("l4(18, 28)=" + l4.size() + " : " + l4);
		System.out.println();

		System.out.println(orderSet.print());
		System.out.println("t5(" + t5.getKey() + ") index=" + orderSet.getIndex(t5));
		System.out.println("t5 index=" + orderSet.getIndex("t5"));
		System.out.println("no_10(" + no_10.getKey() + ") index=" + orderSet.getIndex(no_10));
		System.out.println("getFirst=" + orderSet.getFirst());

		System.out.println("orderSet.asList");
		for (LimitedOrderSet.OrderItem item : orderSet.asList()) {
			System.out.println(item.getKey() + ": " + item.getValue());
		}
		System.out.println();

		System.out.println("orderSet.asKeyValueMap");
		for (Map.Entry<String, Double> entry : orderSet.asKeyValueMap().entrySet()) {
			System.out.println(entry.getKey() + ": " + entry.getValue());
		}

		orderSet.clear();
		System.out.println("orderSet.clear " + orderSet);

		System.out.println("orderSet.INCREASE");
		orderSet = new LimitedOrderSet(10, false);
		for (int i = 0; i < 20; i++) {
			String memberId = "t" + (i + 1);
			double score = 100 * (i + 1);
			orderSet.update(memberId, score);
		}
		System.out.println(orderSet.print());

		System.out.println("t1 ok!");
		System.out.println();
	}

	public static void t2_cover() {

		System.out.println("t2");

		int limit = 5;
		int c = limit + 10;

		LimitedOrderSet orderList = new LimitedOrderSet(limit);

		long t = System.currentTimeMillis();

		for (int i = 0; i < c; i++) {
			String memberId = "m" + i;
			Double score = 100.0 * (c - i);
			orderList.update(memberId, score);
		}

		System.out.println(orderList.print());

		LimitedOrderSet.OrderItem t1 = orderList.getItem(1);
		System.out.println("update rank 1(" + t1.getKey() + ") value to 10");
		orderList.update(t1.getKey(), 10.0);
		System.out.println(orderList.print());

		t1 = orderList.getItem(1);
		System.out.println("update rank 1(" + t1.getKey() + ") value to 10");
		orderList.update(t1.getKey(), 10.0);
		System.out.println(orderList.print());

		t1 = orderList.getItem(1);
		System.out.println("update rank 1(" + t1.getKey() + ") value to 8");
		orderList.update(t1.getKey(), 8.0);
		System.out.println(orderList.print());

		LimitedOrderSet.OrderItem t5 = orderList.getItem(5);
		System.out.println("update rank 5(" + t5.getKey() + ") value to 800");
		orderList.update(t5.getKey(), 800.0);
		System.out.println(orderList.print());

		System.out.println("t2 ok!");
		System.out.println();
	}

	public static void t3_performance() {

		System.out.println("t3 single thread performance");

		final int limit = 100_0000;
		LimitedOrderSet orderSet = new LimitedOrderSet(limit);

		long t = System.currentTimeMillis();
		int c = 200_0000;
		for (int i = 0; i < c; i++) {
			String memberId = String.valueOf(i + 1);
			double score = RandomUtils.random(100000.0, 2000000.0);
			orderSet.update(memberId, score);
		}

		long use = (System.currentTimeMillis() - t);
		// orderSet.foreach(System.out::println);
		System.out.println(orderSet);
		System.out.println("c:" + c + ", limit:" + limit + ", total ms: " + use + ", avg ms: " + (double) use / c);

		System.out.println("t3 ok!");
		System.out.println();
	}

	public static void t4_performance() {

		System.out.println("t4 multi thread performance");
		final int limit = 1000_0000;
		final LimitedOrderSet orderSet = new LimitedOrderSet(limit);

		ExecutorService executor = Executors.newFixedThreadPool(16);

		long t = System.currentTimeMillis();

		final int c1 = 1_00;
		final int c2 = 100_0000;
		AtomicInteger sum = new AtomicInteger(0);
		final CountDownLatch countDownLatch = new CountDownLatch(c1);
		for (int i = 0; i < c1; i++) {
			executor.submit(new RunnableTask(i) {
				@Override
				public void run() {
					long t = System.currentTimeMillis();

					for (int i1 = 0; i1 < c2; i1++) {
						String memberId = this.i + "_" + (i1 + 1);
						long score = RandomUtils.randomIntValue(1, 200000);
						double saveScore = BigDecimal.valueOf(Double.parseDouble(score + "." + (RankConstants.TIME_MAX_VALUE - System.currentTimeMillis()))).doubleValue();
						orderSet.update(memberId, saveScore);
						sum.incrementAndGet();
					}

					countDownLatch.countDown();

					long use = (System.currentTimeMillis() - t);
					System.out.println("thread:" + Thread.currentThread().getId() + ", i:" + this.i + ", c2:" + c2 + ", use ms: " + use + ", avg ms: " + (double) use / c2);
				}
			});
		}

		try {
			countDownLatch.await();
		} catch (InterruptedException e) {
			e.printStackTrace();
		}

		long use = (System.currentTimeMillis() - t);

		executor.shutdownNow();

		orderSet.foreach(300_0000, 300_0010, System.out::println);

		System.out.println("c:" + (c1 * c2) + ", limit:" + limit + ", use ms: " + use + ", avg ms: " + (double) use / (c1 * c2) + ", sum: " + sum);
		System.out.println(orderSet);
		System.out.println("t4 ok!");
		System.out.println();
	}

	public static void main(String[] args) {
		t0();
		t1_cover();
		t2_cover();
		for (int i = 0; i < 5; i++) {
			t3_performance();
		}
		t4_performance();
	}
}
