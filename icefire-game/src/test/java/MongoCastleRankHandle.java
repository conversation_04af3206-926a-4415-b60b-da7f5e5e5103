import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.game.biz.model.rank.RankMember;
import com.google.gson.JsonObject;

import java.io.*;
import java.util.List;

public class MongoCastleRankHandle {
	public static void main(String[] args) throws Exception {
		File directory = new File("/Users/<USER>/guoyang/castle_rank");

		for(File file : directory.listFiles()) {
			if(file.isDirectory()) {
				continue;
			}
			if(!file.getName().contains("log")) {
				continue;
			}
			handleSingleCastleRank(file);
		}
		//		File file = new File("/Users/<USER>/guoyang/castle_rank/8_1065.log");


	}

	private static void handleSingleCastleRank(File file) throws Exception {

		String orinFileName = file.getName();
		String[] serverSplit = orinFileName.split("_");

		int orinServerId = Integer.parseInt(serverSplit[0]);
		String orinCastleMetaId = serverSplit[1].replace(".log", "");

		System.out.println(orinFileName + "   " + orinServerId + "   " + orinCastleMetaId);

		BufferedReader br = new BufferedReader(new FileReader(file));
		String conteng = br.readLine();
		//		System.out.println(conteng);

		conteng = conteng.replace("NumberLong(\"", "").replace("\")", "").replace("NumberLong(", "").replace(")", "");

		System.out.println(conteng);

		//		conteng.replace("\")", "");
		JSONObject obj = JSONObject.parseObject(conteng);
		String metaId = obj.getString("metaId");
		JSONArray members = obj.getJSONArray("ranks");
		File output = new File("/Users/<USER>/guoyang/castle_rank/output/" + orinServerId + "_" + orinCastleMetaId);


		try(FileOutputStream inputStream = new FileOutputStream(output);) {

			int serverId = -1;
			System.out.println("metaId ------>>>    " + metaId);
			for(int i= 0; i< members.size();i++) {
				JSONObject member = members.getJSONObject(i);
				long roleId = member.getLongValue("roleId");
				int rank = member.getIntValue("rank");
				serverId = member.getIntValue("serverId");


				System.out.println("roleid ---->  " + roleId + ", rank ---->  " + rank + ", serverId ----->  " + serverId);

				if(serverId != orinServerId || !orinCastleMetaId.equals(metaId)) {
					System.out.println("!!!!!!!!!!!!!!!!!!!!! error !!!!!!!!!!!!!!!!!!");
					break;
				}

				String str = roleId + " " + rank + "\n";
				inputStream.write(str.getBytes());
				inputStream.flush();
			}
		}



	}
}
