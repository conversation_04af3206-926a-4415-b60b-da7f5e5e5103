import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl
import com.lc.billion.icefire.game.Application
import com.lc.billion.icefire.game.biz.config.LiBaoConfig
import com.lc.billion.icefire.game.biz.manager.RoleItemManager
import com.lc.billion.icefire.game.biz.manager.RoleManager
import com.lc.billion.icefire.game.biz.model.email.EmailConstants
import com.lc.billion.icefire.game.biz.model.email.SystemEmail
import com.lc.billion.icefire.game.biz.model.item.SimpleItem
import com.lc.billion.icefire.game.biz.service.impl.email.MailCreator
import com.lc.billion.icefire.game.biz.service.impl.email.MailSender
import com.lc.billion.icefire.game.biz.service.impl.item.ItemServiceImpl
import com.lc.billion.icefire.game.support.LogReasons
import com.simfun.sgf.utils.JavaUtils
// 替换补单，用于处理SDK发错货的情况

var roleId = 19700007670795;
var costProduct = "AP_Exchange_01";
var sendProduct = "junxiang101";
var costMeta = Application.getBean(ConfigServiceImpl.class).getConfig(LiBaoConfig.class).getById(costProduct)
var sendMeta = Application.getBean(ConfigServiceImpl.class).getConfig(LiBaoConfig.class).getById(sendProduct)
var role = Application.getBean(RoleManager.class).getRole(roleId)
var itemManager = Application.getBean(RoleItemManager.class);
var res = "对玩家"+roleId+"进行替换补单,"
res +="尝试扣除"+costProduct+"开始:"
//扣除
for (SimpleItem item : costMeta.getItems()){
    var count = itemManager.getItemAmount(role,item.getMetaId())
    if(count >= item.getCount()){
        count = item.getCount()
        res += "道具"+item.getMetaId()+"足够扣除，扣除"+count +"个,"
    }else{
        res += "道具"+item.getMetaId()+"不够扣除，扣除"+count +"个,"
    }
    if(count > 0){
        itemManager.removeItem(role,item.getMetaId(),count, LogReasons.ItemLogReason.GM_CMD_COST_ITEM)
    }
}

res += "补发"+sendProduct + "成功"
//补发
Application.getBean(ItemServiceImpl.class).give(role,sendMeta.getItems(), LogReasons.ItemLogReason.GM_CMD_GIVE_ITEM)

String mailId = EmailConstants.FAKE_BUY_SUCCESS;
String libaoName = sendMeta.getName();
if (!JavaUtils.bool(libaoName)) {
    libaoName = "";
} else {
    libaoName = "@" + libaoName + "@";
}
SystemEmail systemMail = Application.getBean(MailCreator.class).createSystemMail(role.getPersistKey(), role.getCurrentServerId(), mailId, sendMeta.getItems(), List.of(libaoName), true, LogReasons.ItemLogReason.GM_CMD_GIVE_ITEM);
if (systemMail != null) {
    systemMail.setCanReward(false);
    Application.getBean(MailSender.class).sendOneMail(systemMail);
}


return res
