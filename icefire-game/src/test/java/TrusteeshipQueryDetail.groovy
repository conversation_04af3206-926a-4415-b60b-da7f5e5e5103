import com.lc.billion.icefire.game.Application
import com.lc.billion.icefire.game.biz.dao.mongo.roles.TrusteeshipDao

// 托管信息查询
TrusteeshipDao trusteeshipDao = Application.getBean(TrusteeshipDao.class);
Map<Long, Set<Long>> ret = new HashMap<>();
for (var entry : trusteeshipDao.sureRequests.entrySet()) {
    var alist = ret.computeIfAbsent(entry.getValue().getPersistKey(), o -> new HashSet<>());
    alist.add(entry.getValue().getLaunchId());
    alist.add(entry.getValue().getAcceptId());
}
return ret;