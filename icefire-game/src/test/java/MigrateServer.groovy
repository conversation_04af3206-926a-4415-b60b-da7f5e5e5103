//import com.lc.billion.icefire.game.Application
//import com.lc.billion.icefire.game.biz.dao.mongo.root.RoleServerInfoDao
//import com.lc.billion.icefire.game.biz.model.role.Role
//import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency
//import com.lc.billion.icefire.game.biz.service.impl.migrate.MigrateService
//import com.longtech.ls.zookeeper.ConfigCenter
//
//var roleServerInfoDao = Application.getBean(RoleServerInfoDao.class);
//MigrateService service = Application.getBean(MigrateService.class);
//ServiceDependency srvDep = Application.getBean(ServiceDependency.class);
//
//ConfigCenter configCenter = Application.getBean(ConfigCenter.class)
//
//
//var allServerInfoList = roleServerInfoDao.findAllByDB();
//
//long roleId = 24300014006271L;
//int serverId = Application.getServerId();
//Role role = srvDep.getRoleDao().findById(roleId);
//
//return role.isOnline()

import com.lc.billion.icefire.game.Application
import com.lc.billion.icefire.game.biz.service.impl.migrate.MigrateService
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl
import com.longtech.ls.zookeeper.ConfigCenter

MigrateService service = Application.getBean(MigrateService.class);


/**
 * 迁移玩家去目标服
 * 如果玩家在线，则不处理
 * 如果玩家已经转冷，低于一定等级，并且建号超过一定时间，则直接删除，但是mysql中的用户账号会保留，如果用户下次进来，会在游戏服重新创建一个新号
 *
 * @param targetServer 角色没有绑定导量服时的去向，保底去向服
 * @param deleteRoleGameDataLevel 见下
 * @param deleteRoleGameDataMinute 建号时间 >= 这么多分钟 并且 等级 <= deleteRoleGameDataLevel，则不迁服，直接删除玩家数据
 * @param count 先迁(或者删除)这么多个
 * @param skipOnline 是否跳过在线玩家
 * @return 各种情况的统计
 */
int deleteRoleGameDataLevel = 3;
int deleteRoleGameDataMinute = 300;
boolean skipOnline = true;

// 一般只调下面2个参数就行了
int defaultTargetServer = 254;
int count = 1000;  // 不要调太大，否则可能会导致mongo压力过大

// 如果是新手服
if (Application.getBean(ConfigCenter.class).getCurrentGameServerConfig().isGuideServer()) {
    // 暂停清理玩家数据
    Application.getBean(WorldServiceImpl.class).startClear = false;
}

return service.migrateRoleToTargetServer2(defaultTargetServer, deleteRoleGameDataLevel, deleteRoleGameDataMinute, count, skipOnline);
