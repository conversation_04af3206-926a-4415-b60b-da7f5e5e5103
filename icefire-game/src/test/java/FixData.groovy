import com.lc.billion.icefire.game.Application
import com.lc.billion.icefire.game.ServerConfigManager
import com.lc.billion.icefire.game.biz.dao.mongo.roles.PlayerPushInfoDao
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDeviceDao
import com.lc.billion.icefire.game.biz.model.role.RoleDevice
import com.lc.billion.icefire.game.biz.service.impl.DaoService
import com.lc.billion.icefire.game.biz.service.impl.push.UpushService
import com.lc.billion.icefire.protocol.constant.PsPushType

var upush = Application.getBean(UpushService.class);
upush.pushMessageOperation("10043", PsPushType.CITY_UNDER_ATTACK.value, true, "您正在遭受攻击", "请速回", "");

//daoService.findById(100000240402);
