import com.lc.billion.icefire.game.Application
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleActivityMissionDao
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao
import com.lc.billion.icefire.game.biz.model.activity.tradepost.RoleTradePostActivityRecord
import com.lc.billion.icefire.game.biz.model.alliance.Alliance
import org.jongo.bson.BsonDocument
import org.jongo.marshall.Marshaller
import org.jongo.marshall.jackson.JacksonMapper

import static org.jongo.marshall.jackson.JacksonMapper.Builder.jacksonMapper
var mapper = jacksonMapper().build().marshaller;
var dao = Application.getBean(RoleTradePostActivityRecord.class);
for (var data : dao.findAll()) {
    long size = asBsonDocument(mapper, data).getSize();
    if (size >= 1024 * 1024 * 5) {
        return "id:" + data.getPersistKey();
    }
}
private static BsonDocument asBsonDocument(<PERSON><PERSON> marshaller, Object obj) {
    try {
        return marshaller.marshall(obj);
    } catch (Exception var4) {
        Exception e = var4;
        String message = String.format("Unable to save object %s due to a marshalling error", obj);
        throw new IllegalArgumentException(message, e);
    }
}