import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

public class SoldierDetailDiff {
    private AtomicInteger status = new AtomicInteger(0);
    public static void main(String[] args) throws ParseException {
        String jsonBefore = "{\"client_id\":\"H5_5.2_weixin.weixin.0-hall20562.weixin.sanguo2\",\"device_id\":\"\",\"event\":\"soldier_detail\",\"event_time\":\"1735184844708\",\"lib\":\"{}\",\"project_id\":\"20562\",\"properties\":\"{\\\"proj_atk_point\\\":\\\"\\\",\\\"proj_server_id\\\":\\\"20014\\\",\\\"proj_reason\\\":\\\"\\\",\\\"proj_all\\\":\\\"234529\\\",\\\"proj_trace_id\\\":\\\"4246583375768946983\\\",\\\"proj_reserve\\\":\\\"{}\\\",\\\"proj_world_alive\\\":\\\"{2001400003063196={11009=21952}, 2001400003063309={11009=10270, 21009=7177, 31009=4505}, 2001400003063137={11009=12528, 21009=8989, 31009=435}, 2001400003063252={11009=10809, 21009=7779, 31009=3364}}\\\",\\\"proj_city_inner\\\":\\\"{11009=15701, 21009=55631, 31009=75389}\\\",\\\"proj_reason_type\\\":\\\"CURE_IMMEDIATELY\\\",\\\"proj_level\\\":\\\"27\\\",\\\"proj_level_id\\\":\\\"1072\\\",\\\"proj_user_id_odd\\\":\\\"1\\\",\\\"proj_update\\\":\\\"{}\\\",\\\"proj_season\\\":\\\"2\\\",\\\"proj_hospital\\\":\\\"{}\\\",\\\"proj_cure\\\":\\\"{}\\\",\\\"proj_camp\\\":\\\"{}\\\",\\\"proj_def_point\\\":\\\"\\\",\\\"proj_oserver_id\\\":\\\"157\\\",\\\"proj_total_online_time\\\":\\\"1126807\\\",\\\"proj_vip_level\\\":\\\"8\\\",\\\"proj_world_hurt\\\":\\\"{2001400003063196={}, 2001400003063309={}, 2001400003063137={}, 2001400003063252={}}\\\",\\\"proj_role_id\\\":\\\"15700003828459\\\"}\",\"type\":\"game\",\"user_id\":\"103622225\",\"__pack_meta__\":\"7|MTczNDk3MDE1NzY2MTk1NzYzMA==|332|179\",\"__topic__\":\"\",\"__source__\":\"**************\",\"__tag__:__hostname__\":\"138-sanguo2-game-10-172-222-117\",\"__tag__:__path__\":\"/home/<USER>/ga_log/Ga_138-sanguo2-game-10-172-222-117_20241226_11.log\",\"__tag__:__user_defined_id__\":\"138-sanguo2-online\",\"__tag__:__pack_id__\":\"E9766FC45BC08955-17A2\",\"__tag__:__receive_time__\":\"1735184844\",\"__time__\":\"1735184844\"}";
        String jsonAfter = "{\"client_id\":\"H5_5.2_weixin.weixin.0-hall20562.weixin.sanguo2\",\"device_id\":\"\",\"event\":\"soldier_detail\",\"event_time\":\"1735188770514\",\"lib\":\"{}\",\"project_id\":\"20562\",\"properties\":\"{\\\"proj_atk_point\\\":\\\"[x:1147,y:729,sr:20014,rg:7,bl:false]\\\",\\\"proj_reason\\\":\\\"2001400003092447\\\",\\\"proj_all\\\":\\\"168673\\\",\\\"proj_self_army_type\\\":\\\"JOIN\\\",\\\"proj_city_inner\\\":\\\"{21009=33849, 31009=46324}\\\",\\\"proj_reason_type\\\":\\\"ATTACK\\\",\\\"proj_level_id\\\":\\\"1072\\\",\\\"proj_season\\\":\\\"2\\\",\\\"proj_hospital\\\":\\\"{}\\\",\\\"proj_cure\\\":\\\"{}\\\",\\\"proj_atk_army_type\\\":\\\"RALLY_SEVEN_CAPTURE\\\",\\\"proj_def_point\\\":\\\"[x:1136,y:740,sr:20014,rg:7,bl:false]\\\",\\\"proj_total_online_time\\\":\\\"1131386\\\",\\\"proj_server_id\\\":\\\"20014\\\",\\\"proj_trace_id\\\":\\\"3470044830687220641\\\",\\\"proj_reserve\\\":\\\"{}\\\",\\\"proj_world_alive\\\":\\\"{2001400003094442={11009=28229, 21009=30771, 31009=29500}}\\\",\\\"proj_level\\\":\\\"27\\\",\\\"proj_user_id_odd\\\":\\\"1\\\",\\\"proj_update\\\":\\\"{}\\\",\\\"proj_camp\\\":\\\"{}\\\",\\\"proj_oserver_id\\\":\\\"157\\\",\\\"proj_vip_level\\\":\\\"8\\\",\\\"proj_world_hurt\\\":\\\"{2001400003094442={}}\\\",\\\"proj_role_id\\\":\\\"15700003828459\\\"}\",\"type\":\"game\",\"user_id\":\"103622225\",\"__pack_meta__\":\"3|MTczMTc3NTU2MDA3MTE0ODExNg==|292|279\",\"__topic__\":\"\",\"__source__\":\"**************\",\"__tag__:__hostname__\":\"138-sanguo2-game-10-172-222-117\",\"__tag__:__path__\":\"/home/<USER>/ga_log/Ga_138-sanguo2-game-10-172-222-117_20241226_12.log\",\"__tag__:__user_defined_id__\":\"138-sanguo2-online\",\"__tag__:__pack_id__\":\"FD77567ABF85428-ED6\",\"__tag__:__receive_time__\":\"1735188770\",\"__time__\":\"1735188770\"}";
        var resultBefore = pareSoldier(jsonBefore);
        var resultAfter = pareSoldier(jsonAfter);
        for (var entry : resultBefore.entrySet()) {
            var id = entry.getKey();
            int num = entry.getValue();
            var after = resultAfter.getOrDefault(id, 0);
            if (after < num) {
                System.out.println("soldierManager.add(role, \"" + id + "\", " + (num - after) + ");");
            } else if (after > num) {
                System.out.println("soldierManager.sub(role, \"" + id + "\", " + (after - num) + ");");
            }
        }
//        var prop = jsonData.getJSONObject("properties");
//        var proj_world_alive = prop.getJSONObject("proj_world_alive");
//        var proj_city_inner = prop.getJSONObject("proj_city_inner");
//        System.out.println(prop);
    }
    private static Map<String, Integer> pareSoldier(String json1) {
        Map<String, Integer> map = new HashMap<>();
        JSONObject jsonData = JSON.parseObject(json1.replace("=", ":"));
        var prop = jsonData.getJSONObject("properties");
        var proj_world_alive = prop.getJSONObject("proj_world_alive");
//        parese(prop, map, "proj_city_inner");
        var proj_city_inner = prop.getJSONObject("proj_city_inner");
        for (var entry : proj_city_inner.entrySet()) {
            var id = entry.getKey();
            var num = (Integer)entry.getValue();
            map.put(id, map.getOrDefault(id, 0) + num);
        }
        var step = map.values().stream().mapToInt(Integer::intValue).sum();
        System.out.println(step);
        if (proj_world_alive != null) {
            for (Object alive : proj_world_alive.values()) {
                var aliveMap = (Map<String, Integer>) alive;
                for (var dd : aliveMap.entrySet()) {
                    map.put(dd.getKey(), map.getOrDefault(dd.getKey(), 0) + dd.getValue());
                }
            }
        }

        var world_hurt = prop.getJSONObject("proj_world_hurt");
        if (world_hurt != null) {
            for (Object alive : world_hurt.values()) {
                var aliveMap = (Map<String, Integer>) alive;
                for (var dd : aliveMap.entrySet()) {
                    map.put(dd.getKey(), map.getOrDefault(dd.getKey(), 0) + dd.getValue());
                }
            }
        }
        parese(prop, map, "proj_cure");
//        parese(prop, map, "proj_camp");
//        parese(prop, map, "proj_reserve");
        parese(prop, map, "proj_update");
        parese(prop, map, "proj_hospital");
        System.out.println(prop);
        Integer all = prop.getInteger("proj_all");
        var calcValue = map.values().stream().mapToInt(Integer::intValue).sum();
        if (all != calcValue) {
            throw new RuntimeException("all=" + all + "calcValue=" + calcValue);
        }
        return map;
    }
    static void parese(JSONObject prop, Map<String, Integer> map, String fieldName) {
        var data = prop.getJSONObject(fieldName);
        System.out.println(data.getClass());
        for (var entry : data.entrySet()) {
            map.put(entry.getKey(), map.getOrDefault(entry.getKey(), 0) + (Integer)entry.getValue());
        }
    }
}
