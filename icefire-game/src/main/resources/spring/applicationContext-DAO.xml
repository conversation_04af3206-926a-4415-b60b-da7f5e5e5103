<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans  
     http://www.springframework.org/schema/beans/spring-beans-4.3.xsd  
     http://www.springframework.org/schema/context  
     http://www.springframework.org/schema/context/spring-context-4.3.xsd">

<!--  	<bean id="JongoClient"
		class="com.lc.billion.icefire.game.biz.dao.jongo.JongoClient"
		destroy-method="close">
		<constructor-arg name="dbUrl" value="#{configCenter.currentGameServerConfig.mongoDb.url}" />
		<constructor-arg name="dbName" value="#{configCenter.currentGameServerConfig.mongoDb.dbName}" />
	</bean>
 -->
	<bean id="IdGeneratorFactory"
		class="com.lc.billion.icefire.game.biz.dao.id.IdGeneratorFactory">
		<constructor-arg name="jongoClient" value="#{multiJongoClient.getJongoClient(configCenter.currentGameServerConfig.getGameServerId())}" />
		<constructor-arg name="serverId" value="#{configCenter.currentGameServerConfig.getGameServerId()}" />
		<constructor-arg name="webJedisClient" ref="webRedisClient3" />
	</bean>

	<bean id="FlywayHolder"
		class="com.lc.billion.icefire.game.biz.flyway.FlywayHolder">
		<constructor-arg name="dbUrl" value="#{configCenter.currentGameServerConfig.mongoDb.url}" />
		<constructor-arg name="dbName" value="#{configCenter.currentGameServerConfig.mongoDb.dbName}" />
	</bean>

	<bean id="FlywayMultiHolder"
		  class="com.lc.billion.icefire.game.biz.flyway.FlywayMultiHolder">
	</bean>

	<context:component-scan
		base-package="com.lc.billion.icefire.game.biz.dao.impl,com.lc.billion.icefire.game.biz.base,com.lc.billion.icefire.game.biz.dao.mongo,com.lc.billion.icefire.gvgcontrol.biz.dao,com.lc.billion.icefire.gvgbattle.biz.dao.mongo,com.lc.billion.icefire.kvkcontrol.biz.dao.mongo,com.lc.billion.icefire.kvkseason.biz.dao,com.lc.billion.icefire.csabattle.biz.dao,com.lc.billion.icefire.tvtcontrol.biz.dao"
		name-generator="com.simfun.sgf.common.spring.SpringPackageBeanNameGenerator"
		use-default-filters="false">
		<context:include-filter type="annotation"
			expression="org.springframework.stereotype.Repository" />
	</context:component-scan>

</beans>
