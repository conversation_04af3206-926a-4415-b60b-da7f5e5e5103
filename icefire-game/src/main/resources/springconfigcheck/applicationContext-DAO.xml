<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans  
     http://www.springframework.org/schema/beans/spring-beans-4.3.xsd  
     http://www.springframework.org/schema/context  
     http://www.springframework.org/schema/context/spring-context-4.3.xsd">

	<bean id="JongoClient"
		class="com.lc.billion.icefire.game.tool.config.impl.JongoClientForConfigCheck">
	</bean>

	<bean id="IdGeneratorFactory"
		class="com.lc.billion.icefire.game.tool.config.impl.IdGeneratorFactoryForConfigCheck">
	</bean>

	<bean id="JedisClient"
		class="com.lc.billion.icefire.game.tool.config.impl.JedisClientForConfigCheck">
	</bean>

	<bean id="ConfigCenter"
		  class="com.lc.billion.icefire.game.tool.config.impl.ConfigCenterForCompileCheck">
	</bean>

	<!-- <bean id="EntityMapperService" class="com.lc.billion.icefire.game.biz.service.impl.entitymapper.EntityMapperService"> 
		</bean> -->

	<bean id="AsyncOperationServiceImpl"
		class="com.lc.billion.icefire.game.tool.config.impl.AsyncOperationServiceImplForConfigCheck">
	</bean>

	<context:component-scan
		base-package="com.lc.billion.icefire.game.biz.dao.impl,com.lc.billion.icefire.game.biz.dao.mongo"
		name-generator="com.simfun.sgf.common.spring.SpringPackageBeanNameGenerator"
		use-default-filters="false">
		<context:include-filter type="annotation"
			expression="org.springframework.stereotype.Repository" />
	</context:component-scan>

	<bean id="configureService"
		class="com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl">
		<property name="configPackages">
			<list>
				<value>com.lc.billion.icefire.game.biz.config</value>
				<value>com.lc.billion.icefire.gvgbattle.biz.config</value>
			</list>
		</property>
	</bean>

	<bean id="configLoader"
		class="com.lc.billion.icefire.core.config.load.impl.DefaultConfigLoader">
	</bean>

	<bean id="LocaleConfigResolver"
		class="com.lc.billion.icefire.core.config.service.impl.LocaleMetaResolver">
	</bean>
	<bean id="JsonMetaResolver"
		class="com.lc.billion.icefire.core.config.service.impl.JsonMetaResolver">
	</bean>
	<context:component-scan
		base-package="com.lc.billion.icefire.game.biz.service.impl.activity.timeresolve"
		name-generator="com.simfun.sgf.common.spring.SpringPackageBeanNameGenerator"
		use-default-filters="false">
		<context:include-filter type="annotation"
			expression="org.springframework.stereotype.Service" />
	</context:component-scan>
</beans>
