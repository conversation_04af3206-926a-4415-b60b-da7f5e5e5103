package com.lc.billion.icefire.kvkseason.biz.dao.mongo.root;

import java.util.Collection;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.ServerSeasonTaskProgress;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 * @date 2021/11/15
 */
@Repository
public class ServerSeasonTaskProgressDao extends RootDao<ServerSeasonTaskProgress> {

	protected ServerSeasonTaskProgressDao() {
		super(ServerSeasonTaskProgress.class, false);
	}

	@Override
	protected void putMemoryIndexes(ServerSeasonTaskProgress entity) {

	}

	@Override
	protected void removeMemoryIndexes(ServerSeasonTaskProgress entity) {

	}

	@Override
	protected MongoCursor<ServerSeasonTaskProgress> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	public ServerSeasonTaskProgress getByServerId(int serverId) {
		ServerSeasonTaskProgress record = findById(serverId * 1l);
		if (record == null) {
			record = newEntityInstance();
			record.setPersistKey(serverId * 1l);
			return createEntity(Application.getServerId(), record);
		}
		return record;
	}

	public void clearPreSeasonData() {
		Collection<ServerSeasonTaskProgress> all = findAll();
		if (JavaUtils.bool(all)) {
			for (ServerSeasonTaskProgress record : all) {
				delete(record);
			}
		}
	}
}
