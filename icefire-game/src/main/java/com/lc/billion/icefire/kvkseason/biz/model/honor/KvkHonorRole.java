package com.lc.billion.icefire.kvkseason.biz.model.honor;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;

/**
 * <AUTHOR>
 *
 */
public class KvkHonorRole extends AbstractEntity {

	private static final long serialVersionUID = 4800432926998346307L;

	@MongoId
	private Long roleId;
	/** 荣誉值 */
	private long honorValue;

	@Override
	public void setPersistKey(Long id) {
		roleId = id;
	}

	@Override
	public Long getPersistKey() {
		return roleId;
	}

	@Override
	public Long getGroupingId() {
		return roleId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public long getHonorValue() {
		return honorValue;
	}

	public void addHonorValue(long honorValue) {
		this.honorValue += honorValue;
	}

}
