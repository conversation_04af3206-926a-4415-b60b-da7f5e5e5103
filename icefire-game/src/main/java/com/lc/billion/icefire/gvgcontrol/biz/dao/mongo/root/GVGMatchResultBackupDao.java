package com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGMatchResultBackup;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Repository
public class GVGMatchResultBackupDao extends RootDao<GVGMatchResultBackup> {

    private final ConcurrentHashMap<Long, ArrayList<GVGMatchResultBackup>> rstGroupByTurn = new ConcurrentHashMap<>();

    public GVGMatchResultBackupDao() {
        super(GVGMatchResultBackup.class, true);
    }

    @Override
    protected MongoCursor<GVGMatchResultBackup> doFindAll(int db) {
        return dbFindAllForWorldEntity(db);
    }

    @Override
    protected void putMemoryIndexes(GVGMatchResultBackup entity) {
        rstGroupByTurn.compute(entity.getBackupTime(), (round, rstList) -> {
            if (rstList == null) {
                rstList = new ArrayList<>();
            }

            rstList.add(entity);
            return rstList;
        });
    }

    @Override
    protected void removeMemoryIndexes(GVGMatchResultBackup entity) {
        rstGroupByTurn.compute(entity.getBackupTime(), (round, rstList) -> {
            if (rstList == null) {
                return null;
            }

            rstList.remove(entity);
            return rstList;
        });
    }

    public List<GVGMatchResultBackup> getRoundData(long createTime){
        return rstGroupByTurn.get(createTime);
    }

    public long getLatestRoundTime(){
        return rstGroupByTurn.keySet()
                .stream()
                .mapToLong(Long::longValue)
                .max()
                .orElse(0);
    }

    public GVGMatchResultBackup create(Long allianceId, int serverId, List<Long> memberList, GvgMatchType matchType, int roundId, long backupTime){
        GVGMatchResultBackup newData = newEntityInstance();
        newData.setAllianceId(allianceId);
        newData.setBackupTime(backupTime);
        newData.setServerId(serverId);
        newData.setMatchType(matchType);
        newData.setRoundId(roundId);
        newData.setRoleList(new ArrayList<>(memberList));
        return createEntity(Application.getServerId(), newData);
    }
}
