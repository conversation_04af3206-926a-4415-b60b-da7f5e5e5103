package com.lc.billion.icefire.gvgcontrol.biz.service.rpc;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.service.AbstractRPCProxyService;
import com.lc.billion.icefire.rpc.service.gvg.IGVGControlRemoteGVGBattleService;
import com.longtech.cod.rpc.client.RpcClient;
import com.longtech.cod.rpc.client.RpcProxyBuilder;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.GameServerConfig;
import com.simfun.sgf.utils.JavaUtils;

/**
 * control和battle之间的连接
 * 
 * <AUTHOR>
 *
 */
@Service
public class GVGControlRPCToGVGBattleProxyService extends AbstractRPCProxyService {

	private Map<Integer, RpcProxyBean<IGVGControlRemoteGVGBattleService>> migrateRemoteGVGBattleServices = new HashMap<>();

	@Override
	protected ServerType[] getSrcServerType() {
		return new ServerType[] { ServerType.GVG_CONTROL };
	}

	@Override
	protected ServerType[] getTargetServerType() {
		return new ServerType[] { ServerType.GVG_BATTLE };
	}

	@Override
	protected boolean createRPCClient(GameServerConfig gameServerConfig) {
		RpcProxyBuilder<IGVGControlRemoteGVGBattleService> rpcProxyBuilder = RpcProxyBuilder.create(IGVGControlRemoteGVGBattleService.class).connect(getSerializer(),
				gameServerConfig.getRpcIp(), gameServerConfig.getRpcPort());
		RpcClient rpcClient = rpcProxyBuilder.createRpcClient();
		IGVGControlRemoteGVGBattleService service = rpcProxyBuilder.buildSync(rpcClient, getTimeOutMills(), getRetryTimes(), createWait());
		RpcProxyBean<IGVGControlRemoteGVGBattleService> rpcProxyBean = new RpcProxyBean<IGVGControlRemoteGVGBattleService>(service, rpcClient);
		migrateRemoteGVGBattleServices.put(gameServerConfig.getGameServerId(), rpcProxyBean);
		logger.info("rpc GVG_CONTROL to GVG_BATTLE {}->{},{}:{}", Application.getServerId(), gameServerConfig.getGameServerId(), gameServerConfig.getRpcIp(),
				gameServerConfig.getRpcPort());
		return true;
	}

	@Override
	protected void rpcIpChanged(GameServerConfig gameServerConfig) {
		RpcProxyBean<IGVGControlRemoteGVGBattleService> rpcProxyBean = migrateRemoteGVGBattleServices.get(gameServerConfig.getGameServerId());
		if (rpcProxyBean != null) {
			RpcClient rpcClient = rpcProxyBean.getRpcClient();
			rpcClient.setStop(true);
		}
		logger.info("rpcIpChanged {}", gameServerConfig.getGameServerId());
		createRPCClient(gameServerConfig);
	}

	@Override
	protected void rpcPortChanged(GameServerConfig gameServerConfig) {
		logger.info("rpcPortChanged {}", gameServerConfig.getGameServerId());
		rpcIpChanged(gameServerConfig);
	}

	@Override
	protected void removeRPCClient(int serverId) {
		RpcProxyBean<IGVGControlRemoteGVGBattleService> rpcProxyBean = migrateRemoteGVGBattleServices.remove(serverId);
		if (rpcProxyBean != null) {
			RpcClient rpcClient = rpcProxyBean.getRpcClient();
			rpcClient.setStop(true);
		}
	}

	@Override
	protected boolean containsRPCClient(int serverId) {
		return migrateRemoteGVGBattleServices.containsKey(serverId);
	}

	@Override
	protected boolean checkInstance() {
		return true;
	}

	@Override
	protected boolean createWait() {
		return false;
	}

	public IGVGControlRemoteGVGBattleService getGVGControlRemoteGVGBattleService(int serverId) {
		RpcProxyBean<IGVGControlRemoteGVGBattleService> rpcProxyBean = migrateRemoteGVGBattleServices.get(serverId);
		if (rpcProxyBean == null) {
			return null;
		}
		return rpcProxyBean.getProxy();
	}

	public Collection<IGVGControlRemoteGVGBattleService> getGVGControlRemoteGVGBattleServices() {
		if (JavaUtils.bool(migrateRemoteGVGBattleServices)) {
			Collection<RpcProxyBean<IGVGControlRemoteGVGBattleService>> values = migrateRemoteGVGBattleServices.values();
			if (JavaUtils.bool(values)) {
				List<IGVGControlRemoteGVGBattleService> ret = new ArrayList<>();
				values.forEach(bean -> ret.add(bean.getProxy()));
				return ret;
			}
		}
		return Collections.emptyList();
	}

}
