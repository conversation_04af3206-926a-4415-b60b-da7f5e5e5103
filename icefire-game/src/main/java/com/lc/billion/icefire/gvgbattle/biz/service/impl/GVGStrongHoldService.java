package com.lc.billion.icefire.gvgbattle.biz.service.impl;

import com.google.api.client.util.Lists;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.battle.FightContext;
import com.lc.billion.icefire.game.biz.battle.result.FightLostType;
import com.lc.billion.icefire.game.biz.config.BuffConfig;
import com.lc.billion.icefire.game.biz.config.SoldierConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.alliances.AllianceMemberDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.ArmyDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleExtraDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.OfficialsInfoDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.RoleServerInfoDao;
import com.lc.billion.icefire.game.biz.manager.*;
import com.lc.billion.icefire.game.biz.manager.prop.base.ICalcProp;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceMember;
import com.lc.billion.icefire.game.biz.model.alliance.war.AllianceWar;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmySoldier;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.email.*;
import com.lc.billion.icefire.game.biz.model.prop.AttributeValue;
import com.lc.billion.icefire.game.biz.model.prop.Prop;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleExtra;
import com.lc.billion.icefire.game.biz.model.role.RoleServerInfo;
import com.lc.billion.icefire.game.biz.model.scene.MapRoute;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.soldier.Soldier;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceConstants;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceOutput;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.alliance.war.AllianceWarService;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import com.lc.billion.icefire.game.biz.service.impl.email.MailCreator;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroOutput;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.login.LoginServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.notice.NoticeConstants;
import com.lc.billion.icefire.game.biz.service.impl.notice.NoticeServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneNodeRefreshServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.soldier.SoldierServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.soldier.SoldierUpdateReasonType;
import com.lc.billion.icefire.game.biz.service.impl.tvt.TVTGameService;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgBuildingConfig;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgBuildingConfig.GvgBuildingMeta;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgRewardConfig;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.*;
import com.lc.billion.icefire.gvgbattle.biz.manager.gvg.GVGBattleDataVoManager;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.*;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.StrongHoldNode;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.constant.*;
import com.lc.billion.icefire.protocol.structure.*;
import com.lc.billion.icefire.rpc.vo.gvg.GVGAllianceSignUpInfoVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGBattleRecordVo;
import com.lc.billion.icefire.rpc.vo.gvg.GvgBattleServerDispatchRecordVo;
import com.lc.billion.icefire.rpc.vo.gvg.RoleGVGScoreInfoVo;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.simfun.sgf.utils.JavaUtils;
import com.simfun.sgf.utils.TimeUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * GVG 战场建筑服务类
 * 
 * <AUTHOR>
 * @date 2020/12/25
 */
@Service
public class GVGStrongHoldService {

	private static final Logger logger = LoggerFactory.getLogger(GVGStrongHoldService.class);

	@Autowired
	private ServiceDependency srvDpd;
	@Autowired
	private RoleManager roleManager;
	@Autowired
	private AllianceServiceImpl allianceService;
	@Autowired
	private StrongHoldNodeDao strongHoldNodeDao;
	@Autowired
	private SceneServiceImpl sceneService;
	@Autowired
	private AllianceBattlePointDao allianceBattlePointDao;
	@Autowired
	private RoleGVGBattleDao roleGVGBattleDao;
	@Autowired
	private AllianceMemberManager allianceMemberManager;
	@Autowired
	private RoleCurrencyManager roleCurrencyManager;
	@Autowired
	private RoleCalcPropManager roleCalcPropManager;
	@Autowired
	private ArmyManager armyManager;
	@Autowired
	private ArmyServiceImpl armyService;
	@Autowired
	private ConfigServiceImpl configService;
	@Autowired
	private ArmyDao armyDao;
	@Autowired
	private SceneNodeRefreshServiceImpl sceneNodeRefreshService;
	@Autowired
	private GVGBattleService gvgBattleService;
	@Autowired
	private RoleDao roleDao;
	@Autowired
	private NoticeServiceImpl noticeService;
	@Autowired
	private GVGBattleFieldTimeLineDao gvgBattleFieldTimeLineDao;
	@Autowired
	private GVGBattleDataVoManager gvgBattleDataVoManager;
	@Autowired
	private WorldServiceImpl worldService;
	@Autowired
	private AllianceManager allianceManager;
	@Autowired
	private SoldierServiceImpl soldierService;
	@Autowired
	private RoleServerInfoDao roleServerInfoDao;
	@Autowired
	private AllianceDao allianceDao;
	@Autowired
	private RoleExtraDao roleExtraDao;
	@Autowired
	private TVTGameService tvtGameService;
	@Autowired
	protected GVGBuildingService gvgBuildingService;
	@Autowired
	private GVGWuChaoManager wuChaoManager;
	@Autowired
	private RoleGVGExtraInfoDao roleGVGExtraInfoDao;
	@Autowired
	private NoticeServiceImpl noticeServiceImpl;
	@Autowired
	private GVGBattleScoreService gvgBattleScoreService;
	@Autowired
	private SoldierManager soldierManager;
	@Autowired
	private MailCreator mailCreator;

	@Autowired
	protected AllianceWarService warService;
	@Autowired
	private AllianceMemberDao allianceMemberDao;
	@Autowired
	private OfficialsInfoDao officialsInfoDao;
	@Autowired
	private ConfigCenter configCenter;

	public void onEnterWorld(Role role) {
		if (Application.isBattleServer()) {
			tryFixRoleArmyTarget(role);	// GVG不会预加载最近玩家数据，所有关服前没有结束的行军，启动的时候都不会被加载（因此不会过ArmyService的检查），玩家上线时的加载会导致队伍会丢失targetNode
			RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(role.getId());
			if (roleGVGBattle == null) {
				Alliance alliance = null;
				if (!JavaUtils.bool(role.getAllianceId())) {
					logger.info("玩家: {} 首次进入GVG战斗服, 创建对象, 玩家联盟ID为空", role.getId());
				}else {
					alliance = allianceService.getAllianceById(role.getAllianceId());
					if (alliance == null) {
						logger.info("玩家: {} 首次进入GVG战斗服 创建对象 玩家联盟: {}为空", role.getId(), role.getAllianceId());
					}
				}

				roleGVGBattle = roleGVGBattleDao.create(role, alliance);
				logger.info("玩家: {} 进入GVG战场, 初始化战场数据对象", role.getId());
			}

			RoleGVGExtraInfo roleGVGExtraInfo = roleGVGExtraInfoDao.findById(role.getId());
			if (roleGVGExtraInfo == null) {
				roleGVGExtraInfo = roleGVGExtraInfoDao.create(role.getRoleId());
				roleGVGExtraInfoDao.save(roleGVGExtraInfo);
			}

			if(Application.getServerType() == ServerType.TVT_BATTLE){
				roleGVGBattle.setAllianceId(role.getAllianceId());
				Alliance alliance = allianceService.getAllianceById(role.getAllianceId());
				roleGVGBattle.setAllianceName(alliance.getName());
				roleGVGBattle.setAllianceAliasName(alliance.getAliasName());
				roleGVGBattleDao.save(roleGVGBattle);
			}

			sendGVGBattleInfo(role);

			// 人数变化了，发消息给所有联盟，不过这里应该可以优化，只有当人数确实变化的时候才发
			pushInformationBothSides(0, 0L);

			if (roleGVGBattle.isFirstEnter()) {
//				sendStrongHoldFirstInfo(role);
				roleGVGBattle.setFirstEnter(false);
				roleGVGBattleDao.save(roleGVGBattle);
			}

			RoleServerInfo roleServerInfo = roleServerInfoDao.findById(role.getPersistKey());
			logger.info("玩家进入战场 Role: {}/{} RoleServerInfo空否: {}", role.getId(), Application.getServerType(), roleServerInfo != null ? 0 : 1);
			if(roleServerInfo != null) {
				if(roleServerInfo.getGvgSide() == 1)
					pushInformationBothSides(0, 0L);
				if(Application.getServerType() == ServerType.TVT_BATTLE){
					RoleExtra roleExtra = roleExtraDao.findById(role.getId());
					logger.info("玩家进入战场 Role: {} RoleExtra空否: {}", role.getId(), roleExtra != null ? 0 : 1);
					if(roleExtra != null){
						roleGVGBattle.setTvtHideScore(roleExtra.getTvtHideScore());
						roleGVGBattle.setScore(roleExtra.getTvtPowerScore());
						roleGVGBattle.setTeamId(roleExtra.getTvtTeamId());
						long oldRank = tvtGameService.getRoleTvtPowerScoreRank(role.getId());
						roleGVGBattle.setOldRank((int)oldRank);
						roleGVGBattleDao.save(roleGVGBattle);
						logger.info("玩家进入战场 Role: {} 值: {}/{}/{}", role.getId(), roleExtra.getTvtTeamId(), roleExtra.getTvtHideScore(), roleExtra.getTvtPowerScore());
					}
				}
			}
		}
	}

	private void tryFixRoleArmyTarget(Role role) {
		Collection<ArmyInfo> armies = armyDao.findByRoleId(role.getId());
		if(armies == null || armies.isEmpty()){
			logger.info("[GVG]tryFixRoleArmyTarget, no army role: {}", role.getId());
			return;
		}

		for (ArmyInfo army : armies) {
			if (army.getTargetNode() == null) {
				var targetNode = sceneService.getSceneNode(Application.getServerId(), army.getEndPoint());
				if(targetNode != null) {
					logger.info("[GVG]tryFixRoleArmyTarget, role: {}, army: {}, targetNode: {}, endPos: {}", role.getId(), army.getPersistKey(), targetNode.getPersistKey(), army.getEndPoint());
					army.setTargetNode(targetNode);
					armyDao.save(army);
				} else {
					logger.warn("[GVG]tryFixRoleArmyTarget target no longer exist, role: {}, army: {}, targetNode: {}", role.getId(), army.getPersistKey(), army.getEndPoint());
					armyManager.returnArmyImmediately(army);
				}
			}
		}
	}

	/**
	 *
	 * 广播给当前所属联盟，驻扎等信息
	 */
	public void broadcastStrongHoldDetailInfoUpdate(StrongHoldNode node) {
		if (node == null) {
			return;
		}
		long allianceId = node.getAllianceId();
		if (allianceId <= 0) {
			return;
		}
		Alliance alliance = allianceManager.getAllianceById(allianceId);
		if (alliance == null) {
			ErrorLogUtil.errorLog("broadcastStrongHoldDetailInfoUpdate alliance not exists", "allianceId",allianceId);
			return;
		}
		GcStrongHoldDetailInfo info = toGcStrongHoldDetailInfo(node.getPersistKey());
		if (info == null) {
			return;
		}
		allianceManager.broadcast(alliance, info, null);
	}

	private void broadcastWuChaoArmyAppear(){
		var wagon = wuChaoManager.getSupplyWagon();
		if(wagon == null){
			return;
		}
		GcGvGWuChaoSupplyWagonBegin msg = makeSupplyWagonMsg(wagon);

		allianceManager.broadcast(wagon.getOccupierAllianceId(), msg);
		allianceManager.broadcast(wagon.getChallengerAllianceId(), msg);
	}

	private GcGvGWuChaoSupplyWagonBegin makeSupplyWagonMsg(GVGSupplyWagon wagon) {
		var pathNodes = wagon.getPath().nodes();
		var beginPos = pathNodes[0];
		var endPos = pathNodes[pathNodes.length - 1];

		var allianceId = wagon.getOccupierAllianceId();
		var alliance = allianceService.getAllianceById(allianceId);


		var msg = new GcGvGWuChaoSupplyWagonBegin();

		// 行军路线
		PsMapRoute mapRoute = new PsMapRoute();
		mapRoute.setId(wagon.getArmyId());
		mapRoute.setStartX((int)beginPos.getX());
		mapRoute.setStartY((int)beginPos.getY());
		mapRoute.setEndX((int)endPos.getX());
		mapRoute.setEndY((int)endPos.getY());
		mapRoute.setBattleRelation(PsBattleRelation.BLUE);
		mapRoute.setSpeedRatio(1);
		mapRoute.setArmyType(PsArmyType.GVG_GATHER);
		mapRoute.setStartPointSize(new PsPointSize((int)beginPos.getX(), (int)beginPos.getY(), wuChaoManager.getDaYingPosSize(wagon.getOccupierAllianceId())));
		mapRoute.setEndPointSize(new PsPointSize((int)endPos.getX(), (int)endPos.getY(), wuChaoManager.getDaYingPosSize(wagon.getChallengerAllianceId())));
		for(int i = 1; i < pathNodes.length - 1; ++i){
			mapRoute.addToMidPointSizes(new PsPointSize((int)pathNodes[i].getX(), (int)pathNodes[i].getY(), wuChaoManager.getWuChaoPosSize()));
		}
		msg.setRoute(mapRoute);

		// 部队
		PsMapArmy mapArmy = new PsMapArmy();
		mapArmy.setId(wagon.getArmyId());
		mapArmy.setType(PsArmyType.GVG_GATHER);
		mapArmy.setStartX((int)beginPos.getX());
		mapArmy.setStartY((int)beginPos.getY());
		mapArmy.setEndX((int)endPos.getX());
		mapArmy.setEndY((int)endPos.getY());
		mapArmy.setSpeedRatio(1);
		mapArmy.setRemainTime(wagon.getRemainTime());
		mapArmy.setTotalTime(wagon.getTotalTime());
		mapArmy.setWorkType(PsArmyWorkType.RETURN);
		mapArmy.setBattleRelation(PsBattleRelation.BLUE);
		mapArmy.setHeroMetaId("");
		mapArmy.setBodyType(PsArmyBodyType.NORMAL);
		mapArmy.setAllianceAliasName(alliance.getAliasName());
		mapArmy.setBodyContext("");
		mapArmy.setOriginalTotalTime((int) wagon.getTravelTime());
		mapArmy.setNpcMetaId("");
		mapArmy.setEndTime(wagon.getEndTime());
		mapArmy.setStartPointSize(new PsPointSize((int)beginPos.getX(), (int)beginPos.getY(), wuChaoManager.getDaYingPosSize(wagon.getOccupierAllianceId())));
		mapArmy.setEndPointSize(new PsPointSize((int)endPos.getX(), (int)endPos.getY(), wuChaoManager.getDaYingPosSize(wagon.getChallengerAllianceId())));
		for(int i = 1; i < pathNodes.length - 1; ++i){
			mapArmy.addToMidPointSizes(new PsPointSize((int)pathNodes[i].getX(), (int)pathNodes[i].getY(), wuChaoManager.getWuChaoPosSize()));
		}
		msg.setSupplyWagon(mapArmy);
		msg.setOccupierAllianceId(wagon.getOccupierAllianceId());
		return msg;
	}

	private void broadcastWuChaoArmyDisappear(){
		var wagon = wuChaoManager.getSupplyWagon();
		if(wagon == null){
			return;
		}

		var msg = new GcGvGWuChaoSupplyWagonEnd(wagon.getArmyId());

		allianceManager.broadcast(wagon.getOccupierAllianceId(), msg);
		allianceManager.broadcast(wagon.getChallengerAllianceId(), msg);
	}

	private void onSupplyWagonArrive(GVGWuChaoManager wuChaoManager){
		var node = sceneService.getSceneNode(Application.getServerId(), wuChaoManager.wuChaoPos());
		if(!(node instanceof StrongHoldNode strongHold)){
			return;
		}
        logger.info("[GVG]onSupplyWagonArrive");

		int addScore = wuChaoCurCompleteScore();

		// 加联盟积分
		var alliance = allianceManager.getAllianceById(wuChaoManager.getSupplyWagon().getOccupierAllianceId());
		gvgBattleScoreService.addAlliancePoint(alliance, addScore, AddPointType.WUCHAO_COMPLETE);

		// 加个人积分
		gvgBattleScoreService.addRolePointBatch(strongHold.getGarrisonRoleList(), addScore, AddPointType.WUCHAO_COMPLETE, true);

		// 退回部队
		returnAllNodeArmies(strongHold);

		// 更新占领状态
		strongHold.setOccupyState(false);
		strongHold.setAllianceId(0L);
		strongHold.setOccupyTime(0);
		strongHold.setOccupyBeginTime(0);
		strongHold.setOldAllianceId(0);

		// 同步AOI
		sceneService.update(strongHold, null);

		// 广播乌巢辎重车行军消失
		broadcastWuChaoArmyDisappear();

		noticeWuchaoScoreAdd(alliance, addScore);
	}

	private void noticeWuchaoScoreAdd(Alliance alliance, int addScore) {
		if (alliance == null || addScore <= 0) {
			return;
		}

		String gvgWuChaoTransportCompleteBroadcast = configService.getConfig(GvgSettingConfig.class).getGvgWuChaoTransportCompleteBroadcast();
		if (!JavaUtils.bool(gvgWuChaoTransportCompleteBroadcast)) {
			return;
		}

		noticeServiceImpl.marqueeGVGNotice(gvgWuChaoTransportCompleteBroadcast, alliance.getName(), String.valueOf(addScore));
	}

	// 本次赢得乌巢得分（用于结算）
	public int wuChaoCurCompleteScore(){
		int completeCount = wuChaoManager.getCompleteCount() - 1;
		var gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
		return gvgSettingConfig.getWuChaoCompleteScore(completeCount);
	}

	// 下一次赢得乌巢得分
	public int wuChaoNextCompleteScore(){
		int completeCount = wuChaoManager.getCompleteCount();
		var gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
		return gvgSettingConfig.getWuChaoCompleteScore(completeCount);
	}

	// 回退所有跟建筑相关的部队，包括已经在驻防中、驻防行军中、攻击行军中
	public void returnAllNodeArmies(StrongHoldNode node){
		logger.info("[GVG]returnArmy : {}({})", node.getMetaId(), node.getAllianceId());

		var armies = getNodeArmies(node);
		for(var army: armies) {
			if (army.getWorkType() != ArmyWorkType.SETOUT
					&& army.getWorkType() != ArmyWorkType.DEFENDING) {
				logger.info("[GVG]returnArmy : army can't return {}({}), {}({}, {})", node.getMetaId(), node.getAllianceId(), army.getPersistKey(), army.getRoleId(), army.getWorkType());
				continue;
			}

			if(army.getMapRoute() == null) {
				logger.info("[GVG]returnArmy : army map route null {}({}), {}({}, {})", node.getMetaId(), node.getAllianceId(), army.getPersistKey(), army.getRoleId(), army.getWorkType());
				continue;
			}
			armyManager.returnArmy(army);
		}

		strongHoldNodeDao.save(node);
	}

	// 回退所有跟建筑相关的已经在驻防中的部队
	public void returnAllGarrison(StrongHoldNode node){
		var armies = getNodeGarrisonArmies(node);
		for(var army: armies){
			armyManager.returnArmy(army);
		}

		strongHoldNodeDao.save(node);
	}

	public void garrisonStrongHold(ArmyInfo army){
		// 数据检查 BEGIN
		var role = roleManager.getRole(army.getRoleId());
		if(role == null){
			ErrorLogUtil.errorLog("[GVG]beginOccupyStrongHold, role is null","armyId", army.getPersistKey(), "roleId",army.getRoleId());
			armyManager.returnArmy(army);
			return;
		}

		var targetNode = army.getTargetNode();
		if(!(targetNode instanceof StrongHoldNode)){
			ErrorLogUtil.errorLog("[GVG]beginOccupyStrongHold, type error", "armyId",army.getPersistKey(), "roleId",army.getRoleId());
			armyManager.returnArmy(army);
			return;
		}

		var strongHoldNode = (StrongHoldNode)targetNode;

		if(!commonDataCheck(role, strongHoldNode)){
			ErrorLogUtil.errorLog("[GVG]beginOccupyStrongHold, commonDataCheck fail",
					"armyId",army.getPersistKey(), "roleId",army.getRoleId(),
					"strongHoldId",strongHoldNode.getPersistKey(), "type",strongHoldNode.getBuildingType());
			armyManager.returnArmy(army);
			return;
		}

		// 如果是敌方，且有驻防，则转到战斗逻辑
		if(checkNeedAttackWhenGarrison(role, strongHoldNode, army)) {
			ErrorLogUtil.errorLog("[GVG]beginOccupyStrongHold, checkNeedAttackWhenGarrison fail", "armyId",army.getPersistKey(),
					"roleId",army.getRoleId(), "strongHoldId",strongHoldNode.getPersistKey(),
					"type",strongHoldNode.getBuildingType());
			return;
		}
		// 数据检查 END


		// 部队驻扎进入，主要是处理超过限制的情况
		doArmyGarrison(strongHoldNode, army);

		// 重新设置队长
		tryAutoSetLeader(strongHoldNode);

		// 驻扎部队进入了一个非自己联盟所属的建筑，执行占领
		tryBeginOccupyStrongHold(role, strongHoldNode, army);

		warService.broadcastUpdate(strongHoldNode, PsAllianceWarInfoUpdateReason.STATION_ADD);

		// 同步
		broadcastStrongHoldMatchInfo(strongHoldNode);
		// 同步
		broadcastStrongHoldDetailInfoUpdate(strongHoldNode);
	}

	private boolean checkNeedAttackWhenGarrison(Role role, StrongHoldNode strongHoldNode, ArmyInfo army){
		// 如果是敌方，且有驻防
		if(strongHoldNode.getAllianceId() > 0
				&& !Objects.equals(role.getAllianceId(), strongHoldNode.getAllianceId())
				&& !noGarrison(strongHoldNode)) {
			if(army.getRallyContext() != null){
				army.setArmyType(ArmyType.RALLY_STRONGHOLD);
				army.setWorkType(ArmyWorkType.SETOUT);
			} else {
				army.setArmyType(ArmyType.PVP_STRONGHOLD);
				army.setWorkType(ArmyWorkType.SETOUT);
			}

			return true;
		}

		return false;
	}

	public int getGarrisonArmyNumMax(StrongHoldNode strongHoldNode) {
		var ret = 1;
		var leaderId = strongHoldNode.leaderArmyId();
		if(leaderId == 0){
			return ret;
		}
		var leaderArmy = armyDao.findById(leaderId);
		if(leaderArmy == null) {
			ErrorLogUtil.errorLog("[GVG]getGarrisonMaxNum leaderArmy null", "leaderId",leaderId);
			return ret;
		}

		Role role = roleManager.getRole(leaderArmy.getRoleId());
		ret += role.getNumberProps().getInt(Prop.RALLY_CAPACITY_14000);
		return ret;
	}

	public int getSoldierMaxNum(StrongHoldNode strongHoldNode) {
		var leaderId = strongHoldNode.leaderArmyId();
		if (leaderId == 0) {
			return 0;
		}

		var leaderArmy = armyDao.findById(leaderId);
		if (leaderArmy == null) {
			ErrorLogUtil.errorLog("[GVG]getSoldierMaxNum leaderArmy null", "leaderId",leaderId);
			return 0;
		}

		return armyManager.getRallySoldierMax(leaderArmy);
	}

	public int getSoldierNum(StrongHoldNode strongHoldNode) {
		var garrisonArmies = getNodeGarrisonArmies(strongHoldNode);
		var count = 0;
		for(var garrisonArmy: garrisonArmies) {
			count += garrisonArmy.getSoldierCount();
		}

		return count;
	}

	private ArrayList<ArmyInfo> trySplitArmy(ArmyInfo originalArmy){
		var armyList = new ArrayList<ArmyInfo>();

		armyList.add(originalArmy);

		if(originalArmy.getRallyContext() == null) {
			logger.info("[GVG]trySplitArmy, not rally, originalArmyId: {}", originalArmy.getPersistKey());
			return armyList;
		}

		var rallyContext = originalArmy.getRallyContext();
		for(var joinArmyId: rallyContext.getJoinArmyIdList()){
			ArmyInfo joinArmy = armyManager.findById(joinArmyId);
			if(joinArmy == null){
				continue;
			}
			armyList.add(joinArmy);
		}

		// 拆分后的部队，让他们跟车头一样的终点和行军类型
		var endPos = originalArmy.getEndPoint();
		var endPosSize = originalArmy.getEndSize();
		var targetNode = originalArmy.getTargetNode();
		var armyType = originalArmy.getArmyType();

		for (var army : armyList) {
			if(army.equals(originalArmy)){
				continue;
			}

			var oldPos = army.getEndPoint();

			army.setEndX(endPos.getX());
			army.setEndY(endPos.getY());
			army.setEndSize(endPosSize);
			army.setMapRoute(new MapRoute(army));
			army.setPos(endPos.toPosition());

			army.setTargetNode(targetNode);
			army.setTargetNodeId(targetNode.getPersistKey());
			army.setTargetNodeType(targetNode.getNodeType());

			army.setArmyType(armyType);
			army.setWorkType(ArmyWorkType.SETOUT);

			armyDao.updateArmyTargetIndex(army.getCurrentServerId(), oldPos, army);
		}

		logger.info("[GVG]trySplitArmy, rally, originalArmyId: {}, splitArmyIdList: {}", originalArmy.getPersistKey(), rallyContext.getJoinArmyIdList());
		return armyList;
	}

	// 处理部队驻防，主要是针对集结部队的分别处理
	private void doArmyGarrison(StrongHoldNode strongHoldNode, ArmyInfo army) {
		List<ArmyInfo> arrivingArmyList = trySplitArmy(army);
		var garrisonArmyList = getNodeGarrisonArmies(strongHoldNode);
		var garrisonArmyNumMax = garrisonArmyList.size() > 0 ?  getGarrisonArmyNumMax(strongHoldNode) : 10;
		var nodeArmySeat = garrisonArmyNumMax - garrisonArmyList.size();
		var nodeSoldierNumMax = garrisonArmyList.size() > 0 ?  getSoldierMaxNum(strongHoldNode) : 999999999;
		var nodeSoldierSeatCount = nodeSoldierNumMax - getSoldierNum(strongHoldNode);
		for (var arrivingArmy : arrivingArmyList) {
			var rst = oneArmyGarrison(arrivingArmy, nodeArmySeat, nodeSoldierSeatCount, garrisonArmyList);
			nodeArmySeat -= rst.takeArmySeat;
			nodeSoldierSeatCount -= rst.takeSoldierSeat;
			if(rst.takeArmySeat > 0) {	// 新部队驻防
				setArmyGarrison(strongHoldNode, arrivingArmy);
			}
		}
	}

	private void tryAutoSetLeader(StrongHoldNode strongHoldNode){
		if(strongHoldNode.isAutoSetLeader()) {
			strongHoldNode.autoChooseLeader();
		}
	}

	public void loadOfficeBuff() {
		logger.info("loadOfficeBuff start");
		GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo = gvgBattleDataVoManager.findGvgBattleServerDispatchRecordVo();
		int server1 = gvgBattleServerDispatchRecordVo.getAlliance1ServerId();
		if (gvgBattleServerDispatchRecordVo == null) {
			return;
		}
		if (server1 > 0) {
			officialsInfoDao.checkOfficialsLoaded(server1);
		}


		int server2 = gvgBattleServerDispatchRecordVo.getAlliance2ServerId();
		if (server2 > 0 && server2 != server1) {
			officialsInfoDao.checkOfficialsLoaded(server2);
		}

		logger.info("loadOfficeBuff end");
	}

	private static class GarrisonResult {
		public int takeArmySeat;
		public int takeSoldierSeat;
		// 构造方法
		public GarrisonResult(int takeArmySeat, int takeSoldierSeat){
			this.takeArmySeat = takeArmySeat;
			this.takeSoldierSeat = takeSoldierSeat;
		}
	}

	// 如果已经有驻防则需要跟已有的合并，如果没有则新增一个部队。
	// 期间主要是处理超过限制的情况：【1】部队数量限制、【2】部队兵力限制、【3】总兵力限制
	private GarrisonResult oneArmyGarrison(ArmyInfo newArmy, int nodeArmySeat, int nodeSoldierSeatCount, List<ArmyInfo> garrisonArmyList){
		// 没兵了也返回，其他类型转换过来的可能存在没兵的情况
		if(newArmy.getSoldierCount() <= 0){
			ErrorLogUtil.errorLog("[GVG]beginOccupyStrongHold, army count is empty", "armyId",newArmy.getPersistKey(), "roleId",newArmy.getRoleId());
			armyManager.returnArmy(newArmy);
			return new GarrisonResult(0, 0);
		}

		ArmyInfo oldArmy = garrisonArmyList.stream()
				.filter(garrisonArmy -> Objects.equals(garrisonArmy.getRoleId(), newArmy.getRoleId()))
				.findFirst().orElse(null);

		// 如果是新进驻，则需要先判断部队数量限制
		if(oldArmy == null && nodeArmySeat <= 0) {
			armyManager.returnArmy(newArmy);
			return new GarrisonResult(0, 0);
		}

		// 先补英雄
		if(oldArmy != null) {
			// 给老队伍补英雄
			if (oldArmy.getHeros() == null || oldArmy.getHeros().isEmpty()) {
				logger.info("[GVG]oneArmyGarrison add hero, roleId: {}, newArmyId: {}, oldArmyId: {}, heroList: {}",
						newArmy.getRoleId(), newArmy.getPersistKey(), oldArmy.getPersistKey(), newArmy.getHeros());
				oldArmy.getHeros().addAll(newArmy.getHeros());
				newArmy.getHeros().clear();
			}
		}

		int newArmySoldierCount = newArmy.getSoldierCount();

		int oldArmySoldierCount = oldArmy != null ? oldArmy.getSoldierCount(): 0;
		// 单算本部队还可以进入多少
		var roleMaxSoldierCount = armyManager.getSetOutSoldierMax(oldArmy != null ? oldArmy: newArmy);
		int armySoldierSeatCount = roleMaxSoldierCount - oldArmySoldierCount;
		// 单个部队还能进入的数量 VS 整体还能进入的数量
		int soldierSeatCount = Math.min(armySoldierSeatCount, nodeSoldierSeatCount);

		// 如果一个位置都没有了，则直接退回
		if(soldierSeatCount <= 0) {
			logger.info("[GVG]oneArmyGarrison soldierSeatCount <= 0, roleId: {}, newArmyId: {}, " +
							"newArmySoldierCount: {}, oldArmySoldierCount: {}, roleMaxSoldierCount: {}, nodeSoldierSeatCount: {}",
					newArmy.getRoleId(), newArmy.getPersistKey(),
					newArmySoldierCount, oldArmySoldierCount, roleMaxSoldierCount, nodeSoldierSeatCount);

			armyManager.returnArmy(newArmy);
			return new GarrisonResult(0, 0);
		}

		// 退回超出的部队
		if(newArmySoldierCount > soldierSeatCount) {
			var returnSoliderCount = newArmySoldierCount - soldierSeatCount;
			armyManager.divideArmy(newArmy, returnSoliderCount, "GVGStrongHold");

			var newArmySoliderCountNew = newArmy.getSoldierCount();
			logger.info("[GVG]oneArmyGarrison divideArmy, roleId: {}, newArmyId: {}, " +
							"newArmySoldierCount: {}, oldArmySoldierCount: {}, roleMaxSoldierCount: {}, nodeSoldierSeatCount: {}, " +
							"returnSoliderCount: {}, newArmySoliderCountNew: {}",
					newArmy.getRoleId(), newArmy.getPersistKey(),
					newArmySoldierCount, oldArmySoldierCount, roleMaxSoldierCount, nodeSoldierSeatCount
					, returnSoliderCount, newArmySoliderCountNew);
		}

		// 如果已经有部队了，则进驻进去
		if(oldArmy != null) {
			// 给老队伍补兵
			var oldArmySoldierMap = oldArmy.getArmySoldiers();
			var newArmySoldierMap = newArmy.getArmySoldiers();

			newArmySoldierMap.forEach((soldierMetaId, newArmySoldier) -> {
				int addCount = newArmySoldier.getCount();
				newArmySoldier.setCount(0);
				var oldArmySoldier = oldArmySoldierMap.get(soldierMetaId);
				if (oldArmySoldier == null) {
					oldArmySoldierMap.put(soldierMetaId, new ArmySoldier(soldierMetaId, addCount));
				} else {
					oldArmySoldier.setCount(oldArmySoldier.getCount() + addCount);
				}
			});

			var oldArmySoliderCountNew = oldArmy.getSoldierCount();
			var newArmySoliderCountNew = newArmy.getSoldierCount();
			// 有老队伍，则不需要新队伍了
			armyManager.takeBackArmy(newArmy);

			logger.info("[GVG]oneArmyGarrison oldArmyInc, roleId: {}, oldArmyId {}, newArmyId: {} " +
							"newArmySoldierCount: {}, oldArmySoldierCount: {}, roleMaxSoldierCount: {}, nodeSoldierSeatCount: {}, " +
							"oldArmySoliderCountNew: {}, newArmySoliderCountNew: {}",
					newArmy.getRoleId(),oldArmy.getPersistKey(), newArmy.getPersistKey(),
					newArmySoldierCount, oldArmySoldierCount, roleMaxSoldierCount, nodeSoldierSeatCount,
					oldArmySoliderCountNew, newArmySoliderCountNew);

			return new GarrisonResult(0, oldArmySoliderCountNew - oldArmySoldierCount);
		} else {
			// 打印日志
			logger.info("[GVG]oneArmyGarrison oldArmyInc, roleId: {}, newArmyId: {}, " +
							"newArmySoldierCount: {}, oldArmySoldierCount: {}, roleMaxSoldierCount: {}, nodeSoldierSeatCount: {}",
					newArmy.getRoleId(), newArmy.getPersistKey(),
					newArmySoldierCount, oldArmySoldierCount, roleMaxSoldierCount, nodeSoldierSeatCount);
			return new GarrisonResult(1, newArmy.getSoldierCount());
		}
	}

	/**
	 * 开始占领建筑，经过一段时间后，会完成占领，调用{@link #onOccupied(StrongHoldNode) }(占领完成)
	 */
	private void tryBeginOccupyStrongHold(Role role, StrongHoldNode strongHoldNode, ArmyInfo army) {
		if(Objects.equals(strongHoldNode.getAllianceId(), role.getAllianceId())){
			logger.info("[GVG]tryBeginOccupyStrongHold no need begin role: {}, roleAlliance: {}, node: {}", role.getId(), role.getAllianceId(), strongHoldNode.getPersistKey());
			// 对于已经占领的，进去的时候更新速度
			updateRolePointSpeed(List.of(role.getId()));
			return;
		}
		logger.info("[GVG]tryBeginOccupyStrongHold role: {}, roleAlliance: {}, node: {}({},{}), nodeAlliance: {}, nodeOldAlliance: {}, army: {}",
				role.getId(), role.getAllianceId(), strongHoldNode.getPersistKey(), strongHoldNode.getMetaId(), strongHoldNode.getBuildingType(), strongHoldNode.getAllianceId(), strongHoldNode.getOldAllianceId(), army.getPersistKey());

		var oldAllianceId = strongHoldNode.getAllianceId();
		// 上次占领时间
		long lastOccupyTime = strongHoldNode.getOccupyTime();

		// 更新建筑的占领信息：开始占领计时
		setStrongHoldOccupyingState(role, strongHoldNode);

		// 如果有旧联盟，则对旧联盟执行结算
		if(oldAllianceId > 0) {
			// 如果是官渡，则对旧联盟执行结算
			recountGuanDuOccupyTimeOfOldAlliance(strongHoldNode, oldAllianceId, lastOccupyTime);

			// 更新旧联盟Buff
			updateAllianceBuildingBuff(oldAllianceId);
			removeAllianceBuff(oldAllianceId, strongHoldNode);

			// 更新旧联盟的积分速度
			updateAlliancePointSpeed(oldAllianceId);
		}

		// 占领状态改变后，更新相关的allianceWar
		warService.warDel(army);	// 可能是向己方建筑发起的驻防，但是中途被占领了（此时创建war），占领者在本army到达前又全部撤退（checkNeedAttackWhenGarrison返回false），这时候有war，需要删掉
		updateAllianceWar(strongHoldNode, oldAllianceId);

		// 乌巢占领
		onOccupyWuChao(strongHoldNode);

		// 建筑易主后，同步建筑属性变化
		sceneService.update(strongHoldNode, null);

		// 同步给客户端战斗服信息  todo gvg 这里应该不需要同步
		pushInformationBothSides(0, 0L);

		pushPiliCheNextAttackTime(strongHoldNode);

		// bi打点
		beginOccupyStrongHoldBI(role, strongHoldNode, oldAllianceId);
	}

	private void setArmyGarrison(StrongHoldNode node, ArmyInfo army) {
		var endPos = node.getPosition();

		army.setArmyType(ArmyType.GARRISON_STRONGHOLD);
		army.setWorkType(ArmyWorkType.DEFENDING);

		// 如果之前是集结部队，则修改终点
		Point oldPos = Point.getInstance(army.getEndX(), army.getEndY());
		if(!oldPos.equals(endPos)) {
			army.setEndX(endPos.getX());
			army.setEndY(endPos.getY());
			if (army.getMapRoute() != null) {
				army.getMapRoute().setEnd(endPos);
			}
			army.setPos(endPos.toPosition());
			armyDao.updateArmyTargetIndex(army.getCurrentServerId(), oldPos, army);
		}

		var targetNode = sceneService.getSceneNode(Application.getServerId(), army.getEndPoint());
		if(targetNode != null){
			army.setTargetNode(targetNode);
			army.setTargetNodeId(targetNode.getPersistKey());
			army.setTargetNodeType(targetNode.getNodeType());
		}
		armyManager.saveArmy(army);

		// 数据同步
		sceneService.remove(army);				// aoi中移除这个行军
		armyService.updateArmyProgress(army);	// 更新信息给前端
	}

	private void onOccupyWuChao(StrongHoldNode node){
		if(node.getBuildingType() == BuildingType.WuChao){
			wuChaoManager.beginSupplyWagon(node.getAllianceId(), this::onSupplyWagonArrive);
			broadcastWuChaoArmyAppear();
		}
	}

	private void beginOccupyStrongHoldBI(Role role, StrongHoldNode node, long oldAllianceId) {
		// 打点联盟总积分
		Alliance alliance = allianceService.getAllianceByRole(role);
		if (alliance == null) {
			ErrorLogUtil.errorLog("[GVG]allianceOccupyStrongHold 新归属联盟为空", "roleId",role.getId());
			return;
		}

		GvgBuildingMeta meta = configService.getConfig(GvgBuildingConfig.class).get(node.getMetaId());
		if (meta == null) {
			ErrorLogUtil.errorLog("[GVG]occupyBattleBuilding GvgBuildingMeta is null", "roleId",role.getId(), "metaId",node.getMetaId());
			return;
		}
		try {
			if(Application.getServerType() == ServerType.GVG_BATTLE){
				srvDpd.getBiLogUtil().gvgOccupyBuilding(role, Application.getServerId(), alliance.getPersistKey(), node.getMetaId(), oldAllianceId);
			}
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("[GVG]占领联盟建筑异常", e,"metaId",meta.getId(),
					"allianceId",alliance.getId(), "oldAllianceId",oldAllianceId);
		}
	}

	public void recountGuanDuOccupyTimeOfOldAlliance(StrongHoldNode node, long oldAllianceId, long lastOccupiedTime) {
		if(lastOccupiedTime <= 0){
			ErrorLogUtil.errorLog("[GVG]recountGuanDuOccupyTimeOfOldAlliance lastOccupiedTime <=0 error",
					"oldAllianceId",oldAllianceId, "lastOccupiedTime",lastOccupiedTime);
			return;
		}
		// 官渡的占领时间
		GvgBuildingMeta gvgBuildingMeta = configService.getConfig(GvgBuildingConfig.class).get(node.getMetaId());
		if(gvgBuildingMeta == null){
			ErrorLogUtil.errorLog("[GVG]recountGuanDuOccupyTimeOfOldAlliance gvgBuildingMeta null",
					"oldAllianceId",oldAllianceId, "metaId",node.getMetaId());
			return;
		}

		if (gvgBuildingMeta.getBuildingtype() != BuildingType.GuanDu.getPsType() ) {
			return;
		}

		if(oldAllianceId <= 0L){
			ErrorLogUtil.errorLog("[GVG]recountGuanDuOccupyTimeOfOldAlliance oldAllianceId error", "oldAllianceId",oldAllianceId);
			return;
		}

		long addOccupiedTime = (TimeUtil.getNow() - lastOccupiedTime) / TimeUtil.SECONDS_MILLIS;
		gvgBattleScoreService.addAllianceOccupyGuanduTime(oldAllianceId, addOccupiedTime);
	}

	private void setStrongHoldOccupyingState(Role role, StrongHoldNode node) {
		// 生效时间
		var occupyTime = calculateOccupyTime(role, node);

		// 设置新占领的状态
		node.setOldAllianceId(node.getAllianceId());
		node.setOccupyState(false);
		node.setAllianceId(role.getAllianceId());
		node.setOccupyTime(occupyTime);
		node.setOccupyBeginTime(TimeUtil.getNow());
		node.setAutoSetLeader(true);	// 默认打开自动设置队长
		logger.info("setStrongHoldOccupyingState role {} building {} alliance {} time {}", role.getId(), node.getMetaId(), node.getAllianceId(), node.getOccupyTime());
		strongHoldNodeDao.save(node);
	}


	private long calculateOccupyTime(Role role, StrongHoldNode node) {
		GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
		long reduceTime = 0L;
		if (role != null) {
			int propValue = role.getNumberProps().getInt(Prop.GVG_BUILDING_OCCUPY_REDUCE_51020);
			if (propValue > 0) {
				reduceTime = propValue * TimeUtil.SECONDS_MILLIS;
			}
		}
		return TimeUtil.getNow() +
				(node.getBuildingType() == BuildingType.WuChao ? 0 : gvgSettingConfig.getOccupyEffectTime() * TimeUtil.SECONDS_MILLIS) - reduceTime;
	}


	private void clearStrongHoldOccupyState(StrongHoldNode node) {
		node.setOccupyState(false);
		node.setOldAllianceId(node.getAllianceId());	// 记录旧联盟Id
		node.setAllianceId(0L);
		node.setOccupierList(Lists.newArrayList());

		strongHoldNodeDao.save(node);
	}


	public void updateAlliancePointSpeed(long allianceId){
		if(allianceId <= 0){
			return;
		}

		Alliance alliance = allianceService.getAllianceById(allianceId);
		if (alliance == null) {
			ErrorLogUtil.errorLog("[GVG]updateAlliancePointSpeed: alliance == null", "allianceId",allianceId);
			return;
		}

		// 结算一次
		gvgBattleService.recountAlliancePoint(alliance.getId(), false);
		// 更新速度
		gvgBattleService.updateAllianceBattlePointSpeed(alliance.getId());
	}

	public void updateRolePointSpeed(List<Long> roleIdList) {
		for (var roleId : roleIdList) {
			var role = roleManager.getRole(roleId);
			if(role == null) {
				ErrorLogUtil.errorLog("[GVG]updateRolePointSpeed role null", "roleId",roleId);
				continue;
			}
			recountRoleBattlePoint(role, false);
			updateRoleSpeed(role);
		}
	}

	private int occupyAddPoint(StrongHoldNode node){
		long allianceId = node.getAllianceId();
		GvgBuildingMeta meta = configService.getConfig(GvgBuildingConfig.class).get(node.getMetaId());
		if (meta == null) {
			ErrorLogUtil.errorLog("[GVG]occupyAddPoint GvgBuildingMeta is null", "metaId",node.getMetaId());
			return 0;
		}

		// 首次占领点数，再次占领没有点数
		int additionPoint = node.occupied() ? meta.occupyScore() : meta.firstOccupyScore();
		if (additionPoint > 0) {
			List<Long> rallyArmyAllRole = node.getGarrisonRoleList();
			for (Long roleId : rallyArmyAllRole) {
				// 个人点数
				Role role = roleDao.findById(roleId);
				if (role == null) {
					continue;
				}
				gvgBattleScoreService.addRolePoint(role, additionPoint, AddPointType.FIRST_OCCUPY, false);
			}
			gvgBattleScoreService.addAlliancePoint(allianceId, additionPoint, AddPointType.FIRST_OCCUPY, false);
		}

		// 如果是首次占领，设置标识，表示已经占领过了
		if (!node.occupied()) {
			node.setOccupied(true);
		}

		return additionPoint;
	}

	/**
	 * 完成占领的处理
	 */
	public void onOccupied(StrongHoldNode node) {
		logger.info("[GVG]onOccupied, alliance: {}, node: {}({}, {})", node.getAllianceId(), node.getPersistKey(), node.getMetaId(), node.getBuildingType());
		long allianceId = node.getAllianceId();
		// 重置占领中的倒计时状态
		setStrongHoldOccupied(node);

		// 占领建筑后加BUFF
		updateAllianceBuildingBuff(node.getAllianceId());
		addAllianceBuff(node);

		// 更新联盟的积分累积速度
		updateAlliancePointSpeed(allianceId);

		// 更新个人积分累积速度
		updateRolePointSpeed(node.getGarrisonRoleList());

		// 占领建筑后加点数（包括个人和联盟）
		int addScore = occupyAddPoint(node);

		// 同步积分（包括积分速度等）变化
		pushInformationBothSides(addScore, allianceId);

		// 同步建筑属性变化
		sceneService.update(node, null);

		// 占领后推送
		broadCastOccupyBuilding(node);

		// 系统公告
//		noticeOccupied(node, allianceId);
	}

	// 官渡变更推送霹雳车
	private void pushPiliCheNextAttackTime(StrongHoldNode node) {
		try {
			if (node.getBuildingType() != BuildingType.GuanDu) {
				return;
			}
			List<StrongHoldNode> piliChe = strongHoldNodeDao.findBuildingByType(BuildingType.PiLiChe);
			if (!JavaUtils.bool(piliChe)) {
				return;
			}
			StrongHoldNode strongHoldNode = piliChe.get(0);
			sceneService.update(strongHoldNode, null);
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("GVG pushPiliCheNextAttackTime error", e);
		}
	}

	private void noticeOccupied(StrongHoldNode node, long allianceId) {
		Alliance alliance = allianceService.getAllianceById(allianceId);
		if(alliance == null){
			return;
		}
		GvgBuildingMeta meta = configService.getConfig(GvgBuildingConfig.class).get(node.getMetaId());
		if (meta == null) {
			return;
		}

		if (Application.getServerType() == ServerType.TVT_BATTLE) {
			noticeService.systemMarqueeByCurrentServerId(node.getCurrentServerId(),
					PsSystemMarquee.TVT_OCCUPY_BUILD,
					"@" + alliance.getName() + "@",
					NoticeConstants.getKey(meta.getBuildName()));
		} else {
			noticeService.systemMarqueeByCurrentServerId(node.getCurrentServerId(),
					PsSystemMarquee.GVG_OCCUPY_BUILD,
					alliance.getName(),
					alliance.getAliasName(),
					NoticeConstants.getKey(meta.getBuildName()));
		}
	}

	private void setStrongHoldOccupied(StrongHoldNode node) {
		node.setOccupyState(true);

		if (node.getBuildingType() == BuildingType.PiLiChe) {
			node.setLastActionTime(TimeUtil.getNow());
		}
		strongHoldNodeDao.save(node);
	}

	private void updateAllianceBuildingBuff(long allianceId) {
		if(allianceId <= 0) {
			return;
		}

		// 占领后，全员加属性
		List<AllianceMember> members = allianceMemberManager.getMembers(allianceId);
		for (AllianceMember member : members) {
			// 更新属性
			Role memberRole = roleManager.getRole(member.getPersistKey());
			if (memberRole == null) {
				ErrorLogUtil.errorLog("[GVG]updateAllianceBuildingBuff can't find role",
						"roleId",member.getPersistKey(), "allianceId",allianceId);
				continue;
			}
			// buff CD以后再生效
			roleCalcPropManager.addChangeType(memberRole, ICalcProp.RoleType.GVG_BUILDING);
			sendGVGBattleInfo(memberRole);
		}
	}

	/**
	 * 获取联盟GVG战场的建筑
	 */
	public List<StrongHoldNode> getAllianceOccupyBuildings(long allianceId) {
		List<StrongHoldNode> nodes = new ArrayList<>();
		Collection<StrongHoldNode> allNodes = strongHoldNodeDao.findAll();
		for (StrongHoldNode node : allNodes) {
			if (node.isOccupiedByAlliance(allianceId)) {
				nodes.add(node);
			}
		}

		return nodes;
	}


	// 判断玩家是否可迁城的范围：避免迁入到对方的出生范围
	public boolean isCanMoveArea(int serverId, Long allianceId, Point point) {
		GvgBattleServerDispatchRecordVo dispatchRecordInfo = gvgBattleDataVoManager.getDispatchRecordInfo();
		Long allianceId1 = dispatchRecordInfo.getAllianceId1();
		Long allianceId2 = dispatchRecordInfo.getAllianceId2();
		if (!JavaUtils.bool(allianceId) || !JavaUtils.bool(allianceId2)) {
			return true;
		}

		Long otherSideAllianceId = allianceId.equals(allianceId1) ? allianceId2 : allianceId1;
		GVGBirthArea gvgBirthArea = gvgBattleDataVoManager.findGvgBirthArea(otherSideAllianceId);
		if (gvgBirthArea != null) {
			int mapHeight = worldService.getMapHeight(serverId);
			int mapWidth = worldService.getMapWidth(serverId);
			int minX = Math.max(gvgBirthArea.getMinX(), 0);
			int maxX = Math.min(gvgBirthArea.getMaxX(), mapWidth - 1);
			int minY = Math.max(gvgBirthArea.getMinY(), 0);
			int maxY = Math.min(gvgBirthArea.getMaxY(), mapHeight - 1);
			if (point.getX() >= minX && point.getX() <= maxX && point.getY() >= minY && point.getY() <= maxY) {
				return false;
			}
		}

		return true;
	}


	// 判断玩家是否在可迁城区域
	public boolean isInMoveArea(Role role, Point point) {
		Long allianceId = role.getAllianceId();
		if (!JavaUtils.bool(allianceId))
			return false;

		// 判断玩家是否可迁城的范围：避免迁入到对方的出生范围
		if (!isCanMoveArea(role.getCurrentServerId(), allianceId, point)) {
			ErrorLogUtil.errorLog("玩家GVG迁城 迁入到对方联盟的出生地",
					"roleId",role.getId(), "pointX",point.getX(), "pointY",point.getY());
			return false;
		}

		// 判断坐标重复
		SceneNode sceneNode = sceneService.getSceneNode(role.getCurrentServerId(), point);
		if (sceneNode != null) {
			ErrorLogUtil.errorLog("玩家GVG迁城 坐标重复", "roleId",role.getId(),
					"pointX",point.getX(), "pointY",point.getY());
			return false;
		}

		// 开打以后，除了对方的安全区，都可以随意迁城
		return true;
	}

	/**
	 * 判断当前活动阶段是否可迁城
	 */
	public boolean isMoveGvgActivityStage(long now) {
		GVGBattleFieldTimeLine gvgBattleFieldTimeLine = gvgBattleFieldTimeLineDao.find();

		if(gvgBattleFieldTimeLine == null){
			logger.info("[GVG]isMoveGvgActivityStage can't find ready time data, now: {}", now);
			return false;
		}

		var readyEndTime = gvgBattleFieldTimeLine.getReadyEndTime();
		if(now < readyEndTime){
			// 时辰未到
			return false;
		}

		return true;
	}

	/**
	 * 购买体力
	 */
	public void buyStamina(Role role) {
		RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(role.getId());
		if (roleGVGBattle == null) {
			ErrorLogUtil.errorLog("buyStamina roleGVGBattle is null", "roleId",role.getId());
			return;
		}

		GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
		if (roleGVGBattle.getGvgStamine() > gvgSettingConfig.getMaxStamina()) {
			ErrorLogUtil.errorLog("玩家购买gvg体力,体力达到上限", "roleId",role.getId());
			return;
		}

		// 判断是否达到购买上限
		if (roleGVGBattle.getBuyStaminaCount() >= gvgSettingConfig.getBuyStaminaMax()) {
			ErrorLogUtil.errorLog("buyStamina roleGVGBattle is max count", "roleId",role.getId(),
					"buyCount",roleGVGBattle.getBuyStaminaCount(), "maxCount",gvgSettingConfig.getBuyStaminaMax());
			return;
		}

		// 获取花费钻石数
		int needDiamond = gvgSettingConfig.getBuyStaminaCost(roleGVGBattle.getBuyStaminaCount() + 1);
		if (!roleCurrencyManager.checkAndCost(role, Currency.DIAMOND, needDiamond, LogReasons.MoneyLogReason.GVG_BUY_STAMINA)) {
			ErrorLogUtil.errorLog("buyStamina not have enough diamond", "roleId",role.getId(), "needDiamond",needDiamond);
			return;
		}

		roleGVGBattle.setGvgStamine(roleGVGBattle.getGvgStamine() + gvgSettingConfig.getBuyStaminaNum());
		roleGVGBattle.setBuyStaminaCount(roleGVGBattle.getBuyStaminaCount() + 1);
		roleGVGBattleDao.save(roleGVGBattle);

		// 更新战场信息
		sendGVGBattleInfo(role);
	}

	/**
	 * 使用技能治疗伤兵
	 * */
	public void cureWoundedSoldier(Role role){
		long now = TimeUtil.getNow();
		GVGBattleFieldTimeLine gvgBattleFieldTimeLine = gvgBattleFieldTimeLineDao.find();
		if (gvgBattleFieldTimeLine != null && gvgBattleFieldTimeLine.getReadyEndTime() > now) {
			ErrorLogUtil.errorLog("cureWoundedSoldier Ready Time", "roleId",role.getId());
			return;
		}

		RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(role.getId());
		if (roleGVGBattle == null) {
			ErrorLogUtil.errorLog("cureWoundedSoldier roleGVGBattle is null", "roleId",role.getId());
			return;
		}

		// 判断CD
//		GcCureWoundedSoldier msg = new GcCureWoundedSoldier();
		if(roleGVGBattle.getLastCureWoundedTime() > 0 && now < roleGVGBattle.getLastCureWoundedTime()){
			return;
		}


		GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
		int curCount = soldierService.gvgCureSoldier(role, SoldierUpdateReasonType.GVG_BATTLE_USE_SKILL, false);
		if (curCount <= 0) {
			return;
		}
		roleGVGBattle.setLastCureWoundedTime(now + gvgSettingConfig.getCureWoundedSkillCD() * TimeUtils.SECONDS_MILLIS);
		roleGVGBattleDao.save(roleGVGBattle);

//		sendGVGBattleInfo(role);

		// 统计联盟治疗总数
		AllianceBattlePoint alliance1Point = allianceBattlePointDao.findById(role.getAllianceId());
		if(alliance1Point != null){
			alliance1Point.setTotalCureScore(alliance1Point.getTotalCureScore() + curCount);
			allianceBattlePointDao.save(alliance1Point);
		}


		// 同步
		Map<String, Soldier> woundedSoldiers = soldierManager.getAllWounded(role);

		List<Soldier> changeSoldiers = new ArrayList<>();
		List<Soldier> currWounded = new ArrayList<>();
		if (woundedSoldiers != null) {
			for (Map.Entry<String, Soldier> soldierEntry : woundedSoldiers.entrySet()) {
				Soldier byMetaId = soldierManager.getByMetaId(role, soldierEntry.getKey());
				if (byMetaId != null) {
					changeSoldiers.add(byMetaId);
					SoldierConfig.SoldierMeta soldierMeta = soldierService.getSoldierMeta(soldierEntry.getKey());
					currWounded.add(new Soldier(soldierMeta, soldierEntry.getValue() == null ? 0 : soldierEntry.getValue().getCount()));
				}
			}
		}

		role.send(SoldierManager.wrapperWoundedUpdate(changeSoldiers, currWounded, soldierManager.getDailyCureCount(role), soldierManager.getHospitalCapacity(role)));

		srvDpd.getNoticeService().noticeCureSoldier(role, curCount);

		// 同步CD
		sendGVGBattleInfo(role);

		sendCureSkillSuccess(role);

		GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo = gvgBattleDataVoManager.findGvgBattleServerDispatchRecordVo();



		if (Application.getServerType() == ServerType.TVT_BATTLE) {
//			srvDpd.getBiLogUtil().tvtAutoCureSoldier(role, cureMap.toString(), back ? 2 : 1);
		} else {
			int happen_time = (int) ((TimeUtil.getNow() - gvgBattleServerDispatchRecordVo.getBattleStartTime()) / TimeUtil.MINUTE_MILLIS);
			srvDpd.getBiLogUtil().gvgAutoCureSoldier(role, happen_time, curCount);
		}



	}

	private void sendCureSkillSuccess(Role role) {
		GcGVGCureSoldierSkill msg = new GcGVGCureSoldierSkill();
		role.send(msg);
	}

	public void addCureWoundedSoldier(Role role, int curCount){
		if(!Application.isBattleServer()){
			return;
		}

		// 统计联盟治疗总数
		AllianceBattlePoint alliance1Point = allianceBattlePointDao.findById(role.getAllianceId());
		if(alliance1Point != null){
			alliance1Point.setTotalCureScore(alliance1Point.getTotalCureScore() + curCount);
			allianceBattlePointDao.save(alliance1Point);
		}
	}

	/**
	 * 购买迁城次数
	 */
	public boolean buyMoveCity(Role role) {
		RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(role.getId());
		if (roleGVGBattle == null) {
			ErrorLogUtil.errorLog("buyMoveCity roleGVGBattle is null", "roleId",role.getId());
			return false;
		}

		// 判断是否达到购买上限
		GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
		if (roleGVGBattle.getBuyMoveCount() >= gvgSettingConfig.getBuyMoveMax()) {
			ErrorLogUtil.errorLog("buyMoveCity roleGVGBattle is max count", "roleId",role.getId(),
					"buyCount",roleGVGBattle.getBuyStaminaCount(), "maxCount",gvgSettingConfig.getBuyMoveMax());
			return false;
		}

		// 获取花费钻石数
		int needDiamond = gvgSettingConfig.getBuyMoveCost(roleGVGBattle.getBuyMoveCount() + 1);
		if (!roleCurrencyManager.checkAndCost(role, Currency.DIAMOND, needDiamond, LogReasons.MoneyLogReason.GVG_BUY_MOVE_CITY)) {
			ErrorLogUtil.errorLog("buyMoveCity Role not have enough diamond", "roleId",role.getId(), "needDiamond",needDiamond);
			return false;
		}

		roleGVGBattle.setMoveCount(roleGVGBattle.getMoveCount() + 1);
		roleGVGBattle.setBuyMoveCount(roleGVGBattle.getBuyMoveCount() + 1);
		roleGVGBattleDao.save(roleGVGBattle);

		sendGVGBattleInfo(role);
		return true;
	}

	public void sendGVGBattleInfo(Role role) {
		GcGVGBattleInfo msg = new GcGVGBattleInfo();
		RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(role.getId());
		if (roleGVGBattle != null) {
			msg.setMoveBuycount(roleGVGBattle.getBuyMoveCount());
			msg.setStaminaBuycount(roleGVGBattle.getBuyStaminaCount());
			msg.setMoveCount(roleGVGBattle.getMoveCount());
			msg.setBattlePoint(roleGVGBattle.getBattlePoint());
			msg.setSpeed(roleGVGBattle.getSpeed());
			msg.setLastCalTime(roleGVGBattle.getLastCalTime());
			msg.setCureWoundedTime(roleGVGBattle.getLastCureWoundedTime());
			msg.setFreeMoveCityTime(roleGVGBattle.getFreeMoveCityTime());
			msg.setMoveCityItemCostCount(getGVGMoveCostCount(roleGVGBattle));
		}

		role.send(msg);
	}

	public void sendWuChaoInfo(Role role){
		var supplyWagon = wuChaoManager.getSupplyWagon();
		if(supplyWagon != null) {
			var supplyWagonMsg = makeSupplyWagonMsg(supplyWagon);
			role.send(supplyWagonMsg);
		}
	}

	private int getGVGMoveCostCount(RoleGVGBattle roleGVGBattle) {
		if (TimeUtil.getNow() >= roleGVGBattle.getFreeMoveCityTime()) {
			return 0;
		}
		return roleGVGBattle.getMoveCityItemCostCount();
	}

	public GcStrongHoldDetailInfo toGcStrongHoldDetailInfo(Long id) {
		if (id <= 0) {
			return null;
		}

		GcStrongHoldDetailInfo detailInfo = new GcStrongHoldDetailInfo();
		detailInfo.setId(id);

		StrongHoldNode strongHoldNode = strongHoldNodeDao.findById(id);
		if (strongHoldNode == null) {
			ErrorLogUtil.errorLog("getStrongHoldDetailInfo, strongHoldNode == null", "id",id);
			return detailInfo;
		}

		var friendArmies = getNodeFriendArmies(strongHoldNode);
		if(!friendArmies.isEmpty()) {
			// 按约定，队长是数组的第一个
			var leaderArmyId = strongHoldNode.leaderArmyId();
			boolean findLeader = false;
			// 先放入队长
			for (var army : friendArmies) {
				if (army.getWorkType() == ArmyWorkType.RETURN || army.getWorkType() == ArmyWorkType.SETOUT) {
					continue;
				}
				if (army.getPersistKey().equals(leaderArmyId)) {
					detailInfo.addToStationArmys(toPsStrongHoldArmyInfo(strongHoldNode.getPersistKey(), army));
					findLeader = true;
				}
			}
			if(!findLeader) {
				ErrorLogUtil.errorLog("getStrongHoldDetailInfo, can't find leaderArmy", "nodeId",id, "leaderArmyId",leaderArmyId);
			}
			// 再放入普通成员
			for (var army : friendArmies) {
				if (army.getWorkType() == ArmyWorkType.RETURN || army.getWorkType() == ArmyWorkType.SETOUT) {
					continue;
				}
				if (!army.getPersistKey().equals(leaderArmyId)) {
					detailInfo.addToStationArmys(toPsStrongHoldArmyInfo(strongHoldNode.getPersistKey(), army));
				}
			}
		}

		detailInfo.setTroopMax(getGarrisonArmyNumMax(strongHoldNode));
		detailInfo.setFightTroopMax(getSoldierMaxNum(strongHoldNode));
		detailInfo.setAutoSetLeader(strongHoldNode.isAutoSetLeader());
		return detailInfo;
	}

	private PsStrongHoldArmyInfo toPsStrongHoldArmyInfo(Long strongHoldId, ArmyInfo army) {
		PsStrongHoldArmyInfo psArmy = new PsStrongHoldArmyInfo();
		psArmy.setDetailInfo(new PsStationArmyUnit());

		long roleId = army.getRoleId();
		Role role = roleManager.getRole(roleId);
		RoleSimpleInfo roleSimpleInfo = new RoleSimpleInfo(role, null);
		psArmy.getDetailInfo().setRoleInfo(roleSimpleInfo.toPsSimpleInfo());

		psArmy.setArmyId(army.getPersistKey());
		psArmy.setStartTime(army.getStartWorkTime());
		psArmy.setTotalTime(army.getTotalWorkTime());
		psArmy.getDetailInfo().setArmyInfo(new PsArmySimpleInfo());
		psArmy.getDetailInfo().getArmyInfo().setSoldiers(army.getSoldierMap());
		psArmy.getDetailInfo().getArmyInfo().setHeroInfo(HeroOutput.toHeroSimpleList(role.getRoleId(), army.getHeros()));
		psArmy.setArrived(army.getWorkType() == ArmyWorkType.DEFENDING);

		return psArmy;
	}

	/**
	 *
	 * 推送战场服的主ui信息。双方积分、增长速度
	 *
	 */
	public void pushInformationBothSides(int addScore, Long addScoreAlliance) {
		GcGvgBattleServerInfo info = wrapperBattleServerBothSidesInfo(addScore, addScoreAlliance);
		Collection<Role> all = roleDao.findAll();
		for (Role role : all)
			if (role.isOnline())
				role.send(info);

	}

	private GcGvgBattleServerInfo wrapperBattleServerBothSidesInfo(int addScore, Long addScoreAlliance) {
		GcGvgBattleServerInfo info = new GcGvgBattleServerInfo();
		GvgBattleServerDispatchRecordVo dispatchRecordInfo = gvgBattleDataVoManager.getDispatchRecordInfo();
		//
		PsGVGBattleServerAllianceInfo allianceInfo1 = buildPsGVGBattleAllianceInfo(dispatchRecordInfo.getAllianceId1(), dispatchRecordInfo.getAlliance1ServerId());
		PsGVGBattleServerAllianceInfo allianceInfo2 = buildPsGVGBattleAllianceInfo(dispatchRecordInfo.getAllianceId2(), dispatchRecordInfo.getAlliance2ServerId());

		// 联盟人数
		var allianceMemberDaoAll = allianceMemberDao.findAll();
		Map<Long, Integer> allianceMemberCountMap = new HashMap<>();
		for(var allianceMember: allianceMemberDaoAll){
			Long allianceId = allianceMember.getAllianceId();
			if (allianceId == null) {
				continue;
			}
			allianceMemberCountMap.put(allianceId, allianceMemberCountMap.getOrDefault(allianceId, 0) + 1);
		}

		allianceInfo1.setAllianceMemberCount(allianceMemberCountMap.getOrDefault(allianceInfo1.getAllianceId(), 0));
		allianceInfo2.setAllianceMemberCount(allianceMemberCountMap.getOrDefault(allianceInfo2.getAllianceId(), 0));

		// 大额积分变化
		if(addScore > 0){
			if (Objects.equals(allianceInfo1.getAllianceId(), addScoreAlliance)) {
				allianceInfo1.setAddScore(addScore);
			} else if(Objects.equals(allianceInfo2.getAllianceId(), addScoreAlliance)){
				allianceInfo2.setAddScore(addScore);
			}
		}

		info.setAlliance1(allianceInfo1);
		info.setAlliance2(allianceInfo2);

		info.setMatchType(dispatchRecordInfo.getMatchType().getPsState());
		return info;
	}

	private PsGVGBattleServerAllianceInfo buildPsGVGBattleAllianceInfo(Long allianceId, int allianceServerId) {
		PsGVGBattleServerAllianceInfo allianceInfo = new PsGVGBattleServerAllianceInfo();
		if (allianceId == null) {
			return allianceInfo;
		}
		allianceInfo.setAllianceId(allianceId);
		allianceInfo.setAllianceServerId(allianceServerId);

		Alliance alliance = allianceService.getAllianceById(allianceId);
		if (alliance != null) {
			allianceInfo.setAllianceName(alliance.getName());
			allianceInfo.setAllianceAlias(alliance.getAliasName());
			PsAllianceFlagInfo flag = new PsAllianceFlagInfo();
			flag.setBannerColor(alliance.getBannerColor());
			flag.setBanner(alliance.getBanner());
			flag.setBadgeColor(alliance.getBadgeColor());
			flag.setBadge(alliance.getBadge());
			allianceInfo.setFlag(flag);
		}

		AllianceBattlePoint alliancePoint = allianceBattlePointDao.findById(allianceId);
		if (alliancePoint == null) {
			allianceInfo.setScoreSpeed(0);
			allianceInfo.setScore(0);
		} else {
			allianceInfo.setScoreSpeed(alliancePoint.getPointSpeed());
			allianceInfo.setScore(alliancePoint.getValue());
		}
		long lastCountTime = alliancePoint != null ? alliancePoint.getLastCountTime() : 0L;
		allianceInfo.setUpdateTime(lastCountTime);


		allianceInfo.setColor(gvgBattleDataVoManager.findGvgBirthArea(allianceId).getColor());

		allianceInfo.setAddScore(0);
		return allianceInfo;
	}


	public void gvgReadyFinish() {
		Collection<StrongHoldNode> all = strongHoldNodeDao.findAll();
		for (StrongHoldNode node : all) {
			// todo：xiehong 这里好像没有用了，需要去掉
			strongHoldNodeDao.save(node);
			sceneService.update(node, null);
		}
	}

	/**
	 * 获得所有参战者的伤亡数量
	 *
	 * @param army
	 * @return
	 */
	public int getBattleLossTotalCount(ArmyInfo army) {
		if (army.getFightContext() == null) {
			return 0;
		}
		var fightResult = army.getFightContext().getFightResult();
		int lossTotal = fightResult.getAttackerLostInfo().getValueAll(FightLostType.DEAD);
		lossTotal += fightResult.getAttackerLostInfo().getValueAll(FightLostType.BADLY);
		lossTotal += fightResult.getDefenderLostInfo().getValueAll(FightLostType.BADLY);
		lossTotal += fightResult.getDefenderLostInfo().getValueAll(FightLostType.BADLY);
		return lossTotal;
	}

	public List<StrongHoldNode> getStrongHoldNodeByType(BuildingType type) {
		List<StrongHoldNode> nodes = new ArrayList<>();
		Collection<StrongHoldNode> strongHoldNodes = strongHoldNodeDao.findAll();
		if (JavaUtils.bool(strongHoldNodes)) {
			for (StrongHoldNode node : strongHoldNodes) {
				if (node.getBuildingType() == type) {
					nodes.add(node);
				}
			}
		}

		return nodes;
	}

	public void addGvgRoleScoreInfo(Long failAllianceId, Long victoryAllianceId, GVGBattleRecordVo gvgBattleRecordVo) {
		try {
			Collection<RoleGVGBattle> roleGVGBattles = roleGVGBattleDao.findAll();
			if (JavaUtils.bool(roleGVGBattles)) {
				for (RoleGVGBattle roleGVGBattle : roleGVGBattles) {
					if (roleGVGBattle.getAllianceId() != null) {
						RoleGVGScoreInfoVo scoreInfoVo = new RoleGVGScoreInfoVo();
						scoreInfoVo.setRoleId(roleGVGBattle.getRoleId());
						scoreInfoVo.setHead(roleGVGBattle.getHead());
						scoreInfoVo.setName(roleGVGBattle.getName());
						scoreInfoVo.setHeadFrame(roleGVGBattle.getHeadFrame());
						scoreInfoVo.setSex(roleGVGBattle.getSex());

						Role role = roleDao.findById(roleGVGBattle.getRoleId());
						if (role != null) {
							scoreInfoVo.setServerId(role.getoServerId());
						}

						// 填充积分数据
						scoreInfoVo.setPoint(roleGVGBattle.getBattlePoint());
						scoreInfoVo.setAllianceContribute(roleGVGBattle.getContributeAllianceScore());
						scoreInfoVo.setKillNum(roleGVGBattle.getKillEnemyNum());


						Alliance alliance = allianceDao.findById(roleGVGBattle.getAllianceId());
						if (alliance != null) {
							scoreInfoVo.setAllianceId(alliance.getId());
							scoreInfoVo.setAllianceAliasName(alliance.getAliasName());
							scoreInfoVo.setAllianceName(alliance.getName());
						}

						if (victoryAllianceId != null && victoryAllianceId.equals(roleGVGBattle.getAllianceId())) {
							gvgBattleRecordVo.addVictoryRoleGvgInfo(scoreInfoVo);
						} else if (failAllianceId != null && failAllianceId.equals(roleGVGBattle.getAllianceId())) {
							gvgBattleRecordVo.addFailRoleGvgInfo(scoreInfoVo);
						}

						gvgBattleService.updateRoleGameRecord(scoreInfoVo, gvgBattleRecordVo.getMatchType());
					}
				}
			}
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("GVG战场 增加玩家积分榜信息异常", e);
		}
	}

	public void wrapperLoadGVG(Role role) {
		GcAllianceLoadGVG gcAllianceLoadGVG = new GcAllianceLoadGVG();
		Long allianceId = role.getAllianceId();
		if (!JavaUtils.bool(allianceId)) {
			role.send(gcAllianceLoadGVG);
			return;
		}
		Alliance alliance = allianceDao.findById(allianceId);
		if (alliance == null) {
			role.send(gcAllianceLoadGVG);
			return;
		}
		PsAllianceInfo psAllianceInfo = AllianceOutput.toInfo(alliance);
		if (psAllianceInfo != null) {
			GVGAllianceSignUpInfoVo gvgAllianceSignUpInfoVo = gvgBattleDataVoManager.findGvgAllianceSignUpInfoVos(allianceId);
			if (gvgAllianceSignUpInfoVo != null) {
				psAllianceInfo.setCurMember(allianceMemberManager.getMembers(allianceId).size());
				psAllianceInfo.setMaxMember(gvgAllianceSignUpInfoVo.getFormalMemberIds().size());
			}
			gcAllianceLoadGVG.setAlliance(psAllianceInfo);
		}
		role.send(gcAllianceLoadGVG);
	}


	public void wrapperGcAllianceMemberListGVG(Role role) {
		GcAllianceMemberList msg = new GcAllianceMemberList();
		Long allianceId = role.getAllianceId();
		if (!JavaUtils.bool(allianceId)) {
			role.send(msg);
			return;
		}
		Alliance alliance = allianceDao.findById(allianceId);
		if (alliance == null) {
			role.send(msg);
			return;
		}

		List<PsAllianceMemberInfo> psMembers = new ArrayList<>();
		List<AllianceMember> members = allianceMemberManager.getMembers(alliance.getPersistKey());
		for (AllianceMember m : members) {
			PsAllianceMemberInfo info = AllianceOutput.toInfo(m);
			RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(m.getPersistKey());
			if (roleGVGBattle != null) {
				info.setGvgPoint(roleGVGBattle.getBattlePoint());
			}
			psMembers.add(info);
		}

		msg.setMembers(psMembers);
		role.send(msg);
	}

	public void sendStrongHoldResultInfo(Long failAllianceId, Long victoryAllianceId) {
		if(Application.getServerType() == ServerType.TVT_BATTLE){
			return;
		}
		GvgRewardConfig gvgRewardConfig = configService.getConfig(GvgRewardConfig.class);
		Collection<RoleGVGBattle> roleGVGBattles = roleGVGBattleDao.findAll();
		if (JavaUtils.bool(roleGVGBattles)) {
			for (RoleGVGBattle roleGVGBattle : roleGVGBattles) {
				Role role = roleDao.findById(roleGVGBattle.getRoleId());
				if (role == null) {
					ErrorLogUtil.errorLog("sendStrongHoldResultInfo Role is null", "roleId",roleGVGBattle.getRoleId());
					continue;
				}

				// 失败
				boolean send = false;
				if (JavaUtils.bool(role.getAllianceId()) && JavaUtils.bool(failAllianceId) && role.getAllianceId().equals(failAllianceId)) {
					GcStrongHoldResultInfo resultInfo = new GcStrongHoldResultInfo();
					resultInfo.setVictory(false);
					GvgRewardConfig.GvgRewardMeta rewardMeta = gvgRewardConfig.getLoseAlliancePersonalScoreRankReward(roleGVGBattle.getBattlePoint());
					if (rewardMeta != null) {
						resultInfo.setDropId1(rewardMeta.getReward());
					} else {
						resultInfo.setDropId1("");
					}
					send = true;
					role.send(resultInfo);
				}

				// 成功
				if (JavaUtils.bool(role.getAllianceId()) && JavaUtils.bool(victoryAllianceId) && role.getAllianceId().equals(victoryAllianceId)) {
					GcStrongHoldResultInfo resultInfo = new GcStrongHoldResultInfo();
					resultInfo.setVictory(true);
					GvgRewardConfig.GvgRewardMeta rewardMeta = gvgRewardConfig.getWinAlliancePersonalScoreRankReward(roleGVGBattle.getBattlePoint());
					if (rewardMeta != null) {
						resultInfo.setDropId1(rewardMeta.getReward());
					} else {
						resultInfo.setDropId1("");
					}

					send = true;
					role.send(resultInfo);
				}

				if(!send){
					GcStrongHoldResultInfo resultInfo = new GcStrongHoldResultInfo();
					role.send(resultInfo);
				}
			}
		}
	}

	public void sendGVGAllianceMemberInfo(Role role) {
		if (!JavaUtils.bool(role.getAllianceId())) {
			return;
		}

		GcGVGAllianceMemberInfo msg = new GcGVGAllianceMemberInfo();
		List<AllianceMember> members = allianceMemberManager.getMembers(role.getAllianceId());
		for (AllianceMember member : members) {
			RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(member.getPersistKey());
			if (roleGVGBattle == null) {
				continue;
			}

			PsGvgAllianceMember memberInfo = new PsGvgAllianceMember();
			memberInfo.setRoleId(member.getPersistKey());
			memberInfo.setPoint(roleGVGBattle.getBattlePoint());
			memberInfo.setLastCalTime(roleGVGBattle.getLastCalTime());
			memberInfo.setSpeed(roleGVGBattle.getSpeed());
			msg.addToMembersInfo(memberInfo);
		}

		role.send(msg);
	}

	public Set<Point> getGvgBuildingRefreshNpcPoint(GvgBuildingMeta meta) {
		Set<Point> points = new HashSet<>();
		if (meta == null) {
			return points;
		}

		int x = meta.getBuildingPosition().getX();
		int y = meta.getBuildingPosition().getY();
		GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
		int size = gvgSettingConfig.getRefreshNpcWidth();
		// 右上
		for (int i = x; i <= x + size; i++) {
			for (int j = y; j <= y + size; j++) {
				points.add(Point.getInstance(i, j));
			}
		}

		// 右下
		int minY = y - size > 0 ? y - size : 0;
		for (int i = x; i <= x + size; i++) {
			for (int j = y; j >= minY; j--) {
				points.add(Point.getInstance(i, j));
			}
		}

		// 左上
		int minX = x - size > 0 ? x - size : 0;
		for (int i = x; i >= minX; i--) {
			for (int j = y; j <= y + size; j++) {
				points.add(Point.getInstance(i, j));
			}
		}

		// 左下
		for (int i = x; i >= minX; i--) {
			for (int j = y; j >= minY; j--) {
				points.add(Point.getInstance(i, j));
			}
		}

		return points;
	}

	public Map<Prop, Double> getAllStrongHoldBuff() {
		Map<Prop, Double> buffs = new HashMap<>();
		var metas = configService.getConfig(GvgBuildingConfig.class).getMetaMap().values();
		if (JavaUtils.bool(metas)) {
			for (GvgBuildingMeta meta : metas) {
				List<AttributeValue> gvgBuildingAttribute = gvgBuildingService.getGVGBuildingAttribute(meta);
				if (JavaUtils.bool(gvgBuildingAttribute)) {
					for (AttributeValue att : gvgBuildingAttribute) {
						buffs.put(att.getAttributeType(), 0d);
					}
				}
			}
		}

		return buffs;
	}

	/**
	 * 启动创建gvg物资点
	 */
	public void startRefreshGvgRes() {
		sceneNodeRefreshService.asyncRefreshGvgRes();
	}

	/**
	 * 更新玩家速度
	 */
	public void updateRoleSpeed(Role role) {
		RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(role.getId());
		if (roleGVGBattle == null || !JavaUtils.bool(role.getAllianceId())) {
			return;
		}

		int oldSpeed = roleGVGBattle.getSpeed();
		int speed = 0;
		Long allianceId = role.getAllianceId();
		Collection<StrongHoldNode> strongHoldNodes = strongHoldNodeDao.findAll();
		GvgBuildingConfig gvgBuildingConfig = configService.getConfig(GvgBuildingConfig.class);
		if (JavaUtils.bool(strongHoldNodes)) {
			for (StrongHoldNode strongHoldNode : strongHoldNodes) {
				if (!strongHoldNode.isOccupiedByAlliance(allianceId)) {
					continue;
				}

				if(isRoleInStrongHold(role.getRoleId(), strongHoldNode)){
					GvgBuildingMeta meta = gvgBuildingConfig.get(strongHoldNode.getMetaId());
					if (meta != null && meta.getBuildingPoints() > 0) {
						speed += meta.getBuildingPoints();
					}
				}
			}
		}

		// 设置初次开始时间
		if (speed > 0 && roleGVGBattle.getLastCalTime() == 0) {
			logger.info("GVG玩家个人积分速度更新, 玩家: {} 设置首次时间: {}", role.getId(), TimeUtil.getNow());
			roleGVGBattle.setLastCalTime(TimeUtil.getNow());
		}

		roleGVGBattle.setSpeed(speed);
		roleGVGBattleDao.save(roleGVGBattle);
		logger.info("GVG玩家个人积分速度更新, 玩家: {} 老速度: {} 新速度: {}", role.getId(), oldSpeed, roleGVGBattle.getSpeed());
		sendGVGBattleInfo(role);
	}


	/**
	 * 结算玩家积分:只结算自动产出部分
	 */
	public void recountRoleBattlePoint(Role role, boolean send) {
		RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(role.getId());
		if (roleGVGBattle == null || !JavaUtils.bool(role.getAllianceId())) {
			return;
		}

		// 当前时间
		long now = TimeUtil.getNow();
		int speed = roleGVGBattle.getSpeed();
		if (speed > 0) {
			// 上次结算时间
			long lastCalTime = roleGVGBattle.getLastCalTime();
			// 当前积累值
			int value = roleGVGBattle.getBattlePoint();
			// 本次理论应该增加的值
			int theoryValue = (int) ((now - lastCalTime) / TimeUtil.SECONDS_MILLIS * speed);

			gvgBattleScoreService.addRolePoint(role, theoryValue, AddPointType.OCCUPYING, false);
			roleGVGBattle.setLastCalTime(now);

			logger.info("GVG玩家个人积分结算, 玩家: {} 当前时间: {} 上次结算时间: {} 原值: {} 加值: {} 新值: {}", role.getId(), now, lastCalTime, value, theoryValue, roleGVGBattle.getBattlePoint());
		} else {
			// 代表开始计时
			if (roleGVGBattle.getLastCalTime() > 0) {
				roleGVGBattle.setLastCalTime(now);
				roleGVGBattleDao.save(roleGVGBattle);
				logger.info("GVG玩家个人积分结算, 玩家: {} 速度为0, 更新结算时间: {}", role.getId(), now);
			}
		}

		if (send) {
			sendGVGBattleInfo(role);
		}
	}

	public boolean isInBirthArea(Long targetRoleId, Point point){
		Role targetRole = roleDao.findById(targetRoleId);
		if(targetRole == null){
			logger.info("GVG攻打目标玩家, 目标玩家: {}为空", targetRoleId);
			return  false;
		}

		Long targetAllianceId = targetRole.getAllianceId();
		if(targetAllianceId == null){
			logger.info("GVG攻打目标玩家, 目标玩家: {}联盟为空", targetRoleId);
			return  false;
		}

		GVGBirthArea gvgBirthArea = gvgBattleDataVoManager.findGvgBirthArea(targetAllianceId);
		if (gvgBirthArea != null) {
			int mapHeight = worldService.getMapHeight(targetRole.getCurrentServerId());
			int mapWidth = worldService.getMapWidth(targetRole.getCurrentServerId());
			int minX = gvgBirthArea.getMinX() < 0 ? 0 : gvgBirthArea.getMinX();
			int maxX = gvgBirthArea.getMaxX() > mapWidth - 1 ? mapWidth - 1 : gvgBirthArea.getMaxX();
			int minY = gvgBirthArea.getMinY() < 0 ? 0 : gvgBirthArea.getMinY();
			int maxY = gvgBirthArea.getMaxY() > mapHeight - 1 ? mapHeight - 1 : gvgBirthArea.getMaxY();
			if (point.getX() >= minX && point.getX() <= maxX && point.getY() >= minY && point.getY() <= maxY) {
				return true;
			}else {
				return false;
			}
		}else {
			return false;
		}
	}

	/**
	 * 获取联盟占领建筑总数
	 * */
	public int getOccupyBuildCount(long allianceId){
		int count = 0;
		Collection<StrongHoldNode> strongHoldNodes = strongHoldNodeDao.findAll();
		if (JavaUtils.bool(strongHoldNodes)) {
			for (StrongHoldNode strongHoldNode : strongHoldNodes) {
				if (strongHoldNode.isOccupiedByAlliance(allianceId)) {
					count += 1;
				}
			}
		}

		return count;
	}

	/*
	* 获取联盟建筑总积分
	* */
	public int getBuildScore(AllianceBattlePoint allianceBattlePoint){
		// 先触发一次计算
		gvgBattleService.recountAlliancePoint(allianceBattlePoint.getPersistKey(), false);
		return allianceBattlePoint.getValue() - allianceBattlePoint.getArmyFactoryScore() - allianceBattlePoint.getKillEnemyScore()
				- allianceBattlePoint.getGatherScore() - allianceBattlePoint.getKillNpcScore();
	}

	/**
	 * 集结次数
	 * */
	public void addAllianceRallyCount(Long allianceId){
		AllianceBattlePoint allianceBattlePoint = allianceBattlePointDao.findById(allianceId);
		if(allianceBattlePoint != null){
			allianceBattlePoint.setTotalRallyCount(allianceBattlePoint.getTotalRallyCount() + 1);
			allianceBattlePointDao.save(allianceBattlePoint);
		}
	}

	/**
	 * 建筑集结次数
	 * */
	public void addStrongHoldAllianceRallyCount(StrongHoldNode node, Long allianceId){
		node.getRallyCounts().compute(allianceId, (k, v) -> v == null ? 1 : v + 1);
		strongHoldNodeDao.save(node);
	}

	/**
	 * 建筑进攻次数
	 * */
	public void addStrongHoldAllianceAttackCount(StrongHoldNode node, Long allianceId){
		node.getAttackCounts().compute(allianceId, (k, v) -> v == null ? 1 : v + 1);
		strongHoldNodeDao.save(node);
	}



	public StrongHoldNode getStrongHoldByMetaId(String strongHoldMetaId){
		Collection<StrongHoldNode> strongHoldNodes = strongHoldNodeDao.findAll();
		if (JavaUtils.bool(strongHoldNodes)) {
			for (StrongHoldNode strongHoldNode : strongHoldNodes) {
				if(strongHoldNode.getMetaId().equals(strongHoldMetaId)){
					return strongHoldNode;
				}
			}
		}

		return null;
	}


	public boolean canInteract(StrongHoldNode node) {
		// 建筑是否解锁
		GVGBattleFieldTimeLine gvgBattleFieldTimeLine = gvgBattleFieldTimeLineDao.find();
		if (gvgBattleFieldTimeLine == null) {
			ErrorLogUtil.errorLog("[GVG]canInteract can't find time data", "nodeId",node.getPersistKey());
			return false;
		}

		GvgBuildingConfig.GvgBuildingMeta gvgBuildingMeta = configService.getConfig(GvgBuildingConfig.class).get(node.getMetaId());
		if(gvgBuildingMeta == null){
			ErrorLogUtil.errorLog("[GVG]canInteract can't find gvgBuildingMeta", "nodeId",node.getPersistKey());
			return false;
		}

		if (gvgBuildingMeta.getUnlockTime() > 0) {
			long unlockTime = gvgBattleFieldTimeLine.getStartTime() + gvgBuildingMeta.getUnlockTime() * TimeUtil.SECONDS_MILLIS;
			long now = TimeUtil.getNow();
			if(now < unlockTime){
				logger.info("[GVG]canInteract unlock, node: {}, now: {}, unlockTime: {}", node.getPersistKey(), now, unlockTime);
				return false;
			}
		}

		// 乌巢是否在CD
		if(node.getBuildingType() == BuildingType.WuChao){
			if(wuChaoManager.inCD()){
				logger.info("[GVG]canInteract wu chao in cd, node: {}", node.getPersistKey());
				return false;
			}
		}

		return true;
	}


	public long supplyWagonBeginTime(){
		if(wuChaoManager.getSupplyWagon() != null){
			return wuChaoManager.getSupplyWagon().getBeginTime();
		} else {
			return 0;
		}
	}

	public long supplyWagonEndTime(){
		if(wuChaoManager.getSupplyWagon() != null){
			return wuChaoManager.getSupplyWagon().getEndTime();
		} else {
			return 0;
		}
	}

	public long wuChaoNextOpenTime(){
		return wuChaoManager.getNextOpenTime();
	}

	private PsGVGStrongHoldMarchInfo genArmySimpleInfo(StrongHoldNode strongHoldNode){
		var psSHMarchInfo = new PsGVGStrongHoldMarchInfo();

		var armyQueue = armyDao.getArmysByTargetPoint(Application.getServerId(), strongHoldNode.getPosition());
		if(armyQueue == null) {
			return null;
		}
		for(var armyInfo : armyQueue.toArray(new ArmyInfo[0])){
			if(armyInfo.getWorkType() == ArmyWorkType.RETURN){
				continue;	// 已经打道回府的不算
			}
			var armyOwnerId = armyInfo.getRoleId();
			var armyOwnerAlliance = allianceService.getAllianceByRoleId(armyOwnerId);

			var psSHArmyInfo = new PsNodeArmySimple();
			psSHArmyInfo.setId(armyInfo.getPersistKey());
			psSHArmyInfo.setAllianceId(armyOwnerAlliance.getId());
			psSHArmyInfo.setInGarrison(armyInfo.getWorkType() == ArmyWorkType.DEFENDING);

			psSHMarchInfo.addToArmyList(psSHArmyInfo);
		}

		psSHMarchInfo.setId(strongHoldNode.getPersistKey());
		psSHMarchInfo.setGarrisonAllianceId(strongHoldNode.getAllianceId());

		return psSHMarchInfo;
	}

	public void broadcastStrongHoldMatchInfo(StrongHoldNode strongHoldNode){
		var msg = new GcGvGStrongHoldMarchSimpleInfo();
		var psArmy = genArmySimpleInfo(strongHoldNode);
		if(psArmy == null) {
			return;
		}
		msg.addToMarchInfoList(psArmy);
		var roleList = roleDao.findAll();
		for(var role: roleList){
			role.send(msg);
		}
	}

	public void syncAllStrongArmyToRole(Role role){
		var nodeList = strongHoldNodeDao.findAll();
		var msg = new GcGvGStrongHoldMarchSimpleInfo();

		for(var node: nodeList){
			var psArmy = genArmySimpleInfo(node);
			if(psArmy != null) {
				msg.addToMarchInfoList(psArmy);
			}
		}

		role.send(msg);
	}

	/**
	 * @param metaId
	 * @return
	 */
	public int getStrongHoldSize(String metaId) {
		if (!JavaUtils.bool(metaId)) {
			return 1;
		}
		GvgBuildingMeta gvgBuildingMeta = configService.getConfig(GvgBuildingConfig.class).get(metaId);
		if (gvgBuildingMeta == null) {
			return 1;
		}
		int buildingRange = gvgBuildingMeta.getBuildingRange();
		return buildingRange <= 0 ? 1 : buildingRange;
	}

	private void broadCastOccupyBuilding(StrongHoldNode node) {
		if (node == null) {
			return;
		}
		try {
			Alliance alliance = allianceService.getAllianceById(node.getAllianceId());
			if (alliance == null) {
				return;
			}
			// 跑马灯
			GvgBuildingMeta gvgBuildingMeta = configService.getConfig(GvgBuildingConfig.class).get(node.getMetaId());
			if (gvgBuildingMeta != null && JavaUtils.bool(gvgBuildingMeta.getBuildingOccupyBroadcast())) {
				noticeServiceImpl.marqueeGVGNotice(gvgBuildingMeta.getBuildingOccupyBroadcast(), alliance.getName());
			}
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("broadCastOccupyBuilding error", e);
		}
	}

	/**
	 * 霹雳车打乌巢
	 */
	public void piliCheAttackGuandu() {
		List<StrongHoldNode> piliCheList = strongHoldNodeDao.findBuildingByType(BuildingType.PiLiChe);
		if (!JavaUtils.bool(piliCheList)) {
			return;
		}
		StrongHoldNode piliCheNode = piliCheList.get(0);

		List<StrongHoldNode> guanduList = strongHoldNodeDao.findBuildingByType(BuildingType.GuanDu);
		StrongHoldNode guanduNode = guanduList.get(0);
		double damageRate = configService.getConfig(GvgSettingConfig.class).getGvgPilicheDamage();
		double rate = 1 - damageRate /100;

		Map<Long, Integer> mailParam = new HashMap<>();

		var garrisonArmies = getNodeGarrisonArmies(guanduNode);
		for (ArmyInfo armyInfo : garrisonArmies) {
			if (armyInfo == null) {
				continue;
			}
			Map<String, ArmySoldier> armySoldiers = armyInfo.getArmySoldiers();
			if (!JavaUtils.bool(armySoldiers)) {
				continue;
			}
			Role role = roleDao.findById(armyInfo.getRoleId());
			if (role == null) {
				continue;
			}

			HashMap<String, Integer> woundMap = new HashMap<>();
			for (Map.Entry<String, ArmySoldier> soldierEntry : armySoldiers.entrySet()) {
				String armyId = soldierEntry.getKey();
				ArmySoldier value = soldierEntry.getValue();
				int count = value.getCount();
				int count_fixed = (int) Math.ceil(count * rate);
				int dec = Math.max(0, count - count_fixed);
				if (dec <= 0) {
					continue;
				}
				value.setCount(count_fixed);
				woundMap.put(armyId, dec + woundMap.getOrDefault(armyId, 0));

				mailParam.put(role.getId(), dec + mailParam.getOrDefault(role.getId(), 0));
			}

			if (!JavaUtils.bool(woundMap)) {
				continue;
			}

			soldierService.addWounded(role, woundMap, SoldierUpdateReasonType.PILICHE, Strings.EMPTY, true);
			armyDao.save(armyInfo);
		}

		List<AbstractEmail> mailList = new ArrayList<>();
		Point position = piliCheNode.getPosition();
		for (Map.Entry<Long, Integer> entry : mailParam.entrySet()) {
			List<String> mailParams = new ArrayList<>();
			String pointParam = mailCreator.getPointParam(Application.getServerId(), position.getX(), position.getY());
			mailParams.add(pointParam);
			mailParams.add(String.valueOf(entry.getValue()));
			SystemEmail mail = mailCreator.createSystemMail(entry.getKey(), Application.getServerId(), EmailConstants.GVG_PILICHE_ATTACK_GUANDU,
					null, 1, null, mailParams, false, SystemEmailType.PILICHE_ATTACK,
					"", null);
			mailList.add(mail);
		}
		if (JavaUtils.bool(mailList)) {
			srvDpd.getMailSender().sendBatchMail(mailList);
		}
	}

	public ArmyInfo[] getNodeArmies(SceneNode node){
		var armyQueue = armyDao.getArmysByTargetPoint(Application.getServerId(), node.getPosition());
		return armyQueue != null? armyQueue.toArray(new ArmyInfo[0]) : new ArmyInfo[0];
	}

	// 正在驻防、采集、援护的部队
	public ArrayList<ArmyInfo> getNodeGarrisonArmies(SceneNode node) {
		var armies = getNodeArmies(node);
		var retList = new ArrayList<ArmyInfo>();
		for (var army : armies) {
			if (army.getWorkType().equals(ArmyWorkType.DEFENDING)
					|| army.getWorkType().equals(ArmyWorkType.GATHERING)
					|| army.getWorkType().equals(ArmyWorkType.REINFORCING)) {
				retList.add(army);
			}
		}
		return retList;
	}

	// 建筑的友方部队，包括已经驻防和驻防行军中
	public ArrayList<ArmyInfo> getNodeFriendArmies(StrongHoldNode node){
		var armies = getNodeArmies(node);
		var retList = new ArrayList<ArmyInfo>();
		for(var army: armies){
			var role = roleManager.getRole(army.getRoleId());
			if(role == null){
				continue;
			}

			if(Objects.equals(role.getAllianceId(), node.getAllianceId())) {
				retList.add(army);
			}
		}
		return retList;
	}

	public ArrayList<ArmyInfo> getNodeEnemyArmies(StrongHoldNode node){
		var retList = new ArrayList<ArmyInfo>();
		if(node.getAllianceId() <= 0){// 没有归属的node没有敌人
			return retList;
		}

		var armies = getNodeArmies(node);
		for(var army: armies){
			if(army.getWorkType() == ArmyWorkType.RETURN){
				continue;
			}

			var role = roleManager.getRole(army.getRoleId());
			if(role == null){
				continue;
			}

			if(!Objects.equals(role.getAllianceId(), node.getAllianceId())) {
				retList.add(army);
			}
		}
		return retList;
	}

	public int garrisonCount(StrongHoldNode node){
		var armies = getNodeArmies(node);

		int count = 0;
		for(var army: armies){
			if(army.getWorkType().equals(ArmyWorkType.DEFENDING)) {
				count++;
			}
		}

		return count;
	}

	public boolean noGarrison(StrongHoldNode node){
		var armies = getNodeArmies(node);

		for(var army: armies){
			if(army.getWorkType().equals(ArmyWorkType.DEFENDING)) {
				return false;
			}
		}

		return true;
	}

	public boolean isRoleInStrongHold(long roleId, StrongHoldNode node){
		var armies = getNodeArmies(node);

		for(var army: armies){
			if(army.getWorkType().equals(ArmyWorkType.DEFENDING) && army.getRoleId().equals(roleId)) {
				return true;
			}
		}

		return false;
	}

	public boolean isArmyInStrongHold(long armyId, StrongHoldNode node){
		var armies = getNodeArmies(node);

		for(var army: armies){
			if(army.getWorkType().equals(ArmyWorkType.DEFENDING) && army.getPersistKey().equals(armyId)) {
				return true;
			}
		}

		return false;
	}

	// army离开建筑，由以下两种情况触发
	// 【1】onRecallArmyDone(由returnArmy触发)
	// 【2】onDeletedArmy(takebackArmy由触发)
	public void onArmyLeftStrongHold(ArmyInfo army, ArmyWorkType oldWorkType) {
		var node = army.getTargetNode();
		if(!(node instanceof StrongHoldNode)){
			ErrorLogUtil.errorLog("[GVG]onArmyLeftStrongHold node type error", "armyId",army.getPersistKey(),
					"roleId",army.getRoleId(), "workType",army.getWorkType());
			return;
		}
		var strongHoldNode = (StrongHoldNode) node;

		var role = roleManager.getRole(army.getRoleId());
		if(role == null){
			ErrorLogUtil.errorLog("[GVG]onArmyLeftStrongHold can't find role","armyId",army.getPersistKey(),
					"roleId",army.getRoleId(), "workType",army.getWorkType());
			return;
		}

		// 删除敌对关系
		armyDelEnemy(army, oldWorkType, strongHoldNode, role);

		// 更新驻防队长
		tryAutoSetLeader(strongHoldNode);

		broadcastStrongHoldDetailInfoUpdate(strongHoldNode);

		broadcastStrongHoldMatchInfo(strongHoldNode);

		// 更新玩家速度，这里不需要更新联盟的速度
		updateRolePointSpeed(List.of(role.getId()));
	}

	private void armyDelEnemy(ArmyInfo army, ArmyWorkType oldWorkType, StrongHoldNode strongHoldNode, Role role) {
		if(oldWorkType == ArmyWorkType.DEFENDING){
			// 当我是驻防状态的时候，删除目标相关的其他部队对我的影响
			var nodeArmies = getNodeArmies(strongHoldNode);
			for(var setOutArmy: nodeArmies) {
				if(setOutArmy.getWorkType() != ArmyWorkType.SETOUT) {
					continue;
				}
				var armyAllianceId = armyService.getArmyAlliance(setOutArmy);
				if(!Objects.equals(armyAllianceId, role.getAllianceId())) {
					// 如果不是一个阵营的部队，删除敌对关系
					armyManager.removeEnemy(role, setOutArmy);
				}
			}
			warService.broadcastUpdate(strongHoldNode, PsAllianceWarInfoUpdateReason.STATION_RECALL);
		} else if(oldWorkType == ArmyWorkType.SETOUT) {
			// 当我是出征状态的时候，删除我对目标相关的其他部队的影响
			var nodeArmies = getNodeArmies(strongHoldNode);
			for(var garrisonArmy: nodeArmies) {
				if(garrisonArmy.getWorkType() != ArmyWorkType.DEFENDING) {
					continue;
				}
				var garrisonRole = roleManager.getRole(garrisonArmy.getRoleId());
				if(!Objects.equals(garrisonRole.getAllianceId(), role.getAllianceId())) {
					// 如果不是一个阵营的部队，删除敌对关系
					armyManager.removeEnemy(garrisonRole, army);
				}
			}
		}
	}

	public boolean commonDataCheck(Role armyOwner, StrongHoldNode targetNode){
		// gvg战斗服停了，战斗结束了
		if (!Application.getBean(LoginServiceImpl.class).isLoginSwitch()) {
			ErrorLogUtil.errorLog("[GVG]commonDataCheck status error");
			return false;
		}

		// 入场阶段，不允许行军
		if (!isMoveGvgActivityStage(TimeUtil.getNow())) {
			ErrorLogUtil.errorLog("[GVG]commonDataCheck stage error");
			return false;
		}

		if(!canInteract(targetNode)){
			ErrorLogUtil.errorLog("[GVG]commonDataCheck can't interact");
			return false;
		}

		if (strongHoldNodeDao.findById(targetNode.getPersistKey()) == null) {
			ErrorLogUtil.errorLog("[GVG]commonDataCheck can't find node data");
			return false;
		}

		if (allianceService.getAllianceByRole(armyOwner) == null) {
			ErrorLogUtil.errorLog("[GVG]commonDataCheck can't find alliance data");
			return false;
		}

		return true;
	}

	// GVG朝着StrongHold移动的部队，出发前的检查
	public boolean setOutToStrongHoldCheck(Role role, SceneNode targetNode, ArmySetoutParam param){
		if(!(targetNode instanceof StrongHoldNode)){
			ErrorLogUtil.errorLog("[GVG]setOutToStrongHoldCheck targetNode instanceof StrongHoldNode",
					"roleId",role.getId(), "targetNode",targetNode.getPersistKey(), "armyType",param.getArmyType());
			return false;
		}

		StrongHoldNode strongHoldNode = (StrongHoldNode) targetNode;

		if(!commonDataCheck(role, strongHoldNode)){
			ErrorLogUtil.errorLog("[GVG]setOutToStrongHoldCheck commonDataCheck fail",
					"roleId",role.getId(), "targetNode",targetNode.getPersistKey(), "armyType",param.getArmyType());
			return false;
		}

		if(param.getArmyType() == ArmyType.RALLY_STRONGHOLD) {
			// 相同玩家不能有其他正在集结或者集结行军中的部队
			var armies = getNodeArmies(strongHoldNode);
			for(var nodeArmy: armies){
				if(nodeArmy.getRallyContext() == null) {    // 集结中+集结行军中（ArmyWorkType.RALLYING只表示集结中）
					continue;
				}

				if(Objects.equals(nodeArmy.getRoleId(), role.getId())){
					ErrorLogUtil.errorLog("[GVG]setOutToStrongHoldCheck already making rally",
							"roleId",role.getId(), "targetNode",targetNode.getPersistKey(), "armyType",param.getArmyType());
					return false;
				}
			}
		}

		return true;
	}

	public void setOutToStrongHoldStart(ArmyInfo army){
		armyService.addArmyToAoi(army, army.getWorkType() == ArmyWorkType.SETOUT);
		var targetNode = army.getTargetNode();
		if(!(targetNode instanceof StrongHoldNode strongHoldNode)){
			ErrorLogUtil.errorLog("[GVG]setOutToStrongHoldStart node type error", "armyId",army.getPersistKey());
			return;
		}

		var role = army.getOwner();
		if(role == null){
			ErrorLogUtil.errorLog("[GVG]setOutToStrongHoldStart owner is null","armyId", army.getPersistKey());
			return;
		}

		if(!Objects.equals(role.getAllianceId(), strongHoldNode.getAllianceId())) {
			// 出发的时候是攻击StrongHold
			warService.createWar(army);
		}
		logger.info("[GVG]setOutToStrongHoldStart begin, army: {}, army alliance: {}, target alliance{}, role: {}", army.getPersistKey(), role.getAllianceId(), strongHoldNode.getAllianceId(), role.getId());

		broadcastStrongHoldMatchInfo(strongHoldNode);

		broadcastStrongHoldDetailInfoUpdate(strongHoldNode);
	}

	// 单部队攻击或者集结攻击GVG建筑，到达处理
	// 会有三种结果：
	// 【1】基础检查不通过，部队直接返回，返回false
	// 【2】发现是空建筑、友方、或者敌方无部队驻防，则直接转化为驻防部队，返回false
	// 【3】发现是敌方有驻防，则返回true，触发战斗
	public boolean attackerArriveStrongHold(ArmyInfo army){
		warService.warDel(army);

		var role = roleManager.getRole(army.getRoleId());
		if(role == null){
			ErrorLogUtil.errorLog("[GVG]attackerArriveStrongHold, role is null", "armyId",army.getPersistKey());
			return false;
		}

		var targetNode = army.getTargetNode();
		if(!(targetNode instanceof StrongHoldNode strongHoldNode)){
			ErrorLogUtil.errorLog("[GVG]attackerArriveStrongHold, target type error", "armyId",army.getPersistKey());
			armyManager.returnArmy(army);
			return false;
		}

        if(!commonDataCheck(role, strongHoldNode)){
			ErrorLogUtil.errorLog("[GVG]attackerArriveStrongHold commonDataCheck fail", "armyId",army.getPersistKey());
			armyManager.returnArmy(army);
			return false;
		}

		// 建筑无归属或者已经被友方占领
		if (strongHoldNode.getAllianceId() <= 0													// 无归属
				|| Objects.equals(role.getAllianceId(), strongHoldNode.getAllianceId())){    	// 友方驻防
			logger.info("[GVG]attackerArriveStrongHold occupied by our own forces, army: {}, alliance: {}, role: {}, workType: {}, armyType: {}",
					army.getPersistKey(), role.getAllianceId(), role.getId(), army.getWorkType(), army.getArmyType());
			army.setArmyType(ArmyType.GARRISON_STRONGHOLD);
			army.setWorkType(ArmyWorkType.SETOUT);

			return false;
		}

		// 建筑被敌方占领，但是没有驻防部队，不需要战斗，清理当前建筑的相关占领方状态即可
		if(noGarrison(strongHoldNode)){
			logger.info("[GVG]attackerArriveStrongHold occupied by other forces, but no garrison, army: {}, alliance: {}, role: {}, workType: {}, armyType: {}",
					army.getPersistKey(), role.getAllianceId(), role.getId(), army.getWorkType(), army.getArmyType());
			army.setArmyType(ArmyType.GARRISON_STRONGHOLD);
			army.setWorkType(ArmyWorkType.SETOUT);
			return false;
		} else {
			logger.info("[GVG]attackerArriveStrongHold occupied by other forces, have garrison, army: {}, alliance: {}, role: {}, workType: {}, armyType: {}",
					army.getPersistKey(), role.getAllianceId(), role.getId(), army.getWorkType(), army.getArmyType());
		}

		return true;
	}

	// 执行攻击StrongHold，战斗结束后
	public void onStrongHoldBattleEnd(ArmyInfo army){
		// 不管赢没赢，只要进攻了，就加杀兵积分
		gvgBattleScoreService.onGVGFightEnd(army);

		if(!army.getFightContext().isWin()) { // 没打赢直接回城
			logger.info("[GVG]onStrongHoldBattleEnd, not win army: {}", army.getPersistKey());
			return;
		}


		SceneNode targetNode = armyManager.getArmyTargetNode(army);
		if(!(targetNode instanceof StrongHoldNode)) {
			logger.info("[GVG]onStrongHoldBattleEnd, target type error: {}", army.getPersistKey());
			armyManager.returnArmy(army);
			return;
		}
		var strongHoldNode = (StrongHoldNode)targetNode;

		var role = roleManager.getRole(army.getRoleId());
		if(role == null) {
			ErrorLogUtil.errorLog("[GVG]onStrongHoldBattleEnd, role is null", "armyId",army.getPersistKey());
			armyManager.returnArmy(army);
			return;
		}

		if(!commonDataCheck(role, strongHoldNode)){
			ErrorLogUtil.errorLog("[GVG]onStrongHoldBattleEnd, commonDataCheck fail", "armyId",army.getPersistKey());
			armyManager.returnArmy(army);
			return;
		}

		// 将部队类型改成驻防，这样可以走到：GVGStrongHoldGarrisonOperation.armyArrive
		army.setArmyType(ArmyType.GARRISON_STRONGHOLD);
		army.setWorkType(ArmyWorkType.SETOUT);

		logger.info("[GVG]onStrongHoldBattleEnd attacker win: army: {}, role: {},node: {}({})", army.getPersistKey(), army.getRoleId(), strongHoldNode.getMetaId(), strongHoldNode.getAllianceId());

		// 让战败方的所有相关部队都退回
		returnAllGarrison(strongHoldNode);

		// 同步建筑属性变化
		sceneService.update(strongHoldNode, null);

		// 同步给客户端战斗服信息
		pushInformationBothSides(0, 0L);

		// 同步
		broadcastStrongHoldDetailInfoUpdate(strongHoldNode);

		// 同步
		broadcastStrongHoldMatchInfo(strongHoldNode);
	}


	private void updateAllianceWar(StrongHoldNode strongHoldNode, Long oldAllianceId){
		var allianceId = strongHoldNode.getAllianceId();
		warService.onDefenceChange(strongHoldNode, oldAllianceId, allianceId);
	}

	private void removeAllianceBuff(long allianceId, StrongHoldNode node) {
		if (allianceId == 0 || node == null) {
			return;
		}
		try {
			List<String> gvgBuildingBuffList = getGVGBuildingBuffList(node);
			if (!JavaUtils.bool(gvgBuildingBuffList)) {
				return;
			}
			List<AllianceMember> members = allianceMemberManager.getMembers(allianceId);
			for (AllianceMember member : members) {
				// 更新属性
				Role memberRole = roleManager.getRole(member.getPersistKey());
				if (memberRole == null) {
					continue;
				}
				for (String buffId : gvgBuildingBuffList) {
					if (!JavaUtils.bool(buffId)) {
						continue;
					}
					GcBuffRemove gcBuffRemove = new GcBuffRemove();
					gcBuffRemove.setBuffId(buffId);
					memberRole.send(gcBuffRemove);
				}
			}
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("removeAllianceBuff error",e, "metaId",node.getMetaId(), "allianceId",allianceId);
		}
	}

	private void addAllianceBuff(StrongHoldNode node) {
		try {
			if (node == null || node.getAllianceId() == 0) {
				return;
			}
			long allianceId = node.getAllianceId();
			List<String> gvgBuildingBuffList = getGVGBuildingBuffList(node);
			if (!JavaUtils.bool(gvgBuildingBuffList)) {
				return;
			}

			GVGBattleFieldTimeLine gvgBattleFieldTimeLine = gvgBattleFieldTimeLineDao.find();
			long duration = gvgBattleFieldTimeLine != null ? gvgBattleFieldTimeLine.getEndTime() - TimeUtil.getNow() : TimeUtil.DAY_MILLIS;

			List<AllianceMember> members = allianceMemberManager.getMembers(allianceId);
			for (String buffId : gvgBuildingBuffList) {

				PsBuffInfo psBffInfo = createBuildingPsBffInfo(buffId, node.getOccupyTime(), duration);
				if (psBffInfo == null) {
					continue;
				}
				GcBuffUpdate buffUpdate = new GcBuffUpdate();
				buffUpdate.setBuff(psBffInfo);
				for (AllianceMember member : members) {
					// 更新属性
					Role memberRole = roleManager.getRole(member.getPersistKey());
					if (memberRole == null) {
						continue;
					}
					memberRole.send(buffUpdate);
				}
			}
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("addAllianceBuff error", e,"metaId",node.getMetaId(),
					"allianceId",node.getAllianceId());
		}
	}

	public List<PsBuffInfo> getRoleValidBuff(Role role) {
		List<PsBuffInfo> list = new ArrayList<>();
		if (role == null || role.getAllianceId() == null || role.getAllianceId() == 0) {
			return list;
		}

		BuffConfig buffConfig = configService.getConfig(BuffConfig.class);
		List<StrongHoldNode> allianceOccupyBuildings = getAllianceOccupyBuildings(role.getAllianceId());

		GVGBattleFieldTimeLine gvgBattleFieldTimeLine = gvgBattleFieldTimeLineDao.find();
		long duration = gvgBattleFieldTimeLine != null ? gvgBattleFieldTimeLine.getEndTime() - TimeUtil.getNow() : TimeUtil.DAY_MILLIS;

		for (StrongHoldNode building : allianceOccupyBuildings) {
			for (String buffId : getGVGBuildingBuffList(building)) {
				BuffConfig.BuffMeta buffMeta = buffConfig.getBuffMeta(buffId);
				if (buffMeta == null) {
					continue;
				}
				PsBuffInfo info = createBuildingPsBffInfo(buffMeta.getId(), building.getOccupyTime(), duration);
				if (buffId == null) {
					continue;
				}
				list.add(info);
			}
		}
		return list;
	}

	private PsBuffInfo createBuildingPsBffInfo(String buffId, long startTime, long duration) {
		if (!JavaUtils.bool(buffId)) {
			return null;
		}
		PsBuffInfo info = new PsBuffInfo();
		info.setBuffId(buffId);
		info.setStartTime(startTime);
		info.setDuration(duration < 0 ? 0L : duration);
		info.setMetaId(buffId);// 客户端需要赋值这个字段
		return info;
	}

	private List<String> getGVGBuildingBuffList(StrongHoldNode building) {
		GvgBuildingConfig buildingConfig = configService.getConfig(GvgBuildingConfig.class);

		GvgBuildingMeta gvgBuildingMeta = buildingConfig.get(building.getMetaId());
		if (gvgBuildingMeta == null || !JavaUtils.bool(gvgBuildingMeta.getBuildingBuffIdList())) {
			return new ArrayList<>();
		}
		return gvgBuildingMeta.getBuildingBuffIdList();
	}

	public boolean gvgJoinCommonCheck(Role role, SceneNode targetNode, ArmyInfo rallyArmy, AllianceWar war){
		// gvg战斗服停了，战斗结束了
		if (!Application.getBean(LoginServiceImpl.class).isLoginSwitch()) {
			ErrorLogUtil.errorLog("[GVG]gvgJoinCommonCheck status error",
					"roleId",role.getId(), "targetNode",targetNode.getPersistKey(), "rallyArmyId",rallyArmy.getPersistKey(),
					"rallyArmyType",rallyArmy.getArmyType(), "rallyWorkType",rallyArmy.getWorkType());
			return false;
		}

		// 入场阶段，不允许行军
		if (!isMoveGvgActivityStage(TimeUtil.getNow())){
			ErrorLogUtil.errorLog("[GVG]gvgJoinCommonCheck stage error",
					"roleId",role.getId(), "targetNode",targetNode.getPersistKey(), "rallyArmyId",rallyArmy.getPersistKey(),
					"rallyArmyType",rallyArmy.getArmyType(), "rallyWorkType",rallyArmy.getWorkType());
			return false;
		}

		/* ---------------数据完整检查BEGIN--------------- */
		Alliance alliance = allianceService.getAllianceByRoleId(role.getId());
		if (alliance == null) {
			ErrorLogUtil.errorLog("[GVG]gvgJoinCommonCheck alliance null",
					"roleId",role.getId(), "targetNode",targetNode.getPersistKey(), "rallyArmyId",rallyArmy.getPersistKey(),
					"rallyArmyType",rallyArmy.getArmyType(), "rallyWorkType",rallyArmy.getWorkType(),
					"allianceId",role.getAllianceId());
			return false;
		}

		var rallyTargetNode = armyManager.getArmyTargetNode(rallyArmy);
		if(rallyTargetNode == null){
			ErrorLogUtil.errorLog("[GVG]gvgJoinCommonCheck rallyTargetNode null",
					"roleId",role.getId(), "targetNode",targetNode.getPersistKey(), "rallyArmyId",rallyArmy.getPersistKey(),
					"rallyArmyType",rallyArmy.getArmyType(), "rallyWorkType",rallyArmy.getWorkType(),
					"allianceId",role.getAllianceId(), "targetNodeId",rallyArmy.getTargetNodeId());
			return false;
		}
		/* ---------------数据完整检查END--------------- */

		// 参加集结 检测是否是同一个联盟
		if (!war.getAttackerAllianceId().equals(alliance.getPersistKey())) {
			ErrorLogUtil.errorLog("[GVG]gvgJoinCommonCheck rallyTargetNode null",
					"roleId",role.getId(), "targetNode",targetNode.getPersistKey(), "rallyArmyId",rallyArmy.getPersistKey(),
					"rallyArmyType",rallyArmy.getArmyType(), "rallyWorkType",rallyArmy.getWorkType(),
					"allianceId",role.getAllianceId(), "targetNode",rallyArmy.getTargetNodeId(),
					"attackerAllianceId",war.getAttackerAllianceId());
			return false;
		}

		// 集结是否已经出发，集结出发的时候会从RALLYING状态改成SETOUT状（参与部队在到达的时候会从SETOUT改成RALLYING）
		if (rallyArmy.getWorkType() != ArmyWorkType.RALLYING) {
			ErrorLogUtil.errorLog("[GVG]gvgJoinCommonCheck workType error",
					"roleId",role.getId(), "targetNode",targetNode.getPersistKey(), "rallyArmy",rallyArmy.getPersistKey(),
					"rallyArmyType",rallyArmy.getArmyType(), "rallyWorkType",rallyArmy.getWorkType(),
					"allianceId",role.getAllianceId(), "targetNodeId",rallyArmy.getTargetNodeId(),
					"attackerAllianceId",war.getAttackerAllianceId());
			return false;
		}

		// 自己发起的集结不可以参加
		if (rallyArmy.getRoleId().equals(role.getId())) {
			ErrorLogUtil.errorLog("[GVG]gvgJoinCommonCheck leader is self",
					"roleId",role.getId(), "targetNode",targetNode.getPersistKey(),
					"rallyArmyId",rallyArmy.getPersistKey(), "rallyArmyType",rallyArmy.getArmyType(),
					"rallyWorkType",rallyArmy.getWorkType(), "allianceId",role.getAllianceId(),
					"targetNodeId",rallyArmy.getTargetNodeId(), "attackerAllianceId",war.getAttackerAllianceId(),
					"leaderRoleId",rallyArmy.getRoleId());
			return false;
		}

		// 不是向发起集结者的行军
		if (targetNode.equals(rallyTargetNode)) {
			ErrorLogUtil.errorLog("[GVG]gvgJoinCommonCheck rally target is target",
					"roleId",role.getId(), "targetNode",targetNode.getPersistKey(),
					"rallyArmyId",rallyArmy.getPersistKey(), "rallyArmyType",rallyArmy.getArmyType(),
					"rallyWorkType",rallyArmy.getWorkType(), "allianceId",role.getAllianceId(),
					"targetNodeId",rallyArmy.getTargetNodeId(), "attackerAllianceId",war.getAttackerAllianceId(),
					"leaderRoleId",rallyArmy.getRoleId());
			return false;
		}

		// 是集结打我的行军（A集结B， B加入A的联盟，B不可以加入这个集结）
		if (rallyArmy.getTargetNodeId().equals(role.getPersistKey())) {
			ErrorLogUtil.errorLog("[GVG]gvgJoinCommonCheck rally target is self",
					"roleId",role.getId(), "targetNode",targetNode.getPersistKey(),
					"rallyArmyId",rallyArmy.getPersistKey(), "rallyArmyType",rallyArmy.getArmyType(),
					"rallyWorkType",rallyArmy.getWorkType(), "allianceId",role.getAllianceId(),
					"targetNodeId",rallyArmy.getTargetNodeId(), "attackerAllianceId",war.getAttackerAllianceId(),
					"leaderRoleId",rallyArmy.getRoleId());
			return false;
		}

		// 集结部队是否超过上限
		if (rallyArmy.getRallyContext().getJoinArmyIdList().size() >= rallyArmy.getRallyContext().getMaxRallyNum()) {
			ErrorLogUtil.errorLog("[GVG]gvgJoinCommonCheck exceed max member size",
					"roleId",role.getId(), "targetNode",targetNode.getPersistKey(),
					"rallyArmyId",rallyArmy.getPersistKey(), "rallyArmyType",rallyArmy.getArmyType(),
					"rallyWorkType",rallyArmy.getWorkType(), "allianceId",role.getAllianceId(),
					"targetNodeId",rallyArmy.getTargetNodeId(), "attackerAllianceId",war.getAttackerAllianceId(),
					"leaderRoleId",rallyArmy.getRoleId(),"rallyArmySize",rallyArmy.getRallyContext().getJoinArmyIdList().size(),
					"maxRallyNum",rallyArmy.getRallyContext().getMaxRallyNum());
			return false;
		}

		return true;
	}

	private boolean isLeader(Role role, StrongHoldNode node){
		var leaderId = node.leaderArmyId();
		if(leaderId <= 0) {
			logger.info("[GVG]isLeader, leaderId is null, role: {}, nodeId: {}, leaderId: {}", role.getId(), node.getPersistKey(), leaderId);
			return false;
		}

		var leaderArmy = armyManager.findById(leaderId);
		if(leaderArmy == null){
			logger.info("[GVG]isLeader, leaderArmy is null, role: {}, nodeId: {}, leaderId: {}", role.getId(), node.getPersistKey(), leaderId);
			return false;
		}

		return Objects.equals(leaderArmy.getRoleId(), role.getRoleId());
	}

	private boolean isAllianceOfficer(Role role, Long allianceId){
		var allianceMember = allianceMemberManager.getMember(role.getId());
		if(allianceMember == null) {
			logger.info("[GVG]isAllianceOfficer allianceMember null, role: {}, allianceId: {}", role.getId(), allianceId);
			return false;
		}

		if(!allianceMember.getAllianceId().equals(allianceId)){
			logger.info("[GVG]isAllianceOfficer not in alliance, role: {}, allianceId: {}, roleAllianceId: {}",
					role.getId(), allianceId, allianceMember.getAllianceId());
			return false;
		}

		return allianceMember.getRank() >= AllianceConstants.RANK_4;
	}

	public boolean canAccessGarrison(Role applicantRole, StrongHoldNode node){
		if(isLeader(applicantRole, node)){
			return true;
		}

		if(isAllianceOfficer(applicantRole, node.getAllianceId())){
			return true;
		}

		return false;
	}

	public ArmyInfo armyInGarrison(StrongHoldNode strongHoldNode, long armyId){
		var garrisonArmies = getNodeGarrisonArmies(strongHoldNode);
		ArmyInfo findArmy = null;
		for(var garrisonArmy: garrisonArmies){
			if(garrisonArmy.getPersistKey().equals(armyId)){
				findArmy = garrisonArmy;
			}
		}

		return findArmy;
	}

	public boolean changeLeader(Role applicantRole, long strongHoldId, long newLeaderArmyId) {
		logger.info("[GVG]changeLeader, nodeId: {}, newLeaderArmyId: {}, role: {}", strongHoldId, newLeaderArmyId, applicantRole.getId());

		var strongHoldNode = strongHoldNodeDao.findById(strongHoldId);
		if(strongHoldNode == null) {
			ErrorLogUtil.errorLog("[GVG]changeLeader, can't find strongHold", "strongHoldId",strongHoldId);
			return false;
		}

		ArmyInfo newLeaderArmy = armyInGarrison(strongHoldNode, newLeaderArmyId);
		if(newLeaderArmy == null) {
			ErrorLogUtil.errorLog("[GVG]changeLeader, army not in garrison",
					"newLeaderArmyId",newLeaderArmyId, "strongHoldId",strongHoldId, "roleId",applicantRole.getId());
			return false;
		}

		if(!canAccessGarrison(applicantRole, strongHoldNode)){
			ErrorLogUtil.errorLog("[GVG]changeLeader, no access",
					"newLeaderArmyId",newLeaderArmyId, "strongHoldId",strongHoldId, "roleId",applicantRole.getId(),
					"oldLeaderArmyId",strongHoldNode.leaderArmyId());
			return false;
		}

		return strongHoldNode.setLeader(newLeaderArmy);
	}

	public long getPiliCheNextAttackTime(StrongHoldNode pilieCheNode) {
		if (pilieCheNode.getOccupyState() != StrongHoldNode.SHOccupyState.Occupied) {
			return 0;
		}

		if (pilieCheNode.getAllianceId() == 0) {
			return 0;
		}

		List<StrongHoldNode> guanduList = strongHoldNodeDao.findBuildingByType(BuildingType.GuanDu);
		if (!JavaUtils.bool(guanduList)) {
			return 0;
		}
		StrongHoldNode guanduNode = guanduList.get(0);
		if (guanduNode.getOccupyState() == StrongHoldNode.SHOccupyState.NotOccupied) {
			return 0;
		}

		if (guanduNode.getAllianceId() == 0 || guanduNode.getAllianceId() == pilieCheNode.getAllianceId()) {
			return 0;
		}

		int gvgPilicheTime = configService.getConfig(GvgSettingConfig.class).getGvgPilicheTime();
		if (gvgPilicheTime <= 0) {
			return 0;
		}

		long attackGapTime = gvgPilicheTime * TimeUtil.SECONDS_MILLIS;
		return pilieCheNode.getLastActionTime() + attackGapTime;
	}

	public boolean kickGarrisonMember(Role applicantRole, long armyId, StrongHoldNode strongHoldNode){
		ArmyInfo army = armyInGarrison(strongHoldNode, armyId);
		if(army == null) {
			ErrorLogUtil.errorLog("[GVG]kickGarrisonMember armyNotInGarrison",
					"applicantRoleId",applicantRole.getId(), "armyId",armyId,
					"strongHoldNodeId",strongHoldNode.getPersistKey());
			return false;
		}

		if(!canAccessGarrison(applicantRole, strongHoldNode)) {
			ErrorLogUtil.errorLog("[GVG]kickGarrisonMember no access",
					"applicantRoleId",applicantRole.getId(), "armyId",armyId, "armyWorkType",army.getWorkType(),
					"endPos",army.getEnd(), "targetNode",strongHoldNode.getPersistKey(), "leaderArmyId",strongHoldNode.leaderArmyId());
			return false;
		}

		armyManager.returnArmy(army);

		logger.info("[GVG]kickGarrisonMember applicantRole: {},  armyId: {}, workType: {}, endPos: {} , " +
						"strongHoldNode: {}, leaderArmyId: {}",
				applicantRole.getId(), armyId, army.getWorkType(), army.getEnd(), strongHoldNode.getPersistKey(), strongHoldNode.leaderArmyId());
		return true;
	}

	public boolean setAutoSetLeader(Role applicantRole, long strongHoldNodeId, boolean autoSet) {
		var strongHoldNode = strongHoldNodeDao.findById(strongHoldNodeId);
		if(strongHoldNode == null) {
			ErrorLogUtil.errorLog("[GVG]setAutoSetLeader strongHoldNull",
					"applicantRoleId",applicantRole.getId(), "strongNodeHoldId",strongHoldNodeId);
			return false;
		}

		if (!canAccessGarrison(applicantRole, strongHoldNode)) {
			ErrorLogUtil.errorLog("[GVG]setAutoSetLeader no access",
					"applicantRoleId",applicantRole.getId(), "strongNodeHoldId",strongHoldNodeId);
			return false;
		}

		strongHoldNode.setAutoSetLeader(autoSet);

		logger.info("[GVG]setAutoSetLeader, applicantRole: {}, strongHoldNode: {}, autoSet: {}",
				applicantRole.getId(), strongHoldNode.getPersistKey(), autoSet);
		return true;
	}

	// 生成邮件头
	public String mailTitle(ArmyInfo attackerMainArmy, boolean isPvp, boolean isAck) {
		StrongHoldNode strongHoldNode = (StrongHoldNode) attackerMainArmy.getTargetNode();
		if (null == strongHoldNode) {
			return "";
		}

		var strongHoldName = "@" + strongHoldNode.getMetaName() + "@";
		FightContext fightContext = attackerMainArmy.getFightContext();
		List<String> params = com.google.common.collect.Lists.newArrayList();
		//pvp部分
		if (isPvp) {
			Long atkMainId = fightContext.getAttacker().getMainRoleId();
			Long defMainId = fightContext.getDefender().getMainRoleId();
			Role atkMainRole = roleDao.findById(atkMainId);
			Role defMainRole = roleDao.findById(defMainId);
			Alliance defAlliance = allianceDao.findById(defMainRole.getAllianceId());

			if (attackerMainArmy.getRallyContext() != null) {
				if (fightContext.isWin()) {
					//官渡之战集结攻方胜利
					if (isAck) {
						//进攻方
						if (null != defAlliance) {
							params.add(defAlliance.getAliasName());
							params.add(strongHoldName);
						}
						return MailUtils.buildTitle("ui/images/mail/mail_icon5", "MAIL_PVP_JIJIE_ATTACK_WIN_TITLE",
								"MAIL_PVP_JIJIE_ATTACK_WIN_SUBTITLE_2", params);
					}
					//防守失败
					params.add(atkMainRole.getName());
					params.add(strongHoldName);
					return MailUtils.buildTitle("ui/images/mail/mail_icon8", "MAIL_PVP_JIJIE_DEFENCE_LOSE_TITLE",
							"MAIL_PVP_DEFENCE_LOSE_SUBTITLE_2", params);
				}
				//官渡之战集结攻方失败
				if (isAck) {
					//进攻方
					if (null != defAlliance) {
						params.add(defAlliance.getAliasName());
						params.add(strongHoldName);
					}
					return MailUtils.buildTitle("ui/images/mail/mail_icon6", "MAIL_PVP_JIJIE_ATTACK_LOSE_TITLE",
							"MAIL_PVP_JIJIE_ATTACK_LOSE_SUBTITLE_2", params);
				}
				//防守胜利
				params.add(atkMainRole.getName());
				params.add(strongHoldName);
				return MailUtils.buildTitle("ui/images/mail/mail_icon7", "MAIL_PVP_JIJIE_DEFENCE_WIN_TITLE",
						"MAIL_PVP_DEFENCE_WIN_SUBTITLE_2", params);
			}
			if (fightContext.isWin()) {
				//官渡之战单体攻方胜利
				if (isAck) {
					//进攻方
					if (null != defAlliance) {
						params.add(defAlliance.getName());
					}
					return MailUtils.buildTitle("ui/images/mail/mail_icon5", "MAIL_PVP_ATTACK_WIN_TITLE",
							"MAIL_PVP_ATTACK_WIN_SUBTITLE_2", params);
				}
				//防守方
				params.add(atkMainRole.getName());
				params.add(strongHoldName);
				return MailUtils.buildTitle("ui/images/mail/mail_icon8", "MAIL_PVP_DEFENCE_LOSE_TITLE",
						"MAIL_PVP_DEFENCE_LOSE_SUBTITLE_2", params);
			}
			//官渡之战单体攻方失败
			if (isAck) {
				//进攻方
				if (null != defAlliance) {
					params.add(defAlliance.getName());
				}
				return MailUtils.buildTitle("ui/images/mail/mail_icon6", "MAIL_PVP_ATTACK_LOSE_TITLE",
						"MAIL_PVP_ATTACK_LOSE_SUBTITLE_2", params);
			}
			//防守方
			params.add(atkMainRole.getName());
			params.add(strongHoldName);
			return MailUtils.buildTitle("ui/images/mail/mail_icon7", "MAIL_PVP_DEFENCE_WIN_TITLE",
					"MAIL_PVP_DEFENCE_WIN_SUBTITLE_2", params);
		}

		//pve部分
		if (attackerMainArmy.getRallyContext() != null) {
			if (fightContext.isWin()) {
				//pve州府战集结攻方胜利
				if (isAck) {
					params.add(strongHoldName);
					return MailUtils.buildTitle("ui/images/building/building_citywall_1_4", "MAIL_JIJIE_WIN_TITLE",
							"MAIL_JIJIE_WIN_SUBTITLE_2", params);
				}
			}
			//pve州府战集结攻方失败
			if (isAck) {
				params.add(strongHoldName);
				return MailUtils.buildTitle("ui/images/building/building_citywall_1_4", "MAIL_JIJIE_LOSE_TITLE",
						"MAIL_JIJIE_LOSE_SUBTITLE_2", params);
			}
		}

		//pve州府战单体攻方失败
		if (fightContext.isWin()) {
			if (isAck) {
				params.add(strongHoldName);
				return MailUtils.buildTitle("ui/images/building/building_citywall_1_4", "MAIL_PVE_WIN_TITLE",
						"MAIL_PVE_WIN_SUBTITLE_2", params);
			}
		}

		//pve州府战单体攻方失败
		if (isAck) {
			params.add(strongHoldName);
			return MailUtils.buildTitle("ui/images/building/building_citywall_1_4", "MAIL_PVE_LOSE_TITLE",
					"MAIL_PVE_LOSE_SUBTITLE_2", params);
		}

		return "";
	}

	public List<Object> hotFixBackFunc(List<Object> paramList) {
		return null;
	}

	public List<Object> hotFixBackFunc2(List<Object> paramList) {
		return null;
	}
}
