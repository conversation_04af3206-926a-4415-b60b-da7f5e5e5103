package com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.impl;

import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.kvk.SeasonTaskConfig;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.RoleSeasonTaskRecord;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.AbstractSeasonTaskAction;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.SeasonTaskActionType;
import com.longtech.ls.config.ServerType;

/**
 * 
 * 个人荣誉值达到X
 * 
 * <AUTHOR>
 * @date 2021/11/17
 */
@Service
public class PersonalSeasonHonor extends AbstractSeasonTaskAction {
	@Override
	public SeasonTaskActionType getType() {
		return SeasonTaskActionType.PERSONAL_SEASON_HONOR;
	}

	@Override
	public long getProgress(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		RoleSeasonTaskRecord record = taskRecordDao.getRecordByRoleAndMetaId(role, meta.getId());
		if (record == null) {
			return 0;
		}
		return record.getProgress(getType().getId());
	}

	@Override
	protected void updateProgress(Role role, RoleSeasonTaskRecord taskRecord, Object... params) {
//		if (Application.getConfigCenter().getServerType(role.getCurrentServerId()) != ServerType.KVK_SEASON) {
//			return;// 非赛季服 K服，直接返回不处理
//		}
		long honor = (long) params[0];
		//
		//
		SeasonTaskConfig.SeasonTaskMeta meta = getMeta(taskRecord.getMetaId());
		if (meta == null) {
			return;
		}
		if (taskRecord.isFinish()) {
			return;
		}
		int arrIndex = meta.getConditions().indexOf(getType().getId());
		int parma1 = meta.getParam1s().get(arrIndex);
		long currentProgress = getProgress(role, meta);
		if (currentProgress >= parma1) {
			return;
		}
		taskRecord.updateProgress(getType().getId(), honor > parma1 ? parma1 : honor);
		taskRecordDao.save(taskRecord);
	}

	@Override
	public boolean actionFinishCheck(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		int i = meta.getConditions().indexOf(getType().getId());
		int param1 = meta.getParam1s().get(i);
		return getProgress(role, meta) >= param1;
	}
}
