package com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.lc.billion.icefire.gvgcontrol.biz.model.GVGAllianceSignUpInfo;
import com.lc.billion.icefire.gvgcontrol.biz.model.RZERoomInfo;
import com.simfun.sgf.utils.JavaUtils;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;

/**
 * <AUTHOR>
 * 
 */
@Repository
public class RZERoomInfoDao extends RootDao<RZERoomInfo> {

	private Map<Integer, List<RZERoomInfo>> dataByServerId = new MyConcurrentMap<>();
	private Map<Long, List<RZERoomInfo>> dataByAllianceId = new MyConcurrentMap<>();

	public RZERoomInfoDao() {
		super(RZERoomInfo.class, true);
	}

	@Override
	protected MongoCursor<RZERoomInfo> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(RZERoomInfo entity) {
		dataByServerId.compute(entity.getServerId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
		dataByAllianceId.compute(entity.getAllianceId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
	}

	@Override
	protected void removeMemoryIndexes(RZERoomInfo entity) {
		List<RZERoomInfo> set = dataByServerId.get(entity.getServerId());
		if (set != null) {
			set.remove(entity);
		}

		List<RZERoomInfo> set2 = dataByAllianceId.get(entity.getAllianceId());
		if (set2 != null) {
			set2.remove(entity);
		}
	}

	public RZERoomInfo create(Long allianceId, int serverId) {
		int db = Application.getServerId();
		RZERoomInfo gvgAllianceSignUpInfo = newEntityInstance();
		gvgAllianceSignUpInfo.setAllianceId(allianceId);
		gvgAllianceSignUpInfo.setServerId(serverId);
		return createEntity(db, gvgAllianceSignUpInfo);
	}

	public List<RZERoomInfo> findByServerId(int gameServerId) {
		return dataByServerId.get(gameServerId);
	}

	public List<RZERoomInfo> findByAllianceId(Long allianceId ) {
		return dataByAllianceId.get(allianceId);
	}

	public Map<Long, List<RZERoomInfo>> getDataByAllianceId() {
		return dataByAllianceId;
	}

	public Map<Integer, List<RZERoomInfo>> findByServerId() {
		return dataByServerId;
	}

	public void deleteAll() {
		delete(findAll());
	}

	public List<RZERoomInfo> findBySelectTime(int index) {
		List<RZERoomInfo> ret = new ArrayList<>();
		Collection<RZERoomInfo> rzeRoomInfos = findAll();
		for (RZERoomInfo roomInfo : rzeRoomInfos) {
			int signUpTimeIndex = roomInfo.getSignUpTimeIndex();
			if (signUpTimeIndex > 0 && signUpTimeIndex == index) {
				ret.add(roomInfo);
			}
		}
		return ret;
	}

	public RZERoomInfo findAllianceRoomBySelectTime(Long allianceId, int index){
		List<RZERoomInfo> roomInfos = dataByAllianceId.get(allianceId);
		if(JavaUtils.bool(roomInfos)){
			for(RZERoomInfo roomInfo : roomInfos){
				if(roomInfo.getSignUpTimeIndex() == index){
					return roomInfo;
				}
			}
		}

		return null;
	}
}
