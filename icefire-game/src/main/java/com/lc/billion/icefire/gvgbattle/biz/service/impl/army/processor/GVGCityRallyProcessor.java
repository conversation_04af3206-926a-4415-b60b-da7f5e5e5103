package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.processor;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.alliance.war.AllianceWar;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.prop.Prop;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleCity;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.login.LoginServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.notice.NoticeConstants;
import com.lc.billion.icefire.game.biz.service.impl.push.PushUtil;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map.Entry;

/**
 * 集结GVG玩家
 *
 * <AUTHOR>
 * @date 2021.02.22
 */
@Service
public class GVGCityRallyProcessor extends AbstractGvgRallyArmyProcessor {
    @Autowired
    protected GVGStrongHoldService gvgStrongHoldService;
    @Autowired
    private PushUtil pushUtil;

    @Override
    public ArmyType getArmyType() {
        return ArmyType.GVG_RALLY_ATTACK_PLAYER;
    }

    @Override
    protected boolean check(Role role, SceneNode targetNode, ArmySetoutParam param) {
        if (!Application.getBean(LoginServiceImpl.class).isLoginSwitch()) {// gvg战斗服停了，战斗结束了
            return false;
        }
        if (!gvgStrongHoldService.isMoveGvgActivityStage(TimeUtil.getNow()))// 入场阶段，不允许行军
            return false;
        // 目标不是城市
        if (targetNode.getNodeType() != SceneNodeType.CITY) {
            return false;
        }
        // 保护罩检测
        RoleCity targetCity = (RoleCity) targetNode;
        if (sdp.getProtectionService().hasProtection(targetCity)) {
            sdp.getNoticeService().notice(role, NoticeConstants.TARGET_IS_PROTECT, true);
            return false;
        }

        // 判断玩家是否在出生区
        if (gvgStrongHoldService.isInBirthArea(targetNode.getPersistKey(), targetNode.getPosition())) {
            ErrorLogUtil.errorLog("GVG攻击玩家在出生区", "attackRoleId",role.getPersistKey(),
                    "targetRoleId",targetNode.getPersistKey(), "posX",targetNode.getPosition().getX(), "posY",targetNode.getPosition().getY());
            return false;
        }

        // 同联盟检测
        if (allianceService.isSameAlliance(role.getPersistKey(), targetCity.getPersistKey())) {
            return false;
        }
        // 没有联盟战争大厅,集结容量 <=0
        if (role.getNumberProps().getInt(Prop.RALLY_CAPACITY_14000) <= 0) {
            return false;
        }
        // 对同一个目标发起多个集结
        if (role.getAllianceId() == null) {
            return false;
        }
        Alliance alliance = allianceDao.findById(role.getAllianceId());
        for (Entry<Long, AllianceWar> entry : alliance.getWarMap().entrySet()) {
            ArmyInfo army = armyManager.findById(entry.getValue().getArmyId());
            if (army == null) {
                logger.info("gvg city rally check error info id {} war {} armyId {}", entry.getKey(), entry.getValue().getWarType(), entry.getValue().getArmyId());
                ErrorLogUtil.errorLog("gvg city rally check error. ", "id", entry.getKey(), "war", entry.getValue().getWarType(), "armyId", entry.getValue().getArmyId());
                continue;
            }
            if (army.getRoleId().equals(role.getId()) && army.getArmyType() == ArmyType.RALLY && army.getTargetNodeId().equals(targetNode.getPersistKey())) {
                return false;
            }
        }
        return true;
    }

    @Override
    protected void start(ArmyInfo army) {
        // 创建联盟战争
        AllianceWar war = warService.createWar(army);
        // 开始这个行军
        sdp.getArmyServiceImpl().addArmyToAoi(army, addToAoi());
        // 设备推送
        //
        // 发起 集结的广播 邀请加入信息
        SceneNode targetNode = armyManager.getArmyTargetNode(army);
        Role role = army.getOwner();
        if (targetNode.getNodeType() == SceneNodeType.CITY) {
            Role targetRole = roleManager.getRole(targetNode.getPersistKey());
            pushUtil.allianceMemberWar(role, targetRole);
        }
        logger.info("集结 | 发起集结, warId: " + war.getPersistKey() + ", leaderId" + role.getPersistKey() + " " + role.getName());
    }
}
