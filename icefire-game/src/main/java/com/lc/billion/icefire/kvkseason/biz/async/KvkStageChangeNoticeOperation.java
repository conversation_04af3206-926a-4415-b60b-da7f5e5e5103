package com.lc.billion.icefire.kvkseason.biz.async;

import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.AbstractOnlineState;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.kvkseason.biz.service.impl.kvk.KvkSeasonServiceImpl;

/**
 * <AUTHOR>
 * @date 2021/8/31
 */
public class KvkStageChangeNoticeOperation implements AsyncOperation {

	private KvkSeasonServiceImpl kvkSeasonService;
	private WorldServiceImpl worldService;

	public KvkStageChangeNoticeOperation(KvkSeasonServiceImpl kvkSeasonService, WorldServiceImpl worldService) {
		this.kvkSeasonService = kvkSeasonService;
		this.worldService = worldService;
	}

	@Override
	public boolean init() {
		return true;
	}

	@Override
	public boolean run() {
		logger.info("[KVK赛季服 状态变更]");
		worldService.run(bean -> {
			if (bean.getOnlineState().getType() == AbstractOnlineState.Type.GAMING) {
				Role role = worldService.getWorld().getRole(bean.getRoleId());
				if (role == null) {
					return;
				}
				role.send(kvkSeasonService.wrapperKvkStageInfo(role));
			}
		});
		return false;
	}

	@Override
	public void finish() {

	}
}
