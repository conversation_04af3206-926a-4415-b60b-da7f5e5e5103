package com.lc.billion.icefire.gvgcontrol.biz.model;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import lombok.Getter;
import lombok.Setter;
import org.jongo.marshall.jackson.oid.MongoId;

import java.util.ArrayList;

public class GVGMatchResultBackup extends AbstractEntity {

    @MongoId
    private Long id;

    @Setter @Getter
    private int roundId;

    @Setter @Getter
    private long backupTime;

    @Setter @Getter
    private int serverId;

    @Setter @Getter
    private Long allianceId;

    @Setter @Getter
    private GvgMatchType matchType;

    @Setter @Getter
    private ArrayList<Long> roleList;

    @Override
    public Long getGroupingId() {
        return id;
    }

    @Override
    public int hashCodeImpl() {
        return hashCodeForPersistKey();
    }

    @Override
    public boolean equalsImpl(Object obj) {
        return equalsForPersistKey(obj);
    }

    @Override
    public void setPersistKey(Long id) {
        this.id = id;
    }

    @Override
    public Long getPersistKey() {
        return id;
    }
}
