package com.lc.billion.icefire.game.biz.async.people;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.biz.async.gvg.AsyncRPCThreadOperation;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.people.PeopleFriendServiceImpl;
import com.lc.billion.icefire.game.biz.service.rpc.GameRPCToWebProxyService;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.longtech.ls.rpc.ICommonRemoteWebService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @ClassName PeopleDismissalFriendOperation
 * @Description
 * <AUTHOR>
 * @Date 2024/8/24 15:36
 * @Version 1.0
 */
public class PeopleDismissalFriendOperation extends AsyncRPCThreadOperation {
    private final Logger logger = LoggerFactory.getLogger(PeopleDismissalFriendOperation.class);

    private final Role role;
    private final long friendId;

    public PeopleDismissalFriendOperation(Role role, long friendId) {
        this.role = role;
        this.friendId = friendId;
    }


    @Override
    public boolean run() {
        try {
            // 先判断是否为本服
            Role friendRole = Application.getBean(RoleDao.class).findById(friendId);
            if (friendRole != null) {
                // 本服处理
                Application.getBean(PeopleFriendServiceImpl.class)
                        .callbackDismissalFriend(friendRole, role.getAccountId(), role.getRoleId());
                return true;
            } else {
                // 跨服处理
                GameRPCToWebProxyService rpcService = Application.getBean(GameRPCToWebProxyService.class);
                ICommonRemoteWebService commonRemoteWebService = rpcService.getCommonRemoteWebService();
                commonRemoteWebService.dismissalFriend(friendId, role.getAccountId(), role.getRoleId());
            }
        } catch (Exception e) {
            if (!(e instanceof ExpectedException)) {
                ErrorLogUtil.exceptionLog("PeopleDismissalFriendOperation", e, "roleId", role.getRoleId(), "friendId", friendId);
            }
            return false;
        }
        return true;
    }
}
