package com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtPlayerSignupInfo;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: maoqq
 * @Date: 2022/04/11 11:10 AM
 */
@Repository
public class TvtPlayerSignupInfoDao extends RootDao<TvtPlayerSignupInfo> {
    private Map<Integer, List<TvtPlayerSignupInfo>> dataByServerId = new MyConcurrentMap<>();

    private Map<Long, TvtPlayerSignupInfo> dataByRoleId = new MyConcurrentMap<>();

    protected TvtPlayerSignupInfoDao() {
        super(TvtPlayerSignupInfo.class, true);
    }

    @Override
    protected void putMemoryIndexes(TvtPlayerSignupInfo entity) {
        dataByRoleId.put(entity.getRoleId(), entity);
        dataByServerId.compute(entity.getServerId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
    }

    @Override
    protected void removeMemoryIndexes(TvtPlayerSignupInfo entity) {
        dataByRoleId.remove(entity.getRoleId());
        if (dataByServerId.containsKey(entity.getServerId())) {
            dataByServerId.get(entity.getServerId()).remove(entity);
            // 移除外层key
            if (dataByServerId.get(entity.getServerId()).size() == 0) {
                dataByServerId.remove(entity.getServerId());
            }
        }
    }

    @Override
    protected MongoCursor<TvtPlayerSignupInfo> doFindAll(int db) {
        return dbFindAllForWorldEntity(db);
    }

    public void deleteAll() {
        delete(findAll());
    }

    public TvtPlayerSignupInfo create(Long roleId, int hideScore, int serverId, int signupStatus) {
        int db = Application.getServerId();
        TvtPlayerSignupInfo tvtPlayerSignupInfo = newEntityInstance();
        tvtPlayerSignupInfo.setHideScore(hideScore);
        tvtPlayerSignupInfo.setRoleId(roleId);
        tvtPlayerSignupInfo.setServerId(serverId);
        tvtPlayerSignupInfo.setSignupStatus(signupStatus);
        tvtPlayerSignupInfo.setTvtSide(0);
        tvtPlayerSignupInfo.setBattleServerId(0L);
        return createEntity(db, tvtPlayerSignupInfo);
    }

    public Map<Integer, List<TvtPlayerSignupInfo>> getDataByServerId() {
        return dataByServerId;
    }

    public List<TvtPlayerSignupInfo> findDataByServerId(int serverId) {
        return this.dataByServerId.get(serverId);
    }

    public TvtPlayerSignupInfo findByRoleId(Long roleId) {
        return dataByRoleId.get(roleId);
    }
}
