package com.lc.billion.icefire.kvkseason.biz.service.impl.legion;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.manager.AllianceMemberManager;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceMember;
import com.lc.billion.icefire.game.biz.model.kvk.KvkHistoryLegionProsperity;
import com.lc.billion.icefire.game.biz.model.role.AbstractRole;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceOutput;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.KvkHonorLegionDao;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionMemberDao;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionOfficialDao;
import com.lc.billion.icefire.kvkseason.biz.manager.legion.LegionManager;
import com.lc.billion.icefire.kvkseason.biz.manager.legion.LegionOfficialManager;
import com.lc.billion.icefire.kvkseason.biz.model.honor.KvkHonorLegion;
import com.lc.billion.icefire.kvkseason.biz.model.legion.Legion;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionAnnouncement;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionMember;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionOfficial;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.structure.*;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class LegionOutput {

	private static final Logger log = LoggerFactory.getLogger(LegionOutput.class);

	private static LegionMemberDao legionMemberDao = Application.getBean(LegionMemberDao.class);
	private static AllianceDao allianceDao = Application.getBean(AllianceDao.class);
	private static AllianceMemberManager allianceMemberManager = Application.getBean(AllianceMemberManager.class);
	private static LegionOfficialManager legionOfficialManager = Application.getBean(LegionOfficialManager.class);
	private static WorldServiceImpl worldService = Application.getBean(WorldServiceImpl.class);
	private static LegionManager legionManager = Application.getBean(LegionManager.class);
	private static KvkHonorLegionDao kvkHonorLegionDao = Application.getBean(KvkHonorLegionDao.class);
	private static RoleDao roleDao = Application.getBean(RoleDao.class);
	private static LegionOfficialDao legionOfficialDao = Application.getBean(LegionOfficialDao.class);

	public static GcLegionAllianceMemberList toGcLegionAllianceMemberList(Legion legion) {
		GcLegionAllianceMemberList gcLegionAllianceMemberList = new GcLegionAllianceMemberList();
		Collection<LegionMember> legionMembers = legionMemberDao.findByLegionId(legion.getId());
		if (JavaUtils.bool(legionMembers)) {
			long transferCDTime = legionMembers.stream().max(Comparator.comparingLong(LegionMember::getTransferCDTime)).get().getTransferCDTime();
			Long leaderAllianceId = legion.getLeaderAllianceId();
			for (LegionMember legionMember : legionMembers) {
				PsAllianceInfo psAllianceInfo = toPsAllianceInfo(legionMember);
				if (leaderAllianceId.longValue() == legionMember.getPersistKey().longValue()) {
					psAllianceInfo.setTransferCDTime(transferCDTime);
				}
				gcLegionAllianceMemberList.addToMembers(psAllianceInfo);
			}
		}
		gcLegionAllianceMemberList.setLegionId(legion.getId());
		return gcLegionAllianceMemberList;
	}

	public static PsAllianceInfo toPsAllianceInfo(LegionMember legionMember) {
		Long allianceId = legionMember.getPersistKey();
		Alliance alliance = allianceDao.findById(allianceId);
		PsAllianceInfo psAllianceInfo = AllianceOutput.toInfo(alliance);
		psAllianceInfo.setExitCDTime(legionMember.getExitCDTime());
		psAllianceInfo.setTransferCDTime(legionMember.getTransferCDTime());
		return psAllianceInfo;
	}

	public static GcLegionOfficialList toGcLegionOfficialList(Legion legion) {
		GcLegionOfficialList gcLegionOfficialList = new GcLegionOfficialList();
		List<LegionOfficial> legionOfficials = legionOfficialManager.findByLegionId(legion.getId());
		if (JavaUtils.bool(legionOfficials)) {
			for (LegionOfficial legionOfficial : legionOfficials) {
				gcLegionOfficialList.addToLegionOfficials(toPsLegionOfficialInfo(legionOfficial));
			}
		}
		gcLegionOfficialList.setLegionId(legion.getId());
		return gcLegionOfficialList;
	}

	public static PsLegionOfficialInfo toPsLegionOfficialInfo(LegionOfficial legionOfficial) {
		PsLegionOfficialInfo psLegionOfficialInfo = new PsLegionOfficialInfo();
		psLegionOfficialInfo.setGroup(legionOfficial.getGroup());
		psLegionOfficialInfo.setOrder(legionOfficial.getOrder());
		psLegionOfficialInfo.setRank(legionOfficial.getRank());

		Long roleId = legionOfficial.getRoleId();
		if (JavaUtils.bool(roleId)) {
			AllianceMember allianceMember = allianceMemberManager.getMember(roleId);
			if (allianceMember != null) {
				psLegionOfficialInfo.setRoleId(roleId);
				psLegionOfficialInfo.setRoleHead(allianceMember.getHead() == null ? "" : allianceMember.getHead());
				psLegionOfficialInfo.setRoleName(allianceMember.getName());
				psLegionOfficialInfo.setAllianceId(allianceMember.getAllianceId());
				Alliance alliance = allianceDao.findById(allianceMember.getAllianceId());
				if (alliance != null) {
					psLegionOfficialInfo.setAllianceAliasName(alliance.getAliasName());
				}
				psLegionOfficialInfo.setOnline(allianceMember.isOnline());
				psLegionOfficialInfo.setOfflineTime(allianceMember.getOfflineTime());
				AbstractRole abstractRole = worldService.getWorld().getAbstractRole(allianceMember.getPersistKey());
				if (abstractRole != null) {
					psLegionOfficialInfo.setSex(abstractRole.getSex());
					psLegionOfficialInfo.setLevel(abstractRole.getLevel());
					psLegionOfficialInfo.setFightCapacity(abstractRole.getFightPower());
				} else {
					psLegionOfficialInfo.setFightCapacity(allianceMember.getFightPower());
				}
			} else {
				ErrorLogUtil.errorLog("玩家是军团官员，但没有AllianceMember", new RuntimeException(),"roleId",roleId);
			}
		}

		return psLegionOfficialInfo;
	}

	public static GcLegionRequestCount toGcLegionRequestCount(int count) {
		return new GcLegionRequestCount(count);
	}

	public static GcLegionLoad toGcLegionLoad(Legion legion) {
		GcLegionLoad gcLegionLoad = new GcLegionLoad();
		gcLegionLoad.setLegionInfo(toPsLegionInfo(legion));
		return gcLegionLoad;
	}

	public static GcLegionCreate wrapperCreate(Legion legion, int errorCode) {
		GcLegionCreate msg = new GcLegionCreate();
		msg.setLegion(toPsLegionInfo(legion));
		msg.setErrorCode(errorCode);
		return msg;
	}

	public static PsLegionInfo toPsLegionInfo(Legion legion) {
		Long legionId = legion.getId();
		PsLegionInfo info = new PsLegionInfo();
		// info.setAnnouncements(legion.get)
		Collection<LegionMember> legionMembers = legionMemberDao.findByLegionId(legionId);
		info.setCurMember(legionMembers.size());
		info.setMaxMember(legionManager.getLegionMemberMax(legionId));
		long fightingCapacity = 0;
		for (LegionMember legionMember : legionMembers) {
			Alliance alliance = allianceDao.findById(legionMember.getPersistKey());
			if (alliance == null) {
				throw new AlertException("军团成员存在,但是联盟不存在了","LegionMember",legionMember.getPersistKey());
			}
			fightingCapacity += alliance.getAllFightingPower();
		}
		info.setDeclaration(legion.getDeclaration());
		info.setFightingCapacity(fightingCapacity);
		info.setId(legionId);
		info.setLanguage(legion.getLanguage());
		Long leaderAllianceId = legion.getLeaderAllianceId();
		Alliance alliance = allianceDao.findById(leaderAllianceId);
		info.setMainAllianceInfo(AllianceOutput.toPsAllianceSimpleInfo(alliance));
		// info.setMaxMember(maxMember)
		info.setName(legion.getName());
		info.setProsperity(legion.getProsperity());
		info.setServerId(legion.getServerId());
		info.setFlagInfo(legion.toPsAllianceFlagInfo());
		info.setCountry(legion.getCountry());
		legion.getAnnouncementList().forEach(a -> info.addToLegionAnnouncements(LegionOutput.toLegionAnnouncementInfo(a)));
		KvkHonorLegion kvkHonorLegion = kvkHonorLegionDao.findById(legionId);
		if (kvkHonorLegion != null) {
			info.setHonor(kvkHonorLegion.getHonorValue());
		}
		return info;
	}

	public static PsLegionInfo toPsLegionInfo(KvkHistoryLegionProsperity history) {

		PsLegionInfo info = new PsLegionInfo();
		info.setId(history.getLegionId());
		info.setLanguage(history.getLegionLanguage());
		//
		info.setCurMember(history.getCurrentMemberNum());
		info.setMaxMember(history.getLegionMaxMember());
		info.setName(history.getLegionName());
		PsAllianceFlagInfo psAllianceFlagInfo = new PsAllianceFlagInfo();
		psAllianceFlagInfo.setBadge(history.getBadge());
		psAllianceFlagInfo.setBadgeColor(history.getBadgeColor());
		psAllianceFlagInfo.setBanner(history.getBanner());
		psAllianceFlagInfo.setBannerColor(history.getBannerColor());
		info.setFlagInfo(psAllianceFlagInfo);
		info.setServerId(history.getLegionServerId());
		return info;
	}

	public static GcLegionMemberList wrapperMembers(Legion legion) {
		GcLegionMemberList msg = new GcLegionMemberList();
		msg.setLegionId(legion.getId());

		List<PsAllianceMemberInfo> psMembers = new ArrayList<>();
		// 所有军团成员
		Collection<LegionMember> legionMembers = legionMemberDao.findByLegionId(legion.getId());
		for (LegionMember legionMember : legionMembers) {
			List<AllianceMember> members = allianceMemberManager.getMembers(legionMember.getPersistKey());
			Alliance alliance = allianceDao.findById(legionMember.getPersistKey());
			for (AllianceMember m : members) {
				PsAllianceMemberInfo psAllianceMemberInfo = AllianceOutput.toInfo(m);
				psAllianceMemberInfo.setAllianceAliasName(alliance.getAliasName());
				psMembers.add(psAllianceMemberInfo);
			}
		}

		msg.setMembers(psMembers);
		return msg;
	}

	public static PsLegionAnnouncementInfo toLegionAnnouncementInfo(LegionAnnouncement announcement) {
		PsLegionAnnouncementInfo info = new PsLegionAnnouncementInfo();
		info.setIndex(announcement.getIndex());
		info.setDeclaration(announcement.getDeclaration());
		info.setTime(announcement.getTime());
		info.setRoleId(announcement.getRoleId());

		Role role = worldService.getWorld().getRole(announcement.getRoleId());
		if (role != null) {
			info.setHead(role.getHead());
			info.setName(role.getName());
//			info.setGender(role.getSex());
//			info.setLevel(role.getLevel());
		}
		return info;
	}


}
