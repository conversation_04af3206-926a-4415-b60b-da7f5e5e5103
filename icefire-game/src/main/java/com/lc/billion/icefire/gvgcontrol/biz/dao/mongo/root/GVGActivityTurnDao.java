package com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root;

import java.util.concurrent.ConcurrentMap;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGActivityTurn;

/**
 * <AUTHOR>
 *
 */
@Repository
public class GVGActivityTurnDao extends RootDao<GVGActivityTurn> {

	private int maxTurn;
	private ConcurrentMap<Integer, GVGActivityTurn> turnsByWeek = new MyConcurrentMap<>();

	public GVGActivityTurnDao() {
		super(GVGActivityTurn.class, true);
	}

	@Override
	protected MongoCursor<GVGActivityTurn> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(GVGActivityTurn entity) {
		maxTurn = Math.max(maxTurn, entity.getTurn());
		turnsByWeek.put(entity.getWeek(), entity);
	}

	@Override
	protected void removeMemoryIndexes(GVGActivityTurn entity) {
		turnsByWeek.remove(entity.getWeek());
	}

	public GVGActivityTurn create(int week) {
		GVGActivityTurn gvgActivityTurn = newEntityInstance();
		gvgActivityTurn.setTurn(++maxTurn);
		gvgActivityTurn.setWeek(week);
		return createEntity(Application.getServerId(), gvgActivityTurn);
	}

	public GVGActivityTurn findByWeek(int week) {
		return turnsByWeek.get(week);
		// int db = Application.getServerId();
		// Iterator<GVGActivityTurn> it = super.dbFind(db, "week", week).iterator();
		// if (it != null && it.hasNext()) {
		// return it.next();
		// }
		// return null;
	}
}
