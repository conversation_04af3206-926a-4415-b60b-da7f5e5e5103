package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.processor;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.ArmyDao;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.GvgResNode;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import com.lc.billion.icefire.game.biz.service.impl.email.MailService;
import com.lc.billion.icefire.game.biz.service.impl.login.LoginServiceImpl;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGResGatherService;
import com.lc.billion.icefire.protocol.structure.PsArmyProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Queue;

/**
 * 
 * 采集GVG战场资源点
 * 
 * <AUTHOR>
 * @date 2021/1/21
 */
@Service
public class GVGResGatherProcessor extends AbstractGvgArmyProcessor {

	@Autowired
	private ArmyDao armyDao;
	@Autowired
	private GVGResGatherService GVGResGatherService;
	@Autowired
	private MailService mailService;

	@Override
	public ArmyType getArmyType() {
		return ArmyType.GVG_GATHER;
	}

	@Override
	protected void doRecall(ArmyInfo army) {
		if (army.getGvgGatherContext() != null && army.getGvgGatherContext().getCurrentGatherAmount() > 0) {
			Role role = army.getOwner();
			if (role != null) {
				List<String> params = new ArrayList<>();
				params.add(String.format("%.0f", army.getGvgGatherContext().getCurrentGatherAmount()));
				mailService.sendSystemEmail(role, EmailConstants.GVG_GATHER_POINT_MAIL, null, params);
				logger.info("玩家" + army.getRoleId() + "采集结束,预计采集量" + army.getGvgGatherContext().getCarry() + "实际采集" + (int) army.getGvgGatherContext().getCurrentGatherAmount()
						+ " | " + army.getPersistKey());
			}
		}
		removeEnemyToNode(army);
	}

	@Override
	public void recallArmyImmediately(ArmyInfo army) {
		removeEnemyToNode(army);
		if (army.getWorkType() == ArmyWorkType.GATHERING) {
			GVGResGatherService.calculate(army, true, true, false);
			if (army.getGvgGatherContext() != null && army.getGvgGatherContext().getCurrentGatherAmount() > 0) {
				Role role = roleManager.getRole(army.getRoleId());
				if (role != null) {
					List<String> params = new ArrayList<>();
					params.add(String.format("%.0f", army.getGvgGatherContext().getCurrentGatherAmount()));
					mailService.sendSystemEmail(role, EmailConstants.GVG_GATHER_POINT_MAIL, null, params);
					logger.info("玩家" + army.getRoleId() + "采集结束,预计采集量" + army.getGvgGatherContext().getCarry() + "实际采集" + (int) army.getGvgGatherContext().getCurrentGatherAmount()
							+ " | " + army.getPersistKey());
				}
			}
		} else
			armyManager.takeBackArmy(army);// gatherGvgResService.calculate(army, true, true, false);里会调用 takeBackArmy
	}

	@Override
	protected boolean check(Role role, SceneNode targetNode, ArmySetoutParam param) {
		if (!Application.getBean(LoginServiceImpl.class).isLoginSwitch()) {// gvg战斗服停了，战斗结束了
			return false;
		}
		if (!gvgStrongHoldService.isMoveGvgActivityStage(TimeUtil.getNow()))// 入场阶段，不允许行军
			return false;
		if (targetNode.getNodeType() != SceneNodeType.GVG_RES)
			return false;
		GvgResNode resNode = (GvgResNode) targetNode;
		if (resNode.inGathering())
			return false;
		return true;
	}

	@Override
	protected PsArmyProgress toArmyProgressInfo(ArmyInfo army, PsArmyProgress info) {
		if (army.getWorkType() == ArmyWorkType.GATHERING) {
			info.setUpdateTime(army.getGvgGatherContext().getUpdateTime());
			info.setCurrExploitSpeed(army.getGvgGatherContext().getSpeed());
			info.setCurrExploitAmount((long) army.getGvgGatherContext().getCurrentGatherAmount());
			info.setMaxExploitAmount(army.getGvgGatherContext().getCarry());
			info.setStartX(army.getStartX());
			info.setStartY(army.getStartY());
			info.setEndX(army.getEndX());
			info.setEndY(army.getEndY());
			// 采集资源田的MetaId
			info.setResMetaId(Integer.valueOf(army.getGvgGatherContext().getMetaId()));
			//
			info.setRemainTime((long) (army.getGvgGatherContext().getReaminGatherTime() * TimeUtil.SECONDS_MILLIS));
			info.setOriginalTotalTime(army.getGvgGatherContext().getOriginalTotalTime());
		}
		return info;
	}

	@Override
	public boolean recallFreeCheck(ArmyInfo army) {
		if (army.getWorkType() == ArmyWorkType.GATHERING)
			return true;
		return false;
	}

	private void removeEnemyToNode(ArmyInfo myArmy) {
		//
		// 移除已经存在的战斗预警
		Queue<Long> enemyArmys = armyDao.getEnemyByRoleId(myArmy.getRoleId());
		for (Object obj : enemyArmys.toArray()) {
			Long enemyArmyId = (Long) obj;
			ArmyInfo enemy = armyDao.findById(enemyArmyId);
			// 是敌军 && 对资源点 && 是我的资源点
			if (enemy != null && enemy.getTargetNodeType() == SceneNodeType.GVG_RES && enemy.getTargetNodeId().equals(myArmy.getTargetNodeId())) {
				Role role = roleManager.getRole(myArmy.getRoleId());
				armyManager.removeEnemy(role, enemy);
			}
		}
	}
}
