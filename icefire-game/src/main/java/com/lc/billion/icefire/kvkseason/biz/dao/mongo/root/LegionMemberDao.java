package com.lc.billion.icefire.kvkseason.biz.dao.mongo.root;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionMember;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 *
 */
@Repository
public class LegionMemberDao extends RootDao<LegionMember> {

	// <legionId, <allianceId，Entity>>
	private Map<Long, Map<Long, LegionMember>> dataByLegionId = new MyConcurrentMap<>();

	public LegionMemberDao() {
		super(LegionMember.class, false);
	}

	@Override
	protected MongoCursor<LegionMember> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(LegionMember entity) {
		dataByLegionId.compute(entity.getLegionId(), (k, v) -> v == null ? new MyConcurrentMap<>() : v).put(entity.getPersistKey(), entity);
	}

	@Override
	protected void removeMemoryIndexes(LegionMember entity) {
		Map<Long, LegionMember> longLegionMemberMap = dataByLegionId.get(entity.getLegionId());
		if (JavaUtils.bool(longLegionMemberMap)) {
			longLegionMemberMap.remove(entity.getPersistKey());
		}
	}

	public LegionMember create(Alliance alliance, Long legionId) {
		int db = alliance.getDB();
		LegionMember legionMember = newEntityInstance();
		legionMember.setLegionId(legionId);
		legionMember.setPersistKey(alliance.getId());
		return createEntity(db, legionMember);
	}

	public Collection<LegionMember> findByLegionId(Long legionId) {
		Map<Long, LegionMember> longLegionMemberMap = dataByLegionId.get(legionId);
		return JavaUtils.bool(longLegionMemberMap) ? longLegionMemberMap.values() : Collections.EMPTY_LIST;
	}

	public Map<Long, LegionMember> findLegionMemberMapById(Long legionId) {
		Map<Long, LegionMember> map = dataByLegionId.get(legionId);
		return map == null ? Collections.emptyMap() : new HashMap<Long, LegionMember>(map);
	}

}
