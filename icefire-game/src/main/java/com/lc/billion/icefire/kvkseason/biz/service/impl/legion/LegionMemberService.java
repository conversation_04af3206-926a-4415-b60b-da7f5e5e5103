package com.lc.billion.icefire.kvkseason.biz.service.impl.legion;

import java.util.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.biz.manager.AllianceMemberManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceMember;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionMemberDao;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionMember;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 * @Date 2022/3/9
 */
@Service
public class LegionMemberService {
	@Autowired
	private LegionService legionService;
	@Autowired
	private LegionMemberDao legionMemberDao;
	@Autowired
	private AllianceMemberManager allianceMemberManager;
	@Autowired
	private RoleManager roleManager;

	private static final Logger logger = LoggerFactory.getLogger(LegionMemberService.class);

	/**
	 * 获取一个军团 有哪些联盟成员
	 * 
	 * @param allianceId
	 * @return
	 */
	public Collection<LegionMember> getLegionMembers(Long allianceId) {
		if (null == allianceId) {
			return Collections.EMPTY_LIST;
		}
		LegionMember member = legionMemberDao.findById(allianceId);
		if (member == null) {
			return Collections.EMPTY_LIST;
		}
		Collection<LegionMember> byLegionId = legionMemberDao.findByLegionId(member.getLegionId());
		return byLegionId == null ? Collections.EMPTY_LIST : byLegionId;
	}

	/**
	 * 获取一个军团 有哪些联盟
	 *
	 * @param legionId
	 * @return
	 */
	public Collection<LegionMember> getLegionMembersByLegionId(Long legionId) {
		if (null == legionId) {
			return Collections.EMPTY_LIST;
		}
		Collection<LegionMember> byLegionId = legionMemberDao.findByLegionId(legionId);
		return byLegionId == null ? Collections.EMPTY_LIST : byLegionId;
	}

	public Collection<Role> getLegionRoles(Long legionId) {
		if (null == legionId) {
			return Collections.EMPTY_LIST;
		}
		Collection<Role> ret = new ArrayList<>();
		Collection<LegionMember> legionMembers = legionMemberDao.findByLegionId(legionId);
		for (LegionMember legionMember : legionMembers) {
			// 获取联盟下的所有成员
			List<AllianceMember> members = allianceMemberManager.getMembers(legionMember.getPersistKey());
			if (JavaUtils.bool(members)) {
				for (AllianceMember member : members) {
					Role role = roleManager.getRole(member.getPersistKey());
					if (role != null)
						ret.add(role);
				}
			}
		}
		return ret;
	}

	/**
	 * 获取一个军团 除了传入参数联盟外，有哪些联盟成员
	 *
	 * @param allianceId
	 * @return
	 */
	public Collection<LegionMember> getLegionOtherMembers(Long allianceId) {
		if (null == allianceId) {
			return Collections.EMPTY_LIST;
		}
		LegionMember member = legionMemberDao.findById(allianceId);
		if (member == null) {
			return Collections.EMPTY_LIST;
		}
		Map<Long, LegionMember> legionMemberMapById = legionMemberDao.findLegionMemberMapById(member.getLegionId());
		legionMemberMapById.remove(allianceId);
		return legionMemberMapById.values();
	}

	public LegionMember getLegionMemberByAllianceId(Long allianceId) {
		return legionMemberDao.findById(allianceId);
	}
}
