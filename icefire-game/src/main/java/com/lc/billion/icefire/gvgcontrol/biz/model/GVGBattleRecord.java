package com.lc.billion.icefire.gvgcontrol.biz.model;

import java.util.ArrayList;
import java.util.List;

import org.jongo.marshall.jackson.oid.MongoId;
import org.springframework.beans.BeanUtils;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.rpc.vo.gvg.GVGBattleRecordVo;
import com.lc.billion.icefire.rpc.vo.gvg.RoleGVGBattleVo;

/**
 * 战斗记录
 * 
 * <AUTHOR>
 *
 */
public class GVGBattleRecord extends AbstractEntity {

	private static final long serialVersionUID = -3744435090530809617L;

	@MongoId
	private Long id;
	private Long victoryAllianceId;
	private int victoryAllianceServerId;
	private int victoryAlliancePoint;
	private List<RoleGVGBattleVo> victoryRoleGVGBattleVos = new ArrayList<>();;
	private Long failAllianceId;
	private int failAllianceServerId;
	private int failAlliancePoint;
	private List<RoleGVGBattleVo> failRoleGVGBattleVos = new ArrayList<>();;
	private int battleServerId;
	private long endRecAwardTime;
	private int turn;
	private boolean sendAwardReady;
	private boolean sendAward;
	// 胜方轮空
	private boolean bye;

	private long activityId;
	private int activityRound;

	private boolean stop;

	// 战斗类别
	private GvgMatchType matchType;
	// 杯赛战斗轮次
	private int gameRound;
	private int round;

	public int getWarZoneId() {
		return warZoneId;
	}

	public void setWarZoneId(int warZoneId) {
		this.warZoneId = warZoneId;
	}

	//gvg cup warZoneId;
	private int warZoneId;

	public int getRound() {
		return round;
	}

	public void setRound(int round) {
		this.round = round;
	}

	public static GVGBattleRecord copyGVGBattleRecord(GVGBattleRecordVo gvgBattleRecordVo) {
		GVGBattleRecord gvgBattleRecord = new GVGBattleRecord();
		BeanUtils.copyProperties(gvgBattleRecordVo, gvgBattleRecord);
		return gvgBattleRecord;
	}

	public Long getVictoryAllianceId() {
		return victoryAllianceId;
	}

	public void setVictoryAllianceId(Long victoryAllianceId) {
		this.victoryAllianceId = victoryAllianceId;
	}

	public int getVictoryAllianceServerId() {
		return victoryAllianceServerId;
	}

	public void setVictoryAllianceServerId(int victoryAllianceServerId) {
		this.victoryAllianceServerId = victoryAllianceServerId;
	}

	public int getVictoryAlliancePoint() {
		return victoryAlliancePoint;
	}

	public void setVictoryAlliancePoint(int victoryAlliancePoint) {
		this.victoryAlliancePoint = victoryAlliancePoint;
	}

	public Long getFailAllianceId() {
		return failAllianceId;
	}

	public void setFailAllianceId(Long failAllianceId) {
		this.failAllianceId = failAllianceId;
	}

	public int getFailAllianceServerId() {
		return failAllianceServerId;
	}

	public void setFailAllianceServerId(int failAllianceServerId) {
		this.failAllianceServerId = failAllianceServerId;
	}

	public int getFailAlliancePoint() {
		return failAlliancePoint;
	}

	public void setFailAlliancePoint(int failAlliancePoint) {
		this.failAlliancePoint = failAlliancePoint;
	}

	public int getBattleServerId() {
		return battleServerId;
	}

	public void setBattleServerId(int battleServerId) {
		this.battleServerId = battleServerId;
	}

	public List<RoleGVGBattleVo> getVictoryRoleGVGBattleVos() {
		return victoryRoleGVGBattleVos;
	}

	public void setVictoryRoleGVGBattleVos(List<RoleGVGBattleVo> victoryRoleGVGBattleVos) {
		this.victoryRoleGVGBattleVos = victoryRoleGVGBattleVos;
	}

	public List<RoleGVGBattleVo> getFailRoleGVGBattleVos() {
		return failRoleGVGBattleVos;
	}

	public void setFailRoleGVGBattleVos(List<RoleGVGBattleVo> failRoleGVGBattleVos) {
		this.failRoleGVGBattleVos = failRoleGVGBattleVos;
	}

	public long getEndRecAwardTime() {
		return endRecAwardTime;
	}

	public void setEndRecAwardTime(long endRecAwardTime) {
		this.endRecAwardTime = endRecAwardTime;
	}

	@Override
	public void setPersistKey(Long id) {
		this.id = id;
	}

	@Override
	public Long getPersistKey() {
		return id;
	}

	@Override
	public Long getGroupingId() {
		return id;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public void addRoleGVGBattleVos(RoleGVGBattleVo roleGVGBattleVo, Long allianceId) {
		if (roleGVGBattleVo != null) {
			if (victoryAllianceId.equals(allianceId)) {
				if (victoryRoleGVGBattleVos == null) {
					victoryRoleGVGBattleVos = new ArrayList<>();
				}
				victoryRoleGVGBattleVos.add(roleGVGBattleVo);
			} else if (failAllianceId.equals(allianceId)) {
				if (failRoleGVGBattleVos == null) {
					failRoleGVGBattleVos = new ArrayList<>();
				}
				failRoleGVGBattleVos.add(roleGVGBattleVo);
			}
		}
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public int getTurn() {
		return turn;
	}

	public void setTurn(int turn) {
		this.turn = turn;
	}

	public boolean isSendAward() {
		return sendAward;
	}

	public void setSendAward(boolean sendAward) {
		this.sendAward = sendAward;
	}

	public boolean isBye() {
		return bye;
	}

	public void setBye(boolean bye) {
		this.bye = bye;
	}

	public long getActivityId() {
		return activityId;
	}

	public void setActivityId(long activityId) {
		this.activityId = activityId;
	}

	public int getActivityRound() {
		return activityRound;
	}

	public void setActivityRound(int activityRound) {
		this.activityRound = activityRound;
	}

	public GvgMatchType getMatchType() {
		return matchType;
	}

	public void setMatchType(GvgMatchType matchType) {
		this.matchType = matchType;
	}

	public int getGameRound() {
		return gameRound;
	}

	public void setGameRound(int gameRound) {
		this.gameRound = gameRound;
	}

	public boolean isStop() {
		return stop;
	}

	public void setStop(boolean stop) {
		this.stop = stop;
	}

	public boolean isSendAwardReady() {
		return sendAwardReady;
	}

	public void setSendAwardReady(boolean sendAwardReady) {
		this.sendAwardReady = sendAwardReady;
	}
}
