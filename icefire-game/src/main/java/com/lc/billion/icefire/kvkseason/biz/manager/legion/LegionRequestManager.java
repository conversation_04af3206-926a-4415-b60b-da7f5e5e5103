package com.lc.billion.icefire.kvkseason.biz.manager.legion;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.manager.IManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionDao;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionRequestDao;
import com.lc.billion.icefire.kvkseason.biz.model.legion.Legion;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionRequest;
import com.lc.billion.icefire.kvkseason.biz.service.impl.legion.LegionOutput;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 *
 */
@Component
public class LegionRequestManager implements IManager {

	@Autowired
	private LegionRequestDao legionRequestDao;
	@Autowired
	private LegionDao legionDao;
	@Autowired
	private RoleManager roleManager;

	public List<LegionRequest> findLegionRequest(Long legionId, int type) {
		Map<Integer, List<LegionRequest>> map = legionRequestDao.findByLegionId(legionId);
		if (map == null) {
			return null;
		}
		return map.get(type);

	}

	public LegionRequest findLegionRequest(Long legionId, Long allianceId, int type) {
		return legionRequestDao.findByLegionIdAndAllianceIdAndType(legionId, allianceId, type);
	}

	public LegionRequest createLegionRequest(Role legionLeader, Long legionId, Long allianceId, int type) {
		int db = Application.getServerId();
		LegionRequest legionRequest = legionRequestDao.create(db, legionId, allianceId, type);
		if (legionLeader != null) {
			// 军团长推送军团请求红点
			List<LegionRequest> legionRequests = findLegionRequest(legionId, LegionRequest.TYPE_REQUEST);
			int requestSize = legionRequests == null ? 0 : legionRequests.size();
			legionLeader.send(LegionOutput.toGcLegionRequestCount(requestSize));
		}
		return legionRequest;
	}

	public List<LegionRequest> findByAllianceId(int type, Long allianceId) {
		Map<Integer, List<LegionRequest>> legionMaps = legionRequestDao.findByAllianceId(allianceId);
		if (JavaUtils.bool(legionMaps)) {
			return legionMaps.get(type);
		}
		return Collections.emptyList();
	}

	public void deleteLegionRequest(Role legionLeader, Long legionId, Long allianceId, int type) {
		List<LegionRequest> find = findByAllianceId(type, allianceId);
		if (JavaUtils.bool(find)) {
			// TODO 玩家联盟申请删除-个人版
			// GcAllianceRequestDeleteForRole allianceRequestDeleteForRole = new
			// GcAllianceRequestDeleteForRole();
			List<LegionRequest> removes = new ArrayList<>();
			for (LegionRequest request : find) {
				if (request.getLegionId().equals(legionId)) {
					removes.add(request);
					if (type == LegionRequest.TYPE_INVITE) {
						// allianceRequestDeleteForRole.addToInviteAllianceIds(allianceId);
					} else if (type == LegionRequest.TYPE_REQUEST) {
						// allianceRequestDeleteForRole.addToRequestAllianceIds(allianceId);
					}
				}
			}
			legionRequestDao.delete(removes);
			// TODO 通知本人
			// Role role = roleDao.findById(roleId);
			// if (role != null && role.isOnline()) {
			// role.send(allianceRequestDeleteForRole);
			// }
			if (type == LegionRequest.TYPE_REQUEST) {
				// 只有请求才需要通知军团
				if (legionLeader != null) {
					// 军团长推送军团请求红点
					List<LegionRequest> legionRequests = findLegionRequest(legionId, LegionRequest.TYPE_REQUEST);
					int requestSize = legionRequests == null ? 0 : legionRequests.size();
					legionLeader.send(LegionOutput.toGcLegionRequestCount(requestSize));
				}
			}
		}
	}

	public void deleteLegionRequest(Role legionLeader, Long legionId, int type) {
		List<LegionRequest> find = findLegionRequest(legionId, type);
		if (JavaUtils.bool(find)) {
			// TODO 玩家联盟申请删除-个人版
			// GcAllianceRequestDeleteForRole allianceRequestDeleteForRole = new
			// GcAllianceRequestDeleteForRole();
			List<LegionRequest> removes = new ArrayList<>();
			for (LegionRequest request : find) {
				if (request.getLegionId().equals(legionId)) {
					removes.add(request);
					if (type == LegionRequest.TYPE_INVITE) {
						// allianceRequestDeleteForRole.addToInviteAllianceIds(allianceId);
					} else if (type == LegionRequest.TYPE_REQUEST) {
						// allianceRequestDeleteForRole.addToRequestAllianceIds(allianceId);
					}
				}
			}
			legionRequestDao.delete(removes);
			// TODO 通知本人
			// Role role = roleDao.findById(roleId);
			// if (role != null && role.isOnline()) {
			// role.send(allianceRequestDeleteForRole);
			// }
			if (type == LegionRequest.TYPE_REQUEST) {
				// 只有请求才需要通知军团
				if (legionLeader != null) {
					// 军团长推送军团请求红点
					List<LegionRequest> legionRequests = findLegionRequest(legionId, LegionRequest.TYPE_REQUEST);
					int requestSize = legionRequests == null ? 0 : legionRequests.size();
					legionLeader.send(LegionOutput.toGcLegionRequestCount(requestSize));
				}
			}
		}
	}

	public void deleteLegionRequest(Long allianceId, int type) {
		Map<Integer, List<LegionRequest>> legionRequests = legionRequestDao.findByAllianceId(allianceId);
		if (JavaUtils.bool(legionRequests)) {
			List<LegionRequest> list = legionRequests.get(type);
			if (list == null) {
				return;
			}
			Set<Long> legionIds = new HashSet<>();
			List<LegionRequest> removes = new ArrayList<>();
			list.forEach(request -> legionIds.add(request.getLegionId()));
			removes.addAll(list);
			legionRequestDao.delete(removes);
			if (type == LegionRequest.TYPE_REQUEST) {
				// 只有请求才需要通知军团
				legionIds.forEach(legionId -> {
					Legion legion = legionDao.findById(legionId);
					if (legion != null) {
						Long leaderRoleId = legion.getLeaderRoleId();
						if (JavaUtils.bool(leaderRoleId)) {
							Role role = roleManager.getRole(leaderRoleId);
							if (role != null && role.isOnline()) {
								// 军团长推送军团请求红点
								List<LegionRequest> findLegionRequest = findLegionRequest(legionId, LegionRequest.TYPE_REQUEST);
								int requestSize = findLegionRequest == null ? 0 : findLegionRequest.size();
								role.send(LegionOutput.toGcLegionRequestCount(requestSize));
							}
						}
					}
				});
			}
		}
	}

	public void deleteLegionRequest(Role allianceLeader, int type) {
		Map<Integer, List<LegionRequest>> legionRequests = legionRequestDao.findByAllianceId(allianceLeader.getAllianceId());
		if (JavaUtils.bool(legionRequests)) {
			List<LegionRequest> list = legionRequests.get(type);
			if (list == null) {
				return;
			}
			Set<Long> legionIds = new HashSet<>();
			List<LegionRequest> removes = new ArrayList<>();
			list.forEach(request -> legionIds.add(request.getLegionId()));
			removes.addAll(list);
			legionRequestDao.delete(removes);
			if (type == LegionRequest.TYPE_REQUEST) {
				// 只有请求才需要通知军团
				legionIds.forEach(legionId -> {
					Legion legion = legionDao.findById(legionId);
					if (legion != null) {
						Long leaderRoleId = legion.getLeaderRoleId();
						if (JavaUtils.bool(leaderRoleId)) {
							Role role = roleManager.getRole(leaderRoleId);
							if (role != null && role.isOnline()) {
								// 军团长推送军团请求红点
								List<LegionRequest> findLegionRequest = findLegionRequest(legionId, LegionRequest.TYPE_REQUEST);
								int requestSize = findLegionRequest == null ? 0 : findLegionRequest.size();
								role.send(LegionOutput.toGcLegionRequestCount(requestSize));
							}
						}
					}
				});
			}
		}
	}

	public void deleteLegionRequest(Role allianceLeader, long legionId, int type) {
		LegionRequest legionRequest = legionRequestDao.findByLegionIdAndAllianceIdAndType(legionId, allianceLeader.getAllianceId(), type);
		if (legionRequest != null) {
			legionRequestDao.delete(legionRequest);
			if (type == LegionRequest.TYPE_REQUEST) {
				// 只有请求才需要通知军团
				Legion legion = legionDao.findById(legionId);
				if (legion != null) {
					Long leaderRoleId = legion.getLeaderRoleId();
					if (JavaUtils.bool(leaderRoleId)) {
						Role role = roleManager.getRole(leaderRoleId);
						if (role != null && role.isOnline()) {
							// 军团长推送军团请求红点
							List<LegionRequest> findLegionRequest = findLegionRequest(legionId, LegionRequest.TYPE_REQUEST);
							int requestSize = findLegionRequest == null ? 0 : findLegionRequest.size();
							role.send(LegionOutput.toGcLegionRequestCount(requestSize));
						}
					}
				}
			}
		}
	}
}
