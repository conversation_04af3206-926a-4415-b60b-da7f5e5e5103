package com.lc.billion.icefire.game.msg.handler.impl.buildsearch;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.buildsearch.BuildSearchService;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgBuildSearchProgress;
import com.lc.billion.icefire.protocol.GcBuildSearchProgress;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;

@Slf4j
@Controller
public class CgBuildSearchProgressHandler extends CgAbstractMessageHandler<CgBuildSearchProgress> {
    
    @Autowired
    private BuildSearchService buildSearchService;

    @Override
    protected void handle(Role role, CgBuildSearchProgress message) {
        try {
            int buildId = message.getBuildId();
            
            log.debug("处理建筑探索进度查询请求: roleId={}, buildId={}", role.getId(), buildId);
            
            // 发送建筑探索进度响应
            sendBuildSearchProgressResponse(role, buildId);
            
        } catch (Exception e) {
            log.error("处理建筑探索进度查询请求异常: roleId={}, message={}", role.getId(), message, e);
        }
    }
    
    /**
     * 发送建筑探索进度响应消息
     * @param role 角色
     * @param buildId 建筑ID
     */
    private void sendBuildSearchProgressResponse(Role role, int buildId) {
        // 获取建筑探索进度
        var progressData = buildSearchService.getBuildSearchProgress(role, buildId);
        
        // 创建响应消息
        var response = new GcBuildSearchProgress();
        response.setBuildId(buildId);
        
        if (progressData != null) {
            response.setCompletedPointIds(new ArrayList<>(progressData.getCompletedPoints()));
            response.setLastSearchPointId(progressData.getLastCompletedPoint());
            log.debug("建筑探索进度查询成功: roleId={}, buildId={}, completedCount={}", 
                     role.getId(), buildId, response.getCompletedPointIds().size());
        } else {
            log.warn("建筑探索进度查询失败，建筑配置不存在: roleId={}, buildId={}", role.getId(), buildId);
            response.setCompletedPointIds(new ArrayList<>());
            response.setLastSearchPointId(0);
        }
        
        // 发送响应消息
        role.send(response);
    }
}
