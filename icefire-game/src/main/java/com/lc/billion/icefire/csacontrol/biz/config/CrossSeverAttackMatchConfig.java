package com.lc.billion.icefire.csacontrol.biz.config;

import com.alibaba.fastjson.JSON;
import com.lc.billion.icefire.core.common.RandomUtils;
import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 跨服抢城匹配配置
 *
 * <AUTHOR>
 */
@Config(name = "CrossSeverAttackMatch", metaClass = CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta.class)
public class CrossSeverAttackMatchConfig {

	private static final Logger logger = LoggerFactory.getLogger(CrossSeverAttackMatchConfig.class);

	@MetaMap
	private Map<String, CrossSeverAttackMatchMeta> dataById;

	// activityId -> List<metaId>
	private Map<String, List<String>> groupByActivity = new HashMap<>();

	// 默认配置
	private List<String> defaultConfigIdList = new ArrayList<>();

	// 活动指定默认配置  key : activityId
	private Map<String, List<String>> defaultConfigMap = new HashMap<>();

	public void init(List<CrossSeverAttackMatchMeta> list) {

		dataById = new HashMap<>();

		for (CrossSeverAttackMatchMeta meta : list) {

			dataById.put(meta.getId(), meta);

			String activityId = meta.getActivityListId();
			if (meta.getGroupType() == 0) {

				if (StringUtils.isEmpty(activityId)) {
					this.defaultConfigIdList.add(meta.getId());
				} else {
					this.defaultConfigMap.compute(activityId, (k, v) -> v == null ? new ArrayList<>() : v).add(meta.getId());
				}

			} else {

				this.groupByActivity.compute(activityId, (k, v) -> v == null ? new ArrayList<>() : v).add(meta.getId());
			}
		}

		if (this.defaultConfigIdList.size() == 0) {
			throw new AlertException("default config is lack");
		}

		for (Map.Entry<String, List<String>> entry : this.defaultConfigMap.entrySet()) {

			if (entry.getValue() != null && entry.getValue().size() != this.defaultConfigIdList.size()) {
				throw new AlertException("activity default config is lack","activity",entry.getKey());
			}
		}

		logger.debug("CrossSeverAttackMatchConfig data = {}", JSON.toJSONString(dataById));
		logger.debug("CrossSeverAttackMatchConfig groupByActivity = {}", JSON.toJSONString(groupByActivity));
	}

	public CrossSeverAttackMatchMeta getMeta(String metaId) {
		return dataById.get(metaId);
	}

	public Collection<CrossSeverAttackMatchMeta> getAllMeta() {
		return dataById.values();
	}

	public Map<String, CrossSeverAttackMatchMeta> getDataById() {
		return dataById;
	}

	public void setDataById(Map<String, CrossSeverAttackMatchMeta> dataById) {
		this.dataById = dataById;
	}

	public Map<String, List<String>> getGroupByActivity() {
		return groupByActivity;
	}

	public void setGroupByActivity(Map<String, List<String>> groupByActivity) {
		this.groupByActivity = groupByActivity;
	}

	public List<String> getDefaultConfigIdList() {
		return defaultConfigIdList;
	}

	public void setDefaultConfigIdList(List<String> defaultConfigIdList) {
		this.defaultConfigIdList = defaultConfigIdList;
	}

	public Map<String, List<String>> getDefaultConfigMap() {
		return defaultConfigMap;
	}

	public void setDefaultConfigMap(Map<String, List<String>> defaultConfigMap) {
		this.defaultConfigMap = defaultConfigMap;
	}

	public static class CrossSeverAttackMatchMeta extends AbstractMeta {

		/**
		 * 活动id
		 */
		private String activityListId;
		/**
		 * 比赛时间
		 */
		@JsonIgnore
		private long battleStartTime;

		/**
		 * 对战服务器
		 *
		 * 由于服务器压力过大，此字段可能是运行过程中重新生成的，代码使用这个字段时需要进行检查
		 */
		@JsonIgnore
		private List<Integer> battleServerList = new ArrayList<>();

		/**
		 * 匹配时间组
		 */
        @JsonIgnore
		private List<Long> canChooseTime = new ArrayList<>();

		/**
		 * 匹配时间选择数量
		 */
		private int matchTimeSelectNum;

		/**
		 * 是否分组
		 * @param jsonNode
		 */
		private int groupType;

		@Override
		public void init(JsonNode jsonNode) {

			canChooseTime = MetaUtils.parseLongList(jsonNode.get("matchTimeGroup").asText(), META_SEPARATOR_2);

			// 分组处理
			if (groupType == 1) {

				battleServerList = MetaUtils.parseIntegerList(jsonNode.get("matchSever").asText(), META_SEPARATOR_2);

				if (battleServerList.isEmpty()) {
					ErrorLogUtil.errorLog("match config error", "metaId",this.id);
				}
			}
		}

		public CrossSeverAttackMatchMeta copy(CrossSeverAttackMatchMeta targetConfig) {

			CrossSeverAttackMatchMeta result = new CrossSeverAttackMatchMeta();

			result.setId(targetConfig.getId());
			result.setServer(this.getServer());
			result.setActivityListId(this.activityListId);
			result.setBattleStartTime(this.battleStartTime);
			result.setBattleServerList(new ArrayList<>(targetConfig.getBattleServerList()));
			result.setCanChooseTime(new ArrayList<>(targetConfig.getCanChooseTime()));
			result.setMatchTimeSelectNum(this.matchTimeSelectNum);
			result.setGroupType(1);

			return  result;
		}

		public CrossSeverAttackMatchMeta copy(String matchConfigId, List<Integer> battleServerList, List<Long> canChooseTime) {

			CrossSeverAttackMatchMeta result = new CrossSeverAttackMatchMeta();
			result.setId(matchConfigId);
			result.setServer(this.getServer());
			result.setActivityListId(this.activityListId);
			result.setBattleStartTime(this.battleStartTime);
			result.setBattleServerList(new ArrayList<>(battleServerList));
			result.setCanChooseTime(new ArrayList<>(canChooseTime));
			result.setMatchTimeSelectNum(this.matchTimeSelectNum);
			result.setGroupType(1);

			return  result;
		}

		public String getActivityListId() {
			return activityListId;
		}

		public void setActivityListId(String activityListId) {
			this.activityListId = activityListId;
		}

		public long getBattleStartTime() {
			return battleStartTime;
		}

		public void setBattleStartTime(long battleStartTime) {
			this.battleStartTime = battleStartTime;
		}

		public List<Long> getCanChooseTime() {
			return canChooseTime;
		}

		public void setCanChooseTime(List<Long> canChooseTime) {
			this.canChooseTime = canChooseTime;
		}

		public int getMatchTimeSelectNum() {
			return matchTimeSelectNum;
		}

		public void setMatchTimeSelectNum(int matchTimeSelectNum) {
			this.matchTimeSelectNum = matchTimeSelectNum;
		}

		public List<Integer> getBattleServerList() {
			return battleServerList;
		}

		public void setBattleServerList(List<Integer> battleServerList) {
			this.battleServerList = battleServerList;
		}

		public int getGroupType() {
			return groupType;
		}

		public void setGroupType(int groupType) {
			this.groupType = groupType;
		}
	}
}
