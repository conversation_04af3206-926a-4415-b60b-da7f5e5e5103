package com.lc.billion.icefire.gvgcontrol.biz.service.rpc.impl;

import java.util.ArrayList;
import java.util.List;

import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GVGAllianceSignUpInfoDao;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GvgBattleServerDispatchRecordDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGAllianceSignUpInfo;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgBattleServerDispatchRecord;
import com.lc.billion.icefire.gvgcontrol.biz.service.GVGControlService;
import com.lc.billion.icefire.rpc.service.gvg.IGVGBattleRemoteGVGControlService;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGAllianceSignUpInfoVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGBattleRecordVo;
import com.lc.billion.icefire.rpc.vo.gvg.GvgBattleServerDispatchRecordVo;

/**
 * GVG战斗服到GVG中控服
 * 
 * <AUTHOR>
 *
 */
@Service
public class GVGBattleRemoteGVGControlServiceImpl implements IGVGBattleRemoteGVGControlService {

	private static final Logger logger = LoggerFactory.getLogger(GVGBattleRemoteGVGControlServiceImpl.class);

	@Autowired
	private GvgBattleServerDispatchRecordDao gvgBattleServerDispatchRecordDao;
	@Autowired
	private GVGControlService gvgControlService;
	@Autowired
	private ActivityDao activityDao;
	@Autowired
	private GVGAllianceSignUpInfoDao gvgAllianceSignUpInfoDao;

	@Override
	public GvgBattleServerDispatchRecordVo findGvgBattleServerDispatchRecord(int gvgBattleServerId) {
		logger.info("战斗服主动拉取对阵信息start，{}", gvgBattleServerId);
		GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord = gvgBattleServerDispatchRecordDao.findById(Long.valueOf(gvgBattleServerId));
		if (gvgBattleServerDispatchRecord != null) {
			logger.info("信息返回");
			return new GvgBattleServerDispatchRecordVo(gvgBattleServerDispatchRecord);
		}
		logger.info("信息空");
		return null;
	}

	@Override
	public String uploadGVGBattleRecord(GVGBattleRecordVo gvgBattleRecordVo) {
		gvgControlService.uploadGVGBattleRecord(gvgBattleRecordVo);
		return null;
	}

	@Override
	public ActivityVo findActivityVo() {
		Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG);
		if (activity == null) {
			return null;
		}
		return new ActivityVo(activity);
	}

	@Override
	public GvgBattleServerDispatchRecordVo registerGVGBattleServerToGVGControlServer(int serverId) {
		return gvgControlService.updateAndBroadcastGvgBattleServerDispatchRecord(serverId);
	}

	@Override
	public List<GVGAllianceSignUpInfoVo> findGVGAllianceSignUpInfoVo(int battleServerId) {
		List<GVGAllianceSignUpInfoVo> ret = new ArrayList<>();
		GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord = gvgBattleServerDispatchRecordDao.findById(Long.valueOf(battleServerId));
		if (gvgBattleServerDispatchRecord == null) {
			ErrorLogUtil.errorLog("战斗服未分配对阵信息就启动了","battleServerId",battleServerId);
		} else {
			var gvgAllianceSignUpInfo = gvgAllianceSignUpInfoDao.findByMatchType(gvgBattleServerDispatchRecord.getMatchType(), gvgBattleServerDispatchRecord.getAllianceId1());
			if (gvgAllianceSignUpInfo != null) {
				ret.add(new GVGAllianceSignUpInfoVo(gvgAllianceSignUpInfo));
			}

			var gvgAllianceSignUpInfo2 = gvgAllianceSignUpInfoDao.findByMatchType(gvgBattleServerDispatchRecord.getMatchType(), gvgBattleServerDispatchRecord.getAllianceId2());
			if (gvgAllianceSignUpInfo2 != null) {
				ret.add(new GVGAllianceSignUpInfoVo(gvgAllianceSignUpInfo2));
			}
		}
		return ret;
	}

	@Override
	public void noticeDestroyBattleServer(int battleServerId) {
		gvgControlService.updateGvgBattleServerDispatchRecord(battleServerId);
	}

}
