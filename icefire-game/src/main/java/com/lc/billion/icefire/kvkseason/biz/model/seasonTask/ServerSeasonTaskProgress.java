package com.lc.billion.icefire.kvkseason.biz.model.seasonTask;

import java.util.HashMap;
import java.util.Map;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;

/**
 * 
 * 赛季任务 服务器相关的进度记录
 * 
 * <AUTHOR>
 * @date 2021/11/15
 */
public class ServerSeasonTaskProgress extends AbstractEntity {

	@MongoId
	private Long serverId;

	private Map<Integer, Integer> occupyCityTimes = new HashMap<>();// 占领王城次数 <level, 次数>
	private Map<Integer, Integer> occupyCity = new HashMap<>();// 占领王城状态 <level， 数量>, 赛季任务结算后 不更新

	@Override
	public void setPersistKey(Long id) {
		this.serverId = id;
	}

	@Override
	public Long getPersistKey() {
		return this.serverId;
	}

	@Override
	public Long getGroupingId() {
		return this.serverId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public int getOccupyCityTimes(int castleLevel) {
		if (occupyCityTimes == null)
			return 0;
		return occupyCityTimes.get(castleLevel) == null ? 0 : occupyCityTimes.get(castleLevel);
	}

	public void addOccupyCityTimes(int castleLevel) {
		if (occupyCityTimes == null)
			occupyCityTimes = new HashMap<>();
		occupyCityTimes.compute(castleLevel, (k, v) -> v == null ? 1 : v + 1);
	}

	public int getOccupyCityNum(int castleLevel) {
		if (occupyCity == null)
			return 0;
		return this.occupyCity.get(castleLevel) == null ? 0 : this.occupyCity.get(castleLevel);
	}

	public void addOccupyCity(int castleLevel) {
		if (occupyCity == null)
			occupyCity = new HashMap<>();
		occupyCity.compute(castleLevel, (k, v) -> v == null ? 1 : v + 1);
	}

	public void delOccupyCity(int castleLevel) {
		if (occupyCity == null)
			occupyCity = new HashMap<>();
		occupyCity.compute(castleLevel, (k, v) -> v == null ? 0 : v - 1 < 0 ? 0 : v - 1);
	}

}
