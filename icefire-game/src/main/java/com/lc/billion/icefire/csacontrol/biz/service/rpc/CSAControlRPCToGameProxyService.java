package com.lc.billion.icefire.csacontrol.biz.service.rpc;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.service.AbstractRPCProxyService;
import com.lc.billion.icefire.rpc.service.crossalliance.ICSAControlRemoteGameService;
import com.longtech.cod.rpc.client.RpcClient;
import com.longtech.cod.rpc.client.RpcProxyBuilder;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.GameServerConfig;

/**
 * <AUTHOR>
 *
 */
@Service
public class CSAControlRPCToGameProxyService extends AbstractRPCProxyService {

	private Map<Integer, RpcProxyBean<ICSAControlRemoteGameService>> csaControlRemoteServices = new HashMap<>();

	@Override
	protected ServerType[] getSrcServerType() {
		return new ServerType[] { ServerType.CSA_CONTROL };
	}

	@Override
	protected ServerType[] getTargetServerType() {
		return new ServerType[] { ServerType.GAME, ServerType.KVK_SEASON};
	}

	@Override
	protected boolean createRPCClient(GameServerConfig gameServerConfig) {
		RpcProxyBuilder<ICSAControlRemoteGameService> rpcProxyBuilder = RpcProxyBuilder.create(ICSAControlRemoteGameService.class).connect(getSerializer(),
				gameServerConfig.getRpcIp(), gameServerConfig.getRpcPort());
		RpcClient rpcClient = rpcProxyBuilder.createRpcClient();
		ICSAControlRemoteGameService service = rpcProxyBuilder.buildSync(rpcClient, getTimeOutMills(), getRetryTimes(), createWait());
		RpcProxyBean<ICSAControlRemoteGameService> rpcProxyBean = new RpcProxyBean<ICSAControlRemoteGameService>(service, rpcClient);

		ServerType serverType = gameServerConfig.serverType();
		if (serverType == ServerType.GAME) {
			csaControlRemoteServices.put(gameServerConfig.getGameServerId(), rpcProxyBean);
			logger.info("rpc CSA_CONTROL to GAME {}->{},{}:{}", Application.getServerId(), gameServerConfig.getGameServerId(), gameServerConfig.getRpcIp(),
					gameServerConfig.getRpcPort());
		} else if (serverType == ServerType.KVK_SEASON) {
			KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getLsConfig().getKvkSeasons().getServerGroupByKServerId(gameServerConfig.getGameServerId());
			if (kvkSeasonServerGroupConfig == null) {
				ErrorLogUtil.errorLog("赛季服的KvkSeasonServerGroupConfig找不到", "serverId",gameServerConfig.getGameServerId());
			} else {
				Set<Integer> oServerIds = kvkSeasonServerGroupConfig.getOServerIds();
				// gvgControlRemoteServiceSet.add(rpcProxyBean);
				for (Integer serverId : oServerIds) {
					csaControlRemoteServices.put(serverId, rpcProxyBean);
					logger.info("rpc CSA_CONTROL to O_GAME {}->{},{}:{}", Application.getServerId(), serverId, gameServerConfig.getRpcIp(), gameServerConfig.getRpcPort());
				}
			}
		} else {
			throw new AlertException("CSA_CONTROL to当前服务器类型的RPC建立在这里是不被允许的","serverType",serverType);
		}

		return true;
	}

	@Override
	protected void rpcIpChanged(GameServerConfig gameServerConfig) {
		RpcProxyBean<ICSAControlRemoteGameService> rpcProxyBean = csaControlRemoteServices.get(gameServerConfig.getGameServerId());
		if (rpcProxyBean != null) {
			RpcClient rpcClient = rpcProxyBean.getRpcClient();
			rpcClient.setStop(true);
		}
		logger.info("rpcIpChanged {}", gameServerConfig.getGameServerId());
		createRPCClient(gameServerConfig);
	}

	@Override
	protected void rpcPortChanged(GameServerConfig gameServerConfig) {
		logger.info("rpcPortChanged {}", gameServerConfig.getGameServerId());
		rpcIpChanged(gameServerConfig);
	}

	@Override
	protected void removeRPCClient(int serverId) {

		ServerType serverType = configCenter.getServerType(serverId);
		if (serverType == ServerType.GAME) {
			RpcProxyBean<ICSAControlRemoteGameService> rpcProxyBean = csaControlRemoteServices.remove(serverId);
			if (rpcProxyBean != null) {
				RpcClient rpcClient = rpcProxyBean.getRpcClient();
				rpcClient.setStop(true);
				logger.info("CSA control rpc to game 移除{} 不空", serverId);
			} else {
				logger.info("CSA control rpc to game 移除{} 空", serverId);
			}
		} else if (serverType == ServerType.KVK_SEASON) {
			KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getLsConfig().getKvkSeasons().getServerGroupByKServerId(serverId);
			if (kvkSeasonServerGroupConfig == null) {
				ErrorLogUtil.errorLog("赛季服的KvkSeasonServerGroupConfig找不到", "serverId",serverId);
			} else {
				Set<Integer> oServerIds = kvkSeasonServerGroupConfig.getOServerIds();
				for (Integer oServerId : oServerIds) {
					RpcProxyBean<ICSAControlRemoteGameService> rpcProxyBean = csaControlRemoteServices.remove(oServerId);
					if (rpcProxyBean != null) {
						RpcClient rpcClient = rpcProxyBean.getRpcClient();
						rpcClient.setStop(true);
						logger.info("rpc CSA_CONTROL to GAME 移除{} 不空", oServerId);
					} else {
						logger.info("rpc CSA_CONTROL to GAME 移除{} 空", oServerId);
					}
				}
			}
		} else {
			throw new AlertException("rpc CSA_CONTROL to GAME的RPC移除在这里是不被允许的","serverType",serverType);
		}
	}

	@Override
	protected boolean containsRPCClient(int serverId) {
		return csaControlRemoteServices.containsKey(serverId);
	}

	@Override
	protected boolean checkInstance() {
		return true;
	}

	@Override
	protected boolean createWait() {
		return false;
	}

	public ICSAControlRemoteGameService getCSAControlRemoteGameService(int serverId) {
		RpcProxyBean<ICSAControlRemoteGameService> rpcProxyBean = csaControlRemoteServices.get(serverId);
		if (rpcProxyBean != null) {
			return rpcProxyBean.getProxy();
		}
		return null;
	}

	public Map<Integer, ICSAControlRemoteGameService> getCSAControlRemoteGameServices() {
		Map<Integer, ICSAControlRemoteGameService> ret = new HashMap<>();
		for (Entry<Integer, RpcProxyBean<ICSAControlRemoteGameService>> entry : csaControlRemoteServices.entrySet()) {
			ret.put(entry.getKey(), entry.getValue().getProxy());
		}
		return ret;
	}

	public Map<Integer, RpcProxyBean<ICSAControlRemoteGameService>> getCsaControlRemoteServices() {
		return csaControlRemoteServices;
	}
}
