package com.lc.billion.icefire.kvkseason.biz.async;

import java.text.MessageFormat;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.support.HttpUtil;
import com.lc.billion.icefire.game.biz.async.asyncIO.AsyncIOThreadOperation;
import com.lc.billion.icefire.game.biz.async.asyncIO.IOConstant;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.kvkseason.biz.service.impl.legion.LegionService;
import com.lc.billion.icefire.protocol.GcPlayerInvalidChar;
import com.simfun.sgf.utils.MessageDigestUtils;

/**
 * 
 * 军团全体邮件
 * 
 * <AUTHOR>
 * @Date 2022/5/12
 */
public class LegionAllMemberMailCheckWordOperation extends AsyncIOThreadOperation {
	private Role role;
	private LegionService legionService;
	private String title;
	private String content;
	// 检查结果
	private int action = -1;

	public LegionAllMemberMailCheckWordOperation(Role role, LegionService legionService, String title, String content) {
		this.role = role;
		this.legionService = legionService;
		this.title = title;
		this.content = content;
	}

	@Override
	public boolean init() {
		return true;
	}

	@Override
	public boolean run() {
		String time = System.currentTimeMillis() + "";
		String md5 = MessageDigestUtils.md5(IOConstant.APP_ID + time + IOConstant.Key);
		String url = MessageFormat.format(IOConstant.URL, IOConstant.APP_ID, time, md5);
		JSONObject params = new JSONObject();
		params.put("message", title + content);
		try {
			String result = HttpUtil.postJSON(url, params.toString());
			JSONObject resultJson = JSONObject.parseObject(result);
			JSONObject resultData = JSONObject.parseObject(resultJson.getString("data"));
			if (resultData != null) {
				this.action = resultData.getInteger("action");
			}
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("LegionAllMemberMailCheckWordOperation send", e,
					"url",url, "content",title + content);
		}

		return true;
	}

	@Override
	public void finish() {
		if (action == -1 || action == 2) {
			role.send(new GcPlayerInvalidChar(title + content));
			return;
		}
		legionService.sendLegionAllEmail(role, title, content);
	}
}
