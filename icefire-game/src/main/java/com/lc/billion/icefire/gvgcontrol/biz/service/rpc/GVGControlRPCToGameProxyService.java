package com.lc.billion.icefire.gvgcontrol.biz.service.rpc;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.service.AbstractRPCProxyService;
import com.lc.billion.icefire.rpc.service.gvg.IGVGControlRemoteGameService;
import com.longtech.cod.rpc.client.RpcClient;
import com.longtech.cod.rpc.client.RpcProxyBuilder;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.GameServerConfig;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;

/**
 * <AUTHOR>
 *
 */
@Service
public class GVGControlRPCToGameProxyService extends AbstractRPCProxyService {

	private Map<Integer, RpcProxyBean<IGVGControlRemoteGameService>> gvgControlRemoteServices = new HashMap<>();

	// private Set<RpcProxyBean<IGVGControlRemoteGameService>>
	// gvgControlRemoteServiceSet = new HashSet<>();

	@Override
	protected ServerType[] getSrcServerType() {
		return new ServerType[] { ServerType.GVG_CONTROL };
	}

	@Override
	protected ServerType[] getTargetServerType() {
		return new ServerType[] { ServerType.GAME, ServerType.KVK_SEASON };
	}

	@Override
	protected boolean createRPCClient(GameServerConfig gameServerConfig) {
		RpcProxyBuilder<IGVGControlRemoteGameService> rpcProxyBuilder = RpcProxyBuilder.create(IGVGControlRemoteGameService.class).connect(getSerializer(),
				gameServerConfig.getRpcIp(), gameServerConfig.getRpcPort());
		RpcClient rpcClient = rpcProxyBuilder.createRpcClient();
		IGVGControlRemoteGameService service = rpcProxyBuilder.buildSync(rpcClient, getTimeOutMills(), getRetryTimes(), createWait());
		RpcProxyBean<IGVGControlRemoteGameService> rpcProxyBean = new RpcProxyBean<IGVGControlRemoteGameService>(service, rpcClient);

		ServerType serverType = gameServerConfig.serverType();
		if (serverType == ServerType.GAME) {
			gvgControlRemoteServices.put(gameServerConfig.getGameServerId(), rpcProxyBean);
			// gvgControlRemoteServiceSet.add(rpcProxyBean);
			logger.info("rpc GVG_CONTROL to GAME {}->{},{}:{}", Application.getServerId(), gameServerConfig.getGameServerId(), gameServerConfig.getRpcIp(),
					gameServerConfig.getRpcPort());
		} else if (serverType == ServerType.KVK_SEASON) {
			KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getLsConfig().getKvkSeasons().getServerGroupByKServerId(gameServerConfig.getGameServerId());
			if (kvkSeasonServerGroupConfig == null) {
				ErrorLogUtil.errorLog("赛季服的KvkSeasonServerGroupConfig找不到", "serverId",gameServerConfig.getGameServerId());
			} else {
				Set<Integer> oServerIds = kvkSeasonServerGroupConfig.getOServerIds();
				// gvgControlRemoteServiceSet.add(rpcProxyBean);
				for (Integer serverId : oServerIds) {
					gvgControlRemoteServices.put(serverId, rpcProxyBean);
					logger.info("rpc GVG_CONTROL to OGAME {}->{},{}:{}", Application.getServerId(), serverId, gameServerConfig.getRpcIp(), gameServerConfig.getRpcPort());
				}
			}
		} else {
			throw new AlertException("GVG_CONTROL to的RPC建立在这里是不被允许的","serverType",serverType);
		}

		return true;
	}

	@Override
	protected void rpcIpChanged(GameServerConfig gameServerConfig) {
		RpcProxyBean<IGVGControlRemoteGameService> rpcProxyBean = gvgControlRemoteServices.get(gameServerConfig.getGameServerId());
		if (rpcProxyBean != null) {
			RpcClient rpcClient = rpcProxyBean.getRpcClient();
			rpcClient.setStop(true);
		}
		logger.info("rpcIpChanged {}", gameServerConfig.getGameServerId());
		createRPCClient(gameServerConfig);
	}

	@Override
	protected void rpcPortChanged(GameServerConfig gameServerConfig) {
		logger.info("rpcPortChanged {}", gameServerConfig.getGameServerId());
		rpcIpChanged(gameServerConfig);
	}

	@Override
	protected void removeRPCClient(int serverId) {
		ServerType serverType = configCenter.getServerType(serverId);
		if (serverType == ServerType.GAME) {
			RpcProxyBean<IGVGControlRemoteGameService> rpcProxyBean = gvgControlRemoteServices.remove(serverId);
			if (rpcProxyBean != null) {
				RpcClient rpcClient = rpcProxyBean.getRpcClient();
				rpcClient.setStop(true);
				logger.info("rpc GVG_CONTROL to GAME 移除{} 不空", serverId);
			} else {
				logger.info("rpc GVG_CONTROL to GAME 移除{} 空", serverId);
			}
		} else if (serverType == ServerType.KVK_SEASON) {
			KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getLsConfig().getKvkSeasons().getServerGroupByKServerId(serverId);
			if (kvkSeasonServerGroupConfig == null) {
				ErrorLogUtil.errorLog("赛季服的KvkSeasonServerGroupConfig找不到", "serverId",serverId);
			} else {
				Set<Integer> oServerIds = kvkSeasonServerGroupConfig.getOServerIds();
				for (Integer oServerId : oServerIds) {
					RpcProxyBean<IGVGControlRemoteGameService> rpcProxyBean = gvgControlRemoteServices.remove(oServerId);
					if (rpcProxyBean != null) {
						RpcClient rpcClient = rpcProxyBean.getRpcClient();
						rpcClient.setStop(true);
						logger.info("rpc GVG_CONTROL to GAME 移除{} 不空", oServerId);
					} else {
						logger.info("rpc GVG_CONTROL to GAME 移除{} 空", oServerId);
					}
				}
			}
		} else {
			throw new AlertException("rpc GVG_CONTROL to GAME的RPC移除在这里是不被允许的","serverType",serverType);
		}
	}

	// @Override
	// protected void clearRPCClient() {
	// gvgControlRemoteServices.clear();
	// logger.info("GVG control rpc to game 节点关服清理");
	// }

	@Override
	protected boolean containsRPCClient(int serverId) {
		return gvgControlRemoteServices.containsKey(serverId);
	}

	@Override
	protected boolean checkInstance() {
		return true;
	}

	@Override
	protected boolean createWait() {
		return false;
	}

	public IGVGControlRemoteGameService getGVGControlRemoteGameService(int serverId) {
		RpcProxyBean<IGVGControlRemoteGameService> rpcProxyBean = gvgControlRemoteServices.get(serverId);
		if (rpcProxyBean != null) {
			return rpcProxyBean.getProxy();
		}
		return null;
	}

	public Map<Integer, IGVGControlRemoteGameService> getGVGControlRemoteGameServices() {
		Map<Integer, IGVGControlRemoteGameService> ret = new HashMap<>();
		for (Entry<Integer, RpcProxyBean<IGVGControlRemoteGameService>> entry : gvgControlRemoteServices.entrySet()) {
			ret.put(entry.getKey(), entry.getValue().getProxy());
		}
		return ret;
	}

	// public Collection<IGVGControlRemoteGameService>
	// getGVGControlRemoteGameServices() {
	// if (JavaUtils.bool(gvgControlRemoteServiceSet)) {
	// List<IGVGControlRemoteGameService> ret = new ArrayList<>();
	// gvgControlRemoteServiceSet.forEach(bean -> ret.add(bean.getProxy()));
	// return ret;
	// }
	// return Collections.emptyList();
	// }
	public RpcClient getGvgControlRemoteGameRpcClient(int serverId){
		RpcProxyBean<IGVGControlRemoteGameService> rpcProxyBean = gvgControlRemoteServices.get(serverId);
		if (rpcProxyBean != null) {
			return rpcProxyBean.getRpcClient();
		}
		return null;
	}
}
