package com.lc.billion.icefire.tvtcontrol.biz.service.rpc;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.service.AbstractRPCProxyService;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.rpc.service.tvt.ITvtControlRemoteGameService;
import com.longtech.cod.rpc.client.RpcClient;
import com.longtech.cod.rpc.client.RpcProxyBuilder;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.GameServerConfig;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * @author: maoqq
 * @Date: 2022/04/11 11:50 AM
 */
@Service
public class TvtControlRpcToGameProxyService extends AbstractRPCProxyService {

    private Map<Integer, RpcProxyBean<ITvtControlRemoteGameService>> tvtControlRemoteServices = new HashMap<>();

    @Override
    protected ServerType[] getSrcServerType() {
        return new ServerType[]{ServerType.TVT_CONTROL};
    }

    @Override
    protected ServerType[] getTargetServerType() {
        return new ServerType[]{ServerType.GAME, ServerType.KVK_SEASON};
    }

    @Override
    protected boolean createRPCClient(GameServerConfig gameServerConfig) {
        RpcProxyBuilder<ITvtControlRemoteGameService> rpcProxyBuilder = RpcProxyBuilder.create(ITvtControlRemoteGameService.class).connect(getSerializer(),
                gameServerConfig.getRpcIp(), gameServerConfig.getRpcPort());
        RpcClient rpcClient = rpcProxyBuilder.createRpcClient();
        ITvtControlRemoteGameService service = rpcProxyBuilder.buildSync(rpcClient, getTimeOutMills(), getRetryTimes(), createWait());
        RpcProxyBean<ITvtControlRemoteGameService> rpcProxyBean = new RpcProxyBean<ITvtControlRemoteGameService>(service, rpcClient);

        ServerType serverType = gameServerConfig.serverType();
        if (serverType == ServerType.GAME) {
            tvtControlRemoteServices.put(gameServerConfig.getGameServerId(), rpcProxyBean);
            logger.info("rpc TVT_CONTROL to GAME {}->{},{}:{}", Application.getServerId(), gameServerConfig.getGameServerId(), gameServerConfig.getRpcIp(),
                    gameServerConfig.getRpcPort());
        } else if (serverType == ServerType.KVK_SEASON) {
            KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getLsConfig().getKvkSeasons().getServerGroupByKServerId(gameServerConfig.getGameServerId());
            if (kvkSeasonServerGroupConfig == null) {
                ErrorLogUtil.errorLog("赛季服的KvkSeasonServerGroupConfig找不到", "serverId",gameServerConfig.getGameServerId());
            } else {
                Set<Integer> oServerIds = kvkSeasonServerGroupConfig.getOServerIds();

                for (Integer serverId : oServerIds) {
                    tvtControlRemoteServices.put(serverId, rpcProxyBean);
                    logger.info("rpc TVT_CONTROL to OGAME {}->{},{}:{}", Application.getServerId(), serverId, gameServerConfig.getRpcIp(), gameServerConfig.getRpcPort());
                }
            }
        } else {
            throw new AlertException("TVT_CONTROL to当前服务器类型的RPC建立在这里是不被允许的","serverType",serverType);
        }

        return true;
    }

    @Override
    protected void rpcIpChanged(GameServerConfig gameServerConfig) {
        RpcProxyBean<ITvtControlRemoteGameService> rpcProxyBean = tvtControlRemoteServices.get(gameServerConfig.getGameServerId());
        if (rpcProxyBean != null) {
            RpcClient rpcClient = rpcProxyBean.getRpcClient();
            rpcClient.setStop(true);
        }
        logger.info("rpcIpChanged {}", gameServerConfig.getGameServerId());
        createRPCClient(gameServerConfig);
    }

    @Override
    protected void rpcPortChanged(GameServerConfig gameServerConfig) {
        logger.info("rpcPortChanged {}", gameServerConfig.getGameServerId());
        rpcIpChanged(gameServerConfig);
    }

    @Override
    protected void removeRPCClient(int serverId) {
        ServerType serverType = configCenter.getServerType(serverId);
        if (serverType == ServerType.GAME) {
            RpcProxyBean<ITvtControlRemoteGameService> rpcProxyBean = tvtControlRemoteServices.remove(serverId);
            if (rpcProxyBean != null) {
                RpcClient rpcClient = rpcProxyBean.getRpcClient();
                rpcClient.setStop(true);
                logger.info("rpc TVT_CONTROL to GAME 移除{} 不空", serverId);
            } else {
                logger.info("rpc TVT_CONTROL to GAME 移除{} 空", serverId);
            }
        } else if (serverType == ServerType.KVK_SEASON) {
            KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getLsConfig().getKvkSeasons().getServerGroupByKServerId(serverId);
            if (kvkSeasonServerGroupConfig == null) {
                ErrorLogUtil.errorLog("赛季服的KvkSeasonServerGroupConfig找不到", "serverId",serverId);
            } else {
                Set<Integer> oServerIds = kvkSeasonServerGroupConfig.getOServerIds();
                for (Integer oServerId : oServerIds) {
                    RpcProxyBean<ITvtControlRemoteGameService> rpcProxyBean = tvtControlRemoteServices.remove(oServerId);
                    if (rpcProxyBean != null) {
                        RpcClient rpcClient = rpcProxyBean.getRpcClient();
                        rpcClient.setStop(true);
                        logger.info("rpc TVT_CONTROL to GAME 移除{} 不空", oServerId);
                    } else {
                        logger.info("rpc TVT_CONTROL to GAME 移除{} 空", oServerId);
                    }
                }
            }
        } else {
            throw new AlertException("rpc TVT_CONTROL to GAME的RPC移除在这里是不被允许的","serverType",serverType);
        }
    }

    @Override
    protected boolean containsRPCClient(int serverId) {
        return tvtControlRemoteServices.containsKey(serverId);
    }

    @Override
    protected boolean checkInstance() {
        return true;
    }

    @Override
    protected boolean createWait() {
        return false;
    }

    public int getRpcClientInstanceHashCodeByServerId(int serverId) {
        RpcProxyBean<ITvtControlRemoteGameService> rpcProxyBean = tvtControlRemoteServices.get(serverId);
        if (rpcProxyBean != null) {
            RpcClient rpcClient = rpcProxyBean.getRpcClient();
            if (rpcClient != null) {
                return rpcClient.hashCode();
            }
        }
        return -1;
    }

    public ITvtControlRemoteGameService getTvtControlRemoteGameService(int serverId) {
        RpcProxyBean<ITvtControlRemoteGameService> rpcProxyBean = tvtControlRemoteServices.get(serverId);
        if (rpcProxyBean != null) {
            return rpcProxyBean.getProxy();
        }
        return null;
    }

    public RpcClient getTvtControlRemoteGameRpcClient(int serverId){
        RpcProxyBean<ITvtControlRemoteGameService> rpcProxyBean = tvtControlRemoteServices.get(serverId);
        if (rpcProxyBean != null) {
            return rpcProxyBean.getRpcClient();
        }
        return null;
    }

    public Map<Integer, ITvtControlRemoteGameService> getTvtControlRemoteGameServices() {
        Map<Integer, ITvtControlRemoteGameService> ret = new HashMap<>();
        for (Map.Entry<Integer, RpcProxyBean<ITvtControlRemoteGameService>> entry : tvtControlRemoteServices.entrySet()) {
            ret.put(entry.getKey(), entry.getValue().getProxy());
        }
        return ret;
    }
}
