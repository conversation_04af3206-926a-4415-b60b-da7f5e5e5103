package com.lc.billion.icefire.game.biz.service.impl.recharge;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.ApiConstants;
import com.lc.billion.icefire.core.common.PlatformType;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.pay.TuConstants;
import com.lc.billion.icefire.core.support.HttpUtil;
import com.lc.billion.icefire.core.support.IDGenerator;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.async.recharge.DingDingNotify;
import com.lc.billion.icefire.game.biz.config.LiBaoConfig;
import com.lc.billion.icefire.game.biz.config.LiBaoConfig.LibaoMeta;
import com.lc.billion.icefire.game.biz.config.LibaoPriceConfig;
import com.lc.billion.icefire.game.biz.config.LibaoPriceConfig.LibaoPriceMeta;
import com.lc.billion.icefire.game.biz.config.SettingConfig;
import com.lc.billion.icefire.game.biz.exec.MainWorker;
import com.lc.billion.icefire.game.biz.manager.*;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.currency.CurrencyCostInfo;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.model.email.SystemEmail;
import com.lc.billion.icefire.game.biz.model.event.GameEventType;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.libao.LiBaoTagType;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.player.Player;
import com.lc.billion.icefire.game.biz.model.player.PlayerLoginContext;
import com.lc.billion.icefire.game.biz.model.recharge.RechargePayType;
import com.lc.billion.icefire.game.biz.model.record.type.RoleTotalRecordType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleDevice;
import com.lc.billion.icefire.game.biz.model.role.RoleExtra;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.alipay.AlipayReportServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.continuousrecharge.ContinuousRechargeServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionSwitchService;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionType;
import com.lc.billion.icefire.game.biz.service.impl.libao.LiBaoServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.snspush.StringUtils;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.game.support.LogReasons.ChargeLogReason;
import com.lc.billion.icefire.game.support.LogReasons.ItemLogReason;
import com.lc.billion.icefire.game.support.LogReasons.MoneyLogReason;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.constant.PsRewardClaimStatus;
import com.longtech.cod.rpc.server.IRpcResp;
import com.simfun.sgf.net.msg.InternalMessage;
import com.simfun.sgf.utils.JavaUtils;
import com.simfun.sgf.utils.MessageDigestUtils;
import com.simfun.sgf.utils.TimeUtils;
import com.simfun.sgf.utils.UuidUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * <AUTHOR>
 * @sine 2016年4月13日 下午8:58:44
 */
@Service
public class RechargeServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(RechargeServiceImpl.class);

    @Autowired
    private ServiceDependency srvDpd;

    @Autowired
    private ApplicationContext appCtx;

    @Autowired
    private ConfigServiceImpl configService;

    @Autowired
    private ContinuousRechargeServiceImpl conRechargeService;

    @Autowired
    private RoleCurrencyManager roleCurrencyManager;

    @Autowired
    private RoleDeviceManager roleDeviceManager;

    @Autowired
    private RoleExtraManager roleExtraManager;

    private Map<PlatformType, IPlatformAuthHandler> authHandlers;

    @Autowired
    private RoleManager roleManager;

    @Autowired
    private RoleRecordManager roleRecordManager;
    @Autowired
    private LiBaoGameConfigManager liBaoConfigManager;
    @Autowired
    private ServiceDependency srvDep;
    @Autowired
    private FunctionSwitchService functionSwitchService;
    @Autowired
    private AsyncOperationServiceImpl asyncOperationService;
    @Autowired
    private MainWorker mainWorker;
    @Autowired
    private LiBaoServiceImpl libaoService;

    @Autowired
    private AlipayReportServiceImpl alipayReportService;

    public void onEnterWorld(Role role) {
        sendRechargeInfo(role);
    }

    public void checkAndFixFirstRechargeConfig(Role role) {
        // 老玩家也根据创建时间获取一次首充配置
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getId());
        if (StringUtils.isBlank(roleExtra.getFirstRechargeConfig())) {
            var settingConfig = configService.getConfig(SettingConfig.class);
            var gameConfig = ServerConfigManager.getInstance().getGameConfig();
            RoleDevice roleDevice = roleDeviceManager.getRoleDevice(role.getPersistKey()); // 设置设备信息
            var deviceId = roleDevice != null ? Long.parseLong(roleDevice.getDeviceId()) : 0;
            var firstRechargeConfig = settingConfig.getFirstRechargeConfig(deviceId, role.getCurrentServerId(), role.getCreateTime(), gameConfig.getPlatform());
            roleExtra.setFirstRechargeConfig(firstRechargeConfig);
            roleExtraManager.saveRoleExtra(roleExtra);
            logger.info("checkAndFixFirstRechargeConfig roleId:{} deviceId:{} firstRechargeConfig:{} firstDay:{} secondDay:{}",
                    role.getRoleId(), deviceId, firstRechargeConfig,
                    settingConfig.getFirstRechargeRewards(firstRechargeConfig),
                    settingConfig.getFirstRechargeRewardsSecondDay(firstRechargeConfig));
        }
    }

    @PostConstruct
    public void init() {
        try {
            this.authHandlers = new EnumMap<>(PlatformType.class);
            Map<String, IPlatformAuthHandler> handlers = appCtx.getBeansOfType(IPlatformAuthHandler.class);
            for (IPlatformAuthHandler h : handlers.values()) {
                this.authHandlers.put(h.getType(), h);
            }
            this.authHandlers = Collections.unmodifiableMap(authHandlers);
        } catch (ExpectedException ignored) {

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("Recharge init CaughtException", e);
        }
    }

    public void onResetDayData(Role role) {
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getId());
        if (roleExtra.getFirstRechargeClaimStatus() == PsRewardClaimStatus.INVALID) {
            return;
        }

        // 如果未领取过奖励，就给玩家推送可以领取的数据
        if (roleExtra.getFirstRechargeSecondDayClaimStatus() == PsRewardClaimStatus.INVALID) {
            roleExtra.setFirstRechargeSecondDayClaimStatus(PsRewardClaimStatus.CAN_BE_CLAIMED);
            roleExtraManager.saveRoleExtra(roleExtra);
            sendRechargeInfo(role);

            // 推送
            srvDep.getPushHelper().firstRechargeRecall(role);
        }
    }

    public void onCreateRole(Role role, Player player) {
        var settingConfig = configService.getConfig(SettingConfig.class);
        var gameConfig = ServerConfigManager.getInstance().getGameConfig();
        PlayerLoginContext context = player.getLoginContext();
        var deviceId = 0L;
        if (context != null && context.getExtParam() != null) {
            deviceId = Long.parseLong(context.getExtParam().getDeviceId());
        }
        var firstRechargeConfig = settingConfig.getFirstRechargeConfig(deviceId, role.getCurrentServerId(), role.getCreateTime(), gameConfig.getPlatform());
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getId());
        roleExtra.setFirstRechargeClaimStatus(PsRewardClaimStatus.INVALID);
        roleExtra.setFirstRechargeSecondDayClaimStatus(PsRewardClaimStatus.INVALID);
        roleExtra.setFirstRechargeConfig(firstRechargeConfig);
        roleExtraManager.saveRoleExtra(roleExtra);
        logger.info("onCreateRole roleId:{} deviceId:{} firstRechargeConfig:{} firstDay:{} secondDay:{}",
                role.getRoleId(), deviceId, firstRechargeConfig,
                settingConfig.getFirstRechargeRewards(firstRechargeConfig),
                settingConfig.getFirstRechargeRewardsSecondDay(firstRechargeConfig));
    }

    /**
     * 调用方在RPC线程中，而发货要在主线程中运行，发货运行完后还得把结果返回给RPC调用方web服。所以用future来干。
     *
     * @param result
     * @return
     */
    public String runRechargeInMainWorker(IRpcResp resp, Role role, String orderId, String productId, float currency, int platform, ChargeLogReason reason, boolean isDiscount, String plat_orderId,
                                          String token, String packageName, int orderType, String startTime, String endTime, String extData, int season, JSONObject result) {
        mainWorker.put(new InternalMessage() {
            @Override
            public void execute() {
                try {
                    recharge(role, orderId, productId, currency, platform, reason, isDiscount, plat_orderId, token,
                            packageName, orderType, startTime, endTime, extData, season, result);
                    if (resp != null) {
                        resp.setResult(result.toJSONString());
                    }
                } catch (Exception e) {
                    if (!(e instanceof ExpectedException)) {
                        ErrorLogUtil.exceptionLog("RechargeOperation error", e, "role", role, "order", orderId,
                                "productId", productId, "currency", currency, "platform", platform, "reason", reason,
                                "isDiscount", isDiscount, "plat_orderId", plat_orderId, "token", token,
                                "packageName", packageName, "orderType", orderType, "startTme", startTime,
                                "endTime", endTime, "extData", extData);
                    }
                    result.put("msg", "RechargeOperation error");
                }
            }
        });

        return null;
    }


    /**
     * 对外充值接口（会调用doRecharge,默认calcDollar为true）
     */
    public void recharge(Role role, String orderId, String productId, float currency, int platform, ChargeLogReason reason, boolean isDiscount, String plat_orderId,
                         String token, String packageName, int orderType, String startTime, String endTime, String extData, int season, JSONObject result) {
        if (functionSwitchService.isOpen(FunctionType.LI_BAO)) {
            doRecharge(role, orderId, productId, currency, platform, isDiscount, plat_orderId, extData, season, true, RechargePayType.REAL, result);
        } else {
            ErrorLogUtil.errorLog("libao service is not open","role",role.getId(), "orderId",orderId,
                    "productId",productId, "currency",currency, "platform",platform,
                    "reason",reason, "isDiscount",isDiscount, "plat_orderId",plat_orderId,
                    "token",token, "packageName",packageName, "orderType",orderType,
                    "startTime",startTime, "endTime",endTime, "extData",extData, "season",season);
        }
    }

    /**
     * 福利充值接口
     */
    public void welfareRecharge(long roleId, String productId, String extData, JSONObject result) {

        ErrorLogUtil.errorLog("begin welfareRecharege", "roleId",roleId, "productId",productId);
        if (functionSwitchService.isOpen(FunctionType.LI_BAO)) {
            Role role = srvDep.getWorldService().getWorld().getRole(roleId);
            if (role == null) {
                return;
            }

            int season = Application.getConfigCenter().getSeason();
            doRecharge(role, "", productId, 0, 0,
                    false, "", extData, season, true, RechargePayType.WELFARE, result);
        } else {
            ErrorLogUtil.errorLog("libao service is not open", "roleId",roleId, "productId",productId);
        }
        ErrorLogUtil.errorLog("begin welfareRecharege", "roleId",roleId, "productId",productId);
    }


    /***
     * 使用代币购买
     * @param productId
     * @param extData
     */
    public void tokenRecharge(Role role, String productId, String extData) {

        if (functionSwitchService.isOpen(FunctionType.LI_BAO)) {


            int season = Application.getConfigCenter().getSeason();
            doRecharge(role, "", productId, 0, 0,
                    false, "", extData, season, true, RechargePayType.TOKEN, new JSONObject());


        } else {
            ErrorLogUtil.errorLog("tokenRecharge libao service is not open", "roleId",role.getId(), "productId",productId);
        }
    }

    private void updateRes(JSONObject result,int code,String msg){
        result.put(TuConstants.KEY_RET, code);
        result.put(TuConstants.KEY_MSG, msg);
    }

    /**
     * 充值
     *
     * @param role
     * @param orderId
     * @param platform
     */
    private void doRecharge(Role role, String orderId, String productId, float currency, int platform, boolean isDiscount, String plat_orderId,
                            String extData, int season, boolean calcDollar, RechargePayType payType, JSONObject result) {

        logger.info("Recharge start, Player: {}, recharge orderId: {}, productId:{}, platform: {}, isDiscount:{}, payType: {}",
                role.getPersistKey(), orderId, productId, platform, isDiscount, payType);

//		LibaoMeta liBaoMeta = configService.getConfig(LiBaoConfig.class).getById(productId);

        LibaoMeta liBaoMeta = liBaoConfigManager.getById(productId);
        if (liBaoMeta == null) {
            updateRes(result,TuConstants.LIBAO_META_NOT_EXIST,"libao meta not found");
            return;
        }

        LibaoPriceMeta priceMeta = configService.getConfig(LibaoPriceConfig.class).getById(liBaoMeta.getPriceId());
        if (priceMeta == null) {
            updateRes(result,TuConstants.LIBAO_PRICE_META_NOT_EXIST,"libao price meta not found");
            return;
        }

        if (isDiscount) {// 打折商品，需要修改为打折后的priceMeta
            LibaoPriceMeta discountPriceMeta = configService.getConfig(LibaoPriceConfig.class).getById(liBaoMeta.getPercentLiBaoPrice());
            if (discountPriceMeta == null) {
                ErrorLogUtil.errorLog("充值发货异常,一个打折订单,不存在打折后的price", "role",role.getPersistKey(),
                        "orderId",orderId, "productId",productId, "currency",currency,
                        "amount",liBaoMeta.getDiamond(), "isDiscount",isDiscount,
                        "percentPrice",liBaoMeta.getPercentLiBaoPrice());
                updateRes(result,TuConstants.LIBAO_DISCOUNT_META_NOT_EXIST,"libao discount price meta not found");
                return;
            }
            priceMeta = discountPriceMeta;
        }

        CurrencyCostInfo costInfo = null;
        var tokenPurchase = payType == RechargePayType.TOKEN;
        if (tokenPurchase) {
            //功能检查
            if (!srvDep.getFunctionSwitchService().isOpen(FunctionType.CHARGE_TOKEN)) {
                ErrorLogUtil.errorLog("代币购买功能关闭! ", "role",role.getPersistKey(),
                        "orderId",orderId, "productId",productId, "currency",currency,
                        "amount",liBaoMeta.getDiamond(), "isDiscount",isDiscount,
                        "percentPrice",liBaoMeta.getPercentLiBaoPrice());
                return;
            }
            //礼包本身限购检查
            if (!srvDep.getLiBaoService().canBuy(role.getRoleId(), liBaoMeta.getId(),extData)) {
                ErrorLogUtil.errorLog("代币不能购买此礼包!", "role",role.getPersistKey(),
                        "orderId",orderId, "productId",productId, "currency",currency,
                        "amount",liBaoMeta.getDiamond(), "isDiscount",isDiscount,
                        "percentPrice",liBaoMeta.getPercentLiBaoPrice());
                return;
            }

            //代币不能购买代币
            if (liBaoMeta.getGold() != 0) {
                ErrorLogUtil.errorLog("代币不能购买代币!", "role",role.getPersistKey(),
                        "orderId",orderId, "productId",productId, "currency",currency,
                        "amount",liBaoMeta.getDiamond(), "isDiscount",isDiscount,
                        "percentPrice",liBaoMeta.getPercentLiBaoPrice());
                return;
            }

            //代币价格不能为0
            if (priceMeta.getCoin() <= 0) {
                ErrorLogUtil.errorLog("代币不能购买没有配置代币价格的道具! ", "role",role.getPersistKey(),
                        "orderId",orderId, "productId",productId, "currency",currency,
                        "amount",liBaoMeta.getDiamond(), "isDiscount",isDiscount,
                        "percentPrice",liBaoMeta.getPercentLiBaoPrice());
                return;
            }

        }

        boolean canGive = libaoService.canGive(role, productId, result,extData);
        if (!canGive) {
            return;
        }

        if (tokenPurchase) {
            costInfo = new CurrencyCostInfo();

            //进行token数量检测
            if (!roleCurrencyManager.checkAndCostWithInfo(role, Currency.CHARGE_TOKEN, priceMeta.getCoin(), MoneyLogReason.TOKEN_BUY_LIBAO, liBaoMeta.getId(), costInfo)) {
                ErrorLogUtil.errorLog("代币不足!", "role",role.getPersistKey(),
                        "orderId",orderId, "productId",productId, "currency",currency,
                        "amount",liBaoMeta.getDiamond(), "isDiscount",isDiscount,
                        "percentPrice",liBaoMeta.getPercentLiBaoPrice());
                return;
            }

            //不增加累计充值的部分
            calcDollar = false;
        }


        RechargeContext context = new RechargeContext(liBaoMeta, priceMeta.getDollar());
        context.setUseToken(tokenPurchase);
        context.setLogDetailReason(tokenPurchase ? liBaoMeta.getId() : liBaoMeta.getId() + ";" + orderId + ";" + plat_orderId);
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getPersistKey());
        context.setFirstRecharge(roleExtra.getTotalDollar() == 0);

        // 添加钻石，记录总充值、最大充值金额、充值时间
        handlePayInfo(role, context, calcDollar);

        // 下发首充标记，必须在礼包更新之前
        pushGcFirstRechargeMark(role, context.isFirstRecharge());

        // 尝试开启首冲次日奖励 第一天领过 才可以开启
        if (roleExtra.getFirstRechargeClaimStatus() != PsRewardClaimStatus.INVALID &&
                roleExtra.getFirstRechargeSecondDayClaimStatus() == PsRewardClaimStatus.INVALID) {
            roleExtra.setFirstRechargeSecondDayClaimStatus(PsRewardClaimStatus.CAN_BE_CLAIMED);
            roleExtraManager.saveRoleExtra(roleExtra);
        }
        sendRechargeInfo(role);

        // 首冲礼包判断
        var firstRechargeLibaoId = configService.getConfig(SettingConfig.class).getFirstRechargeLibaoId(roleExtra.getFirstRechargeConfig());
        if (firstRechargeLibaoId.equals("0") || liBaoMeta.getId().equals(firstRechargeLibaoId)) {
            onBuyFirstRechargeLibao(role, firstRechargeLibaoId);
        }

        // 礼包发货
        List<SimpleItem> items = libaoService.give(role, productId, extData, calcDollar ? 0 : priceMeta.getCoin(), context);

        logger.info("Recharge end, Player: {}, recharge orderId: {}, productId:{}, diamond: {}, platform: {}, isDiscount:{}, payType: {}",
                role.getPersistKey(), orderId, productId, context.getTotalDiamond(), platform, isDiscount, payType);

        if (payType == RechargePayType.REAL) {
            srvDpd.getBiLogUtil().logRecharge(role, orderId, context.getProductId(), priceMeta.getDollar(), priceMeta.getRmb(), plat_orderId, roleExtra.getPayTimes(), context.getLiBaoMeta().getProductType());
            srvDpd.getCoreLogUtil().logRecharge(role, orderId, context.getProductId(), priceMeta.getDollar(), priceMeta.getRmb(), plat_orderId, roleExtra.getPayTimes(), roleExtra.getTotalRmb(), roleExtra.getTotalDollar(), context.getLiBaoMeta().getProductType());
            // 充值
            try {
                srvDep.getBiLogUtil().roleRechargeProfile(role, context.getProductId(), priceMeta.getRmb(),
                        roleExtra.getPayTimes(), roleExtra.getTotalRmb(), roleExtra.getTotalDollar(), roleExtra.getDayRechargeRmb());
                if (roleExtra.getPayTimes() == 1) {
                    srvDep.getBiLogUtil().roleFirstRechargeProfile(role, context.getProductId(), priceMeta.getRmb());
                }
            } catch (ExpectedException ignored) {

            } catch (Exception e) {
                ErrorLogUtil.exceptionLog("roleRechargeProfile error",e);
            }

            //如果是真实充值且是转盘礼包
            if(liBaoMeta.getDiamond() <= 0){
                if(liBaoMeta.getId().contains("AP_WHEEL")){
                    //转盘礼包特殊处理，处理为持续性资产上报
                    var params = !JavaUtils.bool(context.getLogDetailReason()) ? null : context.getLogDetailReason().split(";");
                    srvDep.getBiLogUtil().financialContinueAssetIncrease(role,liBaoMeta.getId(),liBaoMeta.getShowName(),TimeUtil.getNow(),TimeUtil.DAY_SECONDS * 28,params,LogReasons.MoneyLogReason.RECHARGE);
                }
            }

        } else if (tokenPurchase) {
            srvDpd.getBiLogUtil().logRechargeWithToken(role, orderId, context.getProductId(),
                    costInfo.getCost(Currency.CHARGE_TOKEN), costInfo.getCost(Currency.FREE_CHARGE_TOKEN),
                    plat_orderId, roleExtra.getPayTimes(), context.getLiBaoMeta().getProductType(), priceMeta.getRmb());
        }

        // dingding
        DingDingNotify dingDingNotify = new DingDingNotify(srvDpd, role.getPersistKey(), productId, tokenPurchase, liBaoMeta.getGold() > 0);
        asyncOperationService.execute(dingDingNotify);

        // 处理邮件、任务相关
        afterRecharge(role, context, items);
    }


    /**
     * gm充值
     *
     * @param role
     * @param productId
     */
    public void gmcharge(Role role, String productId) {
        //直接走测试发放
        int season = Application.getConfigCenter().getSeason();
        JSONObject result = new JSONObject();
        logger.info("[GM虚拟充值] Player: {}, libaoId: {}", role.getPersistKey(), productId);
        doRecharge(role, "", productId, 0, 0,
                false, "", "" , season, true, RechargePayType.GM, result);
    }

    /**
     * 测试环境使用gm命令，模拟下单、回调等操作
     * @param role
     * @param productId
     * @param extData
     * @param concurrent
     */
    public void gmcharge(Role role, String productId, String extData,int concurrent) {

        //改为走正式的流程去执行
        asyncOperationService.execute(()->{
            try{
                //先去fetchUserId
                var platformId = fetchPlatformId(role);
                if(platformId == null){
                    throw new RuntimeException("平台id获取错误");
                }
                //先生成订单
                var orders = new ArrayList<String>();
                for (var i=0;i<concurrent;i++){
                    var order = genOrder(role,productId,extData);
                    if(order == null){
                        throw new RuntimeException("订单生成错误");
                    }
                    orders.add(order);
                }

                //再并发执行回调
                for (var order : orders){
                    callback(role,order,platformId,productId,extData);
                }

            }catch (Exception e){
                //环境不允许，回到之前的流程
                int season = Application.getConfigCenter().getSeason();
                JSONObject result = new JSONObject();
                for (var i=0;i<concurrent;i++){
                    doRecharge(role, "", productId, 0, 0,
                            false, "", extData == null ? "" : extData, season, true, RechargePayType.GM, result);
                }
            }
            return false;
        });
    }

    protected String fetchPlatformId(Role role){
        try{
            //取个平台id
            var platformUrl = getWebBaseUrl() + "/rolePlatform?roleId=" + role.getId();
            var res = HttpUtil.get(platformUrl);
            logger.info("测试取平台账号 roleId:{} url:{} res:{}",role.getId(),platformUrl,res);
            return res;
        }catch (Exception e){
            ErrorLogUtil.exceptionLog("测试去平台id错误",e,"roleId",role.getId());
        }
        return null;
    }

    protected String genOrder(Role role,String productId,String extData){
        try{
            var orderUrl = getWebBaseUrl() + "/order";
            var meta = configService.getConfig(LiBaoConfig.class).getById(productId);
            var priceMeta = configService.getConfig(LibaoPriceConfig.class).getById(meta.getPriceId());
            orderUrl+="?roleId="+role.getId()+"&productId="+productId+"&currencyName=RMB&localCurrency="+priceMeta.getRmb()+"&extData="+extData+"&unionid=test&season="+Application.getSeason()+"&platform=11";

            var res = HttpUtil.get(orderUrl);
            logger.info("测试下订单 roleId:{} product:{} extData:{} url:{} res:{}",role.getId(),productId,extData,orderUrl,res);

            var json = JSONObject.parseObject(res);
            if(json.getInteger("ret") != 0){
                return null;
            }
            return json.getString("orderId");
        }catch (Exception e){
            ErrorLogUtil.exceptionLog("测试下单异常",e,"roleId",role.getId(),"product",productId,"extData",extData);
        }
        return null;
    }

    protected boolean callback(Role role,String orderId,String platformId,String productId,String extData){
        try{
            var meta = configService.getConfig(LiBaoConfig.class).getById(productId);
            var priceMeta = configService.getConfig(LibaoPriceConfig.class).getById(meta.getPriceId());

            //执行发货
            Map<String, String> jsonObject = new HashMap<>();
            jsonObject.put("chargedRmbs",String.valueOf(priceMeta.getRmb()));
            jsonObject.put("orderId",orderId);
            jsonObject.put("userId",platformId);
            jsonObject.put("prodId",priceMeta.getId());
            jsonObject.put("platformOrder", UuidUtils.shortUuid());
            var appkey = "c5e1b6909e304c7394dd81c960ed80bc";//测试环境都是这个key
            String data = appkey + sortAndformatParameters(jsonObject) + appkey;
            String mysign = MessageDigestUtils.md5(data);
            jsonObject.put("code",mysign);

            StringBuilder payUrl = new StringBuilder(getWebBaseUrl() + "/pay?");
            for (var entry : jsonObject.entrySet()) {
                payUrl.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }

            var res = HttpUtil.get(payUrl.toString());
            logger.info("测试回调 roleId:{} product:{} extData:{} url:{} res:{}",role.getId(),productId,extData, payUrl.toString(),res);

            return "SUCCESS".equalsIgnoreCase(res);
        }catch (Exception e){
            ErrorLogUtil.exceptionLog("测试回调异常",e,"roleId",role.getId(),"product",productId,"extData",extData);
        }
        return false;
    }

    private String getWebBaseUrl() {
        return "http://" + Application.getConfigCenter().getLsConfig().getWebServer().getRpcIp() + ":8080" + ApiConstants.WARZ_BASE;
    }

    private static String sortAndformatParameters(Map<String, String> parameters) {
        StringBuilder sb = new StringBuilder();
        List<String> entryList = parameters.keySet().stream().sorted().toList();
        for (String entry : entryList) {
            sb.append(String.format("%s=%s&", entry, parameters.get(entry)));
        }
        if (parameters.size() > 0) {
            return sb.substring(0, sb.length() - 1);
        }
        return sb.toString();
    }

    /**
     * 记录总充值、最大充值金额、充值时间
     * <p>
     * 2022年05月25日 sean 新加calcDollar参数,用于判断是不是将礼包的价值dollar计入总付费记录中(目前只有每日必买在使用)
     */
    public void handlePayInfo(Role role, RechargeContext context, boolean calcDollar) {
        // 增加钻石
        roleCurrencyManager.add(role, context.isUseToken() ? Currency.PRESENT_DIAMOND : Currency.DIAMOND, context.getDiamond(), MoneyLogReason.RECHARGE, context.getLogDetailReason());
        // 增加额外钻石
        if (context.getExtDiamond() > 0) {
            roleCurrencyManager.add(role, Currency.PRESENT_DIAMOND, context.getExtDiamond(), MoneyLogReason.RECHARGE, context.getLogDetailReason());
        }
        if (context.getChargeToken() > 0) {
            roleCurrencyManager.add(role, Currency.CHARGE_TOKEN, context.getChargeToken(), MoneyLogReason.RECHARGE, context.getLogDetailReason());
        }
        // 增加首冲双倍钻石
        firstRechargeDouble(role, context);

        // 累计充值美元----不管哪种货币支付方式，统一按美元记录
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getPersistKey());
        if (calcDollar) {
            roleExtra.setTotalDollar(roleExtra.getTotalDollar() + context.getDollar());
            roleExtra.setMaxPay(Math.max(roleExtra.getMaxPay(), context.getDollar()));
        }
        long now = TimeUtil.getNow();
        LibaoPriceMeta priceMeta = configService.getConfig(LibaoPriceConfig.class).getById(context.getLiBaoMeta().getPriceId());
        if (priceMeta != null) {
            roleExtra.incrTotalRmb(priceMeta.getRmb());
            // 记录当天充值数据
            if (!TimeUtil.isSameDay(now, role.getLastRechargeTime())) {
                roleExtra.setDayRechargeRmb(0);
            }
            roleExtra.incrDayRechargeRmb(priceMeta.getRmb());
        }
        // 增加总充值钻石 基础钻石+额外钻石
        roleExtra.setTotalPay(roleExtra.getTotalPay() + context.getTotalEffectDiamond());
        roleExtra.setPayTimes(roleExtra.getPayTimes() + 1);
        roleExtraManager.saveRoleExtra(roleExtra);

        // 记录最后一次充值时间
        role.setLastRechargeTime(now);
        roleManager.saveRole(role);

    }

    public void afterRecharge(Role role, RechargeContext context, List<SimpleItem> items) {
//        role.send(new GcPlayerRechargeNotice((int) context.getTotalEffectDiamond()));
        //判断如果是钻石礼包则发送购买钻石成功消息
        if (context.getLiBaoMeta().hasTag(LiBaoTagType.FIRST_RECHARGE_DOUBLE_DIAMOND)) {
            role.send(new GcPurchaseDiamondsSuccess((int) context.getTotalDiamond()));
        }

        // 发送充值邮件
        sendMail(role, context, items);

        if (context.getChargeToken() > 0) {
            //购买代币，则后续事件不再触发
            return;
        }

        //触发事件
        fireEvent(role, context);
        try {
            srvDpd.getMissionService().onMissionFinish(role, MissionType.RECHARGE_AMOUNT, role);
            conRechargeService.onRecharge(role, (int) context.getTotalEffectDiamond());
            srvDpd.getExcellentMarkService().onRoleRecharge(role);
            // 累计充值次数任务
            roleRecordManager.increaseRoleTotalRecord(role.getPersistKey(), RoleTotalRecordType.TOTAL_PAYMENT_COUNT, 1);
            srvDpd.getMissionService().onMissionFinish(role, MissionType.PAYMENT_COUNT, role);
            srvDpd.getMissionService().onMissionFinish(role, MissionType.PAYMENT_ADD_DIAMOND, context.getLiBaoMeta());
        } catch (ExpectedException ignored) {

        } catch (Exception ex) {
            ErrorLogUtil.exceptionLog(ex);
        }
    }

    private void fireEvent(Role role, RechargeContext context) {
        long diamond = context.getDiamond();
        if (diamond <= 0) {
            return;
        }
        srvDep.getRoleGameEventManager().fire(role, GameEventType.BUY_PACK_INCLUDE_DIAMOND, diamond);
    }

    private void sendMail(Role role, RechargeContext context, List<SimpleItem> items) {
        LibaoMeta liBaoMeta = context.getLiBaoMeta();
        List<SimpleItem> rewards = new ArrayList<>();
        rewards.add(new SimpleItem("1", liBaoMeta.getDiamond()));
//        if (liBaoMeta.getItems() != null) {
//            rewards.addAll(liBaoMeta.getItems());
//        }
        rewards.addAll(items);
        String mailId = EmailConstants.FAKE_BUY_SUCCESS;
        if (JavaUtils.bool(liBaoMeta.getSpecialMail())) {
            mailId = liBaoMeta.getSpecialMail();
        }
        String libaoName = liBaoMeta.getName();
        if (!JavaUtils.bool(libaoName)) {
            libaoName = "";
        } else {
            libaoName = "@" + libaoName + "@";
        }
        SystemEmail systemMail = srvDpd.getMailCreator().createSystemMail(role.getPersistKey(), role.getCurrentServerId(), mailId, rewards, List.of(libaoName), true, LogReasons.ItemLogReason.GM_CMD_GIVE_ITEM);
        if (systemMail != null) {
            systemMail.setCanReward(false);
            this.srvDpd.getMailSender().sendOneMail(systemMail);
        }
    }

    private void pushGcFirstRechargeMark(Role role, boolean isFirstRecharge) {
        if (isFirstRecharge) {
            GcFirstRechargeMark msg = new GcFirstRechargeMark();
            msg.setIsRecharge(true);
            role.send(msg);
        }
    }

    /**
     * <AUTHOR>
     * @Date 2024-02-26 15:21:34.263
     * @Description 领取首充首日奖励
     * @Param
     * @Return
     */
    public void claimFirstRechargeRewards(Role role, int dayIndex) {
        switch (dayIndex) {
            case 1:
                claimFirstRechargeFirstDayRewards(role);
                return;
            case 2:
                claimFirstRechargeSecondDayRewards(role);
                return;
            default:
                var msg = new GcClaimFirstRechargeRewards();
                msg.setErrorCode((byte) 1);
        }

    }

    /**
     * <AUTHOR>
     * @Date 2024-02-26 15:21:34.263
     * @Description 领取首充首日奖励
     * @Param
     * @Return
     */
    public void claimFirstRechargeFirstDayRewards(Role role) {
        var msg = new GcClaimFirstRechargeRewards();
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getId());
        // 如果未领取过奖励，就给玩家推送可以领取的数据
        if (roleExtra.getFirstRechargeClaimStatus() == PsRewardClaimStatus.CAN_BE_CLAIMED) {
            roleExtra.setFirstRechargeClaimStatus(PsRewardClaimStatus.ALREADY_BE_CLAIMED);
            roleExtraManager.saveRoleExtra(roleExtra);

            // 发奖
            List<SimpleItem> si = this.srvDpd.getConfigService().getConfig(SettingConfig.class).getFirstRechargeRewards(roleExtra.getFirstRechargeConfig());
            srvDpd.getItemService().give(role, si, ItemLogReason.FIRST_RECHARGE);

            msg.setErrorCode((byte) 0);
            si.forEach(simpleItem -> {
                msg.addToRewards(simpleItem.toPsObject());
            });
        } else {
            msg.setErrorCode((byte) 1);
        }

        role.send(msg);
        sendRechargeInfo(role);
    }

    /**
     * <AUTHOR>
     * @Date 2024-02-26 15:21:34.263
     * @Description 领取首充次日奖励
     * @Param
     * @Return
     */
    public void claimFirstRechargeSecondDayRewards(Role role) {
        var msg = new GcClaimFirstRechargeRewards();
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getId());
        // 如果未领取过奖励，就给玩家推送可以领取的数据
        if (roleExtra.getFirstRechargeSecondDayClaimStatus() == PsRewardClaimStatus.CAN_BE_CLAIMED) {
            roleExtra.setFirstRechargeSecondDayClaimStatus(PsRewardClaimStatus.ALREADY_BE_CLAIMED);
            roleExtraManager.saveRoleExtra(roleExtra);

            // 发奖
            List<SimpleItem> si = this.srvDpd.getConfigService().getConfig(SettingConfig.class).getFirstRechargeRewardsSecondDay(roleExtra.getFirstRechargeConfig());
            srvDpd.getItemService().give(role, si, ItemLogReason.FIRST_RECHARGE);

            msg.setErrorCode((byte) 0);
            si.forEach(simpleItem -> {
                msg.addToRewards(simpleItem.toPsObject());
            });
        } else {
            msg.setErrorCode((byte) 1);
        }

        role.send(msg);
        sendRechargeInfo(role);
    }

    /**
     * @ProjectName: lsserver
     * @Package: com.lc.billion.icefire.game.biz.service.impl.recharge
     * <AUTHOR>
     * @Date 2024-02-27 14:34:21.814
     * @ClassName RechargeServiceImpl
     * @Description 玩家购买首冲礼包处理
     */
    public void onBuyFirstRechargeLibao(Role role, String libaoId) {
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getId());
        // 如果未领取过奖励，就给玩家推送可以领取的数据
        if (roleExtra.getFirstRechargeClaimStatus() != PsRewardClaimStatus.INVALID) {
            return;
        }

        // 如果礼包是空的 则需要玩家手动领奖
        if (libaoId.equals("0")) {
            roleExtra.setFirstRechargeClaimStatus(PsRewardClaimStatus.CAN_BE_CLAIMED);
        } else {
            roleExtra.setFirstRechargeClaimStatus(PsRewardClaimStatus.ALREADY_BE_CLAIMED);
        }

        roleExtra.setFirstRechargeSecondDayClaimStatus(PsRewardClaimStatus.INVALID);
        roleExtra.setFirstRechargeTime(TimeUtil.getNow());
        roleExtraManager.saveRoleExtra(roleExtra);

        // 下发玩家充值信息
        sendRechargeInfo(role);
    }

    /**
     * 发送玩家的付费信息
     *
     * @param role
     */
    public void sendRechargeInfo(Role role) {
        // 兼容老玩家数据
        checkAndFixFirstRechargeConfig(role);

        RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getId());
        var firstRechargeInfo = new GcPlayerRechargeInfo();
        firstRechargeInfo.setFirstRechargStatus(roleExtra.getFirstRechargeClaimStatus());
        firstRechargeInfo.setFirstRechargeSecondDayStatus(roleExtra.getFirstRechargeSecondDayClaimStatus());
        firstRechargeInfo.setSecondDayRewardTime(TimeUtils.getBeginOfDay(TimeUtils.getNextDay(roleExtra.getFirstRechargeTime())));
        firstRechargeInfo.setPayTimes(roleExtra.getPayTimes());
        firstRechargeInfo.setFirstRechargeConfig(roleExtra.getFirstRechargeConfig());
        firstRechargeInfo.setTotalDollar(roleExtra.getTotalDollar());
        role.send(firstRechargeInfo);
    }

    // 基础档位首冲双倍
    public void firstRechargeDouble(Role role, RechargeContext context) {
        // bug fix 只有常规充值会参与首冲翻倍
        LibaoMeta liBaoMeta = context.getLiBaoMeta();
        if (liBaoMeta.hasTag(LiBaoTagType.FIRST_RECHARGE_DOUBLE_DIAMOND) && firstRecharge(role, liBaoMeta.getId())) {
            RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getPersistKey());
            Map<String, Boolean> firstRechargeDouble = roleExtra.getFirstRechargeDouble();
            firstRechargeDouble.put(liBaoMeta.getId(), false);
            // 增加钻石
            roleCurrencyManager.add(role, Currency.PRESENT_DIAMOND, liBaoMeta.getDiamond(), MoneyLogReason.FIRST_CHARGE_DOUBLE, context.getLogDetailReason());
            pushFirstRechargeDouble(role);
            context.setBaseDoubleDiamond(liBaoMeta.getDiamond());
        }
    }

    // 基础档位首冲下发
    public void pushFirstRechargeDouble(Role role) {
        GcPlayerDoubleRechargeInfo msg = new GcPlayerDoubleRechargeInfo();

        Map<LiBaoTagType, List<LibaoMeta>> typeMetas = liBaoConfigManager.getTagMetas();
        List<LibaoMeta> libaoMetas = typeMetas.get(LiBaoTagType.FIRST_RECHARGE_DOUBLE_DIAMOND);
        if (libaoMetas != null) {
            for (LibaoMeta libaoMeta : libaoMetas) {
                if (firstRecharge(role, libaoMeta.getId())) {
                    msg.putToDoubleRechargeData(libaoMeta.getId(), (byte) 1);
                } else {
                    msg.putToDoubleRechargeData(libaoMeta.getId(), (byte) 0);
                }
            }
        }
        role.send(msg);
    }

    // 基础档位是否首冲
    private boolean firstRecharge(Role role, String metaId) {
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getPersistKey());
        Map<String, Boolean> firstRechargeDouble = roleExtra.getFirstRechargeDouble();
        // 解决老号数据问题
        if (firstRechargeDouble == null) {
            firstRechargeDouble = new HashMap<>();
            roleExtra.setFirstRechargeDouble(firstRechargeDouble);
        }
        return firstRechargeDouble.get(metaId) == null || !firstRechargeDouble.get(metaId).equals(false);
    }

    // 零元礼包购买
    public void freeBuyProduct(Role role, String productId) {
        LiBaoConfig liBaoConfig = configService.getConfig(LiBaoConfig.class);
        var libaoMeta = liBaoConfig.getById(productId);
        if (null == libaoMeta) {
            ErrorLogUtil.errorLog("freeBuyProduct isn't find", "roleId",role.getId(), "productId",productId);
            return;
        }

        LibaoPriceConfig libaoPriceConfig = configService.getConfig(LibaoPriceConfig.class);
        var priceMeta = libaoPriceConfig.getById(libaoMeta.getPriceId());
        if (null == priceMeta) {
            ErrorLogUtil.errorLog("freeBuyProduct price isn't find", "roleId",role.getId(), "productId",productId);
            return;
        }

        // 是否是零元礼包
        if (priceMeta.getRmb() > 0 || priceMeta.getDollar() > 0) {
            ErrorLogUtil.errorLog("freeBuyProduct price isn't zero", "roleId",role.getId(), "productId",productId);
            return;
        }

        // 是否可购买
        if (!srvDpd.getLiBaoService().canBuy(role.getRoleId(), productId,null)) {
            ErrorLogUtil.errorLog("freeBuyProduct isn't buy", "roleId",role.getId(), "productId",productId);
            return;
        }

        String orderId = "freeproduct." + IDGenerator.getInstance().nextOrderID();
        var roleDevice = roleDeviceManager.getRoleDevice(role.getRoleId());
        int platform = 0;
        if (null != roleDevice) {
            platform = roleDevice.getPlatform().getId();
        }

        JSONObject result = new JSONObject();
        doRecharge(role, orderId, productId, (float) priceMeta.getDollar(), platform, false, "",
                "", ChargeLogReason.CHARGE_PAYMENT_ZERO.getReason(), true, RechargePayType.REAL, result);
    }


    /**
     * 充值测试----仅限于开发环境
     *
     * @param role
     * @param orderId
     * @param amount
     * @param platform
     */
    public void rechargeTest(Role role, String orderId, double amount, PlatformType platform) {
        String uid = role.getName();
        String appUid = String.valueOf(role.getPersistKey());

        IPlatformAuthHandler authHandler = authHandlers.get(platform);
        if (authHandler == null) {
            return;
        }

        authHandler.pay(orderId, amount, uid, appUid);
    }
}