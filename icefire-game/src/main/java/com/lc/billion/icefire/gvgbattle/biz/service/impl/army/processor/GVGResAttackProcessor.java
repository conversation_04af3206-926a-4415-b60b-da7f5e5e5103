package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.processor;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import com.lc.billion.icefire.game.biz.service.impl.login.LoginServiceImpl;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.GvgResNode;
import com.simfun.sgf.utils.JavaUtils;
import org.springframework.stereotype.Service;

/**
 * 攻击其他玩家GVG战场的资源点
 * 
 * <AUTHOR>
 * @date 2021/1/25
 */
@Service
public class GVGResAttackProcessor extends AbstractGvgArmyProcessor {
	@Override
	public ArmyType getArmyType() {
		return ArmyType.ATTACK_GVG_GATHER;
	}

	@Override
	protected boolean check(Role role, SceneNode targetNode, ArmySetoutParam param) {
		if (!Application.getBean(LoginServiceImpl.class).isLoginSwitch()) {// gvg战斗服停了，战斗结束了
			return false;
		}
		if (!gvgStrongHoldService.isMoveGvgActivityStage(TimeUtil.getNow()))// 入场阶段，不允许行军
			return false;
		if (targetNode == null || targetNode.getNodeType() != SceneNodeType.GVG_RES)
			return false;
		GvgResNode node = (GvgResNode) targetNode;
		if (!JavaUtils.bool(node.getArmyId()))
			return false;
		return true;
	}
}