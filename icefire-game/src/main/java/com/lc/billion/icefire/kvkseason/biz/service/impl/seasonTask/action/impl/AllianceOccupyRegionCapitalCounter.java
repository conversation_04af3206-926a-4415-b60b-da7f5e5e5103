package com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.impl;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.kvk.SeasonTaskConfig;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.node.RegionCapitalNode;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.AllianceSeasonTaskProgress;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.RoleSeasonTaskRecord;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.AbstractSeasonTaskAction;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.SeasonTaskActionType;
import com.longtech.ls.config.ServerType;
import org.springframework.stereotype.Service;

/**
 * 
 * 所在联盟占领X次Y级城
 * 
 * <AUTHOR>
 * @date 2021/11/16
 */
@Service
public class AllianceOccupyRegionCapitalCounter extends AbstractSeasonTaskAction {
	@Override
	public SeasonTaskActionType getType() {
		return SeasonTaskActionType.ALLIANCE_OCCUPY_REGION_CAPITAL_COUNTER;
	}

	@Override
	public long getProgress(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		Alliance allianceByRole = allianceService.getAllianceByRole(role);
		if (allianceByRole == null) {
			return 0;
		}
		int i = meta.getConditions().indexOf(getType().getId());
		int param2 = meta.getParam2s().get(i);
		AllianceSeasonTaskProgress allianceProgress = allianceSeasonTaskProgressDao.getProgressByAlliance(allianceByRole);
		return allianceProgress.getOccupyCityTimes(param2);
	}

	@Override
	protected void updateProgress(Role role, RoleSeasonTaskRecord taskRecord, Object... params) {
		RegionCapitalNode node = (RegionCapitalNode) params[0];
		if (Application.getConfigCenter().getServerType(node.getCurrentServerId()) != ServerType.KVK_SEASON) {
			return;
		}

		Alliance allianceById = allianceService.getAllianceById(node.getBelongAllianceId());
		if (allianceById == null) {
			return;
		}

		AllianceSeasonTaskProgress allianceProgress = allianceSeasonTaskProgressDao.getProgressByAlliance(allianceById);
		allianceProgress.addOccupyCityTimes(node.getLevel());
		allianceSeasonTaskProgressDao.save(allianceProgress);
	}

	@Override
	public boolean actionFinishCheck(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		int i = meta.getConditions().indexOf(getType().getId());
		int param1 = meta.getParam1s().get(i);
		return getProgress(role, meta) >= param1;
	}
}
