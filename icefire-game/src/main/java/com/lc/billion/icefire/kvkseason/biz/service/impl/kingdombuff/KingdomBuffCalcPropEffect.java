package com.lc.billion.icefire.kvkseason.biz.service.impl.kingdombuff;

import com.lc.billion.icefire.game.biz.config.BuffConfig;
import com.lc.billion.icefire.game.biz.model.prop.CalcPropContainer;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.prop.AbstractRoleCalcPropEffect;
import com.lc.billion.icefire.game.biz.service.impl.prop.extention.PropSource;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.KingdomBuffDao;
import com.lc.billion.icefire.kvkseason.biz.model.kindombuff.KingdomBuff;
import com.simfun.sgf.utils.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2021/9/18
 */
@Service
public class KingdomBuffCalcPropEffect extends AbstractRoleCalcPropEffect {
    @Autowired
    private KingdomBuffDao kingdomBuffDao;

    @Override
    public Type getType() {
        return RoleType.KINGDOM_BUFF;
    }

    @Override
    public PropSource getSource() {
        return PropSource.KINGDOM_BUFF;
    }

    @Override
    public void calcProps(Role role, CalcPropContainer<?> propCtn, Object... extParam) {
        propExtentionService.reCalProp(role.getPersistKey(), getSource());
        Collection<KingdomBuff> roleKingdomBuffs = kingdomBuffDao.findBuffsByBelongServerIdAndCurrentServerId(role.getoServerId(), role.getCurrentServerId());
        if (!JavaUtils.bool(roleKingdomBuffs))
            return;
        for (KingdomBuff buff : roleKingdomBuffs) {
            BuffConfig.BuffMeta buffMeta = configService.getConfig(BuffConfig.class).getBuffMeta(buff.getBuffId());
            if (buffMeta == null) {
                continue;
            }
            addProp(role, propCtn, buffMeta.getProertyTypePara1s());
        }
    }
}
