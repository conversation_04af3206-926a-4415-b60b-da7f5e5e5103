package com.lc.billion.icefire.kvkseason.biz.dao.mongo.root;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.kvkseason.biz.model.honor.KvkHonorAlliance;
import com.lc.billion.icefire.kvkseason.biz.model.honor.KvkHonorLegion;

/**
 * <AUTHOR>
 *
 */
@Repository
public class KvkHonorLegionDao extends RootDao<KvkHonorLegion> {

	public KvkHonorLegionDao() {
		super(KvkHonorLegion.class, false);
	}

	@Override
	protected MongoCursor<KvkHonorLegion> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(KvkHonorLegion entity) {

	}

	@Override
	protected void removeMemoryIndexes(KvkHonorLegion entity) {

	}

	public KvkHonorLegion create(int db, Long legionId) {
		KvkHonorLegion kvkHonorAlliance = newEntityInstance();
		kvkHonorAlliance.setPersistKey(legionId);
		return createEntity(db, kvkHonorAlliance);
	}

}
