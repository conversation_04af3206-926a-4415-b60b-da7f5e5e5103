package com.lc.billion.icefire.kvkseason.biz.tick;

import java.util.Collection;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.common.AbstractTicker;
import com.lc.billion.icefire.game.biz.config.BuffConfig;
import com.lc.billion.icefire.game.biz.model.buff.BuffEffectEnum;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.KingdomBuffDao;
import com.lc.billion.icefire.kvkseason.biz.model.kindombuff.KingdomBuff;
import com.lc.billion.icefire.kvkseason.biz.service.impl.kingdombuff.KingdomBuffServiceImpl;

/**
 * <AUTHOR>
 * @date 2021/9/13
 */
@Service
public class KingdomBuffTicker extends AbstractTicker<KingdomBuff> {

	@Autowired
	private ConfigServiceImpl configService;
	@Autowired
	private KingdomBuffServiceImpl kingdomBuffService;
	@Autowired
	private KingdomBuffDao kingdomBuffDao;

	public KingdomBuffTicker() {
		super(1 * TimeUtil.SECONDS_MILLIS);
	}

	@Override
	protected void tick(KingdomBuff kingdomBuff, long now) {
		// 永久性buff不检测
		BuffConfig.BuffMeta buffMeta = configService.getConfig(BuffConfig.class).getBuffMeta(kingdomBuff.getBuffId());
		if (buffMeta != null && buffMeta.getEffect() != BuffEffectEnum.TIME) {
			return;
		}
		// 检查过期
		if (kingdomBuff.getEndTime() <= now)
			kingdomBuffService.remove(kingdomBuff);
	}

	@Override
	protected Collection<KingdomBuff> findAll() {
		return kingdomBuffDao.findAll();
	}
}
