package com.lc.billion.icefire.csacontrol.biz.model.activity;

import com.lc.billion.icefire.game.biz.model.activity.ActivityContext;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class CSAActivityContext extends ActivityContext implements Serializable{

	private static final long serialVersionUID = 8112965219373628672L;

	private String activityId;

	// serverId -> groupInfo
	private Map<Integer, CSAActivityGroupContext> serverInfoMap = new HashMap<>();

	public Map<Integer, CSAActivityGroupContext> getServerInfoMap() {
		return serverInfoMap;
	}

	public void setServerInfoMap(Map<Integer, CSAActivityGroupContext> serverInfoMap) {
		this.serverInfoMap = serverInfoMap;
	}

	public String getActivityId() {
		return activityId;
	}

	public void setActivityId(String activityId) {
		this.activityId = activityId;
	}
}
