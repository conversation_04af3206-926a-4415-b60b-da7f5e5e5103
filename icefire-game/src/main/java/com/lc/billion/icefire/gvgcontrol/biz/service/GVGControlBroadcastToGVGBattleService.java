package com.lc.billion.icefire.gvgcontrol.biz.service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGAllianceSignUpInfo;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgBattleServerDispatchRecord;
import com.lc.billion.icefire.gvgcontrol.biz.service.rpc.GVGControlRPCToGVGBattleProxyService;
import com.lc.billion.icefire.rpc.service.gvg.IGVGControlRemoteGVGBattleService;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGAllianceSignUpInfoVo;
import com.lc.billion.icefire.rpc.vo.gvg.GvgBattleServerDispatchRecordVo;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 *
 */
@Service
public class GVGControlBroadcastToGVGBattleService {

	private static final Logger logger = LoggerFactory.getLogger(GVGControlBroadcastToGVGBattleService.class);

	@Autowired
	private GVGControlRPCToGVGBattleProxyService gvgControlRPCToGVGBattleProxyService;

	public void broadcastGvgBattleServerDispatchRecord(GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord) {
		IGVGControlRemoteGVGBattleService gvgControlRemoteGVGBattleService = gvgControlRPCToGVGBattleProxyService
				.getGVGControlRemoteGVGBattleService(gvgBattleServerDispatchRecord.getBattleServerId().intValue());
		if (gvgControlRemoteGVGBattleService != null) {
			logger.info("中控服广播对阵信息给战斗服");
			gvgControlRemoteGVGBattleService.broadcastGvgBattleServerDispatchRecord(new GvgBattleServerDispatchRecordVo(gvgBattleServerDispatchRecord));
		} else {
			ErrorLogUtil.errorLog("中控服广播对阵信息给战斗服时找不到战斗服", "gvgBattleServerId",gvgBattleServerDispatchRecord.getBattleServerId());
		}
	}

	public void broadcastGVGActivity(Activity activity, Set<Integer> serverIds) {
		if (activity == null || !JavaUtils.bool(serverIds)) {
			return;
		}

		ActivityVo activityVo = new ActivityVo(activity);
		for (Integer serverId : serverIds) {
			IGVGControlRemoteGVGBattleService gvgControlRemoteGVGBattleService = gvgControlRPCToGVGBattleProxyService.getGVGControlRemoteGVGBattleService(serverId);
			if (gvgControlRemoteGVGBattleService != null) {
				gvgControlRemoteGVGBattleService.broadcastGVGActivity(activityVo);
			}
		}
	}
	/**
	 * 广播gvg活动信息给所有战斗服
	 */
	public void broadcastGVGActivity(Activity activity) {
		if (activity == null) {
			return;
		}
		Collection<IGVGControlRemoteGVGBattleService> gvgControlRemoteGVGBattleServices = gvgControlRPCToGVGBattleProxyService.getGVGControlRemoteGVGBattleServices();
		if (JavaUtils.bool(gvgControlRemoteGVGBattleServices)) {
			ActivityVo activityVo = new ActivityVo(activity);
			for (IGVGControlRemoteGVGBattleService gvgControlRemoteGameService : gvgControlRemoteGVGBattleServices) {
				gvgControlRemoteGameService.broadcastGVGActivity(activityVo);
			}
		} else {
			ErrorLogUtil.errorLog("中控服找不到战斗服的rpc连接不广播");
		}
	}

	/**
	 * 中控服广播给战斗服，启服及分配后
	 * 
	 * @param gvgAllianceSignUpInfos
	 */
	public void broadcastGVGAllianceSignUpInfo(int battleServerId, List<GVGAllianceSignUpInfo> gvgAllianceSignUpInfos) {
		if (JavaUtils.bool(gvgAllianceSignUpInfos)) {
			IGVGControlRemoteGVGBattleService gvgControlRemoteGVGBattleService = gvgControlRPCToGVGBattleProxyService.getGVGControlRemoteGVGBattleService(battleServerId);
			if (gvgControlRemoteGVGBattleService != null) {
				List<GVGAllianceSignUpInfoVo> ret = new ArrayList<>();
				gvgAllianceSignUpInfos.forEach(gvgAllianceSignUpInfo -> ret.add(new GVGAllianceSignUpInfoVo(gvgAllianceSignUpInfo)));
				gvgControlRemoteGVGBattleService.broadcastGVGAllianceSignUpInfo(ret);
			} else {
				ErrorLogUtil.errorLog("中控服找不到战斗服的rpc连接不广播");
			}
		}
	}

	public void broadcastRZEActivity(Activity activity){
		if (activity == null) {
			return;
		}
		Collection<IGVGControlRemoteGVGBattleService> gvgControlRemoteGVGBattleServices = gvgControlRPCToGVGBattleProxyService.getGVGControlRemoteGVGBattleServices();
		if (JavaUtils.bool(gvgControlRemoteGVGBattleServices)) {
			ActivityVo activityVo = new ActivityVo(activity);
			for (IGVGControlRemoteGVGBattleService gvgControlRemoteGameService : gvgControlRemoteGVGBattleServices) {
				gvgControlRemoteGameService.broadcastRZEActivity(activityVo);
			}
		} else {
			ErrorLogUtil.errorLog("RZE 中控服找不到战斗服的rpc连接不广播");
		}
	}
}
