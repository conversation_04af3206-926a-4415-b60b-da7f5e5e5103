package com.lc.billion.icefire.kvkseason.biz.dao.mongo.alliances;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.game.biz.dao.AlliancesEntityDao;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.AllianceSeasonTaskProgress;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 * @date 2021/11/15
 */
@Repository
public class AllianceSeasonTaskProgressDao extends AlliancesEntityDao<AllianceSeasonTaskProgress> {

	private ConcurrentMap<Long, AllianceSeasonTaskProgress> recordMap = new ConcurrentHashMap<>();

	protected AllianceSeasonTaskProgressDao() {
		super(AllianceSeasonTaskProgress.class, true);
	}

	@Override
	protected void putMemoryIndexes(AllianceSeasonTaskProgress entity) {
		recordMap.put(entity.getAllianceId(), entity);
	}

	@Override
	protected void removeMemoryIndexes(AllianceSeasonTaskProgress entity) {
		recordMap.remove(entity.getAllianceId());
	}

	@Override
	protected MongoCursor<AllianceSeasonTaskProgress> doFindAll(int db, List<Long> allianceIds) {
		return dbFindByPrimaryIds(db, allianceIds);
	}

	public AllianceSeasonTaskProgress create(Alliance alliance) {
		AllianceSeasonTaskProgress progress = newEntityInstance();
		progress.setAllianceId(alliance.getPersistKey());
		return createEntity(alliance, progress);
	}

	public AllianceSeasonTaskProgress getProgressByAlliance(Alliance alliance) {
		AllianceSeasonTaskProgress record = recordMap.get(alliance.getPersistKey());
		if (record == null)
			record = create(alliance);
		return record;
	}

	public void clearPreSeasonData() {
		Collection<AllianceSeasonTaskProgress> all = findAll();
		if (JavaUtils.bool(all)) {
			for (AllianceSeasonTaskProgress record : all) {
				delete(record);
			}
		}
	}
}
