package com.lc.billion.icefire.gvgcontrol.biz.model;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import org.jongo.marshall.jackson.oid.MongoId;

public class GvgObserveMatch extends AbstractEntity {
    private static final long serialVersionUID = 7739701817991012817L;
    @MongoId
    private Long battleServerId;
    private int warZoneId;

    @Override
    public void setPersistKey(Long id) {
        this.battleServerId = id;
    }

    @Override
    public Long getPersistKey() {
        return this.battleServerId;
    }

    @Override
    public Long getGroupingId() {
        return this.battleServerId;
    }

    @Override
    public int hashCodeImpl() {
        return hashCodeForPersistKey();
    }

    @Override
    public boolean equalsImpl(Object obj) {
        return equalsForPersistKey(obj);
    }

    public int getWarZoneId() {
        return warZoneId;
    }

    public void setWarZoneId(int warZoneId) {
        this.warZoneId = warZoneId;
    }
}
