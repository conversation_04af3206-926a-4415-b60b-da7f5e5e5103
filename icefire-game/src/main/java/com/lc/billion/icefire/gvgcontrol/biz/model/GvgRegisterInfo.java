package com.lc.billion.icefire.gvgcontrol.biz.model;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import lombok.Getter;
import lombok.Setter;
import org.jongo.marshall.jackson.oid.MongoId;

public class GvgRegisterInfo extends AbstractEntity {
	private static final long serialVersionUID = -4553111762451719954L;

	@Setter
    @Getter
    @MongoId
	private Long allianceId;

	@Setter
    @Getter
    private int serverId;
	// gvg报名状态 0-未报名 1-已报名
	@Deprecated
    private int isRegister;
	// 本期gvg匹配分
	@Deprecated
    private long matchScore;

	@Deprecated
	private int isMatchQualify;


	@Override
	public void setPersistKey(Long id) {
		allianceId = id;
	}

	@Override
	public Long getPersistKey() {
		return allianceId;
	}

	@Override
	public Long getGroupingId() {
		return allianceId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}
}
