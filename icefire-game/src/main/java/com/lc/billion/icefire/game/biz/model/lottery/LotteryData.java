package com.lc.billion.icefire.game.biz.model.lottery;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2019/7/23 下午4:41
 * @Description:
 */
public class LotteryData implements Serializable {

	private String metaId;
	/**
	 * 已经免费抽了几次
	 */
	private int freeTimes;
	/**
	 * 下次可以免费抽取的时间
	 */
	private long nextFreeTime;

	/**
	 * 保底奖励累计次数
	 */
	private int insuranceTimes;

	/**
	 * 伪随机次数
	 */
	private int pseudoRandomTime;

	/**
	 * 总抽取次数
	 */
	private int totalTimes;

	/**
	 * 新的metaId
	 */
	private String cardTypeNId;

	/**
	 * 每日抽取次数，配合 meta.dailyLimit 限制
	 * 配合版号包提审。@郭云帆
	 */
	private int dailyTimes;

	public String getMetaId() {
		return metaId;
	}

	public void setMetaId(String metaId) {
		this.metaId = metaId;
	}

	public int getFreeTimes() {
		return freeTimes;
	}

	public void setFreeTimes(int freeTimes) {
		this.freeTimes = freeTimes;
	}

	public long getNextFreeTime() {
		return nextFreeTime;
	}

	public void setNextFreeTime(long nextFreeTime) {
		this.nextFreeTime = nextFreeTime;
	}

	public int getInsuranceTimes() {
		return insuranceTimes;
	}

	public void setInsuranceTimes(int insuranceTimes) {
		this.insuranceTimes = insuranceTimes;
	}

	public int getPseudoRandomTime() {
		return pseudoRandomTime;
	}

	public void setPseudoRandomTime(int pseudoRandomTime) {
		this.pseudoRandomTime = pseudoRandomTime;
	}

	public int getTotalTimes() {
		return totalTimes;
	}

	public void setTotalTimes(int totalTimes) {
		this.totalTimes = totalTimes;
	}

	public String getCardTypeNId() {
		return cardTypeNId;
	}

	public void setCardTypeNId(String cardTypeNId) {
		this.cardTypeNId = cardTypeNId;
	}

	public int getDailyTimes() {
		return dailyTimes;
	}

	public void setDailyTimes(int dailyTimes) {
		this.dailyTimes = dailyTimes;
	}
}
