package com.lc.billion.icefire.gvgcontrol.biz.model;

import org.jongo.marshall.jackson.oid.MongoId;
import org.springframework.beans.BeanUtils;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.rpc.vo.gvg.AllianceBaseDataVo;

/**
 * <AUTHOR>
 * @date 2020/12/7
 */
public class AllianceBaseData extends AbstractEntity {

	private static final long serialVersionUID = -4305113425828410896L;

	@MongoId
	private Long allianceId;
	private int serverId;
	private String allianceName;// 联盟名
	private String allianceAlias;// 联盟简称
	private Long leaderRoleId;// 盟主roleId
	private String leaderName;// 盟主名字
	private String leaderHead;// 盟主头像
	/**
	 * 联盟旗帜
	 */
	private String banner;
	private int bannerColor;
	/**
	 * 联盟徽章
	 */
	private String badge;
	private int badgeColor;
	private String country;

	private long fightPower;

	public void copyAllianceBaseData(AllianceBaseDataVo allianceBaseDataVo) {
		BeanUtils.copyProperties(allianceBaseDataVo, this);
	}

	@Override
	public void setPersistKey(Long id) {
		allianceId = id;
	}

	@Override
	public Long getPersistKey() {
		return allianceId;
	}

	@Override
	public Long getGroupingId() {
		return allianceId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public Long getAllianceId() {
		return allianceId;
	}

	public void setAllianceId(Long allianceId) {
		this.allianceId = allianceId;
	}

	public int getServerId() {
		return serverId;
	}

	public void setServerId(int serverId) {
		this.serverId = serverId;
	}

	public String getAllianceName() {
		return allianceName;
	}

	public void setAllianceName(String allianceName) {
		this.allianceName = allianceName;
	}

	public String getAllianceAlias() {
		return allianceAlias;
	}

	public void setAllianceAlias(String allianceAlias) {
		this.allianceAlias = allianceAlias;
	}

	public Long getLeaderRoleId() {
		return leaderRoleId;
	}

	public void setLeaderRoleId(Long leaderRoleId) {
		this.leaderRoleId = leaderRoleId;
	}

	public String getLeaderName() {
		return leaderName;
	}

	public void setLeaderName(String leaderName) {
		this.leaderName = leaderName;
	}

	public String getLeaderHead() {
		return leaderHead;
	}

	public void setLeaderHead(String leaderHead) {
		this.leaderHead = leaderHead;
	}

	public String getBanner() {
		return banner;
	}

	public void setBanner(String banner) {
		this.banner = banner;
	}

	public int getBannerColor() {
		return bannerColor;
	}

	public void setBannerColor(int bannerColor) {
		this.bannerColor = bannerColor;
	}

	public String getBadge() {
		return badge;
	}

	public void setBadge(String badge) {
		this.badge = badge;
	}

	public int getBadgeColor() {
		return badgeColor;
	}

	public void setBadgeColor(int badgeColor) {
		this.badgeColor = badgeColor;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public long getFightPower() {
		return fightPower;
	}

	public void setFightPower(long fightPower) {
		this.fightPower = fightPower;
	}
}
