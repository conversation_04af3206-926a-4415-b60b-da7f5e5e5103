package com.lc.billion.icefire.tvtcontrol.biz.service;

import java.util.*;
import java.util.stream.Collectors;

import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtBattleServerDispatchRecord;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtPlayerSignupInfo;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtPlayerSimpleInfo;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.config.WorldMapConfig;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.longtech.ls.zookeeper.GameServerConfig;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.support.HttpUtil;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GVGBattleRecordDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGBattleRecord;
import com.lc.billion.icefire.rpc.vo.gvg.GVGBattleRecordVo;
import com.lc.billion.icefire.rpc.vo.tvt.TvtPlayerSimpleInfoVo;
import com.lc.billion.icefire.tvtcontrol.biz.TvtBattleServerCreateConfig;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtBattleServerDispatchRecordDao;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtPlayerSignupInfoDao;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtPlayerSimpleInfoDao;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtQualifiedServerDao;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtQualifiedServer;
import com.lc.billion.icefire.tvtcontrol.biz.model.activity.TvtActivityStatus;
import com.lc.billion.icefire.tvtcontrol.biz.model.activity.context.TvtActivityContext;
import com.simfun.sgf.utils.JavaUtils;

@Service
public class TvtControlService {

    private static final Logger logger = LoggerFactory.getLogger(TvtControlService.class);
    @Autowired
    private TvtBattleServerDispatchRecordDao tvtBattleServerDispatchRecordDao;
    @Autowired
    private TvtQualifiedServerDao tvtQualifiedServerDao;
    @Autowired
    private ActivityDao activityDao;
    @Autowired
    private TvtPlayerSignupInfoDao tvtPlayerSignupInfoDao;
    @Autowired
    private TvtControlBroadcastToGameService tvtControlBroadcastToGameService;
    @Autowired
    private GVGBattleRecordDao gvgBattleRecordDao;
    @Autowired
    private TvtPlayerSimpleInfoDao tvtPlayerSimpleInfoDao;
    @Autowired
    private AsyncOperationServiceImpl asyncOperationService;
    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private TvtControlBroadcastToBattleService tvtControlBroadcastToBattleService;


    public void startService() {
        WorldMapConfig worldMapConfig = ServerConfigManager.getInstance().getWorldMapConfig();
        ServerType serverType = worldMapConfig.getServerType();
        if (serverType == ServerType.TVT_CONTROL) {
            // 广播匹配信息给game和battle
            // broadcast signupinfo to game
            tvtControlBroadcastToGameService.broadcastTvtPlayerSignupInfos();
            // broadcast battleServerDispatchRecord to gameServer
            tvtControlBroadcastToGameService.broadcastTvtBattleServerDispatchRecords();
            // broadcast battleServerDispatchRecord to battleServer
            Collection<TvtBattleServerDispatchRecord> tvtBattleServerDispatchRecords = tvtBattleServerDispatchRecordDao.findAll();
            if (JavaUtils.bool(tvtBattleServerDispatchRecords)) {
                for (TvtBattleServerDispatchRecord tvtBattleServerDispatchRecord : tvtBattleServerDispatchRecords) {
                    if (tvtBattleServerDispatchRecord.getStatus() != TvtBattleServerDispatchRecord.STATUS_END) {
                        // 结束的不广播给战斗服
                        tvtControlBroadcastToBattleService.broadcastTVTBattleServerDispatchRecord(tvtBattleServerDispatchRecord);
                    }

                    Long battleServerId = tvtBattleServerDispatchRecord.getBattleServerId();
                    tvtControlBroadcastToBattleService.broadcastTVTAllianceSignUpInfo(tvtBattleServerDispatchRecord);
                }
            }

        }
    }

    /**
     * 创建战斗服
     */
    public void noticeCreateBattleServer(List<Integer> serverIds, int dbIndex) {
        if (!JavaUtils.bool(serverIds)) {
            return;
        }
        logger.info("tvt 创建战斗服请求发送：{}", serverIds);
        TvtBattleServerCreateConfig tvtBattleServerCreateConfig = ServerConfigManager.getInstance().getTvtBattleServerCreateConfig();
        String createUrl = tvtBattleServerCreateConfig.getCreateUrl();
        if (createUrl == null) {
            serverIds.forEach(serverId -> configCenter.updateGameServerRestartTimeMs(serverId, TimeUtil.getNow()));
        } else {
            JSONObject sendBattleServerRequest = sendBattleServerRequest(createUrl, tvtBattleServerCreateConfig.getProjectName(), serverIds, dbIndex);
            String status = sendBattleServerRequest.getString("status");
            if ("ok".equals(status)) {
                logger.info("tvt 创建战斗服请求返回成功{}", sendBattleServerRequest);
            } else {
                logger.info("tvt 创建战斗服请求返回失败{}", sendBattleServerRequest);
            }
        }
    }

    public void noticeDestroy(List<Integer> serverIds, int dbIndex) {
        if (!JavaUtils.bool(serverIds)) {
            return;
        }
        // 通知销毁
        logger.info("tvt 通知销毁战斗服{}", serverIds);
        TvtBattleServerCreateConfig tvtBattleServerCreateConfig = ServerConfigManager.getInstance().getTvtBattleServerCreateConfig();
        String destroyUrl = tvtBattleServerCreateConfig.getDestroyUrl();
        if (destroyUrl != null) {
            JSONObject sendBattleServerRequest = sendBattleServerRequest(destroyUrl, tvtBattleServerCreateConfig.getProjectName(), serverIds, dbIndex);
            logger.info("tvt 战斗服销毁{},{}", serverIds, sendBattleServerRequest);
            JSONObject jsonObject = reNoticeCreateBattleServer(serverIds, dbIndex);
            if (jsonObject.containsKey("status")) {
                JSONArray jsonArray = jsonObject.getJSONArray("destroy");
                if (jsonArray != null && jsonArray.size() > 0) {
                    for (Object obj : jsonArray) {
                        Long battleServerId = Long.valueOf(obj.toString());
                        tvtBattleServerDispatchRecordDao.delete(battleServerId);
                        logger.info("tvt 删除TvtBattleServerDispatchRecord记录{}", battleServerId);
                    }
                }
            }
        } else {
            logger.info("没有destroyUrl，不用查状态");
            serverIds.forEach(battleServerId -> tvtBattleServerDispatchRecordDao.delete(Long.valueOf(battleServerId)));
        }
    }


    public void updateTVTBattleServerDispatchRecord(int battleServerId) {
        logger.info("战斗服结束改变状态等待销毁{}", battleServerId);

        TvtBattleServerDispatchRecord record = tvtBattleServerDispatchRecordDao.findById(Long.valueOf(battleServerId));
        if (record != null) {
            record.setStatus(TvtBattleServerDispatchRecord.STATUS_END);
            tvtBattleServerDispatchRecordDao.save(record);
        } else {
            ErrorLogUtil.errorLog("tvt error: 战斗服起来了,没有分配记录");
            Calendar calendar = Calendar.getInstance();
            int minute = calendar.get(Calendar.MINUTE);
            calendar.add(Calendar.MINUTE, 10 - minute % 10);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            tvtBattleServerDispatchRecordDao.create(Long.valueOf(battleServerId), calendar.getTimeInMillis(), calendar.getTimeInMillis(), TvtBattleServerDispatchRecord.STATUS_END, null, null);
        }
    }

    /**
     * 处理战斗服上传的战斗记录信息
     */
    public void uploadTVTBattleRecord(GVGBattleRecordVo gvgBattleRecordVo) {
        logger.info("战斗服结束上传战斗数据{}", gvgBattleRecordVo.getBattleServerId());
        GVGBattleRecord gvgBattleRecord = gvgBattleRecordDao.findById(gvgBattleRecordVo.getId());
        if (gvgBattleRecord == null) {
            gvgBattleRecord = GVGBattleRecord.copyGVGBattleRecord(gvgBattleRecordVo);
            gvgBattleRecordDao.create(gvgBattleRecord);
        }

        // 战斗结果广播给game
        tvtControlBroadcastToGameService.broadcastTvtBattleRecord(gvgBattleRecordVo);

    }

    private JSONObject sendBattleServerRequest(String url, String projectName, List<Integer> serverIds, int dbIndex) {
        JSONObject ret = new JSONObject();
        if (!JavaUtils.bool(serverIds)) {
            ErrorLogUtil.errorLog("tvt 创建战斗服serverIds==null", new RuntimeException());
            return ret;
        }
        StringJoiner joiner = new StringJoiner(",");
        serverIds.forEach(serverId -> joiner.add(String.valueOf(serverId)));
        Map<String, String> params = new HashMap<>();
        params.put(TvtBattleServerCreateConfig.PROJECT_NAME_KEY, projectName);
        params.put(TvtBattleServerCreateConfig.SID_KEY, joiner.toString());
        params.put(TvtBattleServerCreateConfig.DB_INDEX_KEY, String.valueOf(dbIndex));
        try {
            String post = HttpUtil.post(url, params);
            ret = JSONObject.parseObject(post);
        } catch (ExpectedException ignored){

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("发送TVT战斗服指令报错", e);
        }
        return ret;
    }

    /**
     * 第一次失败再次请求
     */
    public JSONObject reNoticeCreateBattleServer(List<Integer> serverIds, int dbIndex) {
        if (!JavaUtils.bool(serverIds)) {
            return new JSONObject();
        }
        logger.info("tvt 创建失败的战斗服查看状态：{}", serverIds);
        TvtBattleServerCreateConfig tvtBattleServerCreateConfig = ServerConfigManager.getInstance().getTvtBattleServerCreateConfig();
        String statusUrl = tvtBattleServerCreateConfig.getStatusUrl();
        if (statusUrl != null) {
            // {"status": "ok", "running": ["90001"], "pending": [], "destroy": []}
            JSONObject sendBattleServerRequest = sendBattleServerRequest(statusUrl, tvtBattleServerCreateConfig.getProjectName(), serverIds, dbIndex);
            logger.info("tvt 战斗服状态查看{}", sendBattleServerRequest);
            return sendBattleServerRequest;
        } else {
            logger.info("没有statusUrl，不用查状态");
            return new JSONObject();
        }
    }

    /**
     * 处理tvt报名
     *
     * @param roleId
     * @param serverId
     * @param hideScore
     * @return
     */
    public int handlePlayerSignup(Long roleId, int serverId, int hideScore) {
        int res = checkTvtPlayerSignup(serverId, roleId);
        if (res == 0) {
            Activity activity = activityDao.findActivityByActivityType(ActivityType.TVT);
            TvtActivityContext activityContext = activity.getActivityContext();
            TvtPlayerSignupInfo entity = tvtPlayerSignupInfoDao.findByRoleId(roleId);
            if (entity != null) {
                logger.info("tvt signup roleId {} , 重复报名", roleId);
                return 4;
            }
            tvtPlayerSignupInfoDao.create(roleId, hideScore, serverId, 1);
            // 玩家报名累计报名数量
            activityContext.setCurrentSignupCount(activityContext.getCurrentSignupCount() + 1);
            // 如果报名数量超出，则不允许再报名
            if (activityContext.getCurrentSignupCount() >= activityContext.getSignupCapacity()) {
                activityContext.setSignupFull(true);

                asyncOperationService.execute(new AsyncOperation() {
                    @Override
                    public boolean init() {
                        return true;
                    }

                    @Override
                    public boolean run() {
                        // 通知game不允许报名
                        tvtControlBroadcastToGameService.broadcastTvtActivity(activity);
                        return false;
                    }

                    @Override
                    public void finish() {

                    }
                });

            }
            activityDao.save(activity);

        }
        return res;
    }

    /**
     * 检查tvt报名条件
     *
     * @param serverId
     * @param roleId
     * @return
     */
    private int checkTvtPlayerSignup(int serverId, Long roleId) {
        int res = 0;
        do {
            //1.检查服务器资格
            TvtQualifiedServer tvtQualifiedServer = tvtQualifiedServerDao.findById(Long.valueOf(serverId));
            if (tvtQualifiedServer == null) {
                res = 1;
                logger.info("tvt 报名失败 服务器{}没有资格", serverId);
                break;
            }
            //2.检查活动状态
            Activity activity = activityDao.findActivityByActivityType(ActivityType.TVT);
            if (activity == null || ((TvtActivityContext) activity.getActivityContext()).getTvtActivityStatus() != TvtActivityStatus.SIGNUP) {
                res = 2;
                logger.info("tvt 报名失败 活动不存在或状态不是报名阶段");
                break;
            }
            //3.检查当前场次是否人满
            TvtActivityContext activityContext = activity.getActivityContext();
            if (activityContext.getCurrentSignupCount() >= activityContext.getSignupCapacity()) {
                res = 3;
                logger.info("tvt 报名失败  人数已满 {}/{}", activityContext.getCurrentSignupCount(), activityContext.getSignupCapacity());
                break;
            }

        } while (false);
        return res;
    }

    /**
     * 接收玩家基本信息
     *
     * @param vo
     */
    public void uploadPlayerSimpleInfo(TvtPlayerSimpleInfoVo vo) {
        if (vo != null) {
            TvtPlayerSimpleInfo entity = tvtPlayerSimpleInfoDao.findById(vo.getRoleId());
            if (entity == null) {
                tvtPlayerSimpleInfoDao.create(vo.getRoleId(), vo.getName(), vo.getAvatar(), vo.getServerId(), vo.getFightPower(), vo.getTownHallLevel(), vo.getGender());
            } else {
                logger.info("tvt 玩家{}基本信息已存在 不处理。。", vo.getRoleId());
            }
            logger.info("tvt 收到{}服务玩家{}的基本信息", vo.getServerId(), vo.getRoleId());
        }
    }

    /**
     * 获得指定数量的战斗服serverId列表
     *
     * @param needNum
     * @return
     */
    public List<Integer> getCanUseBattleServerIds(int needNum) {
        List<Integer> ret = new ArrayList<>();
        TvtBattleServerCreateConfig tvtBattleServerCreateConfig = ServerConfigManager.getInstance().getTvtBattleServerCreateConfig();
        String createUrl = tvtBattleServerCreateConfig.getCreateUrl();
        //如果createUrl为null 说明不是动态创建的TVT，而是内网环境常驻的tvt，从zookeeper中查找战斗服
        if (createUrl == null) {
            //从zookeeper中查找，serverType为TVT_BATTLE的服务器是战斗服。 目前写死，只返回查找到的第一个。
            for (GameServerConfig gsc : configCenter.getLsConfig().getGameServers().values()) {
                if (gsc.serverType() == ServerType.TVT_BATTLE) {
                    ret.add(gsc.getGameServerId());
                    logger.info("查找到测试环境下的TVT战斗服， serverId：{}，", gsc.getGameServerId());
                    return ret;
                }
            }
            if (ret.size() == 0) {
                throw new AlertException("没找到测试环境下的TVT战斗服");
            }
        }

        WorldMapConfig worldMapConfig = ServerConfigManager.getInstance().getWorldMapConfig();
        int tvtBattleServerEndId = worldMapConfig.getTvtBattleServerEndId();
        int tvtBattleServerStartId = worldMapConfig.getTvtBattleServerStartId();
        List<Long> dispatchBattleServerIds = tvtBattleServerDispatchRecordDao.findAll().stream().map(TvtBattleServerDispatchRecord.class::cast)
                .collect(Collectors.mapping(TvtBattleServerDispatchRecord::getBattleServerId, Collectors.toList()));
        logger.info("tvt 已分配battleServerIds={}", dispatchBattleServerIds);
        Map<Integer, GameServerConfig> gameConfigs = configCenter.getLsConfig().getGameServers();
        for (int i = tvtBattleServerStartId; i <= tvtBattleServerEndId; i++) {
            int serverId = i;
            GameServerConfig gameServerConfig = gameConfigs.get(serverId);
            if (gameServerConfig != null && gameServerConfig.isAlive()) {
                logger.info("tvt 战斗服是活跃的，跳过分配{}", serverId);
                continue;
            }
            boolean contains = dispatchBattleServerIds.contains(Long.valueOf(serverId));
            if (!contains) {
                ret.add(serverId);
                if (ret.size() >= needNum) {
                    break;
                }
            }
        }
        return ret;
    }

    public TvtBattleServerDispatchRecord updateAndBroadcastTvtBattleServerDispatchRecord(int battleServerId) {
        TvtBattleServerDispatchRecord tvtBattleServerDispatchRecord = tvtBattleServerDispatchRecordDao.findByBattleServerId(String.valueOf(battleServerId));
        if (tvtBattleServerDispatchRecord == null) {
            // 战斗服起来注册，发现中控服没有信息
            ErrorLogUtil.errorLog("战斗服起来注册,发现中控服没有信息", "battleServerId",battleServerId);
            return null;
        } else {
            logger.info("战斗服启动注册:{}", battleServerId);
            tvtBattleServerDispatchRecord.setStatus(TvtBattleServerDispatchRecord.STATUS_START);
            tvtBattleServerDispatchRecordDao.save(tvtBattleServerDispatchRecord);
            // 广播给game
            tvtControlBroadcastToGameService.broadcastTvtBattleServerDispatchRecord(tvtBattleServerDispatchRecord);
            return tvtBattleServerDispatchRecord;
        }
    }

    /**
     * 获取所有有gvg资格的1赛季serverIds。
     * 如果进入2赛季或更高赛季， 同一组kvk内只保留一个oserverId。
     * 如 2赛季包含1，2，3，4服，  只返回1，2，3，4其中的一个。
     * 目的是gvg中控向game广播，不区分server的情况下，避免重复执行相同的rpc逻辑。
     *
     * @return oServerIds
     */
    public Collection<Integer> getTvtBroadcastRpcTargetsList() {
        Map<Integer, Integer> res = new HashMap<>();
        Collection<TvtQualifiedServer> gvgQualifiedServers = tvtQualifiedServerDao.findAll();
        if (JavaUtils.bool(gvgQualifiedServers)) {
            gvgQualifiedServers.forEach(gvgQualifiedServer -> {
                int serverId = gvgQualifiedServer.getServerId();
                KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getKvkSeasonServerGroupConfigByOServerId(serverId, System.currentTimeMillis());
                int seasonServerId = serverId;
                if (kvkSeasonServerGroupConfig != null) {
                    seasonServerId = kvkSeasonServerGroupConfig.getKServerId();
                }
                res.put(seasonServerId, serverId);
            });
        }
        return res.values();
    }

}
