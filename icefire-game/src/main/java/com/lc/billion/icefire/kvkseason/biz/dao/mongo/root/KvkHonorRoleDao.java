package com.lc.billion.icefire.kvkseason.biz.dao.mongo.root;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.kvkseason.biz.model.honor.KvkHonorRole;

/**
 * <AUTHOR>
 *
 */
@Repository
public class KvkHonorRoleDao extends RootDao<KvkHonorRole> {

	public KvkHonorRoleDao() {
		super(KvkHonorRole.class, false);
	}

	@Override
	protected MongoCursor<KvkHonorRole> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(KvkHonorRole entity) {

	}

	@Override
	protected void removeMemoryIndexes(KvkHonorRole entity) {

	}

	public KvkHonorRole create(int db, Long roleId) {
		KvkHonorRole kvkHonorRole = newEntityInstance();
		kvkHonorRole.setPersistKey(roleId);
		return createEntity(db, kvkHonorRole);
	}

}
