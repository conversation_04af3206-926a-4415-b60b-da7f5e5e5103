package com.lc.billion.icefire.gvgbattle.biz.service.impl;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.graph.Position;
import com.lc.billion.icefire.gvgbattle.biz.model.army.Path;

import java.util.concurrent.atomic.AtomicLong;

public class GVGSupplyWagon {
    static AtomicLong armyIdSeed = new AtomicLong(1000000);
    long armyId;
    Long occupierAllianceId;
    Long challengerAllianceId;
    Path path;
    long beginTime;
    long endTime;
    long totalTime;
    public GVGSupplyWagon(Long occupierAllianceId, Long challengerAllianceId, long totalTime, float percent, Position[] nodeList){
        armyId = armyIdSeed.incrementAndGet();
        this.occupierAllianceId = occupierAllianceId;
        this.challengerAllianceId = challengerAllianceId;
        path = new Path(nodeList);

        long now = TimeUtil.getNow();
        this.totalTime = totalTime;
        beginTime = now;
        endTime = now + (long)(totalTime * (1 - percent));
    }

    public void reverse(){
        long now = TimeUtil.getNow();
        beginTime = now;
        var leftTime = totalTime - (endTime - now);
        endTime = now + leftTime;

        var temp = occupierAllianceId;
        occupierAllianceId = challengerAllianceId;
        challengerAllianceId = temp;

        var nodes = new Position[path.nodes().length];
        for(int i = 0; i < path.nodes().length; ++i){
            var pos = path.nodes()[path.nodes().length - 1 - i];
            nodes[i] = pos;
        }
        path = new Path(nodes);
    }

    public boolean arrived(){
        return TimeUtil.getNow() >= endTime;
    }

    public Long getOccupierAllianceId(){
        return occupierAllianceId;
    }

    public Long getChallengerAllianceId(){
        return challengerAllianceId;
    }

    public Path getPath(){
        return path;
    }

    public long getArmyId(){
        return armyId;
    }

    public long getBeginTime(){
        return beginTime;
    }

    public long getEndTime(){
        return endTime;
    }

    public long getRemainTime(){
        long remainTime = endTime - TimeUtil.getNow();
        if(remainTime < 0){
            remainTime = 0;
        }

        return remainTime;
    }

    public long getTotalTime(){
        return endTime - beginTime;
    }

    public long getTravelTime(){
        return totalTime;
    }
}