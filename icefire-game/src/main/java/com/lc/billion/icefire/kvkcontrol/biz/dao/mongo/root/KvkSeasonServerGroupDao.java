package com.lc.billion.icefire.kvkcontrol.biz.dao.mongo.root;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.kvkcontrol.biz.model.kvk.KvkSeasonServerGroup;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;

/**
 * <AUTHOR>
 *
 */
@Repository
public class KvkSeasonServerGroupDao extends RootDao<KvkSeasonServerGroup> {

	public KvkSeasonServerGroupDao() {
		super(KvkSeasonServerGroup.class, true);
	}

	@Override
	protected MongoCursor<KvkSeasonServerGroup> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(KvkSeasonServerGroup entity) {

	}

	@Override
	protected void removeMemoryIndexes(KvkSeasonServerGroup entity) {

	}

	public KvkSeasonServerGroup create(KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig) {
		KvkSeasonServerGroup kvkSeasonServerGroup = newEntityInstance();
		kvkSeasonServerGroup.setKServerId(kvkSeasonServerGroupConfig.getKServerId());
		kvkSeasonServerGroup.setSeason(kvkSeasonServerGroupConfig.getSeason());
		kvkSeasonServerGroup.setStartTime(kvkSeasonServerGroupConfig.getStartTime());
		kvkSeasonServerGroup.setSettleTime(kvkSeasonServerGroupConfig.getSettleTime());
		kvkSeasonServerGroup.setMatchTime(kvkSeasonServerGroupConfig.getMatchTime());
		kvkSeasonServerGroup.setMatchShowTime(kvkSeasonServerGroupConfig.getMatchShowTime());
		kvkSeasonServerGroup.setEndTime(kvkSeasonServerGroupConfig.getEndTime());
//		kvkSeasonServerGroup.setNextSeasonOServerIds(kvkSeasonServerGroupConfig.getNextSeasonOServerIds());
		kvkSeasonServerGroup.setOServerIds(kvkSeasonServerGroupConfig.getOServerIds());
		return createEntity(Application.getServerId(), kvkSeasonServerGroup);
	}

}
