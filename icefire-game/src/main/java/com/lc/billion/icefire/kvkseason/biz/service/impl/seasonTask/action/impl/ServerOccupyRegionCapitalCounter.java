package com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.impl;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.kvk.SeasonTaskConfig;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.node.RegionCapitalNode;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.RoleSeasonTaskRecord;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.ServerSeasonTaskProgress;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.AbstractSeasonTaskAction;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.SeasonTaskActionType;
import com.longtech.ls.config.ServerType;
import org.springframework.stereotype.Service;

/**
 * 所在服务器占领X次Y级城
 * 
 * <AUTHOR>
 * @date 2021/11/16
 */
@Service
public class ServerOccupyRegionCapitalCounter extends AbstractSeasonTaskAction {
	@Override
	public SeasonTaskActionType getType() {
		return SeasonTaskActionType.SERVER_OCCUPY_REGION_CAPITAL_COUNTER;
	}

	@Override
	public long getProgress(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		ServerSeasonTaskProgress serverRecord = serverSeasonTaskProgressDao.getByServerId(role.getoServerId());
		int i = meta.getConditions().indexOf(getType().getId());
		int param2 = meta.getParam2s().get(i);
		return serverRecord.getOccupyCityTimes(param2);
	}

	@Override
	protected void updateProgress(Role role, RoleSeasonTaskRecord taskRecord, Object... params) {
		RegionCapitalNode node = (RegionCapitalNode) params[0];
		if (Application.getConfigCenter().getServerType(node.getCurrentServerId()) != ServerType.KVK_SEASON) {
			return;
		}

		Alliance alliance = allianceService.getAllianceById(node.getBelongAllianceId());
		if (alliance == null) {
			return;
		}
		int serverId = alliance.getoServerId();
		if (serverId == 0) {
			return;
		}
		ServerSeasonTaskProgress serverProgress = serverSeasonTaskProgressDao.getByServerId(serverId);
		serverProgress.addOccupyCityTimes(node.getLevel());
		serverSeasonTaskProgressDao.save(serverProgress);
	}

	@Override
	public boolean actionFinishCheck(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		int i = meta.getConditions().indexOf(getType().getId());
		int param1 = meta.getParam1s().get(i);
		return getProgress(role, meta) >= param1;
	}
}
