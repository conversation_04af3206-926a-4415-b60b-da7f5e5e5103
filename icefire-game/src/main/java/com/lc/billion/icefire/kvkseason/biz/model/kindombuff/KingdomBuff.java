package com.lc.billion.icefire.kvkseason.biz.model.kindombuff;

import java.util.ArrayList;
import java.util.List;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.annotation.MongoIndex;
import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.protocol.structure.PsBuffInfo;

/**
 *
 * <AUTHOR>
 * @date 2021/9/13
 */
public class KingdomBuff extends AbstractEntity {

	@MongoId
	private Long id;
	@MongoIndex
	private int belongServerId;// 对A服务器（王国）玩家生效

	private List<Integer> effectServerIds = new ArrayList<>(); // A服务器玩家 ，在哪个服的时候才生效

	private String buffId;

	private long endTime;// buff 结束时间

	@Override
	public void setPersistKey(Long id) {
		this.id = id;
	}

	@Override
	public Long getPersistKey() {
		return this.id;
	}

	@Override
	public Long getGroupingId() {
		return this.id;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public int getBelongServerId() {
		return belongServerId;
	}

	public void setBelongServerId(int belongServerId) {
		this.belongServerId = belongServerId;
	}

	public List<Integer> getEffectServerIds() {
		return effectServerIds;
	}

	public void setEffectServerIds(List<Integer> effectServerIds) {
		this.effectServerIds = effectServerIds;
	}

	public String getBuffId() {
		return buffId;
	}

	public void setBuffId(String buffId) {
		this.buffId = buffId;
	}

	public long getEndTime() {
		return endTime;
	}

	public void setEndTime(long endTime) {
		this.endTime = endTime;
	}

	public PsBuffInfo toInfo() {
		PsBuffInfo info = new PsBuffInfo();
		info.setBuffId(String.valueOf(getBuffId()));
		info.setStartTime(getCreateTime());
		info.setDuration(getEndTime() - getCreateTime() <= 0 ? 0 : getEndTime() - getCreateTime());
		info.setMetaId(getBuffId());// 客户端需要赋值这个字段
		return info;
	}
}
