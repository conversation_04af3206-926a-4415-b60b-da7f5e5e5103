package com.lc.billion.icefire.gvgcontrol.biz.service.impl.activity.handler;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.config.ActivityListConfig;
import com.lc.billion.icefire.game.biz.manager.gvg.RZEGameDataVoManager;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.schedule.ScheduleOperation;
import com.lc.billion.icefire.game.biz.service.ScheduleService;
import com.lc.billion.icefire.game.biz.service.impl.activity.AbstractActivityHandler;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GvgBattleServerDispatchRecordDao;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.RZEAllianceInfoDao;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.RZEQualifiedServerDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgBattleServerDispatchRecord;
import com.lc.billion.icefire.gvgcontrol.biz.model.RZEAllianceInfo;
import com.lc.billion.icefire.gvgcontrol.biz.model.RZEQualifiedServer;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.context.GVGActivitySignUpInfo;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.engagement.RZEActivityContext;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.engagement.RZEActivityStatus;
import com.lc.billion.icefire.gvgcontrol.biz.service.GVGControlBroadcastToGVGBattleService;
import com.lc.billion.icefire.gvgcontrol.biz.service.GVGControlBroadcastToGameService;
import com.lc.billion.icefire.gvgcontrol.biz.service.GVGControlService;
import com.lc.billion.icefire.protocol.constant.PsRZEActivityStatus;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.lc.billion.icefire.rpc.vo.gvg.RZEAllianceInfoVo;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.GameServerConfig;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import com.longtech.ls.zookeeper.KvkSeasonsConfig;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.thrift.TBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.*;

@Service
public class RZEActivityHandler extends AbstractActivityHandler<Activity> {
    @Autowired
    private GVGControlBroadcastToGameService gvgControlBroadcastToGameService;
    @Autowired
    private GVGControlBroadcastToGVGBattleService gvgControlBroadcastToGVGBattleService;
    @Autowired
    private RZEGameDataVoManager rzeGameDataVoManager;
    @Autowired
    private GvgBattleServerDispatchRecordDao gvgBattleServerDispatchRecordDao;
    @Autowired
    private GVGControlService gvgControlService;
    @Autowired
    private RZEQualifiedServerDao rzeQualifiedServerDao;
    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private RZEAllianceInfoDao rzeAllianceInfoDao;

    @Override
    protected void onStartImpl(Activity activity) {
        rzeActivityStatusContinue(activity);
    }

    @Override
    protected void onStopImpl(Activity activity) {
        logger.info("RZE 活动结束了，广播活动信息给game和战斗服");
        broadcastRZEActivity(activity);
    }

    @Override
    public void onAdd(Activity activity) {
        broadcastRZEActivity(activity);
    }

    @Override
    public ActivityType getType() {
        return ActivityType.GVG_ENGAGEMENT;
    }

    @Override
    public void tick(Activity activity, long now) {
        // GVG约战阶段处理
        RZEActivityContext gvgEngagementActivityContext = activity.getActivityContext();
        RZEActivityStatus status = gvgEngagementActivityContext.getStatus();
        if (status != null) {
            long nextStatusTijme = gvgEngagementActivityContext.getNextStatusTime();
            if (nextStatusTijme <= now) {
                rzeActivityStatusContinue(activity);
            }
        }
    }

    @Override
    public TBase<?, ?> getActivityInfo(Role role, String activityTypeMetaId) {
        ActivityVo rzeActivityVo = rzeGameDataVoManager.getRzeActivityVo();
        if (rzeActivityVo != null) {
            //TODO 补充协议以及赋值
            return null;
        }
        return null;
    }

    public void broadcastRZEActivity(Activity activity) {
        logger.info("RZE 广播活动信息给game和战斗服");
        gvgControlBroadcastToGameService.broadcastRZEActivity(activity);
        gvgControlBroadcastToGVGBattleService.broadcastRZEActivity(activity);
    }

    public void rzeActivityStatusContinue(Activity activity) {
        GvgSettingConfig config = configService.getConfig(GvgSettingConfig.class);
        List<GvgSettingConfig.GvgActivityTime> rzeActivityTimes = config.getRzeActivityTimes();
        RZEActivityContext activityContext = activity.getActivityContext();
        // 当前阶段
        RZEActivityStatus currentStatus = activityContext.getStatus();
        // 将要进入的阶段
        RZEActivityStatus nextStatus = null;
        GvgSettingConfig.GvgActivityTime nextStatusTime = null;

        //TODO newTurn逻辑
        boolean newTurn = false;
        if (currentStatus == null) {
            nextStatus = RZEActivityStatus.ENGAGE;
            nextStatusTime = rzeActivityTimes.get(0);
        } else {
            PsRZEActivityStatus nextPsStatus = currentStatus.getNextRZEActivityStatus();
            switch (nextPsStatus) {
                case ENGAGE:
                    newTurn = true;
                    nextStatusTime = rzeActivityTimes.get(0);
                    break;
                case REST:
                    nextStatusTime = rzeActivityTimes.get(1);
                    break;
                default:
                    ErrorLogUtil.errorLog("RZE 阶段出错", "nextPsStatus",nextPsStatus);
                    break;
            }
            nextStatus = RZEActivityStatus.findById(nextPsStatus.getValue());
        }
        // 时间计算
        Date nextDate = getRZEActivityNextDate(nextStatusTime.getWeek(), nextStatusTime.getLocalTime());
        Calendar cal = Calendar.getInstance();
        cal.setTime(nextDate);
        if (newTurn) {

        }
        activityContext.setNextStatusTime(cal.getTimeInMillis());
        activityContext.setStatus(nextStatus);
        logger.info("RZE 活动流程: status={}, time={}", activityContext.getStatus(), new Date(activityContext.getNextStatusTime()));
        switch (activityContext.getStatus()) {
            case ENGAGE:
                onActivityStatusEngage(activityContext);
                break;
            case REST:
                onActivityStatusRest();
                break;
            default:
                break;
        }
        activityDao.save(activity);
        broadcastRZEActivity(activity);
    }

    private static Date getRZEActivityNextDate(int week, LocalTime localTime) {
        Calendar cal = Calendar.getInstance();
        int currentDayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
        int targetDayOfWeek = week;
        //转换dayOfWeek：使其1-7表示周一到周日
        currentDayOfWeek -= 1;
        if (currentDayOfWeek == 0) {
            currentDayOfWeek = 7;
        }
        boolean nextWeek = true;
        if (currentDayOfWeek < targetDayOfWeek) {
            nextWeek = false;
        } else {
            int hour = cal.get(Calendar.HOUR_OF_DAY);
            int minute = cal.get(Calendar.MINUTE);
            int second = cal.get(Calendar.SECOND);
            LocalTime localTime2 = LocalTime.of(hour, minute, second);
            boolean isAfter = localTime.isAfter(localTime2);
            if (isAfter) {
                nextWeek = false;
            }
        }
        if (!nextWeek) {
            cal.add(Calendar.DATE, targetDayOfWeek - currentDayOfWeek);
        } else {
            cal.add(Calendar.DATE, 7 - currentDayOfWeek + targetDayOfWeek);
        }
        cal.set(Calendar.HOUR_OF_DAY, localTime.getHour());
        cal.set(Calendar.MINUTE, localTime.getMinute());
        cal.set(Calendar.SECOND, localTime.getSecond());
        return cal.getTime();
    }

    /**
     * 约战阶段处理
     */
    private void onActivityStatusEngage(RZEActivityContext activityContext) {
        //清除战斗服分配记录（约战部分）
        Collection<GvgBattleServerDispatchRecord> gvgBattleServerDispatchRecords = gvgBattleServerDispatchRecordDao.findAll();
        if (JavaUtils.bool(gvgBattleServerDispatchRecords)) {
            List<Integer> battleServerIds = new ArrayList<>();
            gvgBattleServerDispatchRecords.forEach(record -> {
                // 过滤
                if (record.getMatchType() == GvgMatchType.RZE_MON && record.getMatchType() == GvgMatchType.RZE_TUE || record.getMatchType() == GvgMatchType.RZE_WED || record.getMatchType() == GvgMatchType.RZE_THUR || record.getMatchType() == GvgMatchType.RZE_FRI) {
                    battleServerIds.add(record.getBattleServerId().intValue());
                }
            });
            gvgControlService.noticeDestroy(battleServerIds, 0);
            logger.info("销毁所有战斗服(1,2,3,4,5){}", battleServerIds);
        }
        gvgControlBroadcastToGameService.broadcastGvgBattleServerDispatchRecordClear(new GvgMatchType[]{GvgMatchType.RZE_MON, GvgMatchType.RZE_TUE, GvgMatchType.RZE_WED, GvgMatchType.RZE_THUR, GvgMatchType.RZE_FRI});
        logger.info("清理game服的战斗服分配信息(1,2,3,4,5)");
        //TODO 清除约战信息
        //选择本轮参赛服务器
        selectRZEQualifiedServer(activityContext);
        //计算 选择时间 GVGActivityBattleTimeSelect
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        List<GvgSettingConfig.GvgActivityBattleTimeSelect> gvgActivityBattleTimeSelects = gvgSettingConfig.getRzeActivityBattleTimeSelects();
        for (int i = 1; i <= gvgActivityBattleTimeSelects.size(); i++) {
            GvgSettingConfig.GvgActivityBattleTimeSelect gvgActivityBattleTimeSelect = gvgActivityBattleTimeSelects.get(i - 1);
            Date gvgActivityNextDate = GvgSettingConfig.getGVGActivityNextDate(gvgActivityBattleTimeSelect.getWeek(), gvgActivityBattleTimeSelect.getLocalTime());
            Map<Integer, GVGActivitySignUpInfo> signUpInfos = activityContext.getSignUpInfos();
            if (signUpInfos == null) {
                signUpInfos = new HashMap<>();
                activityContext.setSignUpInfos(signUpInfos);
            }
            GVGActivitySignUpInfo activitySignUpInfo = new GVGActivitySignUpInfo();
            activitySignUpInfo.setIndex(i);
            activitySignUpInfo.setSignUpTime(gvgActivityNextDate.getTime());
            activitySignUpInfo.setSignUpNum(0);
            activitySignUpInfo.setSignUpMax(gvgActivityBattleTimeSelect.getMaxNum());
            signUpInfos.put(activitySignUpInfo.getIndex(), activitySignUpInfo);
        }
        logger.info("RZE 计算本次活动的报名时间完成");

        //TODO 清除上周的联盟资格数据，并通知GAME选出本周有资格的联盟，并向中控服汇报
        logger.info("RZE 通知game选出参赛联盟");
        gvgControlBroadcastToGameService.broadcastNoticeSelectRZEAlliance();

        // 延迟delay分钟 向game广播全服联盟简要信息
        int delay = 30;
        logger.info("RZE 延迟{}分钟，向game推送所有约战联盟简要信息", delay);
        scheduleService.schedule(new ScheduleOperation() {
            @Override
            public void execute() {
                broadcastRZEAllianceSimpleInfo();
            }
        }, delay * TimeUtil.MINUTE_MILLIS);
        //TODO 重置RZE任务
    }

    /**
     * 休整阶段处理
     */
    private void onActivityStatusRest() {

    }

    private void broadcastRZEAllianceSimpleInfo() {
        Collection<RZEAllianceInfo> infos = rzeAllianceInfoDao.findAll();
        Collection<RZEAllianceInfoVo> vos = new ArrayList<>();
        infos.forEach(info -> {
            vos.add(info.toVo());
        });
        gvgControlBroadcastToGameService.broadcastRZEAllianceInfoVo(vos);
    }

    private void broadcastRZEAllianceSimpleInfo(int serverId) {
        Collection<RZEAllianceInfo> infos = rzeAllianceInfoDao.findAll();
        Collection<RZEAllianceInfoVo> vos = new ArrayList<>();
        infos.forEach(info -> {
            vos.add(info.toVo());
        });
        gvgControlBroadcastToGameService.broadcastRZEAllianceInfoVo(serverId, vos);
    }

    private void selectRZEQualifiedServer(RZEActivityContext activityContext) {
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG_ENGAGEMENT);
        ActivityListConfig activityListConfig = configService.getConfig(ActivityListConfig.class);
        ActivityListConfig.ActivityListMeta activityListMeta = activityListConfig.getMetaById(activity.getMetaId());
        // 哪些服务器可以参赛
        Map<Integer, GameServerConfig> gameServers = configCenter.getLsConfig().getGameServers();
        KvkSeasonsConfig kvkSeasons = configCenter.getLsConfig().getKvkSeasons();
        for (GameServerConfig gameServerConfig : gameServers.values()) {
            int gameServerId = gameServerConfig.getGameServerId();
            if (!gameServerConfig.isAlive()) {
                logger.info("RZE 服务器{}没启动或已并服", gameServerId);
                continue;
            }
            // TODO 是否对外
            ServerType serverType = ServerConfigManager.getInstance().getServerTypeConfig().getServerType(gameServerId);
            if (serverType != ServerType.GAME && serverType != ServerType.KVK_SEASON) {
                // 非game服跳过
                continue;
            }
            // 区分原始服和赛季服
            switch (serverType) {
                case GAME:
                    handleRZEQualifiedServer(gameServerConfig, gvgSettingConfig, activityListMeta, activityContext, activity);
                    break;
                case KVK_SEASON:
                    KvkSeasonServerGroupConfig serverGroupByKServerId = kvkSeasons.getServerGroupByKServerId(gameServerId);
                    if (serverGroupByKServerId != null) {
                        Set<Integer> oServerIds = serverGroupByKServerId.getOServerIds();
                        if (JavaUtils.bool(oServerIds)) {
                            oServerIds.forEach(sid -> {
                                GameServerConfig gameServerConfig1 = gameServers.get(sid);
                                handleRZEQualifiedServer(gameServerConfig1, gvgSettingConfig, activityListMeta, activityContext, activity);
                            });
                        }
                    } else {
                        ErrorLogUtil.errorLog("RZE 赛季服找不到对应的KvkSeasonServerGroupConfig", "gameServerId",gameServerId);
                    }
                    break;
                default:
                    ErrorLogUtil.errorLog("RZE选择参赛服类型报错", new RuntimeException(),"serverId",gameServerId);
                    break;
            }
        }
    }

    private void handleRZEQualifiedServer(GameServerConfig gameServerConfig, GvgSettingConfig gvgSettingConfig, ActivityListConfig.ActivityListMeta activityListMeta,
                                          RZEActivityContext activityContext, Activity activity) {
        int gameServerId = gameServerConfig.getGameServerId();
        RZEQualifiedServer rzeQualifiedServer = rzeQualifiedServerDao.findById((long) gameServerId);
        boolean isBlockServer = activityListMeta.isBlockServer(gameServerId);
        boolean enabledServers = activityListMeta.isEnabledServers(gameServerId);
        if (rzeQualifiedServer == null) {

            if (isBlockServer) {
                logger.info("RZE 服务器{}在黑名单中，不能参加RZE", gameServerId);
                return;
            }
            if (!enabledServers) {
                logger.info("RZE 服务器{}不在白名单中，不能参加RZE", gameServerId);
                return;
            }
            long now = TimeUtil.getNow();
            int needIntervalDay = gvgSettingConfig.getGvgActivityStartDay();
            long serverOpenTime = gameServerConfig.getOpenTimeMs();
            long rzeQualifiedTime = now - needIntervalDay * TimeUtil.DAY_MILLIS;
            if (serverOpenTime > rzeQualifiedTime) {
                logger.info("RZE 服务器{}开服时间{}，小于设定时间{}，不能参加RZE", gameServerId, serverOpenTime, rzeQualifiedTime);
            }
            rzeQualifiedServerDao.create(gameServerId);
        } else {
            // 之前有资格，但这次可能不在白名单里或者在黑名单里
            if (isBlockServer || !enabledServers) {
                logger.info("RZE 服务器{}在黑名单或不在白名单中，从参赛资格中移除", gameServerId);
                rzeQualifiedServerDao.delete(rzeQualifiedServer);
            } else if (!createValidated(activityListMeta,gameServerId)) {
                // ActivityServiceImpl.activityCreateValidated(gameServerId,
                // activity.getEndTime());
                logger.info("RZE 服务器{}即将进入赛季排期&&剩余时间不足以参加一轮活动，先不参与RZE了,删除参赛资格", gameServerId);
                rzeQualifiedServerDao.delete(rzeQualifiedServer);
            } else {
                // 该服继续参加
            }
        }
    }

    /**
     * 更新约战联盟基本数据
     *
     * @param vos
     */
    public void handleUploadRZEAllianceInfo(Collection<RZEAllianceInfoVo> vos) {
        logger.info("RZE 收到game服上传的 约战联盟数据 {}条", vos.size());
        if (JavaUtils.bool(vos)) {
            vos.forEach(vo -> {
                RZEAllianceInfo entity = rzeAllianceInfoDao.findById(vo.getAllianceId());
                if (entity == null) {
                    entity = rzeAllianceInfoDao.create(vo.getAllianceId(), vo.getServerId(), vo.getRating(), vo.getS1Prosperity(), vo.getName(), vo.getAliasName());
                } else {
                    entity.setRating(vo.getRating());
                    entity.setS1Prosperity(vo.getS1Prosperity());
                    entity.setName(vo.getName());
                    entity.setAliasName(vo.getAliasName());
                    rzeAllianceInfoDao.save(entity);
                }
            });
        }
    }

    /**
     * 查询game服是否有约战活动资格
     *
     * @param serverId
     * @return
     */
    public boolean findRZEServerQualified(int serverId) {
        return rzeQualifiedServerDao.findById((long) serverId) != null;
    }

    /**
     * 查询全服所有联盟数据
     * @return
     */
    public Collection<RZEAllianceInfoVo> findAllRZEAllianceInfoVos() {
        Collection<RZEAllianceInfoVo> res = new ArrayList<>();
        Collection<RZEAllianceInfo> entities = rzeAllianceInfoDao.findAll();
        if (JavaUtils.bool(entities)) {
            entities.forEach(entity -> {
                res.add(entity.toVo());
            });
        }
        return res;
    }
}
