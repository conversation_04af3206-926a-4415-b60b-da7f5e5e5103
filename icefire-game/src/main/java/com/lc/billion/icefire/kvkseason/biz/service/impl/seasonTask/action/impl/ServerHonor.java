package com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.impl;

import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.kvk.SeasonTaskConfig;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.KvkHonorServerDao;
import com.lc.billion.icefire.kvkseason.biz.model.honor.KvkHonorServer;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.RoleSeasonTaskRecord;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.AbstractSeasonTaskAction;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.SeasonTaskActionType;

/**
 * 服务器荣誉值达到X
 * 
 * <AUTHOR>
 * @date 2021/11/16
 */
@Service
public class ServerHonor extends AbstractSeasonTaskAction {
	@Override
	public SeasonTaskActionType getType() {
		return SeasonTaskActionType.SERVER_HONOR;
	}

	@Override
	public long getProgress(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		KvkHonorServer kvkHonorServer = Application.getBean(KvkHonorServerDao.class).findByServerId(role.getoServerId());
		if (kvkHonorServer == null) {
			return 0;
		}
		return kvkHonorServer.getHonorValue();
	}

	@Override
	protected void updateProgress(Role role, RoleSeasonTaskRecord taskRecord, Object... params) {
		// do nothing
	}

	@Override
	public boolean actionFinishCheck(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		int i = meta.getConditions().indexOf(getType().getId());
		int param1 = meta.getParam1s().get(i);
		KvkHonorServer kvkHonorServer = Application.getBean(KvkHonorServerDao.class).findByServerId(role.getoServerId());
		return kvkHonorServer.getHonorValue() >= param1;
	}
}
