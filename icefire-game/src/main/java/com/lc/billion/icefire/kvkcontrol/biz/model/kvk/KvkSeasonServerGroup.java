package com.lc.billion.icefire.kvkcontrol.biz.model.kvk;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import lombok.Getter;
import lombok.Setter;
import org.jongo.marshall.jackson.oid.MongoId;

import java.util.Set;

/**
 * KVK赛季匹配信息，每次计算存库
 * 
 * <AUTHOR>
 *
 */
public class KvkSeasonServerGroup extends AbstractEntity {
	private static final long serialVersionUID = -3008552416497191871L;

	@MongoId
	private Long id;

	@Getter
    @Setter
    private int season;

	private int kServerId;

	private Set<Integer> oServerIds;

	@Getter
    @Setter
    private long startTime;
	@Getter
    @Setter
    private long settleTime;
	@Getter
    @Setter
    private long matchTime;
	@Getter
    @Setter
    private long matchShowTime;
	@Getter
    @Setter
    private long endTime;

	@Override
	public void setPersistKey(Long id) {
		this.id = id;
	}

	@Override
	public Long getPersistKey() {
		return id;
	}

	@Override
	public Long getGroupingId() {
		return id;
	}

    public int getKServerId() {
		return kServerId;
	}

	public void setKServerId(int kServerId) {
		this.kServerId = kServerId;
	}

	public Set<Integer> getOServerIds() {
		return oServerIds;
	}

	public void setOServerIds(Set<Integer> oServerIds) {
		this.oServerIds = oServerIds;
	}

    @Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

}
