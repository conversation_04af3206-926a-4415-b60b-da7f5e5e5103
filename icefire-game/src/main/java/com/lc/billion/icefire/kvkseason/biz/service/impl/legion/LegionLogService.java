package com.lc.billion.icefire.kvkseason.biz.service.impl.legion;

import java.util.List;

import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.BizException;
import com.lc.billion.icefire.game.biz.config.legion.LegionLogConfig;
import com.lc.billion.icefire.game.biz.config.legion.LegionLogConfig.LegionLogMeta;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionLogDao;
import com.lc.billion.icefire.kvkseason.biz.manager.legion.LegionLogManager;
import com.lc.billion.icefire.kvkseason.biz.manager.legion.LegionManager;
import com.lc.billion.icefire.kvkseason.biz.model.legion.Legion;
import com.lc.billion.icefire.kvkseason.biz.model.legion.log.LegionLog;
import com.lc.billion.icefire.kvkseason.biz.model.legion.log.LegionLogType;
import com.lc.billion.icefire.protocol.GcLegionLogAdd;
import com.lc.billion.icefire.protocol.GcLegionLogList;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 *
 */
@Service
public class LegionLogService {

	private static final Logger logger = LoggerFactory.getLogger(LegionLogService.class);

	@Autowired
	private LegionLogDao legionLogDao;
	@Autowired
	private LegionManager legionManager;
	@Autowired
	private LegionLogManager legionLogManager;
	@Autowired
	private ConfigServiceImpl configService;

	public void onEnterWorld(Role role) {
		Long allianceId = role.getAllianceId();
		if (allianceId == null) {
			return;
		}
		Legion legion = legionManager.findLegionByAllianceId(allianceId);
		if (legion == null) {
			return;
		}

		GcLegionLogList gcLegionLogList = legionLogManager.toGcLegionLogList(legion.getId());

		role.send(gcLegionLogList);
	}

	/**
	 * 更新日志
	 */
	public void createLegionLog(Legion legion, LegionLogType type, List<String> params) {
		LegionLogConfig legionLogConfig = configService.getConfig(LegionLogConfig.class);
		LegionLogMeta legionLogMeta = legionLogConfig.getMeta(type);
		if (legionLogMeta == null) {
			ErrorLogUtil.errorLog("缺少配置信息LegionLogConfig", new BizException(),"type",type);
			return;
		}

		String metaId = legionLogMeta.getId();

		List<LegionLog> legionLogs = legionLogDao.findByLegionIdAndMetaId(legion.getId(), metaId);
		if (JavaUtils.bool(legionLogs)) {
			if (legionLogs.size() >= legionLogMeta.getNum()) {
				LegionLog legionLog = legionLogs.get(0);
				legionLogDao.delete(legionLog);
			}
		}

		LegionLog legionLog = legionLogDao.create(legion, metaId, params);

		broadcastGcLegionLogAdd(legionLog);
	}

	public void broadcastGcLegionLogAdd(LegionLog legionLog) {
		GcLegionLogAdd gcLegionLogAdd = new GcLegionLogAdd();
		gcLegionLogAdd.addToLogs(legionLog.toPsLegionLog());
		legionManager.sendMessage(legionLog.getLegionId(), gcLegionLogAdd);
	}
}
