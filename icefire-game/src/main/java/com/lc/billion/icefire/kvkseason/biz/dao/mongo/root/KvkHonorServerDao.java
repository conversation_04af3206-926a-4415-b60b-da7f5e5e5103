package com.lc.billion.icefire.kvkseason.biz.dao.mongo.root;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.kvkseason.biz.model.honor.KvkHonorServer;

/**
 * <AUTHOR>
 *
 */
@Repository
public class KvkHonorServerDao extends RootDao<KvkHonorServer> {

	public KvkHonorServerDao() {
		super(KvkHonorServer.class, false);
	}

	@Override
	protected MongoCursor<KvkHonorServer> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(KvkHonorServer entity) {

	}

	@Override
	protected void removeMemoryIndexes(KvkHonorServer entity) {

	}

	public KvkHonorServer create(int db, int serverId) {
		KvkHonorServer kvkHonorServer = newEntityInstance();
		kvkHonorServer.setPersistKey(Long.valueOf(serverId));
		return createEntity(db, kvkHonorServer);
	}

	public KvkHonorServer findByServerId(int serverId) {
		return findById(Long.valueOf(serverId));
	}
}
