package com.lc.billion.icefire.gvgcontrol.biz.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GVGAllianceSignUpInfoDao;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GVGMatchResultBackupDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGAllianceSignUpInfo;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGMatchResultBackup;
import com.lc.billion.icefire.gvgcontrol.biz.service.impl.activity.handler.GVGActivityHandler;
import com.lc.billion.icefire.rpc.vo.gvg.test.GVGTestMatchAllianceVo;
import com.simfun.sgf.common.tuple.TwoTuple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;

@Service
public class GVGMatchSimulateService {
    @Autowired
    private GVGActivityHandler gvgActivityHandler;
    @Autowired
    private GVGMatchResultBackupDao gvgMatchResultBackupDao;
    @Autowired
    private GVGControlBroadcastToGameService gvgControlBroadcastToGameService;
    @Autowired
    private GVGAllianceSignUpInfoDao gvgAllianceSignUpInfoDao;


    public List<GVGTestMatchAllianceVo> collectAllianceMatchData(long backupTime, GvgMatchType matchType){
        var matchRstList = gvgMatchResultBackupDao.getRoundData(backupTime);

        var serverAllianceMap = new HashMap<Integer, Map<Long, List<Long>>>();
        for(var matchRst: matchRstList){
            if(!matchRst.getMatchType().equals(matchType)){
                continue;
            }

            serverAllianceMap.compute(matchRst.getServerId(), (serverId, allianceMemberMap) -> {
                if(allianceMemberMap == null){
                    allianceMemberMap = new HashMap<>();
                }
                allianceMemberMap.compute(matchRst.getAllianceId(), (allianceId, memberList) -> {
                    if(memberList == null){
                        memberList = new ArrayList<>();
                    }
                    memberList.addAll(matchRst.getRoleList());
                    return memberList;
                });

                return allianceMemberMap;
            });
        }

        return gvgControlBroadcastToGameService.collectAllianceMatchData(serverAllianceMap, matchType);
    }

    public int testMatch(int testId, long backupTime){
        var matchRst = testMatchDo(backupTime);

        // 将ret的内容转成Json格式，并存储在本地文件：testMatchAllianceData.json
        try {
            var objectMapper = new ObjectMapper();
            var json = objectMapper.writeValueAsString(matchRst);
            var file = new File("testMatchAllianceData_" + testId + ".json");
            var fileWriter = new FileWriter(file);
            fileWriter.write(json);
            fileWriter.close();
        } catch (IOException e) {
            ErrorLogUtil.errorLog("collectTestMatchAllianceData", e);
        }
        return matchRst.size();
    }

    public ArrayList<TwoTuple<GVGTestMatchAllianceVo, GVGTestMatchAllianceVo>> testMatchDo(long backupTime){
        if(backupTime <= 0){
            backupTime = gvgMatchResultBackupDao.getLatestRoundTime();
        }

        var matchRst = new ArrayList<TwoTuple<GVGTestMatchAllianceVo, GVGTestMatchAllianceVo>>();
        matchRst.addAll(testMatchDo(backupTime, GvgMatchType.FRIENDLY));
        matchRst.addAll(testMatchDo(backupTime, GvgMatchType.FRIENDLY_TEAM2));

        return matchRst;
    }

    public List<TwoTuple<GVGTestMatchAllianceVo, GVGTestMatchAllianceVo>> testMatchDo(long backupTime, GvgMatchType matchType){
        // 从各个服务器得到初始分数
        var allianceVos = new ArrayList<>(collectAllianceMatchData(backupTime, matchType));

        var dataMap = new HashMap<Long, GVGTestMatchAllianceVo>();
        var groupData = new HashMap<String, List<GVGAllianceSignUpInfo>>();
        for(var data: allianceVos){
            dataMap.put(data.getAllianceId(), data);

            // 历史数据修正分数
            var scoreFixRst = gvgActivityHandler.calcFixedAllianceMatchScore(data.getAllianceId(), data.getMatchType(), data.getFightPower());
            data.setFixedScore(scoreFixRst.fixMatchScore());
            data.setWinLoseRecord(scoreFixRst.winLoseRecord());
            data.setRate(scoreFixRst.rate());

            // 确认匹配组
            var group = gvgActivityHandler.findGvgMatchGroupByServerId(data.getServerId());
            data.setGroup(group);

            groupData.compute(group, (groupId, allianceList) -> {
                if(allianceList == null){
                    allianceList = new ArrayList<>();
                }

                var signUpInfo = new GVGAllianceSignUpInfo();
                signUpInfo.setAllianceId(data.getAllianceId());
                signUpInfo.setMatchType(data.getMatchType());
                signUpInfo.setFixedMatchScore(data.getFixedScore());

                allianceList.add(signUpInfo);

                return allianceList;
            });
        }

        List<TwoTuple<GVGTestMatchAllianceVo, GVGTestMatchAllianceVo>> matchRst = new ArrayList<>();
        groupData.forEach((groupId, signUpList) -> {
            // 针对每个分组，执行匹配
            var signUpMatchRst = gvgActivityHandler.matchNewFriendly(signUpList);
            signUpMatchRst.forEach(rst -> {
                GVGTestMatchAllianceVo firstVo = rst.getFirst() != null ? dataMap.get(rst.getFirst().getAllianceId()): null;
                GVGTestMatchAllianceVo secondVo = rst.getSecond() != null ? dataMap.get(rst.getSecond().getAllianceId()): null;
                matchRst.add(new TwoTuple<>(firstVo, secondVo));
            });
        });

        return matchRst;
    }

    static class BackupComparator {
        int round;
        Long allianceId;
        GvgMatchType matchType;

        public BackupComparator(int round, Long allianceId, GvgMatchType matchType){
            this.round = round;
            this.allianceId = allianceId;
            this.matchType = matchType;
        }

        @Override
        public boolean equals(Object obj) {
            if(!(obj instanceof BackupComparator)){
                return false;
            }

            return this.allianceId.equals(((BackupComparator)obj).allianceId)
                    && this.matchType.equals(((BackupComparator)obj).matchType)
                    && this.round == ((BackupComparator)obj).round;
        }

        @Override
        public int hashCode() {
            return Objects.hash(round, allianceId, matchType);
        }
    }

    /*
    从signUp表构造matchResult表，应该就会使用一次
     */
    public int syncSignUpToBackup(){
        HashMap<BackupComparator, GVGMatchResultBackup> backupMap = new HashMap<>();
        var backupList = gvgMatchResultBackupDao.findAll();
        for(var backup: backupList){
            backupMap.put(new BackupComparator(backup.getRoundId(), backup.getAllianceId(), backup.getMatchType()), backup);
        }

        int count = 0;
        var signUpList = gvgAllianceSignUpInfoDao.findAll();
        for(var signUp: signUpList){
            if(backupMap.containsKey(new BackupComparator(signUp.getRound(), signUp.getAllianceId(), signUp.getMatchType()))){
                continue;
            }

           var lineUp = signUp.getGvgAllianceLineUpInfo();
           var memberList = new ArrayList<Long>();
            memberList.addAll(lineUp.getFormalMemberIds());
            memberList.addAll(lineUp.getTempMemberIds());

            gvgMatchResultBackupDao.create(signUp.getAllianceId(), signUp.getServerId(), memberList, signUp.getMatchType(), signUp.getRound(), signUp.getRound());
            count++;
        }

        return count;
    }
}
