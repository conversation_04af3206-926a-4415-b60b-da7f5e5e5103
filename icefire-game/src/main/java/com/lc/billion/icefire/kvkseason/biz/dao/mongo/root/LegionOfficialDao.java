package com.lc.billion.icefire.kvkseason.biz.dao.mongo.root;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionOfficial;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 *
 */
@Repository
public class LegionOfficialDao extends RootDao<LegionOfficial> {

	private Map<Long, List<LegionOfficial>> dataByLegionId = new MyConcurrentMap<>();

	public LegionOfficialDao() {
		super(LegionOfficial.class, true);
	}

	@Override
	protected MongoCursor<LegionOfficial> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(LegionOfficial entity) {
		dataByLegionId.compute(entity.getLegionId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
	}

	@Override
	protected void removeMemoryIndexes(LegionOfficial entity) {
		List<LegionOfficial> list = dataByLegionId.get(entity.getLegionId());
		if (list != null) {
			list.remove(entity);
		}
	}

	public LegionOfficial create(int db, Long legionId, int rank, int group, int order) {
		LegionOfficial legionOfficial = newEntityInstance();
		legionOfficial.setGroup(group);
		legionOfficial.setOrder(order);
		legionOfficial.setLegionId(legionId);
		legionOfficial.setRank(rank);
		return createEntity(db, legionOfficial);
	}

	public List<LegionOfficial> findByLegionId(Long legionId) {
		return dataByLegionId.get(legionId);
	}

	public LegionOfficial findByLegionId(Long legionId, int group, int order) {
		List<LegionOfficial> legionOfficials = findByLegionId(legionId);
		if (JavaUtils.bool(legionOfficials)) {
			for (LegionOfficial legionOfficial : legionOfficials) {
				if (legionOfficial.getGroup() == group && legionOfficial.getOrder() == order) {
					return legionOfficial;
				}
			}
		}
		return null;
	}

}
