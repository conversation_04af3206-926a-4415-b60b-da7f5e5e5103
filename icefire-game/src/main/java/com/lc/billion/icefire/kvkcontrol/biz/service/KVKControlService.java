package com.lc.billion.icefire.kvkcontrol.biz.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Multimap;
import com.google.common.collect.Table;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.support.dingtalk.DingtalkClient;
import com.lc.billion.icefire.core.utils.CollectionUtils;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.config.SeasonTimeConfig;
import com.lc.billion.icefire.game.biz.config.kvk.KvkGroupConfig;
import com.lc.billion.icefire.game.biz.config.kvk.KvkGroupConfig.KvkGroupMeta;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.kvkcontrol.biz.dao.mongo.root.KvkSeasonServerGroupDao;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.longtech.ls.zookeeper.GameServerConfig;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import com.longtech.ls.zookeeper.KvkSeasonsConfig;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.zookeeper.CreateMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * <p>
 * 1、定时计算更新未来时间 2、启服计算未来赛季信息
 */
@Service
public class KVKControlService {
    private static final Logger log = LoggerFactory.getLogger(KVKControlService.class);

    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private KvkSeasonServerGroupDao kvkSeasonServerGroupDao;
    @Autowired
    private AsyncOperationServiceImpl asyncOperationService;

    private ServerType serverType;

    public void startService() {
        serverType = ServerConfigManager.getInstance().getWorldMapConfig().getServerType();
        if (serverType == ServerType.KVK_CONTROL) {
            log.info("启服重算KVK赛季信息");
            calculateKVKSeason();
        }
    }

    @Scheduled(cron = "0 0 23 * * ? ")
    public void autoCalculateKvkSeason() {
        if (serverType == ServerType.KVK_CONTROL) {
            log.info("定时重算KVK赛季信息开始");
            long start = TimeUtil.getNow();
            calculateKVKSeason();
            log.info("定时重算KVK赛季信息结束，用时：{}", (TimeUtil.getNow() - start));
        }
    }

    private void recalculateKvkSeasonMigrateTime() {
        KvkSeasonsConfig kvkSeasons = configCenter.getLsConfig().getKvkSeasons();
        Map<Integer, KvkSeasonServerGroupConfig> unmodifiable_kServerId_ServerGroups = kvkSeasons.unmodifiable_kServerId_ServerGroups();
        // 需要处理的K服配置
        List<KvkSeasonServerGroupConfig> needCalculateServers = new ArrayList<>();
        if (JavaUtils.bool(unmodifiable_kServerId_ServerGroups)) {
            long currentTimeMillis = TimeUtil.getNow();
            for (KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig : unmodifiable_kServerId_ServerGroups.values()) {
                if (kvkSeasonServerGroupConfig.getSeason() <= 2) {
                    // 二赛季之后的服务器
                    continue;
                }
                // 当前是战争
                if (!kvkSeasonServerGroupConfig.afterStartTime(currentTimeMillis)) {
                    continue;
                }
                // 下赛季未开始
                Set<Integer> oServerIds = kvkSeasonServerGroupConfig.getOServerIds();
                if (!JavaUtils.bool(oServerIds)) {
                    continue;
                }
                long nextSeasonReadyTime = 0;
                for (Integer oServerId : oServerIds) {
                    KvkSeasonServerGroupConfig kvkSeasonServerGroupConfigByOServerIdAndSeason = kvkSeasons.getKvkSeasonServerGroupConfigByOServerIdAndSeason(oServerId,
                            kvkSeasonServerGroupConfig.getSeason() + 1);
                    if (kvkSeasonServerGroupConfigByOServerIdAndSeason == null) {
                        // 没有下赛季信息，跳过
                        continue;
                    }
                    if (kvkSeasonServerGroupConfigByOServerIdAndSeason.afterStartTime(currentTimeMillis)) {
                        // 已经过了下赛季开始时间
                        continue;
                    }

                }
            }
        }
    }

    private void calculateKServer(int kServerId) {
        KvkGroupMeta kvkGroupMeta = configService.getConfig(KvkGroupConfig.class).getKvkServerMeta(kServerId);
        if (kvkGroupMeta == null) {
            return;
        }
        boolean allfound = kvkGroupMeta.getGroups().length > 0;
        for (var oSid : kvkGroupMeta.getGroups()) {
            if (configCenter.getLsConfig().getGameServers().get(oSid) == null) {
                return;
            }
        }
        if (allfound) {
            handleSeason(kServerId, kvkGroupMeta.getSeason());
            log.info("处理 kServerId={} groups={}", kServerId, Arrays.asList(kvkGroupMeta.getGroups()));
        }
    }

    private void calculateKVKSeason0() {
        KvkSeasonsConfig kvkSeasons = configCenter.getLsConfig().getKvkSeasons();
        var gsMap = configCenter.getLsConfig().getGameServers();
        var serverConfig = ServerConfigManager.getInstance().getServerTypeConfig();
        //找到已经配置的赛季服 加载分组信息
        for (var sId : gsMap.keySet()) {
            //从已经配置的赛季服里查找分组信息并且创建分组
            if (serverConfig.getServerType(sId) != ServerType.KVK_SEASON) {
                continue;
            }
            calculateKServer(sId);
        }
        // zookeeper里所有kvk服
        Table<Integer, Integer, KvkSeasonServerGroupConfig> allKVKServers = HashBasedTable.create();
        if (kvkSeasons != null) {
            boolean needNotify = false;
            // 所有的K服信息检测
            Map<Integer, KvkSeasonServerGroupConfig> unmodifiable_kServerId_ServerGroups = kvkSeasons.unmodifiable_kServerId_ServerGroups();
            for (KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig : unmodifiable_kServerId_ServerGroups.values()) {
                // 过期的删除
                boolean expired = configCenter.isKvkSeasonServerGroupConfigExpired(kvkSeasonServerGroupConfig);
                if (expired) {
                    log.info("检测到过期K服：{}", kvkSeasonServerGroupConfig);
                    // TODO 移除
                    // needNotify = true;
                    continue;
                }
                int kServerId = kvkSeasonServerGroupConfig.getKServerId();
                if (allKVKServers.containsColumn(kServerId)) {
                    ErrorLogUtil.errorLog("KVK中控服计算，zookeeper中K服ServerId重复", "kServerId", kServerId);
                    throw new AlertException("KVK中控服计算,zookeeper中K服ServerId重复", "kServerId", kServerId);
                }
                allKVKServers.put(kvkSeasonServerGroupConfig.getSeason(), kvkSeasonServerGroupConfig.getKServerId(), kvkSeasonServerGroupConfig);
            }
            if (needNotify) {
                configCenter.getLsConfig().getKvkSeasons().notifyRefresh();
            }
        }
        handleSeason(allKVKServers);
    }

    /**
     * 启服或每日23:50或热更执行
     */
    public void calculateKVKSeason() {
        log.info("开始计算KVK赛季信息");
        if (serverType == null) {
            serverType = ServerConfigManager.getInstance().getWorldMapConfig().getServerType();
        }
        if (serverType != ServerType.KVK_CONTROL) {
            ErrorLogUtil.errorLog("服务器不是KVK中控服，不执行重算KVK逻辑");
            return;
        }
        try {
            calculateKVKSeason0();
        } catch (ExpectedException ignored) {

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("calculateKVKSeason0 系统异常", e);
        }

    }

    /**
     * 计算k服的匹配信息，未来的，GM操作
     *
     * @param kvkServerId
     */
    public void handleSeason(int kvkServerId, int season) {
        KvkGroupMeta kvkGroupMeta = configService.getConfig(KvkGroupConfig.class).getKvkServerMeta(kvkServerId);
        handleKvkServer(kvkGroupMeta, season);
    }

    private void handleSeason(Table<Integer, Integer, KvkSeasonServerGroupConfig> allKVKServers) {
        var allKVKGroup = configService.getConfig(KvkGroupConfig.class).getKvkServerGroups();
        if (allKVKGroup == null) {
            ErrorLogUtil.errorLog("没有任何K服信息");
            return;
        }
        var platform = ServerConfigManager.getInstance().getGameConfig().getPlatform();
        for (var entry : allKVKGroup.rowKeySet()) {
            Integer season = entry.intValue();
            Map<Integer, KvkGroupMeta> kvkGroupMetas = allKVKGroup.row(season);
            log.info("计算KVK赛季{}的信息", season);
            // zookeeper里对应赛季的K服信息
            Map<Integer, KvkSeasonServerGroupConfig> seasons = allKVKServers.row(season);
            // 赛季配置
            if (JavaUtils.bool(kvkGroupMetas)) {
                Set<Integer> allKvkServerIds = new HashSet<>(seasons.keySet());
                // 二赛季有基础数据
                for (KvkGroupMeta kvkGroupMeta : kvkGroupMetas.values()) {
                    if (kvkGroupMeta.getPlatform() != platform) {
                        continue;
                    }
                    int kvkServerId = kvkGroupMeta.getKvkServerId();
                    allKvkServerIds.remove(kvkServerId);
                    handleKvkServer(kvkGroupMeta, season);
                }
                // 多余的K服清理
                if (allKvkServerIds.size() > 0) {
                    for (Integer kvkServerId : allKvkServerIds) {
                        log.info("K服{}不在配置表或配置出错了，销毁", kvkServerId);
                        configCenter.getLsConfig().getKvkSeasons().removeServerGroup(kvkServerId);
                    }
                }
            } else {
                // 二赛季没有策划配置
                if (JavaUtils.bool(seasons)) {
                    // 删掉所有zookeeper里的二赛季信息
                    seasons.keySet().forEach(kvkServerId -> configCenter.getLsConfig().getKvkSeasons().removeServerGroup(kvkServerId));
                }
            }
        }
        configCenter.getLsConfig().getKvkSeasons().notifyRefresh();
    }

    /**
     * 检查一个分组是否需要创建或者更新
     *
     * @param kvkGroupMeta
     * @return 是否生效
     */
    private boolean handleKvkServer(KvkGroupMeta kvkGroupMeta, int season) {
        long now = TimeUtil.getNow();
        int kvkServerId = kvkGroupMeta.getKvkServerId();
        // zookeeper里的K服信息
        KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getLsConfig().getKvkSeasons().getServerGroupByKServerId(kvkServerId);
        // zookeeper里分组
        Set<Integer> oServerIds = new HashSet<>();
        if (kvkSeasonServerGroupConfig != null) {
            // 有信息要判断更新未来时间
            oServerIds = kvkSeasonServerGroupConfig.getOServerIds();
            // 是否锁定判断，结束阶段前不再计算
            long readyTime = kvkSeasonServerGroupConfig.getStartTime();
            if (readyTime < now) {
                log.info("当前赛季{}赛季已经开始，不用重算kServerId={}", season, kvkServerId);
                return true;
            }
        }

        // 策划配置分组
        int[] groups = kvkGroupMeta.getGroups();
        if (groups == null || groups.length <= 0) {
            ErrorLogUtil.errorLog("策划配表K服分组信息空", "kServerId", kvkServerId);
            return false;
        }

        Set<Integer> tempList = new HashSet<>();
        for (int groupServerId : groups) {
            tempList.add(groupServerId);
        }

        boolean setEquals = CollectionUtils.isSetEquals(oServerIds, tempList);
        if (!setEquals) {
            ErrorLogUtil.errorLog("K服分组配置变化了", "kServerId", kvkServerId, "old", oServerIds, "new", tempList);
        }

        KvkSeasonsConfig kvkSeasonsConfig = configCenter.getLsConfig().getKvkSeasons();
        Calendar openCalendar = Calendar.getInstance();
        // 二赛季开服时间最晚的那个服的开服时间
        long latestOpenTime = 0;
        int lastestOpenWeekDay = 0;
        // 非二赛季结束最晚的那个服的结束时间
        long latestEndTime = 0;
        // game服zk配置
        Map<Integer, GameServerConfig> gameServers = configCenter.getLsConfig().getGameServers();
        for (int gameServerId : groups) {
            GameServerConfig gameServerConfig = gameServers.get(gameServerId);
            if (gameServerConfig == null) {
                ErrorLogUtil.errorLog("KVK配置组里有服务器不存在", new RuntimeException(), "kServerId", kvkServerId, "gameServerId", gameServerId);
                return false;
            }
            // 如果当前是二赛季，需要取开服时间对齐
            // 如果当前是三赛季之后，取上赛季结束时间（即本赛季的准备时间）
            if (season == 2) {
                long openTimeMs = gameServerConfig.getOpenTimeMs();
                if (openTimeMs > latestOpenTime) {
                    latestOpenTime = openTimeMs;
                    lastestOpenWeekDay = gameServerConfig.getOpenServerDayWeek();
                    log.info("二赛季开服时间计算 kServerId={}，serverId={}，time={}", kvkServerId, gameServerId, openTimeMs);
                }
            } else if (season > 2) {
                KvkSeasonServerGroupConfig kvkSeasonServerGroupConfigByServerId = kvkSeasonsConfig.getServerGroupByOServerIdAndTime(gameServerId, now);
                if (kvkSeasonServerGroupConfigByServerId == null) {
                    ErrorLogUtil.errorLog("赛季匹配出错 原服zk中没有之前的赛季信息", "kServerId", kvkServerId, "season", season, "gameServerId", gameServerId);
                    continue;
                }
                if (kvkSeasonServerGroupConfigByServerId.getSeason() + 1 < season) {
                    log.info(" {} 赛季分组kServerId={}暂时不能创建 因为它分组下的服务器 {} 所属K服 {} 仍然是第 {} 赛季", kvkServerId, season, gameServerId, kvkSeasonServerGroupConfigByServerId.getKServerId(), kvkSeasonServerGroupConfigByServerId.getSeason());
                    return false;
                }
                latestEndTime = Math.max(kvkSeasonServerGroupConfigByServerId.getEndTime(), latestEndTime);
                log.info(" {}-服{} 赛季上赛季服务器 {} 结束时间 {} ", kvkServerId, season, gameServerId, kvkSeasonServerGroupConfigByServerId.getSettleTime());
            } else {
                throw new AlertException("赛季匹配报错", "season", season, "kServerId", kvkServerId);
            }
        }
        if (latestOpenTime == 0 && latestEndTime == 0) {
            ErrorLogUtil.errorLog("KVK分组所有服务器计算时间有问题", "kServerId", kvkServerId);
            return false;
        }

        // 上赛季配置
        SeasonTimeConfig seasonTimeConfig = configService.getConfig(SeasonTimeConfig.class);
        if (latestOpenTime > 0) {
            SeasonTimeConfig.SeasonTimeMeta lastSeasonMeta = seasonTimeConfig.getMetaBySeason(lastestOpenWeekDay, season - 1);
            if (null == lastSeasonMeta) {
                ErrorLogUtil.errorLog("上一赛季meta不存在", "season", season, "lastestOpenWeekDay", lastestOpenWeekDay,
                        "kServerId", kvkServerId);
                return false;
            }
            // 计算二赛季通用时间
            openCalendar.setTimeInMillis(latestOpenTime);
            // 战争阶段
            openCalendar.add(Calendar.DATE, lastSeasonMeta.getSeasonTime() + lastSeasonMeta.getRewardExTime() +
                    lastSeasonMeta.getMatchTime() + lastSeasonMeta.getMatchExTime());
        } else if (latestEndTime > 0) {
            // 三赛季及之后的赛季信息
            // 上轮结束时间往后推
            openCalendar.setTimeInMillis(latestEndTime);
        }
        if (kvkGroupMeta.getServerOpenTime() > 0) {
            openCalendar.setTimeInMillis(kvkGroupMeta.getServerOpenTime());
        }
        openCalendar.set(Calendar.MINUTE, 0);
        openCalendar.set(Calendar.SECOND, 0);
        openCalendar.set(Calendar.MILLISECOND, 0);
        if (latestOpenTime > 0) {
            // 时间修正
            long time = openCalendar.getTimeInMillis();
            if (time < now) {
                openCalendar.setTimeInMillis(now);
                openCalendar.set(Calendar.MINUTE, 0);
                openCalendar.set(Calendar.SECOND, 0);
                openCalendar.set(Calendar.MILLISECOND, 0);
            }
        }

        SeasonTimeConfig.SeasonTimeMeta currentSeasonMeta = seasonTimeConfig.getMetaBySeason(0, season);
        if (currentSeasonMeta == null) {
            log.info("SeasonTimeConfig.SeasonTimeMeta is null getMetaBySeason season={} lastestOpenWeekDay={} kServerId={}", season, lastestOpenWeekDay, kvkServerId);
            return false;
        }
        // 开始时间
        long startTime = openCalendar.getTimeInMillis();
        // 结算时间
        // 战争阶段持续一段时间后结算
        openCalendar.add(Calendar.DATE, currentSeasonMeta.getSeasonTime());
        long settleTime = openCalendar.getTimeInMillis();
        // 匹配时间
        openCalendar.add(Calendar.DATE, currentSeasonMeta.getRewardExTime());
        long matchTime = openCalendar.getTimeInMillis();
        // 匹配展示时间
        openCalendar.add(Calendar.DATE, currentSeasonMeta.getMatchTime());
        long matchShowTime = openCalendar.getTimeInMillis();
        // 结束时间
        openCalendar.add(Calendar.DATE, currentSeasonMeta.getMatchExTime());
        long endTime = openCalendar.getTimeInMillis();
        KvkSeasonServerGroupConfig newKvkSeasonServerGroupConfig = new KvkSeasonServerGroupConfig(kvkServerId, season, startTime,
                settleTime, matchTime, matchShowTime, endTime, tempList);
        if (kvkSeasonServerGroupConfig == null) {
            configCenter.getLsConfig().getKvkSeasons().addServerGroup(newKvkSeasonServerGroupConfig);
            configCenter.getLsConfig().getKvkSeasons().notifyRefresh();
            log.info("kServerId={} season={}生成一条KVK分组信息{}", kvkServerId, season, newKvkSeasonServerGroupConfig);
            kvkSeasonServerGroupDao.create(newKvkSeasonServerGroupConfig);
            return true;
        } else if (newKvkSeasonServerGroupConfig.equals(kvkSeasonServerGroupConfig)) {
            ErrorLogUtil.errorLog("记录重复不需要更新", "config", newKvkSeasonServerGroupConfig, "kServerId", kvkServerId);
            return true;
        }

        // 过去时的时间不变
        if (kvkSeasonServerGroupConfig.getStartTime() < now) {
            newKvkSeasonServerGroupConfig.setStartTime(kvkSeasonServerGroupConfig.getStartTime());
        }
        if (kvkSeasonServerGroupConfig.getSettleTime() < now) {
            newKvkSeasonServerGroupConfig.setSettleTime(kvkSeasonServerGroupConfig.getSettleTime());
        }
        if (kvkSeasonServerGroupConfig.getMatchTime() < now) {
            newKvkSeasonServerGroupConfig.setMatchTime(kvkSeasonServerGroupConfig.getMatchTime());
        }
        if (kvkSeasonServerGroupConfig.getMatchShowTime() < now) {
            newKvkSeasonServerGroupConfig.setMatchShowTime(kvkSeasonServerGroupConfig.getMatchShowTime());
        }
        if (kvkSeasonServerGroupConfig.getEndTime() < now) {
            newKvkSeasonServerGroupConfig.setEndTime(kvkSeasonServerGroupConfig.getEndTime());
        }
        configCenter.getLsConfig().getKvkSeasons().updateServerGroup(newKvkSeasonServerGroupConfig);
        configCenter.getLsConfig().getKvkSeasons().notifyRefresh();
        log.info("kServerId={} season={}更新一条KVK分组信息{}", kvkServerId, season, newKvkSeasonServerGroupConfig);
        kvkSeasonServerGroupDao.create(newKvkSeasonServerGroupConfig);

        return true;
    }

    /**
     * 有bug的时候热更后调用
     */
    private void forHotFixCode(String reason) {

    }

    //    @Scheduled(cron = "0 * * * * * ")
    @Scheduled(cron = "0 0 8 * * 1 ")
    public void autoNotifySeasonOver() {
        if (serverType != ServerType.KVK_CONTROL) {
            return;
        }
        log.info("计算本周到期的服务器并且通知到钉钉群");
        long start = TimeUtil.getNow();
        try {
            String msg = autoNotifySeasonOver0();
            asyncOperationService.execute(() -> notifyDingding("赛季结算" + msg));
            log.info("计算本周到期的服务器并且通知到钉钉群 结束 msg={} 用时:{}ms", msg, (TimeUtil.getNow() - start));
        } catch (Exception e) {
            ErrorLogUtil.errorLog("计算到期的服务器异常", e);
        }
    }

    private boolean notifyDingding(String msg) {
        String dingDingKvkNotifyUrl = configCenter.getLsConfig().getDingDingKvkNotifyUrl();
        if (!JavaUtils.bool(dingDingKvkNotifyUrl)) {
            log.info("kvk结算通知地址没有配置，忽略掉本次计算通知");
            return false;
        }
        try {
            DingtalkClient.getInstance().sendMsg(dingDingKvkNotifyUrl, msg, true);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return false;
    }

    private String autoNotifySeasonOver0() {
        Multimap<Integer, GameServerConfig> seasonMap = ArrayListMultimap.create();
        long now = TimeUtil.getNow();
        long weekBegin = TimeUtil.getWeekStartTime(now);
        long nextWeekEnd = TimeUtil.getNextWeekStartTime(now) + TimeUtil.DAY_MILLIS * 7;
        for (var gsc : configCenter.getLsConfig().getGameServers().values()) {
            if (!gsc.isAlive()) {
                //进程没起来或者进入了别的赛季 不做处理
//                continue;
            }
            var serverType = ServerConfigManager.getInstance().getServerTypeConfig().getServerType(gsc.getGameServerId());
            if (serverType != ServerType.KVK_SEASON && serverType != ServerType.GAME) {
                continue;
            }
            if (gsc.isGuideServer()) {
                continue;
            }
            var endTime = getSettleTimeByServerId(gsc.getGameServerId());
            if (endTime == null) {
                continue;
            }
            int season = 1;
            if (serverType == ServerType.KVK_SEASON) {
                var kServer = configCenter.getLsConfig().getKvkSeasons().getServerGroupByKServerId(gsc.getGameServerId());
                season = kServer.getSeason();
                var oServerId = kServer.getOServerIds().stream().findAny();
                if (oServerId.isPresent() && configCenter.getLsConfig().getKvkSeasons().getKvkSeasonServerGroupConfigByOServerIdAndSeason(oServerId.get(), season + 1) != null) {
                    continue;
                }
            } else {
                var kServer = configCenter.getLsConfig().getKvkSeasons().getServerGroupByOServerIdAndTime(gsc.getGameServerId(), now);
                if (kServer != null) {
                    //已经进入了赛季，不做处理
                    continue;
                }
            }
            if (endTime < weekBegin) {
                log.info("赛季到期通知 赛季:{} 服务器{} 已经到期但是尚未处理,到期时间是{}", season, gsc.getGameServerId(), endTime);
                seasonMap.put(season, gsc);
            } else if (endTime <= nextWeekEnd) {
                log.info("赛季到期通知 赛季:{} 服务器{}  将于本周到期", season, gsc.getGameServerId());
                seasonMap.put(season, gsc);
            }
        }
        if (seasonMap.isEmpty()) {
            return "暂无需要处理的信息";
        }
        StringBuffer sum = new StringBuffer();
        sum.append("管理员您好，下周:");
        for (int season : seasonMap.keySet()) {
            var serverConfigList = new ArrayList<>(seasonMap.get(season));
            int minMatchSize = getMatchServerSizeBySeason(season);
            if (serverConfigList.size() > minMatchSize) {
                int remainder = serverConfigList.size() % minMatchSize;
                for (int i = 0; i < remainder; i++) {
                    serverConfigList.removeLast();
                }
            }
            int maxKServerId = getNextServerIdBySeason(season + 1); //下一赛季服务器ID
            sum.append("\r\n");
            sum.append(season);
            sum.append("赛季");
            sum.append("到期的服务器数量为").append(serverConfigList.size());
            sum.append("分别是:");
            sum.append(serverConfigList.stream().map(g -> String.valueOf(g.getGameServerId())).collect(Collectors.joining(",", "[", "]")));
            if (serverConfigList.size() < minMatchSize) {
                sum.append("不足最低要求").append(minMatchSize).append(",下一赛季延后。");
                continue;
            } else {
                if (maxKServerId == 0) {
                    sum.append("满足最低要求，但是没有下一赛季的赛季服配置，请策划配置");
                    continue;
                }
                sum.append("本赛季新的k服ID是");
                sum.append(maxKServerId + 1);
            }
        }
        sum.append("总结完毕，祝好");
        return sum.toString();
    }

    /**
     * @param season
     * @return
     */
    public Integer getNextServerIdBySeason(int season) {
        int maxId = 0;
        for (var ks : configCenter.getLsConfig().getKvkSeasons().unmodifiable_kServerId_ServerGroups().values()) {
            if (ks.getSeason() == season) {
                maxId = Math.max(maxId, ks.getKServerId());
            }
        }
        return maxId;
    }

    /**
     * 按照服务器获取赛季结算开始的时间点
     *
     * @param serverId
     * @return
     */
    public Long getSettleTimeByServerId(int serverId) {
        long openTime = configCenter.getOpenTimeMs(serverId);
        if (openTime == 0) {
            return null;
        }
        int serverOpenDayOfWeek = TimeUtil.getDayOfWeek(openTime);
        var serverType = ServerConfigManager.getInstance().getServerTypeConfig().getServerType(serverId);
        int season = 1;
        if (serverType == ServerType.KVK_SEASON) {
            var kvkSeasonServerGroupConfig = configCenter.getLsConfig().getKvkSeasons().getServerGroupByKServerId(serverId);
            if (kvkSeasonServerGroupConfig == null) {
                return null;
            }
            return kvkSeasonServerGroupConfig.getSettleTime();
        }
        SeasonTimeConfig.SeasonTimeMeta metaBySeason = configService.getConfig(SeasonTimeConfig.class).getMetaBySeason(serverOpenDayOfWeek, season);
        if (metaBySeason == null) {
            return null;
        }
        long startTime = TimeUtil.getBeginOfDay(openTime);
        return startTime + TimeUtil.DAY_MILLIS * metaBySeason.getSeasonTime();
    }

    /**
     * 获取本赛季进入下一赛季需要的服务器数量
     * 二赛季4个服 下一赛季需要6个服所以至少是6x2 ->才能进入下一赛季
     *
     * @param season
     * @return
     */
    public int getMatchServerSizeBySeason(int season) {
        return switch (season) {
            case 1 -> 4;
            case 2 -> 3;
            default -> 1;
        };
    }

    /**
     * 每周三检查一次 本周已经结算的服务器还有没有没配置赛季的
     */
    @Scheduled(cron = "0 0 8 * * 3 ")
    public void autoCheckKvkGroupConfig() {
        if (serverType != ServerType.KVK_CONTROL) {
            return;
        }
        log.info("计算本周到期且没有配置分组的服务器");
        long start = TimeUtil.getNow();
        String msg = autoCheckKvkGroupConfig0();
        asyncOperationService.execute(() -> notifyDingding("赛季结算KVK分组配置检查" + msg));
        checkCreateKvkServer();
        log.info("计算本周结算但是还没上传配置的服务器分组并且通知到钉钉群 结束 msg={} 用时:{}ms", msg, (TimeUtil.getNow() - start));
    }

    private String autoCheckKvkGroupConfig0() {
        Multimap<Integer, GameServerConfig> seasonMap = ArrayListMultimap.create();
        long now = TimeUtil.getNow();
        var kvkGroupConfig = configService.getConfig(KvkGroupConfig.class);
        for (var gsc : configCenter.getLsConfig().getGameServers().values()) {
            var serverType = ServerConfigManager.getInstance().getServerTypeConfig().getServerType(gsc.getGameServerId());
            if (serverType != ServerType.KVK_SEASON && serverType != ServerType.GAME) {
                continue;
            }
            if (gsc.isGuideServer()) {
                continue;
            }
            var settTime = getSettleTimeByServerId(gsc.getGameServerId());
            if (settTime == null || settTime > now) {
                continue;
            }
            int season = 1;
            if (serverType == ServerType.KVK_SEASON) {
                var kvkGroup = kvkGroupConfig.getKvkServerMeta(gsc.getGameServerId());
                if (kvkGroup == null) {
                    //本赛季的配置没有，忽略掉
                    continue;
                }
                season = kvkGroup.getSeason();
                //下一赛季的配置 从本赛季的原服中任何一个服被配置了下一赛季就算有配置了
                var nextGroup = kvkGroupConfig.getKvkGroupBySeasonAndOServerId(season + 1, kvkGroup.getServerList().get(0));
                if (nextGroup != null) {
                    continue;
                }
            } else {
                var kvkGroup = kvkGroupConfig.getKvkGroupBySeasonAndOServerId(season + 1, gsc.getGameServerId());
                if (kvkGroup != null) {
                    continue;
                }
            }
            seasonMap.put(season, gsc);
        }
        if (seasonMap.isEmpty()) {
            return "暂无需要处理的信息";
        }
        var sum = new StringBuilder();
        sum.append("管理员您好，本周:");
        for (int season : seasonMap.keySet()) {
            var serverConfigList = new ArrayList<>(seasonMap.get(season));
            int minMatchSize = getMatchServerSizeBySeason(season);
            if (serverConfigList.size() > minMatchSize) {
                int remainder = serverConfigList.size() % minMatchSize;
                for (int i = 0; i < remainder; i++) {
                    serverConfigList.removeLast();
                }
            }
            int maxKServerId = getNextServerIdBySeason(season + 1); //下一赛季服务器ID
            sum.append("\r\n");
            sum.append(season);
            sum.append("赛季");
            sum.append("但是未上传配置").append(serverConfigList.size());
            sum.append("分别是:");
            sum.append(serverConfigList.stream().map(g -> String.valueOf(g.getGameServerId())).collect(Collectors.joining(",", "[", "]")));
            if (serverConfigList.size() < minMatchSize) {
                sum.append("不足最低要求").append(minMatchSize).append(",下一赛季延后。");
                continue;
            } else {
                if (maxKServerId == 0) {
                    sum.append("满足最低要求，但是没有下一赛季的赛季服配置，请策划配置");
                    continue;
                }
                sum.append("本赛季新的k服ID是");
                sum.append(maxKServerId + 1);
            }
        }
        sum.append("总结完毕，祝好");
        return sum.toString();
    }
    public void gmModifyKvkSeason(int kvkServerId, long startTime) {
        var meta = configService.getConfig(KvkGroupConfig.class).getKvkServerMeta(kvkServerId);
        if (meta == null) {
            log.warn("修改的k服ID找不到分组 kvkServerId={} 开始时间={}", kvkServerId, startTime);
            return;
        }
        meta.setServerOpenTime(startTime);
        handleKvkServer(meta, meta.getSeason());
    }
    private void checkCreateKvkServer() {
        var kvkGroupConfig = configService.getConfig(KvkGroupConfig.class);
        long now = TimeUtil.getNow();
        Set<Integer> needCreateServerIdSet = new HashSet<>();
        for (var group : kvkGroupConfig.getKvkServerGroups().values()) {
            if (group.getServerOpenTime() < now) {
                continue;
            }
            var kServer = configCenter.getLsConfig().getGameServers().get(group.getKvkServerId());
            if (kServer != null) {
                continue;
            }
            needCreateServerIdSet.add(group.getKvkServerId());
        }
        configCenter.getLsConfig().setDataOrCreateNode(CreateMode.PERSISTENT, configCenter.getLsConfig().getZkPath() + "/createServer", JSON.toJSONString(needCreateServerIdSet));
        log.info("本次需要创建的服务器是" + needCreateServerIdSet);
    }
}
