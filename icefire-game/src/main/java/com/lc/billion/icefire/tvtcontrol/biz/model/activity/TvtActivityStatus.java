package com.lc.billion.icefire.tvtcontrol.biz.model.activity;

import com.lc.billion.icefire.protocol.constant.PsTvtActivityStatus;
import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;


/**
 * @author: maoqq
 * @Date: 2022/04/11 4:19 PM
 */
public enum TvtActivityStatus implements IntEnum {

    //每日循环这三个状态
    READY(PsTvtActivityStatus.READY, PsTvtActivityStatus.SIGNUP),
    SIGNUP(PsTvtActivityStatus.SIGNUP, PsTvtActivityStatus.ADMITTANCE),
    ADMITTANCE(PsTvtActivityStatus.ADMITTANCE, PsTvtActivityStatus.READY),
    ;

    private static final TvtActivityStatus[] INDEXES = EnumUtils.toArray(values());

    private PsTvtActivityStatus tvtActivityStatus;
    private PsTvtActivityStatus nextTvtActivityStatus;

    private TvtActivityStatus(PsTvtActivityStatus tvtActivityStatus, PsTvtActivityStatus nextTvtActivityStatus) {
        this.tvtActivityStatus = tvtActivityStatus;
        this.nextTvtActivityStatus = nextTvtActivityStatus;
    }

    public static TvtActivityStatus findById(int id) {
        if (id < 0 || id >= INDEXES.length) {
            return null;
        }
        return INDEXES[id];
    }

    @Override
    public int getId() {
        return tvtActivityStatus.getValue();
    }


    public PsTvtActivityStatus getTvtActivityStatus() {
        return tvtActivityStatus;
    }

    public PsTvtActivityStatus getNextTvtActivityStatus() {
        return nextTvtActivityStatus;
    }

}
