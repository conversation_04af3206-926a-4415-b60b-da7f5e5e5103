package com.lc.billion.icefire.gvgcontrol.biz.model;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import org.jongo.marshall.jackson.oid.MongoId;

public class GvgCupApplyInfo extends AbstractEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8309556351916463647L;

	@MongoId
	private Long allianceId;

	private int serverId;
	// gvg杯赛是否报过名。 报名之后即使没有入围，这个值也是已报过名状态 0-未报名 1-已报名
	private int hasApply;
	// gvg杯赛 参赛状态 0-默认值 1-小组赛已入围 2-小组赛未入围 3-已淘汰*/
	private int statusOfParticipation;

	private String banner;
	private String name;
	private String aliasName;
	private long rating;
	private long ranking;

	private int warZoneId;

	public int getWarZoneId() {
		return warZoneId;
	}

	public void setWarZoneId(int warZoneId) {
		this.warZoneId = warZoneId;
	}

	@Override
	public void setPersistKey(Long id) {
		allianceId = id;
	}

	@Override
	public Long getPersistKey() {
		return allianceId;
	}

	@Override
	public Long getGroupingId() {
		return allianceId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public Long getAllianceId() {
		return allianceId;
	}

	public void setAllianceId(Long allianceId) {
		this.allianceId = allianceId;
	}

	public int getServerId() {
		return serverId;
	}

	public void setServerId(int serverId) {
		this.serverId = serverId;
	}

	public int getHasApply() {
		return hasApply;
	}

	public void setHasApply(int hasApply) {
		this.hasApply = hasApply;
	}

	public int getStatusOfParticipation() {
		return statusOfParticipation;
	}

	public void setStatusOfParticipation(int statusOfParticipation) {
		this.statusOfParticipation = statusOfParticipation;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAliasName() {
		return aliasName;
	}

	public void setAliasName(String aliasName) {
		this.aliasName = aliasName;
	}

	public String getBanner() {
		return banner;
	}

	public void setBanner(String banner) {
		this.banner = banner;
	}

	public long getRating() {
		return rating;
	}

	public void setRating(long rating) {
		this.rating = rating;
	}

	public long getRanking() {
		return ranking;
	}

	public void setRanking(long ranking) {
		this.ranking = ranking;
	}

}
