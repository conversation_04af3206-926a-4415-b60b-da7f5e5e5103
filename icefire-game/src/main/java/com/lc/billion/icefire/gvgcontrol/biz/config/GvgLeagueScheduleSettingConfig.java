package com.lc.billion.icefire.gvgcontrol.biz.config;

import java.util.*;

import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.gvgcontrol.biz.config.GvgLeagueScheduleDisplayConfig.GvGLeagueScheduleDisplayMeta;

@Config(name = "GvgLeagueScheduleSetting", metaClass = GvgLeagueScheduleSettingConfig.GvgLeagueScheduleSettingMeta.class)
public class GvgLeagueScheduleSettingConfig {
    @MetaMap
    private Map<String, GvgLeagueScheduleSettingMeta> idMap;
    // 周六场，可选时间点索引 对应gvg配置中得时间点索引
    private int[] groupSatTimeIndexes;
    // 周日场，可选时间点索引 对应gvg配置中得时间点索引
    private int[] groupSunTimeIndexes;
    // <warZoneId,Set<serverIds>> 各战区下的服务器列表
    private Map<Integer, Set<Integer>> warZoneId2ServerIdsSetMapper = new HashMap<>();
    // <serverId,warZoneId> 服务器id与其所属战区的映射
    private Map<Integer, Integer> serverId2WarZoneIdMapper = new HashMap<>();

    public void init(List<GvgLeagueScheduleSettingMeta> list) {
        String sat = null, sun = null;
        String warZoneStr = null;
        for (GvgLeagueScheduleSettingMeta meta : list) {
            if (meta.getName().equals("gvgLeagueGroupBattle1")) {
                sat = meta.getValue();
            } else if (meta.getName().equals("gvgLeagueGroupBattle2")) {
                sun = meta.getValue();
            } else if (meta.getName().equals("gvgLeagueWarZone")) {
                warZoneStr = meta.getValue();
            }
        }
        if (sat != null) {
            String[] foo = sat.split("[,]");
            groupSatTimeIndexes = new int[foo.length];
            for (int i = 0; i < groupSatTimeIndexes.length; ++i) {
                groupSatTimeIndexes[i] = Integer.parseInt(foo[i]);
            }
        }

        if (sun != null) {
            String[] foo = sun.split("[,]");
            groupSunTimeIndexes = new int[foo.length];
            for (int i = 0; i < groupSatTimeIndexes.length; ++i) {
                groupSunTimeIndexes[i] = Integer.parseInt(foo[i]);
            }
        }

        //解析 杯赛战区 服务器列表数据
        if (warZoneStr != null) {
            String[] foo = warZoneStr.split("[,]");
            for (int i = 0; i < foo.length; ++i) {
                String[] bar = foo[i].split("[;]");
                int warZoneId = Integer.parseInt(bar[0]);
                String[] foobar = bar[1].split("[|]");
                for (int j = 0; j < foobar.length; ++j) {
                    int serverId = Integer.parseInt(foobar[j]);
                    serverId2WarZoneIdMapper.put(serverId, warZoneId);
                    warZoneId2ServerIdsSetMapper.compute(warZoneId, (k, v) -> v == null ? new HashSet<>() : v).add(serverId);
                }
            }
        }
    }

    /**
     * 查询服务器所在战区
     *
     * @param serverId
     * @return 战区id,  如果服务器不属于任何战区，返回-1
     */
    public int findWarZoneByServerId(int serverId) {
        int res = -1;
        if (serverId2WarZoneIdMapper.containsKey(serverId)) {
            res = serverId2WarZoneIdMapper.get(serverId);
        }
        return res;
    }

    /**
     * 查询战区下所有服务器列表
     * @param warZoneId
     * @return 战区不存在返回null
     */
    public Integer[] getServerIdsByWarZoneId(int warZoneId) {
        if (warZoneId2ServerIdsSetMapper.containsKey(warZoneId)) {
            return warZoneId2ServerIdsSetMapper.get(warZoneId).toArray(new Integer[0]);
        }
        return null;
    }


    public int[] findGroupTimeIndexesByType(GvgMatchType type) {
        if (type == GvgMatchType.CUP_SAT) {
            return this.groupSatTimeIndexes;
        } else if (type == GvgMatchType.CUP_SUN) {
            return this.groupSunTimeIndexes;
        }
        return null;
    }

    public static class GvgLeagueScheduleSettingMeta extends AbstractMeta {
        private String name;
        private String value;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

    }
}
