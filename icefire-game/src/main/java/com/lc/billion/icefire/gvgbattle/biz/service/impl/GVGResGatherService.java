package com.lc.billion.icefire.gvgbattle.biz.service.impl;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.dao.mongo.root.GVGResNodeDao;
import com.lc.billion.icefire.game.biz.manager.ArmyManager;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.GvgResNode;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneServiceImpl;
import com.lc.billion.icefire.gvgbattle.biz.model.army.GvgGatherContext;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.AddPointType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 采集GVG资源点
 * 
 * <AUTHOR>
 * @date 2021/1/26
 */
@Service
public class GVGResGatherService {

	public static final Logger logger = LoggerFactory.getLogger(GVGResGatherService.class);
	@Autowired
	private ArmyServiceImpl armyService;
	@Autowired
	private ArmyManager armyManager;
	@Autowired
	private GVGBattleScoreService gvgBattleScoreService;
	@Autowired
	private SceneServiceImpl sceneService;
	@Autowired
	private GVGResNodeDao gvgResNodeDao;

	/**
	 * 结算采集
	 * 
	 * @param army
	 * @param isReturn
	 *            是否返回
	 * @param returnImmediately
	 *            是否立即召回
	 * @param forceRecalculate
	 *            重新根据当前部队计算当前采集状态
	 */
	public void calculate(ArmyInfo army, boolean isReturn, boolean returnImmediately, boolean forceRecalculate) {
		long now = TimeUtil.getNow();
		GvgGatherContext context = army.getGvgGatherContext();
		long delta = (long)context.calculate(now);// 结算
		boolean needReturn = false;
		SceneNode sceneNode = armyManager.getArmyTargetNode(army);
		if (sceneNode == null || sceneNode.getNodeType() != SceneNodeType.GVG_RES) {
			return;
		}

		GvgResNode node = (GvgResNode) sceneNode;
		node.decResource(delta);
		gvgResNodeDao.save(node);

		if (context.isReCaculate() || forceRecalculate) {

//			long armyCarry = armyManager.getGvgGatherCarry(army.getRoleId(), army);
//			long gatherAmount = Double.valueOf(armyCarry * 1d / gvgSettingConfig.getGvgResCarry()).longValue();
//			gatherAmount = Math.min(gatherAmount, gvgSettingConfig.getGvgGatherMaxAmount());

			if (!node.hasRemain()) {
				needReturn = true;
			}
			logger.info("玩家" + army.getRoleId() + "采集重算,预计采集量" + context.getCarry() + ",预计采集剩余时间" + (int) (context.getReaminGatherTime() / 60) + ":"
					+ (int) (context.getReaminGatherTime() % 60) + " | " + army.getPersistKey());
		}

		// 行军需要返回
		if (needReturn || isReturn) {
			if (returnImmediately) {
				onGatherReturnImmediately(army);
			} else {
				onGatherReturn(army);
			}
		} else {
			armyService.updateArmyProgress(army);
		}

		// 没有剩余资源了
		if (!node.hasRemain()) {
			gvgResNodeDao.delete(node);
			sceneService.remove(sceneNode, false);
		}
	}

	private void onGatherReturnImmediately(ArmyInfo army) {
		/**
		 * 这里不要调用armyManager.returnArmyImmediately(army);
		 * 
		 * 会循环调用。returnArmyImmediately里面会结算，还会走到这里
		 */
		armyManager.takeBackArmy(army);
		//
		gvgResourceNodeUpdate(army);

		// 添加积分
		gatherAddPoint(army);
	}

	public void gatherAddPoint(ArmyInfo army) {
		if(army.getGvgGatherContext() != null) {
			var gatherPoint = Double.valueOf(army.getGvgGatherContext().getCurrentGatherAmount()).intValue();
			var role = army.getOwner();
			if(role != null) {
				gvgBattleScoreService.addPoint(role, gatherPoint, AddPointType.GATHER);
			}
		}
	}

	private void onGatherReturn(ArmyInfo army) {
		armyManager.returnArmy(army);
		gvgResourceNodeUpdate(army);
	}

	private void gvgResourceNodeUpdate(ArmyInfo army) {
		SceneNode sceneNode = armyManager.getArmyTargetNode(army);
		if (sceneNode == null || sceneNode.getNodeType() != SceneNodeType.GVG_RES) {
			return;
		}

		GvgResNode node = (GvgResNode) sceneNode;

		if(gvgResNodeDao.findById(node.getPersistKey()) == null){
			// 战场结束时候，node对象已经不在，需要从场景中删除
			sceneService.remove(sceneNode, false);
			return;
		}

		node.setArmyId(null);
		node.setRoleId(null);
		gvgResNodeDao.save(node);
		sceneService.update(node, null);
	}
}
