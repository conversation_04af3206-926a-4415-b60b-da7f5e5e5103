package com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.impl;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.kvk.SeasonTaskConfig;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.node.RegionCapitalNode;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.AllianceSeasonTaskProgress;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.RoleSeasonTaskRecord;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.AbstractSeasonTaskAction;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.SeasonTaskActionType;
import com.longtech.ls.config.ServerType;
import com.simfun.sgf.utils.JavaUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 所在联盟在赛季结算时占领着X个Y级城
 *
 * <AUTHOR>
 * @date 2021/11/18
 */
@Service
public class AllianceOccupyRegionCapitalNumFinal extends AbstractSeasonTaskAction {
	@Override
	public SeasonTaskActionType getType() {
		return SeasonTaskActionType.ALLIANCE_OCCUPY_REGION_CAPITAL_NUM_FINAL;
	}

	@Override
	public long getProgress(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		Alliance allianceByRole = allianceService.getAllianceByRole(role);
		if (allianceByRole == null)
			return 0;
		int i = meta.getConditions().indexOf(getType().getId());
		int param2 = meta.getParam2s().get(i);
		AllianceSeasonTaskProgress allianceProgress = allianceSeasonTaskProgressDao.getProgressByAlliance(allianceByRole);
		return allianceProgress.getOccupyCity(param2);
	}

	@Override
	public void updateProgress(Role role, RoleSeasonTaskRecord taskRecord, Object... params) {
		Long allianceId = (Long) params[0];
		int serverId = (int) params[1];
		if (Application.getConfigCenter().getServerType(serverId) != ServerType.KVK_SEASON) {
			return;
		}
		Alliance alliance = allianceService.getAllianceById(allianceId);
		if (alliance == null) {
			return;
		}
		Collection<RegionCapitalNode> nodes = sdp.getRegionCapitalNodeDao().findByAllianceId(alliance.getPersistKey());
		Map<Integer, Integer> cityOccupy = new HashMap<>();
		if (JavaUtils.bool(nodes)) {
			for (RegionCapitalNode node : nodes) {
				if (Application.getConfigCenter().getServerType(node.getCurrentServerId()) != ServerType.KVK_SEASON) {
					continue;
				}
				cityOccupy.compute(node.getLevel(), (k, v) -> v == null ? 1 : v + 1);
			}
		}
		//
		AllianceSeasonTaskProgress allianceSeasonTaskProgress = allianceSeasonTaskProgressDao.getProgressByAlliance(alliance);
		allianceSeasonTaskProgress.setOccupyCity(cityOccupy);
		allianceSeasonTaskProgressDao.save(allianceSeasonTaskProgress);
	}

	@Override
	public boolean actionFinishCheck(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		int i = meta.getConditions().indexOf(getType().getId());
		int param1 = meta.getParam1s().get(i);
		return getProgress(role, meta) >= param1;
	}
}
