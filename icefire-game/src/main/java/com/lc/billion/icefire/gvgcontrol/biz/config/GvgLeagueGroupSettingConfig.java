package com.lc.billion.icefire.gvgcontrol.biz.config;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;

/**
 * gvg 杯赛 对阵数据
 *
 * <AUTHOR>
 * @created 2021-08-27
 */
@Config(name = "GvgLeagueGroupSetting", metaClass = GvgLeagueGroupSettingConfig.GvgCupGroupSettingMeta.class)
public class GvgLeagueGroupSettingConfig {

    @MetaMap
    private Map<String, GvgCupGroupSettingMeta> idMap;

    private Map<String,GvgCupGroupSettingMeta> datasByObId;

    public GvgCupGroupSettingMeta findByObId(String obId){
        return datasByObId.get(obId);
    }
    public Collection<GvgCupGroupSettingMeta> getAll() {
        return idMap.values();
    }

    public void init(List<GvgCupGroupSettingMeta> list) {
        datasByObId = new HashMap<>();
        for (GvgCupGroupSettingMeta meta : list) {
            // -1表示表中无数据
            if (meta.getBlueServerId() == -1) {
                this.idMap = new HashMap<>();
                datasByObId.clear();
                break;
            }
            datasByObId.put(meta.getObId(),meta);
        }
    }

    public static class GvgCupGroupSettingMeta extends AbstractMeta {

        private Long redAlliance;
        private int redServerId;
        private Long blueAlliance;
        private int blueServerId;
        private GvgMatchType week;
        //数据的周数
        private int gameRound;
        //真正的轮次
        private int round;
        //战区id
        private int warZoneId;
        //观战场次id
        private String obId;
        //场次名称
        private String matchName;

        public Long getRedAlliance() {
            return redAlliance;
        }

        public void setRedAlliance(Long redAlliance) {
            this.redAlliance = redAlliance;
        }

        public int getRedServerId() {
            return redServerId;
        }

        public void setRedServerId(int redServerId) {
            this.redServerId = redServerId;
        }

        public Long getBlueAlliance() {
            return blueAlliance;
        }

        public void setBlueAlliance(Long blueAlliance) {
            this.blueAlliance = blueAlliance;
        }

        public int getBlueServerId() {
            return blueServerId;
        }

        public void setBlueServerId(int blueServerId) {
            this.blueServerId = blueServerId;
        }

        public GvgMatchType getWeek() {
            return week;
        }

        public void setWeek(GvgMatchType week) {
            this.week = week;
        }

        public int getGameRound() {
            return gameRound;
        }

        public void setGameRound(int gameRound) {
            this.gameRound = gameRound;
        }

        public int getRound() {
            return round;
        }

        public void setRound(int round) {
            this.round = round;
        }

        public int getWarZoneId() {
            return warZoneId;
        }

        public void setWarZoneId(int warZoneId) {
            this.warZoneId = warZoneId;
        }

        public String getObId() {
            return obId;
        }

        public void setObId(String obId) {
            this.obId = obId;
        }

        public String getMatchName() {
            return matchName;
        }

        public void setMatchName(String matchName) {
            this.matchName = matchName;
        }
    }

}
