package com.lc.billion.icefire.kvkseason.biz.dao.mongo.root;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.kvkseason.biz.model.legion.Legion;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionMark;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 *
 */
@Repository
public class LegionMarkDao extends RootDao<LegionMark> {

	private Map<Long, List<LegionMark>> dataByLegionId = new MyConcurrentMap<>();

	public LegionMarkDao() {
		super(LegionMark.class, true);
	}

	@Override
	protected MongoCursor<LegionMark> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(LegionMark entity) {
		dataByLegionId.compute(entity.getLegionId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
	}

	@Override
	protected void removeMemoryIndexes(LegionMark entity) {
		List<LegionMark> list = dataByLegionId.get(entity.getLegionId());
		if (list != null) {
			list.remove(entity);
		}
	}

	public LegionMark create(Legion legion, int serverId, Point position, String metaId, String content, String createrName) {
		LegionMark legionMark = newEntityInstance();
		legionMark.setContent(content);
		legionMark.setLegionId(legion.getId());
		legionMark.setMetaId(metaId);
		legionMark.setServerId(serverId);
		legionMark.setX(position.getX());
		legionMark.setY(position.getY());
		legionMark.setCreaterName(createrName);
		return createEntity(legion.getDB(), legionMark);
	}

	public LegionMark findByLegionIdAndServerIdAndMetaId(Long legionId, int serverId, String metaId) {
		List<LegionMark> list = findByLegionIdAndServerId(legionId, serverId);
		if (JavaUtils.bool(list)) {
			Optional<LegionMark> findFirst = list.stream().filter(mark -> mark.getMetaId().equals(metaId)).findFirst();
			if (findFirst != null && findFirst.isPresent()) {
				return findFirst.get();
			}
		}
		return null;
	}

	public List<LegionMark> findByLegionIdAndServerId(Long legionId, int serverId) {
		List<LegionMark> list = findByLegionId(legionId);
		if (JavaUtils.bool(list)) {
			return list.stream().filter(mark -> mark.getServerId() == serverId).collect(Collectors.toList());
		}
		return null;
	}

	public List<LegionMark> findByLegionId(Long legionId) {
		return dataByLegionId.get(legionId);
	}

	public List<LegionMark> findByPosition(Long legionId, int serverId, int x, int y) {
		List<LegionMark> legionMarks = findByLegionIdAndServerId(legionId, serverId);
		if (JavaUtils.bool(legionMarks)) {
			return legionMarks.stream().filter(mark -> mark.getX() == x && mark.getY() == y).collect(Collectors.toList());
		}
		return Collections.emptyList();
	}
}
