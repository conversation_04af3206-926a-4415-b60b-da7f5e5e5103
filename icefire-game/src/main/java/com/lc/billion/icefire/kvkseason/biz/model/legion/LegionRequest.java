package com.lc.billion.icefire.kvkseason.biz.model.legion;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.IAlliancesEntity;

/**
 * <AUTHOR>
 */
public class LegionRequest extends AbstractEntity implements IAlliancesEntity {

	private static final long serialVersionUID = 7566342984572290934L;

	/**
	 * 1-入盟请求
	 */
	public static final int TYPE_REQUEST = 1;
	/**
	 * 2-入盟邀请
	 */
	public static final int TYPE_INVITE = 2;

	@MongoId
	private Long id;
	private Long legionId;
	private Long allianceId;
	private int type;

	@Override
	public void setPersistKey(Long id) {
		this.id = id;
	}

	@Override
	public Long getPersistKey() {
		return id;
	}

	@Override
	public Long getGroupingId() {
		return legionId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCode(legionId, allianceId);
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equals(legionId, allianceId);
	}

	public Long getLegionId() {
		return legionId;
	}

	public void setLegionId(Long legionId) {
		this.legionId = legionId;
	}

	public Long getAllianceId() {
		return allianceId;
	}

	public void setAllianceId(Long allianceId) {
		this.allianceId = allianceId;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

}
