package com.lc.billion.icefire.kvkseason.biz.dao.mongo.roles;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.jongo.MongoCursor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.kvk.SeasonTaskConfig;
import com.lc.billion.icefire.game.biz.dao.RolesEntityDao;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.RoleSeasonTaskRecord;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 * @date 2021/11/15
 */
@Repository
public class RoleSeasonTaskRecordDao extends RolesEntityDao<RoleSeasonTaskRecord> {
	private static final Logger logger = LoggerFactory.getLogger(RoleSeasonTaskRecordDao.class);
	@Autowired
	private ConfigServiceImpl configService;

	// <玩家id，<任务metaId，玩家任务记录>>>
	private ConcurrentMap<Long, ConcurrentMap<String, RoleSeasonTaskRecord>> roleSeasonTasks = new ConcurrentHashMap<>();
	// <玩家id，<任务行为类型，对应的任务list>>
	private ConcurrentMap<Long, ConcurrentMap<Integer, List<RoleSeasonTaskRecord>>> roleSeasonTaskGroupByCondition = new ConcurrentHashMap<>();

	protected RoleSeasonTaskRecordDao() {
		super(RoleSeasonTaskRecord.class, true);
	}

	@Override
	protected void putMemoryIndexes(RoleSeasonTaskRecord entity) {
		//
		roleSeasonTasks.compute(entity.getRoleId(), (k, v) -> v == null ? new ConcurrentHashMap<>() : v).put(entity.getMetaId(), entity);
		//
		for (Integer conditionTypeId : entity.getConditions()) {
			roleSeasonTaskGroupByCondition.compute(entity.getRoleId(), (k, v) -> v == null ? new ConcurrentHashMap<>() : v)
					.compute(conditionTypeId, (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
		}
	}

	@Override
	protected void removeMemoryIndexes(RoleSeasonTaskRecord entity) {
		//
		ConcurrentMap<String, RoleSeasonTaskRecord> taskMap = roleSeasonTasks.get(entity.getRoleId());
		if (taskMap != null)
			taskMap.remove(entity.getMetaId());
		//
		ConcurrentMap<Integer, List<RoleSeasonTaskRecord>> map = roleSeasonTaskGroupByCondition.get(entity.getRoleId());
		if (map != null)
			for (Integer condition : entity.getConditions()) {
				List<RoleSeasonTaskRecord> records = map.get(condition);
				if (JavaUtils.bool(records))
					records.remove(entity);
			}
	}

	@Override
	protected MongoCursor<RoleSeasonTaskRecord> doFindAll(int db, List<Long> roleIds) {
		return dbFindByRoleIds(db, roleIds);
	}

	@Override
	protected MongoCursor<RoleSeasonTaskRecord> doFindByPlayerId(int db, Long roleId) {
		return dbFindByRoleId(db, roleId);
	}

	@Override
	protected Collection<Long> clearMemoryIndexes(Long playerId) {
		Collection<Long> ret = new ArrayList<>();
		//
		// 1
		ConcurrentMap<String, RoleSeasonTaskRecord> remove = roleSeasonTasks.remove(playerId);
		if (JavaUtils.bool(remove)) {
			remove.values().forEach(record -> {
				ret.add(record.getPersistKey());
			});
		}
		// 2
		roleSeasonTaskGroupByCondition.remove(playerId);
		//
		return ret;
	}

	@Override
	public Collection<RoleSeasonTaskRecord> findByRoleId(Long roleId) {
		ConcurrentMap<String, RoleSeasonTaskRecord> concurrentMap = roleSeasonTasks.get(roleId);
		if (JavaUtils.bool(concurrentMap)) {
			return concurrentMap.values();
		}
		return Collections.emptyList();
	}

	public RoleSeasonTaskRecord getRecordByRoleAndMetaId(Role role, String metaId) {
		ConcurrentMap<String, RoleSeasonTaskRecord> taskMap = roleSeasonTasks.get(role.getPersistKey());
		if (!JavaUtils.bool(taskMap)) {
			return null;
		}
		return taskMap.get(metaId);
	}

	public RoleSeasonTaskRecord create(Role role, SeasonTaskConfig.SeasonTaskMeta taskMeta) {
		RoleSeasonTaskRecord record = newEntityInstance();
		record.setRoleId(role.getPersistKey());
		record.setMetaId(taskMeta.getId());
		record.setConditions(taskMeta.getConditions());
		return createEntity(role, record);
	}

	public boolean isFinish(Role role, String metaId) {
		RoleSeasonTaskRecord record = getRecordByRoleAndMetaId(role, metaId);
		if (record == null)
			return false;
		return record.isFinish();
	}

	public boolean isGetReward(Role role, String metaId) {
		RoleSeasonTaskRecord record = getRecordByRoleAndMetaId(role, metaId);
		if (record == null)
			return false;
		return record.isReward();
	}

	public void setRewardStatus(Role role, SeasonTaskConfig.SeasonTaskMeta taskMeta) {
		RoleSeasonTaskRecord record = getRecordByRoleAndMetaId(role, taskMeta.getId());
		if (record == null)
			record = create(role, taskMeta);
		record.setReward(true);
		save(record);
	}

	/**
	 * 返回 这个action类型相关的、在有效期的任务
	 * 
	 * @param role
	 * @param actionType
	 * @return
	 */
	public List<RoleSeasonTaskRecord> getRecordByRoleAndActionType(Role role, int actionType) {
		List<RoleSeasonTaskRecord> records = null;
		ConcurrentMap<Integer, List<RoleSeasonTaskRecord>> map = roleSeasonTaskGroupByCondition.get(role.getPersistKey());
		if (JavaUtils.bool(map)) {
			records = map.get(actionType);
		}
		List<RoleSeasonTaskRecord> ret = new ArrayList<>();
		if (JavaUtils.bool(records)) {
			for (RoleSeasonTaskRecord record : records) {
				if (!record.isExpire())
					ret.add(record);
			}
		}
		return ret;
	}

	/**
	 * 检查是否有脏数据，如果是上个赛季被清理的玩家，至今新赛季才登陆，可能有上个赛季的脏数据，这个是 赛季切换时，数据清理，漏掉了这部分玩家的数据，造成的。
	 * 
	 * 如果用flyway升级，玩家可能在更新后，首次登录，还是会有问题，所以放到登录里面
	 * 
	 * @param roleId
	 * @return TRUE正常 FALSE不正常
	 */
	public void dirtyDataValidation(Long roleId) {
		Role role = Application.getBean(RoleManager.class).getRole(roleId);
		ConcurrentMap<String, RoleSeasonTaskRecord> taskRecordConcurrentMap = roleSeasonTasks.get(roleId);
		// 如果玩家有赛季任务数据 并且 赛季任务数据中的metaId 对应的任务配置不存在， 我们就认为这个数据时不可用
		if (JavaUtils.bool(taskRecordConcurrentMap)) {
			boolean isClean = false;// 是否清理过脏数据
			List<RoleSeasonTaskRecord> clearEntitys = null;
			for (RoleSeasonTaskRecord record : taskRecordConcurrentMap.values()) {
				if (configService.getConfig(SeasonTaskConfig.class).getMetaMap().get(record.getMetaId()) == null) {
					// 找不到对应的配置 可以清理
					if (clearEntitys == null) {
						clearEntitys = new ArrayList<>();
					}
					clearEntitys.add(record);
				}
			}
			// 清理
			if (JavaUtils.bool(clearEntitys)) {
				delete(clearEntitys);
				isClean = true;
				logger.info("玩家{}赛季任务有脏数据：{}，清理后，当前赛季任务信息{}", roleId, clearEntitys, roleSeasonTasks.get(roleId));
			}
			// 有任务数据的情况，1：数据时脏数据(上面清理了) 2： 数据是bug 引起的，任务数据不全
			List<SeasonTaskConfig.SeasonTaskMeta> metaList = configService.getConfig(SeasonTaskConfig.class).getMetaList();
			if (!isClean && JavaUtils.bool(metaList) && metaList.size() > taskRecordConcurrentMap.size()) {
				List<String> addTaskMetaIds = new ArrayList<>();
				for (SeasonTaskConfig.SeasonTaskMeta meta : metaList) {
					RoleSeasonTaskRecord record = getRecordByRoleAndMetaId(role, meta.getId());
					if (record == null) {
						create(role, meta);
						addTaskMetaIds.add(meta.getId());
					}
				}
				if (JavaUtils.bool(addTaskMetaIds)) {
					logger.info("玩家{}赛季任务缺少数据：新增赛季任务metaId：{}", roleId, addTaskMetaIds);
				}
			}
		}
	}

	/**
	 * 是否需要初始化玩家的赛季任务
	 * 
	 * @param roleId
	 * @return
	 */
	public boolean needInit(Long roleId) {
		ConcurrentMap<String, RoleSeasonTaskRecord> taskRecordConcurrentMap = roleSeasonTasks.get(roleId);
		if (JavaUtils.bool(taskRecordConcurrentMap)) {
			return false;
		}
		return true;
	}

	public void clearPreSeasonData() {
		// 注意，这里清理的只是在内存中的，有些玩家已经被清理出内存了，这部分的赛季任务数据不会被清理。这部分数据，在玩家再次登录，load回内存后，检测处理
		Collection<RoleSeasonTaskRecord> all = findAll();
		if (JavaUtils.bool(all)) {
			for (RoleSeasonTaskRecord record : all) {
				delete(record);
			}
		}
	}
}
