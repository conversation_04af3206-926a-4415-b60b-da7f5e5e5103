package com.lc.billion.icefire.kvkseason.biz.service.impl.legion;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.async.rank.LegionRankGetOperation;
import com.lc.billion.icefire.game.biz.config.kvk.KvkGroupConfig;
import com.lc.billion.icefire.game.biz.config.legion.LegionOfficialConfig;
import com.lc.billion.icefire.game.biz.config.legion.LegionOfficialConfig.LegionOfficialMeta;
import com.lc.billion.icefire.game.biz.config.legion.LegionSettingConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ServerInfoDao;
import com.lc.billion.icefire.game.biz.manager.*;
import com.lc.billion.icefire.game.biz.model.ServerInfo;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceMember;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.email.AbstractEmail;
import com.lc.billion.icefire.game.biz.model.email.LegionAllMail;
import com.lc.billion.icefire.game.biz.model.email.MailUtils;
import com.lc.billion.icefire.game.biz.model.milestone.MilestoneType;
import com.lc.billion.icefire.game.biz.model.role.AbstractRole;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleCity;
import com.lc.billion.icefire.game.biz.model.scene.node.RegionCapitalNode;
import com.lc.billion.icefire.game.biz.service.impl.*;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceOutput;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.alliance.prosperity.AllianceProsperityService;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.bilog.enums.LegionManageBehaviorType;
import com.lc.billion.icefire.game.biz.service.impl.email.MailCreator;
import com.lc.billion.icefire.game.biz.service.impl.email.MailSender;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.milestone.MilestoneService;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankConstants;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankContext;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankType;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.game.exception.ToClientException;
import com.lc.billion.icefire.game.support.LogReasons.MoneyLogReason;
import com.lc.billion.icefire.kvkseason.biz.async.LegionAnnouncementCheckWordOperation;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.KvkHonorLegionDao;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionDao;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionMemberDao;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionRequestDao;
import com.lc.billion.icefire.kvkseason.biz.manager.legion.*;
import com.lc.billion.icefire.kvkseason.biz.model.legion.*;
import com.lc.billion.icefire.kvkseason.biz.model.legion.log.LegionLogType;
import com.lc.billion.icefire.kvkseason.biz.service.impl.kvk.KvkSeasonServiceImpl;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.constant.PsAnnouncementType;
import com.lc.billion.icefire.protocol.constant.PsErrorCode;
import com.lc.billion.icefire.protocol.structure.PsAllianceInfo;
import com.lc.billion.icefire.protocol.structure.PsCityInfo;
import com.lc.billion.icefire.protocol.structure.PsLegionInfo;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 军团
 * 
 * <AUTHOR>
 *
 */
@Service
public class LegionService {
	private static final Logger log = LoggerFactory.getLogger(LegionService.class);

	@Autowired
	private ConfigCenter configCenter;
	@Autowired
	private AllianceDao allianceDao;
	@Autowired
	private AllianceManager allianceManager;
	@Autowired
	private LegionMemberDao legionMemberDao;
	@Autowired
	private LegionDao legionDao;
	@Autowired
	private LegionRequestManager legionRequestManager;
	@Autowired
	private LegionManager legionManager;
	@Autowired
	private ManagerService managerService;
	@Autowired
	private RoleCurrencyManager roleCurrencyManager;
	@Autowired
	private ArmyManager armyManager;
	@Autowired
	private AllianceMemberManager allianceMemberManager;
	@Autowired
	private RoleManager roleManager;
	@Autowired
	private LegionRequestDao legionRequestDao;
	@Autowired
	private AllianceServiceImpl allianceService;
	@Autowired
	private LegionMemberService legionMemberService;
	@Autowired
	private WorldServiceImpl worldService;
	@Autowired
	private LegionOfficialManager legionOfficialManager;
	@Autowired
	private ConfigServiceImpl configService;
	@Autowired
	private KvkHonorLegionDao kvkHonorLegionDao;
	@Autowired
	private AsyncOperationServiceImpl asyncOperationService;
	@Autowired
	private CheckStringServiceImpl checkStringService;
	@Autowired
	private AllianceProsperityService allianceProsperityService;
	@Autowired
	private RoleCityManager roleCityManager;
	@Autowired
	private SceneServiceImpl sceneService;
	@Autowired
	private MilestoneService milestoneService;
	@Autowired
	private ServiceDependency sdp;
	@Autowired
	private BiLogUtil biLogUtil;
	@Autowired
	private ServerInfoServiceImpl serverInfoService;
	@Autowired
	private LegionLogService legionLogService;
	@Autowired
	private LegionLogManager legionLogManager;
	@Autowired
	private LegionMarkManager legionMarkManager;

	public void startService() {
	}

	public void onEnterWorld(Role role) {
		getLegionList(role);
		if (!JavaUtils.bool(role.getAllianceId())) {
			// 没有联盟
			return;
		}
		LegionMember legionMember = legionMemberDao.findById(role.getAllianceId());
		if (legionMember == null) {
			// 没有军团
		} else {
			Legion legion = legionDao.findById(legionMember.getLegionId());
			GcLegionLoad gcLegionLoad = LegionOutput.toGcLegionLoad(legion);
			role.send(gcLegionLoad);
			sendLegionAllianceMemberList(role, legion);
			Long leaderRoleId = legion.getLeaderRoleId();
			if (leaderRoleId.longValue() == role.getId().longValue()) {
				// 军团长推送军团请求红点
				List<LegionRequest> legionRequests = legionRequestManager.findLegionRequest(legion.getId(), LegionRequest.TYPE_REQUEST);
				int requestSize = legionRequests == null ? 0 : legionRequests.size();
				role.send(LegionOutput.toGcLegionRequestCount(requestSize));
			}
			GcLegionOfficialList gcLegionOfficialList = LegionOutput.toGcLegionOfficialList(legion);
			role.send(gcLegionOfficialList);
		}
	}

	public void getLegionMemberList(Role role, Long legionId) {
		Legion legion = legionDao.findById(legionId);
		if (legion == null) {
			ErrorLogUtil.errorLog("军团空", "legionId",legionId);
			return;
		}
		GcLegionMemberList gcLegionMemberList = LegionOutput.wrapperMembers(legion);
		role.send(gcLegionMemberList);
	}

	/**
	 * 军团列表
	 *
	 * @param role
	 */
	public void getLegionList(Role role) {
		// 本服军团
		Collection<Legion> legions = legionDao.findAll();
		if (JavaUtils.bool(legions)) {
			GcLegionList gcLegionList = new GcLegionList();
			List<LegionRequest> legionRequests = legionRequestManager.findByAllianceId(LegionRequest.TYPE_REQUEST, role.getAllianceId());
			Set<Long> legionIds = null;
			if (legionRequests != null) {
				legionIds = legionRequests.stream().map(LegionRequest::getLegionId).collect(Collectors.toSet());
			} else {
				legionIds = new HashSet<>();
			}
			for (Legion legion : legions) {
				PsLegionInfo psLegionInfo = LegionOutput.toPsLegionInfo(legion);
				psLegionInfo.setApply(legionIds.contains(legion.getId()));
				gcLegionList.addToMembers(psLegionInfo);
			}
			role.send(gcLegionList);
		}
	}

	/**
	 * 创建军团
	 *
	 * @param role
	 * @param name
	 * @param banner
	 * @param bannerColor
	 * @param badge
	 * @param badgeColor
	 * @param declaration
	 * @param language
	 */
	public void createLegion(Role role, String name, String banner, int bannerColor, String badge, int badgeColor, String declaration, String language, String country) {
		boolean legionSwitch = getLegionSwitch();
		if (!legionSwitch) {
			ErrorLogUtil.errorLog("军团开关没开");
			return;
		}
		Long allianceId = role.getAllianceId();
		if (!JavaUtils.bool(allianceId)) {
			// 没有联盟不能创建军团
			return;
		}

		// int currentServerId = role.getCurrentServerId();

		ServerType currentServerType = configCenter.getCurrentGameServerType();
		if (currentServerType != ServerType.KVK_SEASON) {
			// 不是赛季服不能创建
			return;
		}
		// ServerType serverType = configCenter.getServerType(currentServerId);
		// if (serverType != ServerType.KVK_SEASON) {
		// // 不在K服不能创建
		// return;
		// }

		LegionSettingConfig legionSettingConfig = configService.getConfig(LegionSettingConfig.class);

		name = StringUtils.trim(name);
		if (StringUtils.isBlank(name)) {
			ErrorLogUtil.errorLog("名字不能为空");
			return;
		}

		// 名字重复
		if (legionDao.containsName(name)) {
			// role.send(AllianceOutput.wrapperCreate(null, 1));
			ErrorLogUtil.errorLog("alliance rename already exist", "name",name);
			return;
		}

		boolean checkLegionAbbreviationLengths = legionSettingConfig.checkLegionAbbreviationLengths(name);
		if (!checkLegionAbbreviationLengths) {
			ErrorLogUtil.errorLog("军团名字长度不对", "name",name);
			return;
		}

		if (declaration != null) {
			boolean checkLegionDeclarationLength = legionSettingConfig.checkLegionDeclarationLength(declaration);
			if (!checkLegionDeclarationLength) {
				ErrorLogUtil.errorLog("军团宣言过长");
				return;
			}
		}

		Alliance alliance = allianceDao.findById(allianceId);

		Long leaderId = alliance.getLeaderId();
		if (leaderId.longValue() != role.getId().longValue()) {
			// 必须是盟主
			ErrorLogUtil.errorLog("必须是盟主才能创建军团");
			return;
		}

		// 联盟没加入其他军团
		LegionMember legionMember = legionMemberDao.findById(allianceId);
		if (legionMember != null) {
			// 联盟有军团了
			ErrorLogUtil.errorLog("已有军团了");
			return;
		}

		// 联盟势力值
		int needProsperity = legionSettingConfig.getCreateNeedProsperity();
		if (alliance.getProsperity() < needProsperity) {
			ErrorLogUtil.errorLog("联盟势力值不足", "curProsperity",alliance.getProsperity(), "needProsperity",needProsperity);
			return;
		}

		int needMember = legionSettingConfig.getCreateNeedMemberNum();
		if (allianceManager.getCurMember(alliance) < needMember) {
			ErrorLogUtil.errorLog("联盟人数不足", "curNum",allianceManager.getCurMember(alliance), "needNum",needMember);
			return;
		}

		// 钻石消耗
		int needDiamond = legionSettingConfig.getCreateSpendingDiamonds();
		boolean checkAndCost = roleCurrencyManager.checkAndCost(role, Currency.DIAMOND, needDiamond, MoneyLogReason.LEGION_CREATE, "创建军团");
		if (!checkAndCost) {
			ErrorLogUtil.errorLog("钻石不足", "curDiamond",roleCurrencyManager.getMoney(role, Currency.DIAMOND), "needDiamond",needDiamond);
			return;
		}

		// if (!allianceManager.checkPermission(Auth.ACCESSION_AUDIT, role.getId())) {
		// log.error("玩家权限不足{}", role.getAllianceId());
		// return;
		// }
		LegionLeader leader = new LegionLeader();
		leader.copyProperty(role);
		Legion legion = legionDao.create(alliance, badge, badgeColor, banner, bannerColor, declaration, language, leader, name, country);
		legionMember = createLegionMember(alliance, legion.getId());
		legionOfficialManager.initLegionOfficial(legion);
		// 设置军团长官职
		legionOfficialManager.updateLegionOfficialForLeader(legion.getId(), role.getId());
		// 荣誉值
		kvkHonorLegionDao.create(Application.getServerId(), legion.getId());
		// 势力值重算
		allianceProsperityService.reCalcLegionProsperity(legion, true);
		// TODO 通知前端
		GcLegionCreate gcLegionCreate = new GcLegionCreate();
		gcLegionCreate.setLegion(LegionOutput.toPsLegionInfo(legion));
		role.send(gcLegionCreate);
		// 清理请求
		legionRequestManager.deleteLegionRequest(role, LegionRequest.TYPE_REQUEST);
		updateMemberForAOI(legion.getId(), allianceId);
		//
		milestoneHandleOnJoinLegion(alliance, legion);

		biLogUtil.legionCreate(role, role.getoServerId(), role.getCurrentServerId(), legion.getId(), legion.getName(), alliance.getId(), role.getId(),
				allianceManager.getCurMember(alliance), alliance.getProsperity());
	}

	private void updateMemberForAOI(Long legionId, Long allianceId) {
		// 玩家aoi更新
		List<AllianceMember> members = allianceMemberManager.getMembers(allianceId);
		if (JavaUtils.bool(members)) {
			for (AllianceMember allianceMember : members) {
				RoleCity roleCity = roleCityManager.getRoleCity(allianceMember.getPersistKey());
				if (roleCity != null && roleCity.getPosition() != null) {
					sceneService.update(roleCity, updater -> {
						PsCityInfo psCityInfo = updater.getCity();
						psCityInfo.setLegionId(legionId);
					});
				}
			}
		}
	}

	/**
	 * 创建军团成员
	 *
	 * @param alliance
	 * @return
	 */
	private LegionMember createLegionMember(Alliance alliance, Long legionId) {
		LegionMember legionMember = legionMemberDao.create(alliance, legionId);
		return legionMember;
	}

	/**
	 * 申请加入军团
	 *
	 * @param role
	 * @param legionId
	 *            军团id
	 */
	public void legionRequest(Role role, Long legionId) {
		Long allianceId = role.getAllianceId();
		if (!JavaUtils.bool(allianceId)) {
			// 没有联盟不能申请加入军团
			return;
		}

		ServerType currentServerType = configCenter.getCurrentGameServerType();
		if (currentServerType != ServerType.KVK_SEASON) {
			// 不是赛季服不能申请加入
			return;
		}

		Alliance alliance = allianceDao.findById(allianceId);

		Long leaderId = alliance.getLeaderId();
		if (leaderId.longValue() != role.getId().longValue()) {
			// 必须是盟主
			return;
		}

		// 联盟没加入其他军团
		LegionMember legionMember = legionMemberDao.findById(allianceId);
		if (legionMember != null) {
			// 联盟有军团了
			return;
		}

		// 同一个军团只能发起一个请求
		LegionRequest legionRequest = legionRequestManager.findLegionRequest(legionId, allianceId, LegionRequest.TYPE_REQUEST);
		if (legionRequest != null) {
			log.info("联盟已发起过入盟申请");
			sendGcLegionRequest(role, 3);
			return;
		}

		Legion legion = legionDao.findById(legionId);
		if (legion == null) {
			// 军团可能已经解散了
			return;
		}

		// 必须同阵营/同服务器
		if (legion.getServerId() != alliance.getoServerId()) {
			log.info("必须同阵营/同服务器");
			sendGcLegionRequest(role, 4);
			return;
		}

		// TODO 联盟数已满
		int legionMemberMax = legionManager.getLegionMemberMax(legionId);
		int legionMemberCount = legionManager.getLegionMemberCount(legionId);
		if (legionMemberCount >= legionMemberMax) {
			log.info("军团人数达到上限");
			sendGcLegionRequest(role, 2);
			return;
		}

		Long leaderRoleId = legion.getLeaderRoleId();
		Role legionLeader = roleManager.getRole(leaderRoleId);

		legionRequest = legionRequestManager.createLegionRequest(legionLeader, legionId, allianceId, LegionRequest.TYPE_REQUEST);
		// 同步给军团前端
		GcLegionRequest gcLegionRequest = new GcLegionRequest();
		gcLegionRequest.setErrorCode(0);
		gcLegionRequest.setLegionInfo(LegionOutput.toPsLegionInfo(legion).setApply(true));
		role.send(gcLegionRequest);
	}

	private void sendGcLegionRequest(Role role, int errorCode) {
		GcLegionRequest gcLegionRequest = new GcLegionRequest();
		gcLegionRequest.setErrorCode(errorCode);
		role.send(gcLegionRequest);
	}

	/**
	 * 处理军团申请
	 *
	 * @param role
	 *            军团长
	 * @param allianceId2
	 *            申请的联盟id
	 * @param agree
	 *            是否同意
	 */
	public void requestCheck(Role role, long allianceId2, boolean agree, boolean cancelAll) {
		Alliance alliance = allianceDao.findById(role.getAllianceId());
		if (alliance == null) {
			ErrorLogUtil.errorLog("玩家已经不是联盟的人了", "allianceId",role.getAllianceId());
			return;
		}
		LegionMember legionMember = legionMemberDao.findById(role.getAllianceId());
		if (legionMember == null) {
			// 玩家联盟没军团
			ErrorLogUtil.errorLog("玩家联盟没军团", "allianceId",role.getAllianceId());
			return;
		}
		// 玩家所在的军团
		// Long legionAllianceId = role.getAllianceId();
		Legion legion = legionDao.findById(legionMember.getLegionId());
		if (legion == null) {
			ErrorLogUtil.errorLog("找不到对应的军团", "legionId",legionMember.getLegionId());
			return;
		}
		boolean checkLegionOfficialAuth = legionOfficialManager.checkLegionOfficialAuth(LegionRankAuthEnum.AUTH_LEGION_AUDIT, role);
		if (!checkLegionOfficialAuth) {
			// 权限不足
			return;
		}
		Long legionId = legion.getId();

		if (cancelAll) {
			legionRequestManager.deleteLegionRequest(role, legionId, LegionRequest.TYPE_REQUEST);
			cgLegionRequestListForLegion(role);
		} else {
			LegionMember legionMember2 = legionMemberDao.findById(allianceId2);
			if (legionMember2 != null) {
				// 删除请求
				legionRequestManager.deleteLegionRequest(role, legionId, allianceId2, LegionRequest.TYPE_REQUEST);
				cgLegionRequestListForLegion(role);
				ErrorLogUtil.errorLog("目标联盟已经有军团了", "allianceId",allianceId2);
				return;
			}
			LegionRequest legionRequest = legionRequestManager.findLegionRequest(legion.getId(), allianceId2, LegionRequest.TYPE_REQUEST);
			if (legionRequest == null) {
				ErrorLogUtil.errorLog("联盟没有申请过军团", "allianceId",allianceId2, "legionId",legion.getId());
				return;
			}
			// 删除请求
			legionRequestManager.deleteLegionRequest(role, legionId, allianceId2, LegionRequest.TYPE_REQUEST);

			Alliance alliance2 = allianceDao.findById(allianceId2);
			if (alliance2 != null) {
				if (agree) {
					// 同意加入的话再去检测联盟人数
					if (legionManager.isLegionMemberFull(legionId)) {
						ErrorLogUtil.errorLog("军团人数已满", "roleId",role.getId(), "allianceId",alliance.getId());
						throw new ToClientException(PsErrorCode.ALLIANCE_IS_FULL, "联盟人数已满");
					}
					legionMember2 = createLegionMember(alliance2, legionId);
					bindLegion(allianceId2, legionId, true, "审核加入联盟");
					joinAlliance(alliance2, legion);
					milestoneHandleOnJoinLegion(alliance2, legion);
					// 势力值重算
					allianceProsperityService.reCalcLegionProsperity(legion, true);
					// 同步军团信息给前端GcLegionRequestCheckNotice
					legionManager.sendMessage(legionId, new GcLegionRequestCheckNotice(LegionOutput.toPsLegionInfo(legion)));
					legionManager.sendMessage(legionId, LegionOutput.toGcLegionAllianceMemberList(legion));
					// if (requestRole.isOnline()) {
					// requestRole.send(AllianceOutput.wrapperCheckNotice(alliance));
					// }
					// srvDpd.getMissionService().onMissionFinish(requestRole,
					// MissionType.ALLIANCE_JOIN);
					sendLegionAllianceMemberList(role, legion);
					role.send(new GcLegionRequestCheck(allianceId2));
					updateMemberForAOI(legion.getId(), allianceId2);
					int legionMemberCount = legionManager.getLegionMemberCount(legionId);
					int legionMemberMax = legionManager.getLegionMemberMax(legionId);
					int openServerDay = serverInfoService.getOpenServerDay(Application.getServerId());
					biLogUtil.legionAllianceMembers(legion.getLeaderRoleId(), legion.getoServerId(), Application.getServerId(), legion.getId(), legion.getName(), openServerDay,
							legionMemberCount, legionMemberMax);
					biLogUtil.legionManage(role, legion.getoServerId(), Application.getServerId(), legion.getId(), legion.getName(), legion.getLeaderRoleId(), allianceId2,
							LegionManageBehaviorType.clanJoin);
					legionLogService.createLegionLog(legion, LegionLogType.JOIN, Arrays.asList(alliance2.getAliasName(), alliance2.getName()));
				} else {
					// TODO 拒绝邮件
					// mailService.onAllianceRefuse(requestRole, alliance);
					// BILog
					// srvDpd.getBiLogUtil().allianceOp(requestRole, alliance, BIAllianceAction.REFUSE);
				}
			} else {
				/**
				 * 联盟已解散
				 */
				ErrorLogUtil.errorLog("申请的联盟已解散", "allianceId",allianceId2);
			}
			// TODO 回复前端
			// role.send(new GcAllianceRequestCheck(roleId));
			cgLegionRequestListForLegion(role);
		}
	}

	private void bindLegion(Long allianceId, Long legionId, boolean isJoin, String reason) {
		if (isJoin) {
			log.info("联盟{}加入军团{}{}", allianceId, legionId, reason);
			managerService.onLegionJoin(allianceId, legionId);
			// 清理请求
			legionRequestManager.deleteLegionRequest(allianceId, LegionRequest.TYPE_REQUEST);
		} else {
			log.info("联盟{}退出军团{}{}", allianceId, legionId, reason);
			managerService.onLegionLeave(allianceId, legionId);
		}
		// 刷新军团战力
		flushLegionAllFightPower(allianceId, legionId, isJoin);
		// rankService.updateAllianceRankScore(RankType.ALLIANCE_FIGHT, alliance.getAllFightingPower(), alliance);
		// rankService.updateAllianceRankScore(RankType.ALLIANCE_FIGHT_SINGLE, alliance.getAllFightingPower(), alliance, alliance.getoServerId());
	}

	/**
	 * 更新联盟总战力
	 */
	private void flushLegionAllFightPower(Long allianceId, Long legionId, boolean isJoin) {
		// TODO 刷新每个
		// long total = 0;
		// List<AllianceMember> members =
		// allianceMemberManager.getMembers(alliance.getId());
		// if (null != members) {
		// for (AllianceMember m : members) {
		// Role role = worldService.getWorld().getRole(m.getPersistKey());
		// if (role != null) {
		// total += this.getMemberPower(role);
		// }
		// }
		// }
		// alliance.setAllFightingPower(total);
		// allianceRecommendManager.updateRecommendSort(alliance);
	}

	/**
	 * 加入军团后的响应事件
	 */
	private void joinAlliance(Alliance alliance, Legion legion) {
		// 军团内联盟共享联盟战争
		sdp.getAllianceWarService().onAllianceJoinLegion(alliance, legion);
		// TODO 聊天
		// allianceChatExport.onJoin(role);
		// TODO 重算成员属性
		// roleCalcPropManager.addChangeType(role);
		// TODO 给联盟成员发新成员进入log

		// TODO 新入军团的联盟玩家是否需要广播附近aoi信息
		// RoleCity roleCity = roleCityManager.getRoleCity(role.getPersistKey());
		// if (roleCity.getPosition() != null) {
		// srvDpd.getSceneService().update(roleCity, updater -> {
		// PsCityInfo psCityInfo = updater.getCity();
		// psCityInfo.setAllianceId(String.valueOf(alliance.getId()));
		// psCityInfo.setAllianceAliasName(alliance.getAliasName());
		// });
		// }

		// TODO 小地图更新推送
		// onMemberLocationChange(roleCity, null, true, false);

		// TODO 联盟操作记录
		// allianceOpRecordService.onJoinAlliance(role);

		// TODO 联盟奖励
		// firstJoinAllianceReward(role);

		// BILog
		// srvDpd.getBiLogUtil().allianceOp(role, alliance, BIAllianceAction.JOIN);

		// allianceRecommendManager.updateRecommendSort(alliance);

		// srvDpd.getMissionDailyService().triggerDailyMission(role);

		/** 联盟科技下发 */
		// srvDpd.getAllianceTechService().sendAllianceTechList(role);

		// TODO 公告板下发
		// allianceMessageBordManager.joinAlliance(role);

		// TODO 联盟标记下发
		// allianceMarkService.pushAllianceMarkList(role);

		// 军团事务下发
		sdp.getAllianceAffairService().onAllianceJoinLegion(alliance);

		// TODO 联盟里程碑下发
		// milestoneService.allianceJoin(alliance, role);

		// TODO 成员广播
		// AllianceMember member = allianceMemberDao.findById(role.getId());
		// if (member != null) {
		// member.setJoinTime(System.currentTimeMillis());
		// allianceMemberManager.broadcastMemberUpdate(allianceMemberDao.findById(role.getId()));
		// allianceMemberDao.save(member);
		// }

		// TODO 联盟福利事件下发
		// broadcastBenefitsChange(alliance);

		// 更新个人信息到web服
		// srvDpd.getAsyncOperService().execute(new GlobalUpdateRoleOperation(srvDpd, role, false));

		// srvDpd.getWhispererActivityService().onAllianceJoin(role);

		// TODO 联盟日志
		// List<String> params = new ArrayList<>();
		// params.add(role.getName());
		// allianceLogService.updateAllianceLog(role, alliance, AllianceLogType.JOIN, params);

		// 任务触发
		// srvDpd.getMissionService().onMissionFinish(role,
		// MissionType.ALLIANCE_CITY_OCCUPIED_LEVEL_NUM, role);
		// 头像触发
		// srvDpd.getTitleManager().addTitleForJoinAlliance(role);
	}

	/**
	 * 预退出军团
	 *
	 * @param role
	 */
	public void legionExit(Role role) {
		Long allianceId = role.getAllianceId();
		LegionMember legionMember = legionMemberDao.findById(allianceId);
		if (legionMember == null) {
			ErrorLogUtil.errorLog("玩家没有军团", "allianceId",allianceId);
			return;
		}
		Long legionId = legionMember.getLegionId();
		Legion legion = legionDao.findById(legionId);
		if (legion == null) {
			ErrorLogUtil.errorLog("有LegionMember却没有Legion", "legionMember",legionMember);
			return;
		}
		if (legion.getLeaderAllianceId().longValue() == allianceId.longValue()) {
			ErrorLogUtil.errorLog("主军团无法退出");
			return;
		}
		Alliance alliance = allianceDao.findById(allianceId);
		if (alliance == null) {
			ErrorLogUtil.errorLog("玩家联盟null", "allianceId",allianceId);
			return;
		}
		if (alliance.getLeaderId().longValue() != role.getId().longValue()) {
			ErrorLogUtil.errorLog("玩家不是盟主");
			return;
		}
		if (legionMember.getTransferCDTime() > 0) {
			ErrorLogUtil.errorLog("联盟正在成为主盟");
			return;
		}
		long now = TimeUtil.getNow();
		LegionSettingConfig legionSettingConfig = configService.getConfig(LegionSettingConfig.class);
		long cdTime = legionSettingConfig.getExitRegionCountdown() * TimeUtil.SECONDS_MILLIS;
		long exitCDTime = now + cdTime;
		legionMember.setExitCDTime(exitCDTime);

		legionMemberDao.save(legionMember);

		//
		// 军团邮件
		List<AbstractEmail> sendMailList = new ArrayList<>();
		Collection<LegionMember> legionMembers = legionMemberDao.findByLegionId(legion.getPersistKey());
		for (LegionMember lm : legionMembers) {
			List<AllianceMember> allianceMembers = allianceMemberManager.getMembers(lm.getPersistKey());
			if (JavaUtils.bool(allianceMembers)) {
				for (AllianceMember member : allianceMembers) {
					sendMailList.add(sdp.getMailCreator().createLegionAllianceOutActiveMail(legion, alliance, member.getPersistKey(), legion.getServerId(), MailUtils.buildEmptyTitle()));
				}
			}
		}
		if (JavaUtils.bool(sendMailList)) {
			sdp.getMailSender().sendBatchMail(sendMailList);
		}
		//
		sendLegionAllianceMemberList(role, legion);

		biLogUtil.legionManage(role, legion.getoServerId(), Application.getServerId(), legion.getId(), legion.getName(), legion.getLeaderRoleId(), allianceId,
				LegionManageBehaviorType.clanExit);
	}

	/**
	 * 真实退出
	 *
	 * @param legionMember
	 */
	public void legionExitConfirm(LegionMember legionMember) {
		Alliance alliance = allianceDao.findById(legionMember.getPersistKey());
		LegionLogType legionLogType = null;
		if (legionMember.getExitCDTime() > 0) {
			legionLogType = LegionLogType.EXIT;
			log.info("军团成员退出,legionId={},allianceId={}", legionMember.getLegionId(), legionMember.getPersistKey());
		}
		if (legionMember.getKickCDTime() > 0) {
			legionLogType = LegionLogType.KICK;
			log.info("军团成员被踢,legionId={},allianceId={}", legionMember.getLegionId(), legionMember.getPersistKey());
		}
		legionMember.setExitCDTime(0);
		legionMember.setKickCDTime(0);

		legionManager.sendMessage(legionMember.getLegionId(), new GcLegionExit().setAllianceId(legionMember.getPersistKey()));
		managerService.onLegionLeave(legionMember.getPersistKey(), legionMember.getLegionId());
		onLegionLeave(legionMember.getPersistKey(), legionMember.getLegionId());
		legionMemberDao.delete(legionMember);

		Legion legion = legionManager.findLegionByLegionId(legionMember.getLegionId());
		//
		sdp.getAllianceWarService().onAllianceLeaveLegion(allianceDao.findById(legionMember.getPersistKey()), legion);
		// 势力值重算
		allianceProsperityService.reCalcLegionProsperity(legion, true);

		legionManager.sendMessage(legionMember.getLegionId(), LegionOutput.toGcLegionLoad(legion));

		updateMemberForAOI(0L, legionMember.getPersistKey());

		int legionMemberCount = legionManager.getLegionMemberCount(legionMember.getLegionId());
		int legionMemberMax = legionManager.getLegionMemberMax(legionMember.getLegionId());
		int openServerDay = serverInfoService.getOpenServerDay(Application.getServerId());
		biLogUtil.legionAllianceMembers(legion.getLeaderRoleId(), legion.getoServerId(), Application.getServerId(), legion.getId(), legion.getName(), openServerDay,
				legionMemberCount, legionMemberMax);

		legionLogService.createLegionLog(legion, legionLogType, Arrays.asList(alliance.getAliasName(), alliance.getName()));
	}

	private void onLegionLeave(Long allianceId, Long legionId) {
		Collection<RegionCapitalNode> nodes = legionManager.findRegionCapitalNodeByLegionId(legionId);
		if (JavaUtils.bool(nodes)) {
			for (RegionCapitalNode node : nodes) {
				var armies = sceneService.getNodeArmies(node);
				for (var army : armies) {
					Role owner = army.getOwner();
					if (null == owner) {
						continue;
					}
					// 有驻防，撤回驻防
					if (owner.getAllianceId() != null && owner.getAllianceId() == allianceId.longValue()) {
						armyManager.returnArmy(army);
					}
				}
			}
		}
		// 正在集结中的部队处理：返回玩家城市
		List<AllianceMember> members = allianceMemberManager.getMembers(allianceId);
		if (JavaUtils.bool(members)) {
			for (AllianceMember allianceMember : members) {
				Collection<ArmyInfo> armyInfos = armyManager.findByRoleId(allianceMember.getPersistKey());
				if (JavaUtils.bool(armyInfos)) {
					Collection<ArmyInfo> temp = new ArrayList<>(armyInfos);
					for (ArmyInfo armyInfo : temp) {
						ArmyType armyType = armyInfo.getArmyType();
						boolean rally = armyType.isRally();
						if (rally) {
							armyManager.returnArmyImmediately(armyInfo);
						}
					}
				}
			}
		}
		// 集结已出发的部队处理：无需额外处理（后续考虑）
	}

	/**
	 * 军团申请请求
	 *
	 * @param role
	 */
	public void cgLegionRequestListForLegion(Role role) {
		GcLegionRequestListForLegion gcLegionRequestListForLegion = new GcLegionRequestListForLegion();
		Long allianceId = role.getAllianceId();
		if (!JavaUtils.bool(allianceId)) {
			// 没有联盟
			role.send(gcLegionRequestListForLegion);
			return;
		}
		LegionMember legionMember = legionMemberDao.findById(allianceId);
		if (legionMember == null) {
			// 没有军团
			role.send(gcLegionRequestListForLegion);
			return;
		}
		List<LegionRequest> legionRequests = legionRequestManager.findLegionRequest(legionMember.getLegionId(), LegionRequest.TYPE_REQUEST);
		if (!JavaUtils.bool(legionRequests)) {
			// 没有申请
			role.send(gcLegionRequestListForLegion);
			return;
		}

		for (LegionRequest legionRequest : legionRequests) {
			Long allianceId2 = legionRequest.getAllianceId();
			Alliance alliance = allianceDao.findById(allianceId2);
			if (alliance == null) {
				continue;
			}
			PsAllianceInfo psAllianceInfo = AllianceOutput.toInfo(alliance);
			if (psAllianceInfo == null) {
				continue;
			}
			gcLegionRequestListForLegion.addToRequestAlliances(psAllianceInfo);
		}
		role.send(gcLegionRequestListForLegion);
	}

	public void sendLegionInfo(Role role, long legionId) {
		Legion legion = legionDao.findById(legionId);
		if (legion != null) {
			GcLegionLoad gcLegionLoad = LegionOutput.toGcLegionLoad(legion);
			role.send(gcLegionLoad);
		}
	}

	/**
	 * 军团解散
	 *
	 * @param role
	 */
	public void legionDismiss(Role role) {
		Long allianceId = role.getAllianceId();
		if (!JavaUtils.bool(allianceId)) {
			// 没有联盟
			ErrorLogUtil.errorLog("解散军团没有联盟");
			return;
		}
		LegionMember legionMember = legionMemberDao.findById(allianceId);
		if (legionMember == null) {
			// 没有军团
			ErrorLogUtil.errorLog("解散军团没有军团");
			return;
		}
		Legion legion = legionDao.findById(legionMember.getLegionId());
		if (legion == null) {
			// 没有军团
			ErrorLogUtil.errorLog("解散军团没有军团");
			return;
		}
		Alliance alliance = allianceDao.findById(allianceId);
		if (alliance.getLeaderId().longValue() != role.getId().longValue()) {
			// 不是盟主
			ErrorLogUtil.errorLog("解散军团不是盟主");
			return;
		}
		if (legion.getLeaderRoleId().longValue() != role.getId().longValue()) {
			ErrorLogUtil.errorLog("不是军团长不能解散军团");
			// 不是军团长
			return;
		}
		boolean checkLegionOfficialAuth = legionOfficialManager.checkLegionOfficialAuth(LegionRankAuthEnum.AUTH_LEGION_DISBAND, role);
		if (!checkLegionOfficialAuth) {
			ErrorLogUtil.errorLog("解散军团权限不足");
			// 权限不足
			return;
		}
		Collection<LegionMember> legionMembers = legionMemberDao.findByLegionId(legion.getId());
		if (legionMembers.size() > 1) {
			ErrorLogUtil.errorLog("军团成员大于1");
			// 军团成员大于1
			return;
		}
		// 移除军团相关实体
		legionDao.delete(legion);
		legionMemberDao.delete(legionMember);
		Map<Integer, List<LegionRequest>> legionRequests = legionRequestDao.findByLegionId(legion.getId());
		if (JavaUtils.bool(legionRequests)) {
			Collection<LegionRequest> removes = new ArrayList<>();
			legionRequests.values().forEach(removes::addAll);
			legionRequestDao.delete(removes);
		}
		//
		// 军团邮件, 解散军团时，一定只剩下主联盟了， 所以只发这个联盟的即可
		List<AbstractEmail> sendMailList = new ArrayList<>();
		List<AllianceMember> allianceMembers = allianceMemberManager.getMembers(legionMember.getPersistKey());
		if (JavaUtils.bool(allianceMembers)) {
			for (AllianceMember member : allianceMembers) {
				sendMailList.add(sdp.getMailCreator().createLegionDismissMail(legion, member.getPersistKey(), legion.getServerId(), MailUtils.buildEmptyTitle()));
			}
		}
		if (JavaUtils.bool(sendMailList)) {
			sdp.getMailSender().sendBatchMail(sendMailList);
		}
		//
		// 删除军衔
		legionOfficialManager.onLegionDismiss(legion);

		updateMemberForAOI(0L, allianceId);

		GcLegionDismiss gcLegionDismiss = new GcLegionDismiss().setLegionId(legion.getId());
		role.send(gcLegionDismiss);

		biLogUtil.legionManage(role, legion.getoServerId(), Application.getServerId(), legion.getId(), legion.getName(), legion.getLeaderRoleId(), allianceId,
				LegionManageBehaviorType.legionDissolve);

		legionLogManager.onLegionDismiss(legion);

		legionMarkManager.onLegionDismiss(legion);
	}

	public Legion getLegion(Role role) {
		Long legionId = getLegionId(role);
		if (JavaUtils.bool(legionId)) {
			return legionDao.findById(legionId);
		}
		return null;
	}

	public Long getLegionId(Role role) {
		if (null == role) {
			return null;
		}
		if (isInLegion(role)) {
			LegionMember member = legionMemberService.getLegionMemberByAllianceId(role.getAllianceId());
			return member.getLegionId();
		}
		return null;
	}

	public Legion getLegionByLegionId(Long legionId) {
		if (null == legionId) {
			return null;
		}
		return legionDao.findById(legionId);
	}

	public Long getLegionId(Alliance alliance) {
		if (alliance == null) {
			return null;
		}
		LegionMember member = legionMemberService.getLegionMemberByAllianceId(alliance.getPersistKey());
		if (member != null)
			return member.getLegionId();
		return null;
	}

	public Legion getLegion(Alliance alliance) {
		if (alliance == null) {
			return null;
		}
		LegionMember member = legionMemberService.getLegionMemberByAllianceId(alliance.getPersistKey());
		if (member != null)
			return legionDao.findById(member.getLegionId());
		return null;
	}

	public boolean isInLegion(Role role) {
		Alliance allianceByRole = allianceService.getAllianceByRole(role);
		if (allianceByRole == null) {
			return false;
		}
		LegionMember member = legionMemberService.getLegionMemberByAllianceId(allianceByRole.getPersistKey());
		if (member != null)
			return true;
		return false;
	}

	public boolean isSameLegion(Role role, Role targetRole) {
		return false;
	}

	public boolean isSameLegion(Role role, Long targetAllianceId) {
		return false;
	}

	public void cgLegionTransfer(Role role, long allianceId) {
		Long mainAllianceId = role.getAllianceId();
		if (!JavaUtils.bool(mainAllianceId)) {
			return;
		}
		Alliance mainAlliance = allianceDao.findById(mainAllianceId);
		if (mainAlliance == null) {
			// 没有联盟
			ErrorLogUtil.errorLog("玩家没有联盟");
			return;
		}
		LegionMember mainLegionMember = legionMemberDao.findById(mainAllianceId);
		if (mainLegionMember == null) {
			// 没有军团
			ErrorLogUtil.errorLog("玩家没有军团");
			return;
		}
		Legion legion = legionDao.findById(mainLegionMember.getLegionId());
		if (legion == null) {
			ErrorLogUtil.errorLog("玩家没有军团");
			return;
		}
		if (legion.getLeaderRoleId().longValue() != role.getId().longValue()) {
			ErrorLogUtil.errorLog("玩家不是军团长");
			return;
		}
		// 目标联盟
		LegionMember legionMember = legionMemberDao.findById(allianceId);
		if (legionMember == null) {
			ErrorLogUtil.errorLog("目标联盟没有军团");
			return;
		}
		if (legionMember.getLegionId().longValue() != mainLegionMember.getLegionId().longValue()) {
			ErrorLogUtil.errorLog("不是同一个军团");
			return;
		}
		long exitCDTime = legionMember.getExitCDTime();
		if (exitCDTime > 0) {
			ErrorLogUtil.errorLog("目标联盟正在退出");
			return;
		}

		boolean checkLegionOfficialAuth = legionOfficialManager.checkLegionOfficialAuth(LegionRankAuthEnum.AUTH_LEGION_TRANSFER, role);
		if (!checkLegionOfficialAuth) {
			ErrorLogUtil.errorLog("没有权限操作转让");
			return;
		}

		// 转让倒计时
		LegionSettingConfig legionSettingConfig = configService.getConfig(LegionSettingConfig.class);
		int transferRegionCountdown = legionSettingConfig.getTransferRegionCountdown();
		long transferTime = transferRegionCountdown * TimeUtil.SECONDS_MILLIS;
		long now = TimeUtil.getNow();
		legionMember.setTransferCDTime(now + transferTime);
		legionMemberDao.save(legionMember);

		GcLegionLoad gcLegionLoad = LegionOutput.toGcLegionLoad(legion);
		role.send(gcLegionLoad);
		GcLegionAllianceMemberList gcLegionAllianceMemberList = LegionOutput.toGcLegionAllianceMemberList(legion);
		legionManager.sendMessage(legion.getId(), gcLegionAllianceMemberList);

		biLogUtil.legionManage(role, legion.getoServerId(), Application.getServerId(), legion.getId(), legion.getName(), legion.getLeaderRoleId(), allianceId,
				LegionManageBehaviorType.mainClanTransfer);
	}

	/**
	 * 真实转让
	 *
	 * @param legionMember
	 */
	public void legionTransferConfirm(LegionMember legionMember) {
		legionMember.setTransferCDTime(0);
		legionMemberDao.save(legionMember);
		Legion legion = legionDao.findById(legionMember.getLegionId());
		if (legion == null) {
			// 军团可能已经解散了
			legionMemberDao.delete(legionMember);
			ErrorLogUtil.errorLog("主军团转让时,军团已经解散了");
			return;
		}
		Long oldAllianceId = legion.getLeaderAllianceId();
		// 老团长
		Long oldLeaderRoleId = legion.getLeaderRoleId();
		Role oldLeaderRole = roleManager.getRole(oldLeaderRoleId);
		Long allianceId = legionMember.getPersistKey();
		Alliance alliance = allianceDao.findById(allianceId);
		legionManager.updateLegionLeader(alliance, true);
		GcLegionLoad gcLegionLoad = LegionOutput.toGcLegionLoad(legion);
		legionManager.sendMessage(legion.getId(), gcLegionLoad);
		GcLegionAllianceMemberList gcLegionAllianceMemberList = LegionOutput.toGcLegionAllianceMemberList(legion);
		legionManager.sendMessage(legion.getId(), gcLegionAllianceMemberList);
		log.info("主盟转让，军团{}，{}->{}", legionMember.getLegionId(), oldAllianceId, allianceId);
		// 更新官职信息给新老团长
		GcLegionOfficialList gcLegionOfficialList = LegionOutput.toGcLegionOfficialList(legion);
		if (oldLeaderRole != null && oldLeaderRole.isOnline()) {
			oldLeaderRole.send(gcLegionOfficialList);
			log.info("更新军团官衔给老团长{}", oldLeaderRoleId);
		}
		Long newLeaderRoleId = legion.getLeaderRoleId();
		Role newLeaderRole = roleManager.getRole(newLeaderRoleId);
		if (newLeaderRole != null && newLeaderRole.isOnline()) {
			newLeaderRole.send(gcLegionOfficialList);
			log.info("更新军团官衔给新团长{}", newLeaderRoleId);
		}

		legionLogService.createLegionLog(legion, LegionLogType.TRANSLATE, Arrays.asList(alliance.getAliasName(), alliance.getName()));
	}

	/**
	 * 军团基础信息修改
	 *
	 * @param role
	 * @param message
	 */
	public void cgLegionUpdate(Role role, CgLegionUpdate message) {
		Legion legion = legionManager.findLegionByRole(role);
		if (legion == null) {
			return;
		}

		boolean checkLegionOfficialAuth = legionOfficialManager.checkLegionOfficialAuth(LegionRankAuthEnum.AUTH_LEGION_EDIT, role);
		if (!checkLegionOfficialAuth) {
			// 权限不足
			return;
		}

		LegionSettingConfig legionSettingConfig = configService.getConfig(LegionSettingConfig.class);
		int legionChangeConsume = legionSettingConfig.getLegionChangeConsume();
		boolean checkAndCost = roleCurrencyManager.checkAndCost(role, Currency.DIAMOND, legionChangeConsume, MoneyLogReason.LEGION_CHANGE_CONSUME, "修改军团基础信息");
		if (!checkAndCost) {
			ErrorLogUtil.errorLog("钻石不足",
					"curDiamond",roleCurrencyManager.getMoney(role, Currency.DIAMOND),
					"needDiamond",legionChangeConsume);
			return;
		}

		boolean setName = message.isSetName();
		if (setName) {
			String name = message.getName();
			name = StringUtils.trim(name);
			if (StringUtils.isBlank(name)) {
				ErrorLogUtil.errorLog("名字不能为空");
				return;
			}

			// 名字重复
			if (legionDao.containsName(name)) {
				// role.send(AllianceOutput.wrapperCreate(null, 1));
				ErrorLogUtil.errorLog("alliance rename already exist.", "name",name);
				return;
			}

			boolean checkLegionAbbreviationLengths = legionSettingConfig.checkLegionAbbreviationLengths(name);
			if (!checkLegionAbbreviationLengths) {
				ErrorLogUtil.errorLog("军团名字长度不对");
				return;
			}
			legion.setName(name);
		}
		boolean setDeclaration = message.isSetDeclaration();
		if (setDeclaration) {
			String declaration = message.getDeclaration();
			if (declaration != null) {
				boolean checkLegionDeclarationLength = legionSettingConfig.checkLegionDeclarationLength(declaration);
				if (!checkLegionDeclarationLength) {
					ErrorLogUtil.errorLog("军团宣言过长");
					return;
				}
			}
			legion.setDeclaration(declaration);
		}
		boolean setLanguage = message.isSetLanguage();
		if (setLanguage) {
			legion.setLanguage(message.getLanguage());
		}
		boolean setBannerColor = message.isSetBannerColor();
		if (setBannerColor) {
			legion.setBannerColor(message.getBannerColor());
		}
		boolean setBanner = message.isSetBanner();
		if (setBanner) {
			legion.setBanner(message.getBanner());
		}
		boolean setBadgeColor = message.isSetBadgeColor();
		if (setBadgeColor) {
			legion.setBadgeColor(message.getBadgeColor());
		}
		boolean setBadge = message.isSetBadge();
		if (setBadge) {
			legion.setBadge(message.getBadge());
		}
		boolean setCountry = message.isSetCountry();
		if (setCountry) {
			legion.setCountry(message.getCountry());
		}

		legionDao.save(legion);

		GcLegionLoad gcLegionLoad = LegionOutput.toGcLegionLoad(legion);
		role.send(gcLegionLoad);
	}

	// ------------官衔------------

	/**
	 * 官衔列表
	 *
	 * @param role
	 */
	public void cgLegionOfficialList(Role role, long legionId) {
		Legion legion = null;
		if (legionId > 0) {
			legion = legionManager.findLegionByLegionId(legionId);
		} else {
			legion = legionManager.findLegionByRole(role);
		}
		if (legion == null) {
			return;
		}
		GcLegionOfficialList gcLegionOfficialList = LegionOutput.toGcLegionOfficialList(legion);
		role.send(gcLegionOfficialList);
	}

	/**
	 * 官衔修改
	 *
	 * @param role
	 * @param group
	 * @param order
	 * @param roleId
	 */
	public void cgLegionOfficialUpdate(Role role, int group, int order, long roleId) {
		Legion legion = legionManager.findLegionByRole(role);
		if (legion == null) {
			return;
		}
		// 判断权限
		boolean checkLegionOfficialAuth = legionOfficialManager.checkLegionOfficialAuth(LegionRankAuthEnum.AUTH_LEGION_RANK_APPOINTMENT, role);
		if (!checkLegionOfficialAuth) {
			// 权限不足
			return;
		}

		String title = "";
		if (roleId > 0) {
			// 升官
			AbstractRole abstractRole = worldService.getWorld().getAbstractRole(roleId);
			if (abstractRole == null) {
				ErrorLogUtil.errorLog("玩家不在内存或不在本服");
				return;
			}
			LegionOfficialConfig legionOfficialConfig = configService.getConfig(LegionOfficialConfig.class);
			LegionOfficialMeta legionOfficialMeta = legionOfficialConfig.getMeta(group, order);
			title = legionOfficialMeta.getTitle();
		} else {
			// 撤职
		}

		LegionOfficial legionOfficial = legionOfficialManager.getLegionOfficial(role);
		LegionOfficial legionOfficial2 = legionOfficialManager.getLegionOfficial(legion.getId(), group, order);
		if (legionOfficial2 == null) {
			ErrorLogUtil.errorLog("对应位置没有官职", "legionId",legion.getId(),
					"group",group, "order",order);
			return;
		}
		if (legionOfficial.getRank() <= legionOfficial2.getRank()) {
			ErrorLogUtil.errorLog("不能越级操作");
			return;
		}
		if (legionOfficial.getRank() < LegionConstants.RANK_10) {
			// 如果不是r10，要判断分组
			if (legionOfficial.getGroup() != legionOfficial2.getGroup()) {
				ErrorLogUtil.errorLog("分组不同,不能操作");
				return;
			}
		}
		long appointeeID = 0;
		long appointeeAllianceID = 0;
		// 如果是免职，就是被免职的玩家
		Long roleId2 = legionOfficial2.getRoleId();
		Role role2 = null;
		if (JavaUtils.bool(roleId2)) {
			role2 = roleManager.getRole(roleId2);
		} else {
			// 新官职
		}
		LegionLogType legionLogType = legionOfficialManager.updateLegionOfficial(legionOfficial2, roleId);
		GcLegionOfficialList gcLegionOfficialList = new GcLegionOfficialList();
		gcLegionOfficialList.addToLegionOfficials(LegionOutput.toPsLegionOfficialInfo(legionOfficial2));
		gcLegionOfficialList.setLegionId(legion.getId());
		role.send(gcLegionOfficialList);
		String targetName = "";
		if (role2 != null) {
			targetName = role2.getName();
			appointeeID = roleId2;
			appointeeAllianceID = role2.getAllianceId();
			if (role2.isOnline())
				role2.send(gcLegionOfficialList);
		}
		if (JavaUtils.bool(roleId)) {
			role2 = roleManager.getRole(roleId);
			if (role2 != null) {
				targetName = role2.getName();
				appointeeID = roleId;
				appointeeAllianceID = role2.getAllianceId();
				if (role2.isOnline())
					role2.send(gcLegionOfficialList);
			}
		}
		List<String> params = new ArrayList<>(3);
		params.add(targetName);
		params.add(role.getName());
		params.add(title);

		biLogUtil.legionOfficialUpdate(role, legion.getoServerId(), Application.getServerId(), legion.getId(), legionOfficial2.getRank(), appointeeID, appointeeAllianceID);

		legionLogService.createLegionLog(legion, legionLogType, params);
	}

	// -----------------军团搜索-----------------
	public void cgLegionSearch(Role role, String keyword) {
		int count = 0;
		boolean empty = StringUtils.isEmpty(keyword);
		if (!empty) {
			keyword = keyword.toLowerCase();
		}
		int oServerId = role.getoServerId();
		Collection<Legion> legions = legionDao.findByCurrentServerId(oServerId);
		if (legions.size() == 0) {
			ErrorLogUtil.errorLog("关键字搜索联盟总集空", "serverId",oServerId);
		}
		GcLegionSearch gcLegionSearch = new GcLegionSearch();
		for (Legion legion : legions) {
			// 无条件搜索，默认返回前10条记录
			// 所有联盟的db不等于联盟的currentServerId的都应该是被从远端加载进来的
			if (legion.getDB() != legion.getCurrentServerId()) {
				continue;
			}
			if (empty) {
				gcLegionSearch.addToLegions(LegionOutput.toPsLegionInfo(legion));
				if (count++ > LegionConstants.PAGE_SIZE_SEARCH) {
					break;
				}
			} else {
				if (legion.getName().toLowerCase().contains(keyword)) {
					gcLegionSearch.addToLegions(LegionOutput.toPsLegionInfo(legion));
				}
			}
		}

		role.send(gcLegionSearch);
	}

	// ----------排行榜----------
	public void cgLegionRank(Role role, RankType type, int page) {
		if (page < 1) {
			return;
		}
		int start = (page - 1) * RankConstants.PAGE_SIZE;
		int end = Math.min(page * RankConstants.PAGE_SIZE, RankConstants.MAX_SIZE);

		if (start >= RankConstants.MAX_SIZE) {
			ErrorLogUtil.errorLog("rank max size", "roleId",role.getId(),
					"maxSize",RankConstants.MAX_SIZE);
			return;
		}

		RankContext ctx = new RankContext(type, page, start, end);

		// kvk 军团势力值排行榜区分，结算后从db读取历史数据，否则从redis读取实时数据。
		if (type == RankType.LEGION_PROSPERITY) {
			// 如果是赛季服务器 && 赛季已经结算了，读db中固化的排行榜信息
			ServerInfo serverInfo = Application.getBean(ServerInfoDao.class).findById((long) Application.getServerId());
			if (Application.getServerType() == ServerType.KVK_SEASON && serverInfo != null && serverInfo.getSeasonRewardTimeMap().containsKey(serverInfo.getoServerId())) {
				Application.getBean(KvkSeasonServiceImpl.class).getKvkLegionProsperityRank(role, role.getoServerId(),
						Application.getConfigCenter().getCurrentKvkSeasonServerGroupConfig().getSeason(), ctx);
				return;
			}
		}

		asyncOperationService.execute(new LegionRankGetOperation(role, ctx));
	}

	// ----------踢出----------
	public void cgLegionKick(Role role, long allianceId) {
		Long mainAllianceId = role.getAllianceId();
		Legion legion = legionManager.findLegionByAllianceId(mainAllianceId);
		if (legion == null) {
			ErrorLogUtil.errorLog("玩家没有军团", "mainAllianceId",mainAllianceId);
			return;
		}
		boolean checkLegionOfficialAuth = legionOfficialManager.checkLegionOfficialAuth(LegionRankAuthEnum.AUTH_LEGION_KICKS, role);
		if (!checkLegionOfficialAuth) {
			ErrorLogUtil.errorLog("没有权限踢联盟");
			return;
		}
		Alliance beKickedAlliance = allianceDao.findById(allianceId);
		if (beKickedAlliance == null) {
			ErrorLogUtil.errorLog("踢的联盟不存在");
			return;
		}
		LegionMember legionMember = legionMemberDao.findById(allianceId);
		if (legionMember == null) {
			ErrorLogUtil.errorLog("没有对应的成员", "allianceId",allianceId);
			return;
		}
		long exitCDTime = legionMember.getExitCDTime();
		if (exitCDTime > 0) {
			ErrorLogUtil.errorLog("正在退出或踢出{}", "allianceId",allianceId);
			return;
		}
		long now = TimeUtil.getNow();
		LegionSettingConfig legionSettingConfig = configService.getConfig(LegionSettingConfig.class);
		long cdTime = legionSettingConfig.getExitRegionCountdown() * TimeUtil.SECONDS_MILLIS;
		exitCDTime = now + cdTime;
		legionMember.setKickCDTime(exitCDTime);

		legionMemberDao.save(legionMember);
		//
		// 军团邮件
		List<AbstractEmail> sendMailList = new ArrayList<>();
		Collection<LegionMember> legionMembers = legionMemberDao.findByLegionId(legion.getPersistKey());
		for (LegionMember lm : legionMembers) {
			List<AllianceMember> allianceMembers = allianceMemberManager.getMembers(lm.getPersistKey());
			if (JavaUtils.bool(allianceMembers)) {
				for (AllianceMember member : allianceMembers) {
					sendMailList.add(sdp.getMailCreator().createLegionAllianceKickedOutMail(legion, role, beKickedAlliance, member.getPersistKey(), legion.getServerId(), MailUtils.buildEmptyTitle()));
				}
			}
		}
		if (JavaUtils.bool(sendMailList)) {
			sdp.getMailSender().sendBatchMail(sendMailList);
		}
		// TODO 所有人广播？
		sendLegionAllianceMemberList(role, legion);

		biLogUtil.legionManage(role, legion.getoServerId(), Application.getServerId(), legion.getId(), legion.getName(), legion.getLeaderRoleId(), allianceId,
				LegionManageBehaviorType.removeClan);
	}

	/**
	 * 检查军团名字是否可用
	 *
	 * @param role
	 * @param name
	 */
	public void cgLegionNameExist(Role role, String name) {
		GcLegionNameExist gcLegionNameExist = new GcLegionNameExist();
		boolean exist = legionDao.containsName(name);
		if (exist) {
			gcLegionNameExist.setExist(true);
			role.send(gcLegionNameExist);
			return;
		}

		if (!check(role, name)) {
			gcLegionNameExist.setExist(exist);
			gcLegionNameExist.setLegitimate(false);
			role.send(gcLegionNameExist);
			return;
		}

		boolean checkWord = checkStringService.checkWord(name);
		if (!checkWord) {
			gcLegionNameExist.setExist(exist);
			gcLegionNameExist.setLegitimate(false);
			role.send(gcLegionNameExist);
			return;
		}

		// AllianceNameCheckWordOperation checkWordOperation = new
		// AllianceNameCheckWordOperation(role, exist, name);
		// asyncOperationService.execute(checkWordOperation);
	}

	private boolean check(Role role, String name) {
		boolean allUpperCase = StringUtils.isAllUpperCase(name);
		if (!allUpperCase) {
			ErrorLogUtil.errorLog("军团名字格式不对", "name",name);
			return false;
		}

		LegionSettingConfig legionSettingConfig = configService.getConfig(LegionSettingConfig.class);
		boolean checkLegionAbbreviationLengths = legionSettingConfig.checkLegionAbbreviationLengths(name);
		if (!checkLegionAbbreviationLengths) {
			ErrorLogUtil.errorLog("军团名字超长", "name",name);
			return false;
		}
		return true;
	}

	/**
	 * 军团开关
	 *
	 * @return
	 */
	public boolean getLegionSwitch() {
		int season = Application.getConfigCenter().getSeason();
		if (season <= 1) {
			return false;
		}
		KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = Application.getConfigCenter().getCurrentKvkSeasonServerGroupConfig();
		if (kvkSeasonServerGroupConfig == null) {
			return false;
		}
		KvkGroupConfig.KvkGroupMeta currentKvkGroupMeta = configService.getConfig(KvkGroupConfig.class).getKvkServerMeta(kvkSeasonServerGroupConfig.getKServerId());
		if (currentKvkGroupMeta == null) {
			return false;
		}
		if (!currentKvkGroupMeta.isLegionSwitch()) {
			// 开关关闭了
			return false;
		}
		return true;
	}

	public void cgLegionAllianceMemberList(Role role, long legionId) {
		Legion legion = null;
		if (legionId > 0) {
			legion = legionDao.findById(legionId);
		} else {
			legion = legionManager.findLegionByRole(role);
		}
		if (legion != null) {
			sendLegionAllianceMemberList(role, legion);
		}
	}

	public void sendLegionAllianceMemberList(Role role, Legion legion) {
		GcLegionAllianceMemberList gcLegionAllianceMemberList = LegionOutput.toGcLegionAllianceMemberList(legion);
		role.send(gcLegionAllianceMemberList);
	}

	public void cgLegionRequestCancel(Role role, long legionId, boolean isCancelAll) {
		Long allianceId = role.getAllianceId();
		if (!JavaUtils.bool(allianceId)) {
			// 没有联盟
			return;
		}
		Alliance alliance = allianceDao.findById(allianceId);
		if (alliance == null) {
			return;
		}
		if (alliance.getLeaderId().longValue() != role.getId().longValue()) {
			// 不是盟主
			return;
		}
		if (isCancelAll) {
			// 全清
			Map<Integer, List<LegionRequest>> legionRequests = legionRequestDao.findByAllianceId(allianceId);
			if (JavaUtils.bool(legionRequests)) {
				legionRequestManager.deleteLegionRequest(role, LegionRequest.TYPE_REQUEST);
			}
		} else if (legionId > 0) {
			legionRequestManager.deleteLegionRequest(role, legionId, LegionRequest.TYPE_REQUEST);
		}
		getLegionList(role);
	}

	/**
	 * 联盟加入军团，相关编年史处理
	 *
	 * @param alliance
	 * @param legion
	 */
	public void milestoneHandleOnJoinLegion(Alliance alliance, Legion legion) {
		milestoneService.onMilestoneEventTrigger(MilestoneType.JOIN_LEGION_FINAL, alliance.getId());
		//
		milestoneService.onMilestoneEventTrigger(MilestoneType.LEGION_MEMBER_LEVEL_COUNTER, legion.getPersistKey(), 0);
	}

	public void editLegionAnnouncement(Role role, int index, String declaration, PsAnnouncementType type, boolean chatAndMail) {
		Legion legion = getLegion(role);
		if (legion == null) {
			return;
		}
		// 权限检查
		if (!legionOfficialManager.checkLegionOfficialAuth(LegionRankAuthEnum.AUTH_ANNOUNCEMENT_EDIT, role)) {
			return;
		}
		if (type == PsAnnouncementType.ADD) {
			LegionAnnouncement announcement = getAnnouncement(legion, index);
			if (announcement == null) {

			}
		} else if (type == PsAnnouncementType.DEL) {

		} else if (type == PsAnnouncementType.TOP) {

		}
		LegionAnnouncementCheckWordOperation checkWordOperation = new LegionAnnouncementCheckWordOperation(role, this, legion, index, declaration, type, chatAndMail);
		asyncOperationService.execute(checkWordOperation);
	}

	public void editAnnouncement(Role role, Legion legion, int index, String declaration, PsAnnouncementType type, boolean chatAndMail) {
		if (type == PsAnnouncementType.ADD) {
			LegionAnnouncement announcement = getAnnouncement(legion, index);
			if (announcement == null) {
				if (legion.getAnnouncementList().size() >= 5) {
					ErrorLogUtil.errorLog("editLegionAnnouncement Announcement is max count", "roleId",role.getId());
					return;
				}

				// 新增
				announcement = new LegionAnnouncement();
				announcement.setDeclaration(declaration);
				announcement.setRoleId(role.getId());
				announcement.setTime(TimeUtil.getNow());

				// 无论编辑第几个，首次也要置顶
				if (legion.getAnnouncementList().isEmpty()) {
					announcement.setIndex(1);
				}

				legion.getAnnouncementList().add(announcement);
			} else {
				announcement.setDeclaration(declaration);
				announcement.setTime(TimeUtil.getNow());
				announcement.setRoleId(role.getPersistKey());
			}
		} else if (type == PsAnnouncementType.DEL) {
			LegionAnnouncement announcement = getAnnouncement(legion, index);
			if (announcement == null) {
				ErrorLogUtil.errorLog("editLegionAnnouncement Del Announcement is null", "roleId",role.getId(), "index",index);
				return;
			}

			// 置顶的不可以删除
			if (announcement.getIndex() == 1 || legion.getAnnouncementList().size() <= 1) {
				ErrorLogUtil.errorLog("editLegionAnnouncement Del Top Announcement is null", "roleId",role.getId(), "index",index);
				return;
			}

			legion.getAnnouncementList().remove(announcement);
		} else if (type == PsAnnouncementType.TOP) {
			// 先把原来的取消置顶
			LegionAnnouncement oldAnnouncement = getAnnouncement(legion, 1);
			if (oldAnnouncement != null) {
				oldAnnouncement.setIndex(0);
			}

			// 置顶
			LegionAnnouncement announcement = getAnnouncement(legion, index);
			if (announcement == null) {
				ErrorLogUtil.errorLog("editLegionAnnouncement Top Announcement is null", "roleId",role.getId(), "index",index);
				return;
			}

			announcement.setIndex(1);
		}

		// 排序
		sortAnnouncement(legion.getAnnouncementList());
		legionDao.save(legion);

		if (chatAndMail) {
			// 军团暂时不需要发送邮件
		}
		// retMsg
		GcEditLegionAnnouncement retMsg = new GcEditLegionAnnouncement();
		legion.getAnnouncementList().forEach(a -> retMsg.addToAnnouncements(LegionOutput.toLegionAnnouncementInfo(a)));
		role.send(retMsg);

		// 广播军团
		GcLegionAnnouncement pushMsg = new GcLegionAnnouncement();
		legion.getAnnouncementList().forEach(a -> pushMsg.addToAnnouncements(LegionOutput.toLegionAnnouncementInfo(a)));
		Collection<LegionMember> legionMembersByLegionId = legionMemberService.getLegionMembersByLegionId(legion.getPersistKey());
		for (LegionMember legionMember : legionMembersByLegionId) {
			allianceManager.broadcast(legionMember.getPersistKey(), pushMsg);
		}
	}

	private LegionAnnouncement getAnnouncement(Legion legion, int index) {
		for (LegionAnnouncement announcement : legion.getAnnouncementList()) {
			if (announcement.getIndex() == index) {
				return announcement;
			}
		}
		return null;
	}

	private void sortAnnouncement(List<LegionAnnouncement> announcements) {
		if (announcements.size() > 1) {
			// 置顶不排序
			List<LegionAnnouncement> sortList = announcements.stream().filter(a -> a.getIndex() != 1).sorted((o1, o2) -> (int) (o2.getTime() - o1.getTime())).collect(Collectors.toList());

            int index = 2;
			for (LegionAnnouncement announcement : sortList) {
				announcement.setIndex(index);
				index++;
			}
		}
	}

	/**
	 * 发送军团全体邮件
	 * 
	 * @param role
	 * @param title
	 * @param content
	 */
	public void sendLegionAllEmail(Role role, String title, String content) {
		if (!legionOfficialManager.checkLegionOfficialAuth(LegionRankAuthEnum.AUTH_MAIL_ALL, role)) {
			ErrorLogUtil.errorLog("权限不足");
		}
		Legion legion = getLegion(role);
		if (legion == null) {
			return;
		}
		Collection<LegionMember> legionMembersByLegionId = legionMemberService.getLegionMembersByLegionId(legion.getPersistKey());
		List<AbstractEmail> mailList = new ArrayList<>();
		for (LegionMember legionMember : legionMembersByLegionId) {
			List<AllianceMember> members = allianceMemberManager.getMembers(legionMember.getPersistKey());
			if (!JavaUtils.bool(members)) {
				continue;
			}
			for (AllianceMember member : members) {
				LegionAllMail mail = new LegionAllMail();
				mail.setTitle(title);
				mail.setContent(content);
				mail.setSenderName(role.getName());
				mail.setLegionName(legion.getName());
				mail.setSenderHead(role.getHead());
				mail.setServerId(role.getoServerId());
				mail.setSenderRoleId(role.getPersistKey() + "");
                mail.setSenderRoleInfo(role.toRoleInfo());
				Application.getBean(MailCreator.class).setAbstractEmailField(member.getPersistKey(), role.getCurrentServerId(), MailUtils.buildEmptyTitle(), mail);
				mailList.add(mail);
			}
		}
		if (JavaUtils.bool(mailList))
			Application.getBean(MailSender.class).sendBatchMail(mailList);
	}
}
