package com.lc.billion.icefire.kvkseason.biz.tick;

import java.util.Collection;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.common.AbstractTicker;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionMemberDao;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionMember;
import com.lc.billion.icefire.kvkseason.biz.service.impl.legion.LegionService;

/**
 * <AUTHOR>
 *
 */
@Service
public class LegionMemberTicker extends AbstractTicker<LegionMember> {

	@Autowired
	private LegionMemberDao legionMemberDao;
	@Autowired
	private LegionService legionService;

	public LegionMemberTicker() {
		super(2 * TimeUtil.SECONDS_MILLIS);
	}

	@Override
	protected void tick(LegionMember legionMember, long now) {
		long exitCDTime = legionMember.getExitCDTime();
		if (exitCDTime > 0 && exitCDTime < now) {
			legionService.legionExitConfirm(legionMember);
		}
		long kickCDTime = legionMember.getKickCDTime();
		if (kickCDTime > 0 && kickCDTime < now) {
			legionService.legionExitConfirm(legionMember);
		}
		long transferCDTime = legionMember.getTransferCDTime();
		if (transferCDTime > 0 && transferCDTime < now) {
			legionService.legionTransferConfirm(legionMember);
		}
	}

	@Override
	protected Collection<LegionMember> findAll() {
		return legionMemberDao.findAll();
	}

}
