package com.lc.billion.icefire.kvkseason.biz.service.impl.kvk;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.config.SeasonTimeConfig;
import com.lc.billion.icefire.kvkseason.biz.model.KvkStage;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import lombok.Getter;

public class StageTime {
    public StageTime() {
    }

    public StageTime(long battleStartTime, long rewardStartTime, long matchStartTime, long matchedStartTime, long overTime) {
        this.battleStartTime = battleStartTime;
        this.rewardStartTime = rewardStartTime;
        this.matchStartTime = matchStartTime;
        this.matchedStartTime = matchedStartTime;
        this.overTime = overTime;
    }

    public String toString() {
        String str = "StageTime";
        str += " battleStart " + TimeUtil.formatTime(battleStartTime);
        str += " rewardStart " + TimeUtil.formatTime(rewardStartTime);
        str += " matchStart " + TimeUtil.formatTime(matchStartTime);
        str += " matchedStart " + TimeUtil.formatTime(matchedStartTime);
        return str;
    }

    /**
     * 下赛季没有分组消息的时候，则奖励展示为999
     * 下赛季的结束时间比本赛季的overTime要晚的时候，则结束时间要拉长
     *
     * @param battleStartTime
     * @param meta
     * @param nextSeasonConfig
     */
    public StageTime(long battleStartTime, SeasonTimeConfig.SeasonTimeMeta meta, KvkSeasonServerGroupConfig nextSeasonConfig) {
        int showReward = 999;
        this.battleStartTime = battleStartTime + meta.getOffsetTimeMinutes() * TimeUtil.MINUTE_MILLIS;
        this.rewardStartTime = battleStartTime + meta.getSeasonTime() * TimeUtil.DAY_MILLIS;
        if (null == nextSeasonConfig) {
            this.matchStartTime = rewardStartTime + showReward * TimeUtil.DAY_MILLIS;
        } else {
            this.matchStartTime = rewardStartTime + meta.getRewardExTime() * TimeUtil.DAY_MILLIS;
        }
        this.matchedStartTime = matchStartTime + meta.getMatchTime() * TimeUtil.DAY_MILLIS;
        this.overTime = matchedStartTime + meta.getMatchExTime() * TimeUtil.DAY_MILLIS;
        if (nextSeasonConfig != null) {
            this.overTime = nextSeasonConfig.getStartTime();
            this.matchedStartTime = overTime - meta.getMatchExTime() * TimeUtil.DAY_MILLIS;
            this.matchStartTime = matchedStartTime - meta.getMatchTime() * TimeUtil.DAY_MILLIS;
        }
    }

    private long battleStartTime;
    private long rewardStartTime;
    private long matchStartTime;
    /**
     * 匹配结束时间
     */
    private long matchedStartTime;

    @Getter
    private long overTime;

    public long getBattleStartTime() {
        return battleStartTime;
    }

    public void setBattleStartTime(long battleStartTime) {
        this.battleStartTime = battleStartTime;
    }

    public long getRewardStartTime() {
        return rewardStartTime;
    }

    public void setRewardStartTime(long rewardStartTime) {
        this.rewardStartTime = rewardStartTime;
    }

    public long getMatchStartTime() {
        return matchStartTime;
    }

    public void setMatchStartTime(long matchStartTime) {
        this.matchStartTime = matchStartTime;
    }

    public long getMatchedStartTime() {
        return matchedStartTime;
    }

    public void setMatchedStartTime(long matchedStartTime) {
        this.matchedStartTime = matchedStartTime;
    }

    public KvkStage getStage(long now) {
        var stage = KvkStage.BATTLE;
        if (now > overTime || now > getMatchedStartTime()) {
            stage = KvkStage.MATCHED;
        } else if (now > getMatchStartTime()) {
            stage = KvkStage.MATCHING;
        } else if (now > getRewardStartTime()) {
            stage = KvkStage.SETTLEMENT;
        } else {
            stage = KvkStage.BATTLE;
        }
        return stage;
    }

    public long getNextStageTime(KvkStage stage) {
        switch (stage) {
            case BATTLE:
                return rewardStartTime;
            case MATCHED:
                return overTime;
            case MATCHING:
                return getMatchedStartTime();
            case SETTLEMENT:
                return this.matchStartTime;
        }
        return Long.MAX_VALUE;
    }

    public long getStageTime(KvkStage stage) {
        switch (stage) {
            case BATTLE:
                return this.battleStartTime;
            case MATCHED:
                return this.matchedStartTime;
            case MATCHING:
                return matchStartTime;
            case SETTLEMENT:
                return this.rewardStartTime;
        }
        return Long.MAX_VALUE;
    }
}
