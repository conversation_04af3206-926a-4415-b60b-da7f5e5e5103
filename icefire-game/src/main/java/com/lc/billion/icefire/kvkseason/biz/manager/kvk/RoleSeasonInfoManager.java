package com.lc.billion.icefire.kvkseason.biz.manager.kvk;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.config.kvk.KVKSettingConfig;
import com.lc.billion.icefire.game.biz.manager.AbstractRoleManager;
import com.lc.billion.icefire.game.biz.manager.kvk.IMigrateForKVKManager;
import com.lc.billion.icefire.game.biz.model.player.Player;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.shieldRecord.ShieldEventType;
import com.lc.billion.icefire.game.biz.service.impl.buff.effect.ProtectionServiceImpl;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.roles.RoleSeasonInfoDao;
import com.lc.billion.icefire.kvkseason.biz.model.kvk.RoleSeasonInfo;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.ConfigCenter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class RoleSeasonInfoManager extends AbstractRoleManager implements IMigrateForKVKManager {
    @Autowired
    private RoleSeasonInfoDao roleSeasonInfoDao;
    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private ProtectionServiceImpl protectionService;
    @Autowired
    private ConfigServiceImpl configService;

    @Override
    public void onCreateRole(Role role, Player player) {
        RoleSeasonInfo roleSeasonInfo = roleSeasonInfoDao.create(role);
        roleSeasonInfo.setFirstBeAttack(true);
        roleSeasonInfo.setFirstProtected(true);
        roleSeasonInfoDao.save(roleSeasonInfo);
    }

    @Override
    protected void onCreateRoleFailed(Role role) {
        roleSeasonInfoDao.delete(role.getId());
    }

    @Override
    public void beforeLogin(Role role) {
        RoleSeasonInfo roleSeasonInfo = roleSeasonInfoDao.findById(role.getId());
        if (roleSeasonInfo == null) {
            roleSeasonInfo = roleSeasonInfoDao.create(role);
            roleSeasonInfo.setFirstBeAttack(true);
            roleSeasonInfo.setFirstProtected(true);
            roleSeasonInfoDao.save(roleSeasonInfo);
        }
    }

    @Override
    public void afterLogin(Role role) {

    }

    @Override
    public void migrateForKVKFirstLoginInKVKServer(Role role) {
        // KVK迁服没有登录过程了
        // 玩家先进入赛季服,此时是没有罩子的,飞到黑土地罩子消失,再次登录，会被判定为首次登录k服，此时会加个罩子
//        RoleSeasonInfo roleSeasonInfo = roleSeasonInfoDao.findById(role.getRoleId());
//        if (roleSeasonInfo.isFirstProtected()) {
//            KVKSettingConfig kvkSettingConfig = configService.getConfig(KVKSettingConfig.class);
//            // 保护罩，秒
//            int kvkEnterPertect = kvkSettingConfig.getKvkEnterPertect();
//            if (kvkEnterPertect > 0) {
//                protectionService.protectCity(role, kvkEnterPertect * TimeUtil.SECONDS_MILLIS, ShieldEventType.OPEN_MIGRATE_KVK);
//            }
//            roleSeasonInfo.setFirstProtected(false);
//            roleSeasonInfoDao.save(roleSeasonInfo);
//        }
    }

    @Override
    public void kvkSeasonSwitchFirstLogin(Role role) {
        RoleSeasonInfo roleSeasonInfo = roleSeasonInfoDao.findById(role.getId());
        roleSeasonInfo.setFirstBeAttack(true);
        roleSeasonInfo.setFirstProtected(true);
        roleSeasonInfoDao.save(roleSeasonInfo);
    }

    /**
     * 处理在KVK被攻击
     *
     * @param role
     */
    public void beAttackInKVK(Role role) {
        ServerType currentGameServerType = configCenter.getCurrentGameServerType();
        if (currentGameServerType != ServerType.KVK_SEASON) {
            // 不是赛季服
            return;
        }
        int currentServerId = role.getCurrentServerId();
        ServerType serverType = configCenter.getServerType(currentServerId);
        if (serverType != ServerType.KVK_SEASON) {
            // 不是K服
            return;
        }
        RoleSeasonInfo roleSeasonInfo = roleSeasonInfoDao.findById(role.getId());
        boolean firstBeAttack = roleSeasonInfo.isFirstBeAttack();
        if (!firstBeAttack) {
            return;
        }
        roleSeasonInfo.setFirstBeAttack(false);
        roleSeasonInfoDao.save(roleSeasonInfo);
        // 添加buff
        KVKSettingConfig kvkSettingConfig = configService.getConfig(KVKSettingConfig.class);
        int kvkAttacked = kvkSettingConfig.getKvkAttacked();
        if (kvkAttacked > 0) {
            long protectTime = kvkAttacked * TimeUtil.SECONDS_MILLIS;
            protectionService.protectCity(role, protectTime, ShieldEventType.OPEN_BEATTACK_KVK);
        }
    }
}
