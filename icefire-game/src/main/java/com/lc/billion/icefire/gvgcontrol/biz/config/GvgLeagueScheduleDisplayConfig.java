package com.lc.billion.icefire.gvgcontrol.biz.config;

import java.util.*;

import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.game.biz.model.gvg.GvgCupStatus;
import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.game.exception.AlertException;


/**
 * GVG杯赛赛程表
 *
 * <AUTHOR>
 * @created 2021-08-24
 */
@Config(name = "GvgLeagueScheduleDisplay", metaClass = GvgLeagueScheduleDisplayConfig.GvGLeagueScheduleDisplayMeta.class)
public class GvgLeagueScheduleDisplayConfig {
    @MetaMap
    private Map<String, GvGLeagueScheduleDisplayMeta> idMap;
    // 根据杯赛赛程状态排好序的meta数组
    private GvGLeagueScheduleDisplayMeta[] allStatusArray;

    public GvGLeagueScheduleDisplayMeta[] getAllStatusArray() {
        return allStatusArray;
    }

    public void init(List<GvGLeagueScheduleDisplayMeta> list) {
        Collections.sort(list);
        allStatusArray = list.toArray(new GvGLeagueScheduleDisplayMeta[0]);
        //任取一个阶段，用来便利warZoneId
        Map<Integer, Long[]> times = allStatusArray[0].getTimes();

        //遍历 从头至倒数第二个阶段。 最后u一个阶段不需要单独设置结束时间
        for (int i = 0; i < allStatusArray.length - 1; ++i) {
            //遍历每个warZone
            for (int warZoneId : times.keySet()) {
                // 下一阶段的开始时间，作为上一阶段的结束时间。
                allStatusArray[i].getTimes().get(warZoneId)[1] = allStatusArray[i + 1].getTimes().get(warZoneId)[0];
            }
        }
    }

    public static class GvGLeagueScheduleDisplayMeta extends AbstractMeta implements Comparable<GvGLeagueScheduleDisplayMeta> {

        private int sort;
        private GvgCupStatus status;
        //<warZoneId,startTime>
        private Map<Integer, Long[]> times;

        @Override
        public void init(JsonNode json) {
            try {
                times = new HashMap<>();
                String[] foos = json.path("value").asText().split(String.valueOf(META_SEPARATOR_1));
                for (String foo : foos) {
                    String[] bar = foo.split(String.valueOf(META_SEPARATOR_3));
                    int warZoneId = Integer.parseInt(bar[0]);
                    String[] foobar = bar[1].split("[|]");
                    Calendar calendar = Calendar.getInstance();
                    calendar.set(Calendar.YEAR, Integer.parseInt(foobar[0]));
                    calendar.set(Calendar.MONTH, Integer.parseInt(foobar[1]) - 1);
                    calendar.set(Calendar.DAY_OF_MONTH, Integer.parseInt(foobar[2]));
                    calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(foobar[3]));
                    calendar.set(Calendar.MINUTE, Integer.parseInt(foobar[4]));
                    calendar.set(Calendar.SECOND, Integer.parseInt(foobar[5]));
                    calendar.set(Calendar.MILLISECOND, 0);
                    times.put(warZoneId, new Long[]{calendar.getTimeInMillis(), Long.MAX_VALUE});
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new AlertException("gvg杯赛阶段配表错误","id",this.getId());
            }

        }

        public GvgCupStatus getStatus() {
            return status;
        }

        public void setStatus(GvgCupStatus status) {
            this.status = status;
        }

        public int getSort() {
            return sort;
        }

        public void setSort(int sort) {
            this.sort = sort;
        }

        @Override
        public int compareTo(GvGLeagueScheduleDisplayMeta o) {
            return Integer.compare(this.sort, o.sort);
        }

        public Map<Integer, Long[]> getTimes() {
            return times;
        }

        //检查warZone 在now 时，杯赛是否是本meta 所在阶段。
        public boolean isMe(int warZoneId, Long now) {
            if (!times.containsKey(warZoneId)) {
                return false;
            }
            return times.get(warZoneId)[0] <= now && now < times.get(warZoneId)[1];
        }
    }
}
