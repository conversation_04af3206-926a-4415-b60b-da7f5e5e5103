package com.lc.billion.icefire.tvtcontrol.biz.service.rpc.impl;

import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.rpc.service.tvt.IGameRemoteTvtControlService;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.lc.billion.icefire.rpc.vo.tvt.TvtBattleServerDispatchRecordVo;
import com.lc.billion.icefire.rpc.vo.tvt.TvtPlayerSignupInfoVo;
import com.lc.billion.icefire.rpc.vo.tvt.TvtPlayerSimpleInfoVo;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtBattleServerDispatchRecordDao;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtPlayerSignupInfoDao;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtQualifiedServerDao;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtBattleServerDispatchRecord;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtPlayerSignupInfo;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtQualifiedServer;
import com.lc.billion.icefire.tvtcontrol.biz.service.TvtControlService;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * game-> control RPC service 实现类
 *
 * <AUTHOR>
 */
@Service
public class GameRemoteTvtControlServiceImpl implements IGameRemoteTvtControlService {

    private static final Logger logger = LoggerFactory.getLogger(GameRemoteTvtControlServiceImpl.class);

    @Autowired
    private TvtQualifiedServerDao tvtQualifiedServerDao;
    @Autowired
    private TvtControlService tvtControlService;
    @Autowired
    private ActivityDao activityDao;
    @Autowired
    private TvtPlayerSignupInfoDao tvtPlayerSignupInfoDao;
    @Autowired
    private TvtBattleServerDispatchRecordDao tvtBattleServerDispatchRecordDao;

    @Override
    public int signup(Long roleId, int serverId, int hideScore) {
        return tvtControlService.handlePlayerSignup(roleId, serverId, hideScore);
    }

    @Override
    public void uploadPlayerSimpleInfo(TvtPlayerSimpleInfoVo vo) {
        tvtControlService.uploadPlayerSimpleInfo(vo);
    }

    @Override
    public boolean findTvtServerQualified(int serverId) {
        TvtQualifiedServer tvtQualifiedServer = tvtQualifiedServerDao.findById(Long.valueOf(serverId));
        return tvtQualifiedServer != null;
    }

    @Override
    public List<TvtBattleServerDispatchRecordVo> findTvtBattleServerDispatchRecords(Collection<Integer> serverIds) {
        List<TvtBattleServerDispatchRecordVo> res = new ArrayList<>();
        for (Integer serverId : serverIds) {
            Set<TvtBattleServerDispatchRecord> data = tvtBattleServerDispatchRecordDao.findByServerId(serverId);
            if (JavaUtils.bool(data)) {
                data.forEach(d -> res.add(new TvtBattleServerDispatchRecordVo(d)));
            }
        }
        return res;
    }

    @Override
    public ActivityVo findActivityVo() {
        Activity activity = activityDao.findActivityByActivityType(ActivityType.TVT);
        if (activity == null) {
            return null;
        }
        return new ActivityVo(activity);
    }

    @Override
    public List<TvtPlayerSignupInfoVo> findTvtPlayerSignupInfoVos(Collection<Integer> serverIds) {
        List<TvtPlayerSignupInfoVo> res = new ArrayList<>();
        for (Integer serverId : serverIds) {
            List<TvtPlayerSignupInfo> data = tvtPlayerSignupInfoDao.findDataByServerId(serverId);
            if (JavaUtils.bool(data)) {
                data.forEach(d -> res.add(new TvtPlayerSignupInfoVo(d)));
            }
        }
        return res;
    }
}
