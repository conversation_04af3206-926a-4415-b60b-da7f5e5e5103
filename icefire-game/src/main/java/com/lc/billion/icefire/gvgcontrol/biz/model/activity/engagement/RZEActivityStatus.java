package com.lc.billion.icefire.gvgcontrol.biz.model.activity.engagement;


import com.lc.billion.icefire.protocol.constant.PsRZEActivityStatus;
import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;

/**
 * gvg约战活动状态
 */
public enum RZEActivityStatus implements IntEnum {
    // 约战
    ENGAGE(PsRZEActivityStatus.ENGAGE, PsRZEActivityStatus.REST),
    // 休整
    REST(PsRZEActivityStatus.REST, PsRZEActivityStatus.ENGAGE),
    ;

    private static final RZEActivityStatus[] INDEXES = EnumUtils.toArray(values());

    PsRZEActivityStatus currentStatus;
    PsRZEActivityStatus nextStatus;

    private RZEActivityStatus(PsRZEActivityStatus current, PsRZEActivityStatus next) {
        this.currentStatus = current;
        this.nextStatus = next;
    }

    @Override
    public int getId() {
        return currentStatus.getValue();
    }

    public static RZEActivityStatus findById(int id) {
        if (id < 0 || id >= INDEXES.length) {
            return null;
        }
        return INDEXES[id];
    }

    public PsRZEActivityStatus getNextRZEActivityStatus() {
        return nextStatus;
    }
}
