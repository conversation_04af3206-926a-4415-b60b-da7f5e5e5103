package com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGQualifiedServer;

/**
 * <AUTHOR>
 *
 */
@Repository
public class GVGQualifiedServerDao extends RootDao<GVGQualifiedServer> {

	public GVGQualifiedServerDao() {
		super(GVGQualifiedServer.class, false);
	}

	@Override
	protected MongoCursor<GVGQualifiedServer> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(GVGQualifiedServer entity) {

	}

	@Override
	protected void removeMemoryIndexes(GVGQualifiedServer entity) {

	}

	public GVGQualifiedServer create(int serverId) {
		int db = Application.getServerId();
		GVGQualifiedServer gvgQualifiedServer = newEntityInstance();
		gvgQualifiedServer.setPersistKey(Long.valueOf(serverId));
		gvgQualifiedServer.setFirstJoin(true);
		return createEntity(db, gvgQualifiedServer);
	}

}
