package com.lc.billion.icefire.gvgbattle.biz.service.impl;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.biz.battle.BattleRelation;
import com.lc.billion.icefire.game.biz.battle.result.FightLostType;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.RoleServerInfoDao;
import com.lc.billion.icefire.game.biz.manager.AllianceManager;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.email.AbstractEmail;
import com.lc.billion.icefire.game.biz.model.email.RoleSimpleInfo;
import com.lc.billion.icefire.game.biz.model.email.battle.LsBattleMail;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleServerInfo;
import com.lc.billion.icefire.game.biz.model.scene.MapGrid;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.AllianceBattlePointDao;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.StrongHoldNodeDao;
import com.lc.billion.icefire.gvgbattle.biz.manager.gvg.GVGBattleDataVoManager;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.AllianceBattlePoint;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.SimpleReportData;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.StrongHoldNode;
import com.lc.billion.icefire.protocol.GcGvgObserveSettlement;
import com.lc.billion.icefire.protocol.GcObserveAllReport;
import com.lc.billion.icefire.protocol.GcObserveStrongNodeReport;
import com.lc.billion.icefire.protocol.structure.PsGVGObserveSettlement;
import com.lc.billion.icefire.protocol.structure.PsObserveSimpleReport;
import com.lc.billion.icefire.protocol.structure.PsSimpleReportAllianceInfo;
import com.lc.billion.icefire.rpc.vo.gvg.GvgBattleServerDispatchRecordVo;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * GVG OB直播视角数据的逻辑
 */
@Service
public class GVGOBService {
    private static final Logger logger = LoggerFactory.getLogger(GVGOBService.class);

    @Autowired
    private GVGBattleDataVoManager gvgDataVoManager;

    @Autowired
    private GVGStrongHoldService GVGStrongHoldService;

    @Autowired
    private RoleDao roleDao;

    @Autowired
    private AllianceServiceImpl allianceService;

    @Autowired
    private StrongHoldNodeDao strongHoldNodeDao;

    @Autowired
    private ConfigServiceImpl configService;

    @Autowired
    private AllianceManager allianceManager;

    @Autowired
    private RoleServerInfoDao roleServerInfoDao;

    @Autowired
    private AllianceBattlePointDao allianceBattlePointDao;

    @Autowired
    private WorldServiceImpl worldService;

    @Autowired
    private SceneServiceImpl sceneService;

    /**
     * 初始化OB主播的位置
     */
    public MapGrid initOBBirthPoint(Role role) {
        // 1:地图的长、宽
        int mapHeight = worldService.getMapHeight(role.getCurrentServerId());
        int mapWidth = worldService.getMapWidth(role.getCurrentServerId());

        // 2:兵工厂的半径
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        int area = gvgSettingConfig.getGvgMoveArea() * 2; // 40半径

        // 3:地图中下位置
        int minX1 = gvgSettingConfig.getGvgActivitySafeArea().getxLength() * 2 + 1;
        int maxX1 = gvgSettingConfig.getGvgActivitySafeArea().getxLength() * 2 + area * 2 - 1;
        int minY1 = 1;
        int maxY1 = (mapWidth - area * 2) / 2 - 1;

        for (int x = minX1; x <= maxX1; x++) {
            for (int y = minY1; y <= maxY1; y++) {
                MapGrid mapGrid = worldService.getGrid(role.getCurrentServerId(), x, y);
                if (mapGrid == null) {
                    continue;
                }
                if (mapGrid.isBlock()) {
                    continue;
                }
                Point position = mapGrid.getPosition();
                SceneNode sceneNode = sceneService.getSceneNode(role.getCurrentServerId(), position);
                if (sceneNode == null) {
                    return mapGrid;
                }
            }
        }


        // 4:地图中上位置
        int minX2 = gvgSettingConfig.getGvgActivitySafeArea().getxLength() * 2 + 1;
        int maxX2 = gvgSettingConfig.getGvgActivitySafeArea().getxLength() * 2 + area * 2 - 1;
        int minY2 = (mapHeight - area * 2) / 2 + area * 2 + 1;
        int maxY2 = mapWidth - 1;

        for (int x = minX2; x <= maxX2; x++) {
            for (int y = minY2; y <= maxY2; y++) {
                MapGrid mapGrid = worldService.getGrid(role.getCurrentServerId(), x, y);
                if (mapGrid == null) {
                    continue;
                }
                if (mapGrid.isBlock()) {
                    continue;
                }
                Point position = mapGrid.getPosition();
                SceneNode sceneNode = sceneService.getSceneNode(role.getCurrentServerId(), position);
                if (sceneNode == null) {
                    return mapGrid;
                }
            }
        }

        ErrorLogUtil.errorLog("观战玩家没有找到出生区域", "roleId",role.getPersistKey());
        return null;
    }

    /**
     * 批量添加战斗邮件
     */
    public void addBatchStrongHoldSimpleReport(List<AbstractEmail> mailList, Map<String, String> id2SdkMailIdMap, List<Long> gvgAttackMailIds, List<Long> gvgWinMailIds) {
        if (!JavaUtils.bool(mailList)) {
            return;
        }

        for (AbstractEmail abstractEmail : mailList) {
            if (!gvgAttackMailIds.contains(abstractEmail.getPersistKey()) && !gvgWinMailIds.contains(abstractEmail.getPersistKey())) {
                continue;
            }

            if (abstractEmail instanceof LsBattleMail) {
                LsBattleMail battleMail = (LsBattleMail) abstractEmail;
                if (!JavaUtils.bool(battleMail.getTargetMetaId())) {
                    ErrorLogUtil.errorLog("GVG战场邮件，建筑配置ID为空", "mailId",abstractEmail.getPersistKey());
                    continue;
                }

                String skdMailId = id2SdkMailIdMap.get(String.valueOf(abstractEmail.getPersistKey()));
                if (skdMailId == null) {
                    ErrorLogUtil.errorLog("GVG战场邮件，平台邮件Id为空", "mailId",abstractEmail.getPersistKey());
                    continue;
                }

                StrongHoldNode node = GVGStrongHoldService.getStrongHoldByMetaId(battleMail.getTargetMetaId());
                if (node == null) {
                    ErrorLogUtil.errorLog("GVG战场邮件，建筑对象为空", "mailId",abstractEmail.getPersistKey(),
                            "targetMetaId",battleMail.getTargetMetaId());
                    continue;
                }

                addStrongHoldSimpleReport(node, skdMailId, battleMail, gvgAttackMailIds, gvgWinMailIds);
            }
        }
    }

    /**
     * 添加据点的战报简报
     */
    public void addStrongHoldSimpleReport(StrongHoldNode node, String sdkMailId, LsBattleMail mail, List<Long> gvgAttackMailIds, List<Long> gvgWinMailIds) {
        float killPower = mail.getDefenderLostInfo().getValueAll(FightLostType.POWERLOST);
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        if (killPower < gvgSettingConfig.getGVGObBattleGuideLimitation()) {
            return;
        }

        GvgBattleServerDispatchRecordVo dispatchRecordInfo = gvgDataVoManager.getDispatchRecordInfo();
        if (dispatchRecordInfo == null) {
            ErrorLogUtil.errorLog("addStrongNodeSimpleReport dispatchRecordInfo is null");
            return;
        }

        SimpleReportData simpleReportData = new SimpleReportData();
        simpleReportData.setBattleTime(mail.getCreateTime());
        simpleReportData.setKillPower((long) killPower);
        simpleReportData.setMailId(sdkMailId);
        simpleReportData.setRoleId(mail.getRoleId());

        // 邮件类型
        if (gvgAttackMailIds.contains(mail.getPersistKey()) && !gvgWinMailIds.contains(mail.getPersistKey())) {
            // 只是建筑
            simpleReportData.setMailType(1);
            simpleReportData.setResult(mail.isWin() ? 0 : 1);

            RoleSimpleInfo attacker = mail.getAttacker();
            if (attacker != null) {
                Role attackRole = roleDao.findById(attacker.getId());
                if (attackRole != null && JavaUtils.bool(attackRole.getAllianceId())) {
                    if (attackRole.getAllianceId().equals(dispatchRecordInfo.getAllianceId1())) {
                        simpleReportData.setWinAllianceId(1);
                    } else if (attackRole.getAllianceId().equals(dispatchRecordInfo.getAllianceId2())) {
                        simpleReportData.setWinAllianceId(2);
                    }
                }
            }

        } else if (!gvgAttackMailIds.contains(mail.getPersistKey()) && gvgWinMailIds.contains(mail.getPersistKey())) {
            // 只是总览：防御者成功
            simpleReportData.setMailType(2);
            simpleReportData.setResult(0);

            RoleSimpleInfo defender = mail.getDefender();
            if (defender != null) {
                Role defendRole = roleDao.findById(defender.getId());
                if (defendRole != null && JavaUtils.bool(defendRole.getAllianceId())) {
                    if (defendRole.getAllianceId().equals(dispatchRecordInfo.getAllianceId1())) {
                        simpleReportData.setWinAllianceId(1);
                    } else if (defendRole.getAllianceId().equals(dispatchRecordInfo.getAllianceId2())) {
                        simpleReportData.setWinAllianceId(2);
                    }
                }
            }
        } else if (gvgAttackMailIds.contains(mail.getPersistKey()) && gvgWinMailIds.contains(mail.getPersistKey())) {
            // 建筑+攻击者成功
            simpleReportData.setMailType(3);
            simpleReportData.setResult(mail.isWin() ? 0 : 1);

            RoleSimpleInfo attacker = mail.getAttacker();
            if (attacker != null) {
                Role attackRole = roleDao.findById(attacker.getId());
                if (attackRole != null && JavaUtils.bool(attackRole.getAllianceId())) {
                    if (attackRole.getAllianceId().equals(dispatchRecordInfo.getAllianceId1())) {
                        simpleReportData.setWinAllianceId(1);
                    } else if (attackRole.getAllianceId().equals(dispatchRecordInfo.getAllianceId2())) {
                        simpleReportData.setWinAllianceId(2);
                    }
                }
            }
        }

        if (!mail.isAttackerSide()) {
            // 战斗类型
            simpleReportData.setType(2);

            RoleSimpleInfo defender = mail.getDefender();
            if (defender != null) {
                Role defendRole = roleDao.findById(defender.getId());
                if (defendRole != null && JavaUtils.bool(defendRole.getAllianceId())) {
                    simpleReportData.setName(defendRole.getName());
                    Alliance alliance = allianceService.getAllianceById(defendRole.getAllianceId());
                    if (alliance != null) {
                        simpleReportData.setAllianceAliasName(alliance.getAliasName());
                    }
                }
            }
        } else {
            RoleSimpleInfo attacker = mail.getAttacker();
            if (attacker != null) {
                Role attackRole = roleDao.findById(attacker.getId());
                if (attackRole != null && JavaUtils.bool(attackRole.getAllianceId())) {
                    simpleReportData.setName(attackRole.getName());
                    Alliance alliance = allianceService.getAllianceById(attackRole.getAllianceId());
                    if (alliance != null) {
                        simpleReportData.setAllianceAliasName(alliance.getAliasName());
                    }
                }
            }

            // 战斗类型
            simpleReportData.setType(mail.getArmyType() == ArmyType.RALLY_STRONGHOLD ? 1 : 0);
        }

        node.getReportDataList().add(simpleReportData);
        strongHoldNodeDao.save(node);
    }

    /**
     * 战场所有战报信息
     */
    public void sendAllSimpleReport(Role role) {
        GcObserveAllReport msg = new GcObserveAllReport();

        GvgBattleServerDispatchRecordVo dispatchRecordInfo = gvgDataVoManager.getDispatchRecordInfo();
        if (dispatchRecordInfo == null) {
            ErrorLogUtil.errorLog("sendAllSimpleReport dispatchRecordInfo is null");
            return;
        }

        Map<Long, Integer> rallyCounts = new HashMap<>();
        Map<Long, Integer> attackCounts = new HashMap<>();
        Collection<StrongHoldNode> strongHoldNodes = strongHoldNodeDao.findAll();
        if (JavaUtils.bool(strongHoldNodes)) {
            for (StrongHoldNode node : strongHoldNodes) {
                for (SimpleReportData simpleReportData : node.getReportDataList()) {
                    if (simpleReportData.getMailType() == 2 || simpleReportData.getMailType() == 3) {
                        msg.addToInfos(wrapperObserveSimpleReport(node, simpleReportData));
                    }
                }

                for (Long allianceId : node.getRallyCounts().keySet()) {
                    rallyCounts.compute(allianceId, (k, v) -> v == null ? node.getRallyCounts().get(allianceId) : v + node.getRallyCounts().get(allianceId));
                }

                for (Long allianceId : node.getAttackCounts().keySet()) {
                    attackCounts.compute(allianceId, (k, v) -> v == null ? node.getAttackCounts().get(allianceId) : v + node.getAttackCounts().get(allianceId));
                }
            }
        }

        Alliance alliance1 = allianceManager.getAllianceById(dispatchRecordInfo.getAllianceId1());
        if (alliance1 != null) {
            int rallyCount = rallyCounts.containsKey(alliance1.getPersistKey()) ? rallyCounts.get(alliance1.getPersistKey()) : 0;
            int attackCount = attackCounts.containsKey(alliance1.getPersistKey()) ? attackCounts.get(alliance1.getPersistKey()) : 0;
            msg.setAlliance1(wrapperSimpleReportAllianceInfo(alliance1, rallyCount, attackCount));
        }

        Alliance alliance2 = allianceManager.getAllianceById(dispatchRecordInfo.getAllianceId2());
        if (alliance2 != null) {
            int rallyCount = rallyCounts.containsKey(alliance2.getPersistKey()) ? rallyCounts.get(alliance2.getPersistKey()) : 0;
            int attackCount = attackCounts.containsKey(alliance2.getPersistKey()) ? attackCounts.get(alliance2.getPersistKey()) : 0;
            msg.setAlliance2(wrapperSimpleReportAllianceInfo(alliance2, rallyCount, attackCount));
        }

        role.send(msg);
    }

    /**
     * 战场某个建筑战报信息
     */
    public void sendStrongHoldSimpleReport(Role role, Long id) {
        GcObserveStrongNodeReport msg = new GcObserveStrongNodeReport();

        GvgBattleServerDispatchRecordVo dispatchRecordInfo = gvgDataVoManager.getDispatchRecordInfo();
        if (dispatchRecordInfo == null) {
            ErrorLogUtil.errorLog("sendStrongHoldSimpleReport dispatchRecordInfo is null");
            return;
        }

        StrongHoldNode strongHoldNode = strongHoldNodeDao.findById(id);
        if (strongHoldNode != null) {
            for (SimpleReportData simpleReportData : strongHoldNode.getReportDataList()) {
                if (simpleReportData.getMailType() == 1 || simpleReportData.getMailType() == 3) {
                    msg.addToInfos(wrapperObserveSimpleReport(strongHoldNode, simpleReportData));
                }
            }

            Alliance alliance1 = allianceManager.getAllianceById(dispatchRecordInfo.getAllianceId1());
            if (alliance1 != null) {
                int rallyCount = strongHoldNode.getRallyCounts().containsKey(alliance1.getPersistKey()) ? strongHoldNode.getRallyCounts().get(alliance1.getPersistKey()) : 0;
                int attackCount = strongHoldNode.getAttackCounts().containsKey(alliance1.getPersistKey()) ? strongHoldNode.getAttackCounts().get(alliance1.getPersistKey()) : 0;
                msg.setAlliance1(wrapperSimpleReportAllianceInfo(alliance1, rallyCount, attackCount));
            }

            Alliance alliance2 = allianceManager.getAllianceById(dispatchRecordInfo.getAllianceId2());
            if (alliance2 != null) {
                int rallyCount = strongHoldNode.getRallyCounts().containsKey(alliance2.getPersistKey()) ? strongHoldNode.getRallyCounts().get(alliance2.getPersistKey()) : 0;
                int attackCount = strongHoldNode.getAttackCounts().containsKey(alliance2.getPersistKey()) ? strongHoldNode.getAttackCounts().get(alliance2.getPersistKey()) : 0;
                msg.setAlliance2(wrapperSimpleReportAllianceInfo(alliance2, rallyCount, attackCount));
            }
        }

        role.send(msg);
    }

    private PsSimpleReportAllianceInfo wrapperSimpleReportAllianceInfo(Alliance alliance, int rallyCount, int attackCount) {
        PsSimpleReportAllianceInfo allianceInfo = new PsSimpleReportAllianceInfo();
        allianceInfo.setAllianceId(String.valueOf(alliance.getPersistKey()));
        allianceInfo.setName(alliance.getName());
        allianceInfo.setAliasName(alliance.getAliasName());
        allianceInfo.setFlagInfo(alliance.toPsAllianceFlagInfo());
        allianceInfo.setServerId(alliance.getoServerId());
        allianceInfo.setCountry(alliance.getCountry());
        allianceInfo.setTotalRallyCount(rallyCount);
        allianceInfo.setTotalAttackCount(attackCount);

        return allianceInfo;
    }

    private PsObserveSimpleReport wrapperObserveSimpleReport(StrongHoldNode node, SimpleReportData simpleReportData) {
        PsObserveSimpleReport simpleReport = new PsObserveSimpleReport();
        simpleReport.setSkiMailId(simpleReportData.getMailId());
        simpleReport.setType(simpleReportData.getType());
        simpleReport.setId(node.getPersistKey());
        simpleReport.setMetaId(node.getMetaId());
        simpleReport.setName(simpleReportData.getName());
        simpleReport.setAliasName(simpleReportData.getAllianceAliasName());
        simpleReport.setKillPower(simpleReportData.getKillPower());
        simpleReport.setBattleTime(simpleReportData.getBattleTime());
        simpleReport.setResult(simpleReportData.getResult());
        simpleReport.setColor(simpleReportData.getWinAllianceId());
        simpleReport.setRoleId(simpleReportData.getRoleId() == null ? 0L : simpleReportData.getRoleId());

        return simpleReport;
    }

    /**
     * 获取行军线方法
     */
    public int getObserveBattleRelation(Role role, Role armyRole) {
        RoleServerInfo roleServerInfo = roleServerInfoDao.findById(role.getPersistKey());
        if (roleServerInfo != null) {
            if (roleServerInfo.getGvgSide() == 0 || !JavaUtils.bool(armyRole.getAllianceId())) {
                return 0;
            } else if (roleServerInfo.getGvgSide() == 1) {
                GvgBattleServerDispatchRecordVo dispatchRecordInfo = gvgDataVoManager.getDispatchRecordInfo();
                if (dispatchRecordInfo == null) {
                    ErrorLogUtil.errorLog("isObservePlayer dispatchRecordInfo is null");
                    return 0;
                }

                if (dispatchRecordInfo.getAllianceId1().equals(armyRole.getAllianceId())) {
                    return BattleRelation.RED.getId();
                } else if (dispatchRecordInfo.getAllianceId2().equals(armyRole.getAllianceId())) {
                    return BattleRelation.BLUE.getId();
                }
            }
        }

        return 0;
    }

    /**
     * 获取GVG战场 OB结算信息
     */
    public void sendObserveSettlement(Role role) {
        GcGvgObserveSettlement msg = new GcGvgObserveSettlement();

        GvgBattleServerDispatchRecordVo dispatchRecordInfo = gvgDataVoManager.getDispatchRecordInfo();
        if (dispatchRecordInfo == null) {
            ErrorLogUtil.errorLog("sendObserveSettlement dispatchRecordInfo is null");
            return;
        }

        Long allianceId1 = dispatchRecordInfo.getAllianceId1();
        Long allianceId2 = dispatchRecordInfo.getAllianceId2();

        AllianceBattlePoint allianceBattlePoint1 = allianceBattlePointDao.findById(allianceId1);
        if (allianceBattlePoint1 != null) {
            msg.setObserveSettlemenInfo1(wrapperObserveSettlement(allianceBattlePoint1));
        }

        AllianceBattlePoint allianceBattlePoint2 = allianceBattlePointDao.findById(allianceId2);
        if (allianceBattlePoint2 != null) {
            msg.setObserveSettlemenInfo2(wrapperObserveSettlement(allianceBattlePoint2));
        }
        role.send(msg);
    }

    private PsGVGObserveSettlement wrapperObserveSettlement(AllianceBattlePoint allianceBattlePoint) {
        PsGVGObserveSettlement observeSettlement = new PsGVGObserveSettlement();
        observeSettlement.setAllianceId(String.valueOf(allianceBattlePoint.getPersistKey()));
        Alliance alliance = allianceService.getAllianceById(allianceBattlePoint.getPersistKey());
        if (alliance != null) {
            observeSettlement.setName(alliance.getName());
            observeSettlement.setAliasName(alliance.getAliasName());
            observeSettlement.setFlagInfo(alliance.toPsAllianceFlagInfo());
            observeSettlement.setServerId(allianceBattlePoint.getoServerId());
            observeSettlement.setCountry(alliance.getCountry());
        }

        observeSettlement.setResult(allianceBattlePoint.getResult());
        observeSettlement.setOccupyBuildCount(GVGStrongHoldService.getOccupyBuildCount(alliance.getPersistKey()));
        observeSettlement.setArmyFactoryScore(allianceBattlePoint.getArmyFactoryScore());
        observeSettlement.setTotalBuildScore(GVGStrongHoldService.getBuildScore(allianceBattlePoint));
        observeSettlement.setKillNpcScore(allianceBattlePoint.getKillNpcScore());
        observeSettlement.setKillEnemyScore(allianceBattlePoint.getKillEnemyScore());
        observeSettlement.setTotalCureScore(allianceBattlePoint.getTotalCureScore());
        observeSettlement.setGatherScore(allianceBattlePoint.getGatherScore());
        observeSettlement.setKillEnemyCount(allianceBattlePoint.getKillEnemyCount());
        observeSettlement.setTotalRallyCount(allianceBattlePoint.getTotalRallyCount());

        return observeSettlement;
    }
}
