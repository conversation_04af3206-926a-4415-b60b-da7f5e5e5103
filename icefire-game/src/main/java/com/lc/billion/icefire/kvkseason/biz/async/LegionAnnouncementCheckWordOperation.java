package com.lc.billion.icefire.kvkseason.biz.async;

import java.text.MessageFormat;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.support.HttpUtil;
import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.biz.async.asyncIO.IOConstant;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.kvkseason.biz.model.legion.Legion;
import com.lc.billion.icefire.kvkseason.biz.service.impl.legion.LegionService;
import com.lc.billion.icefire.protocol.constant.PsAnnouncementType;
import com.simfun.sgf.utils.MessageDigestUtils;

/**
 * <AUTHOR>
 * @date 2022/5/11
 */
public class LegionAnnouncementCheckWordOperation implements AsyncOperation {

	private Role role;
	private LegionService legionService;
	/** 有序索引：修改、删除、置顶时有值，新增时为空 */
	private int index;
	/** 公告：新增和修改时有值，删除、置顶时为空 **/
	private String declaration;
	/** 操作类型 **/
	private PsAnnouncementType type;
	/** 是否发布邮件和聊天 **/
	private boolean chatAndMail;
	private Legion legion;
	// 检查结果
	private int action = -1;

	public LegionAnnouncementCheckWordOperation(Role role, LegionService legionService, Legion legion, int index, String declaration, PsAnnouncementType type,
			boolean chatAndMail) {
		this.role = role;
		this.legionService = legionService;
		this.legion = legion;
		this.index = index;
		this.declaration = declaration;
		this.type = type;
		this.chatAndMail = chatAndMail;
	}

	@Override
	public boolean init() {
		return true;
	}

	@Override
	public boolean run() {

		// 删除公告和置顶公告,不进行屏蔽字检查
		if (type == PsAnnouncementType.DEL || type == PsAnnouncementType.TOP) {
			action = 0;
			return true;
		}
		String time = System.currentTimeMillis() + "";
		String md5 = MessageDigestUtils.md5(IOConstant.APP_ID + time + IOConstant.Key);
		String url = MessageFormat.format(IOConstant.URL, IOConstant.APP_ID, time, md5);
		JSONObject params = new JSONObject();
		params.put("message", declaration);
		try {
			String result = HttpUtil.postJSON(url, params.toString());
			JSONObject resultJson = JSONObject.parseObject(result);
			JSONObject resultData = JSONObject.parseObject(resultJson.getString("data"));
			if (resultData != null && resultData.containsKey("action")) {
				this.action = resultData.getInteger("action");
			}
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("LegionAnnouncementCheckWordOperation send",e,
					"url",url, "content",declaration);
		}

		return true;
	}

	@Override
	public void finish() {
		if (action == -1 || action == 2) {
			ErrorLogUtil.errorLog("LegionAnnouncementCheckWordOperation checkWord is error", "roleId",role.getId());
			return;
		}

		legionService.editAnnouncement(role, legion, index, declaration, type, chatAndMail);
	}
}
