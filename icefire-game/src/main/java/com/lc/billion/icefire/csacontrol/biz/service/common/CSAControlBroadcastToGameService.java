package com.lc.billion.icefire.csacontrol.biz.service.common;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivityContext;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivityGroupContext;
import com.lc.billion.icefire.csacontrol.biz.service.rpc.CSAControlRPCToGameProxyService;
import com.lc.billion.icefire.csacontrol.biz.util.CSAUtils;
import com.lc.billion.icefire.game.biz.async.csa.control.CSAControlBroadcastActivityInfoRpcThreadOperation;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.CSAActivityHistoryDao;
import com.lc.billion.icefire.game.biz.manager.csa.CSAActivityHistoryManager;
import com.lc.billion.icefire.game.biz.manager.csa.CSAServerBattleInfoManager;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.rpc.vo.csa.CSAActivityVo;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 *
 */
@Service
public class CSAControlBroadcastToGameService {

	private static final Logger logger = LoggerFactory.getLogger(CSAControlBroadcastToGameService.class);

	@Autowired
	private ConfigServiceImpl configService;
	@Autowired
	private CSAControlRPCToGameProxyService csaControlRPCToGameProxyService;
	@Autowired
	private AsyncOperationServiceImpl asyncOperationService;
	@Autowired
	private ActivityDao activityDao;
	@Autowired
	private CSAServerBattleInfoManager csaServerBattleInfoManager;
	@Autowired
	private CSAActivityHistoryManager csaActivityHistoryManager;
	@Autowired
	private CSAActivityHistoryDao csaActivityHistoryDao;
	@Autowired
	private CSAControlBroadcastToGameService csaControlBroadcastToGameService;

	/**
	 * 广播csa活动信息给有资格的game服
	 *
	 * 用于控制活动状态流转和同步
	 *
	 * @param triggerListener		是否触发监听事件
	 *                              状态流转需要同步控制, 所以只允许一个线程内此参数为true
	 *                              目前只允许tick线程处理监听事件
	 *
	 * @param noticeAgainstServer 	是否通知对手服
	 */
	public void broadcastCSAActivity(Activity activity, int serverId, boolean triggerListener, boolean noticeAgainstServer) {
		if (activity == null) {
			return;
		}

		CSAActivityContext csaActivityContext = activity.getActivityContext();
		CSAActivityVo csaActivityVo = new CSAActivityVo(activity);

		if (serverId != 0) {

			CSAControlBroadcastActivityInfoRpcThreadOperation csaControlBroadcastActivityInfoRpcThreadOperation = new CSAControlBroadcastActivityInfoRpcThreadOperation(serverId, triggerListener, activity, csaActivityVo, activityDao, csaControlRPCToGameProxyService, configService,
					csaServerBattleInfoManager, csaActivityHistoryManager, csaActivityHistoryDao, csaControlBroadcastToGameService);
			asyncOperationService.execute(csaControlBroadcastActivityInfoRpcThreadOperation);

			// 通知对手服
			if (noticeAgainstServer) {
				CSAActivityGroupContext csaActivityGroupContext = CSAUtils.getCsaActivityGroupContext(activity, serverId);
				if (csaActivityGroupContext.isBye()) {
					return;
				}

				int againstServerId = csaActivityGroupContext.getAgainstServerId();
				if (CSAUtils.getCsaActivityGroupContext(activity, againstServerId) == null) {
					ErrorLogUtil.errorLog("against server info is null", "activity",activity.getMetaId(),
							"serverId",serverId, "againstServerId",againstServerId);
					return;
				}
				// 广播活动信息 To对手服
				CSAControlBroadcastActivityInfoRpcThreadOperation againstCsaControlBroadcastActivityInfoRpcThreadOperation = new CSAControlBroadcastActivityInfoRpcThreadOperation(againstServerId, triggerListener, activity, csaActivityVo, activityDao, csaControlRPCToGameProxyService, configService,
						csaServerBattleInfoManager, csaActivityHistoryManager, csaActivityHistoryDao, csaControlBroadcastToGameService);
				asyncOperationService.execute(againstCsaControlBroadcastActivityInfoRpcThreadOperation);
			}

		} else {

            if (JavaUtils.bool(csaActivityContext.getServerInfoMap().keySet())) {

                csaActivityContext.getServerInfoMap().keySet().forEach(attendServer -> {

                    CSAControlBroadcastActivityInfoRpcThreadOperation csaControlBroadcastActivityInfoRpcThreadOperation = new CSAControlBroadcastActivityInfoRpcThreadOperation(attendServer, triggerListener, activity, csaActivityVo, activityDao, csaControlRPCToGameProxyService, configService,
							csaServerBattleInfoManager, csaActivityHistoryManager, csaActivityHistoryDao, csaControlBroadcastToGameService);
                    asyncOperationService.execute(csaControlBroadcastActivityInfoRpcThreadOperation);

                });
            }
		}
	}
}
