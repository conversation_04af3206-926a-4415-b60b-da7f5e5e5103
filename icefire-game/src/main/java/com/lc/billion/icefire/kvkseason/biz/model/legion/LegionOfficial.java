package com.lc.billion.icefire.kvkseason.biz.model.legion;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;

/**
 * <AUTHOR>
 *
 */
public class LegionOfficial extends AbstractEntity {

	private static final long serialVersionUID = -1027103881454343800L;

	@MongoId
	private Long id;
	private Long legionId;
	private int rank;
	// 军团长特写-0
	private int group;
	// 军团长特写-0
	private int order;

	private Long roleId;

	@Override
	public void setPersistKey(Long id) {
		this.id = id;
	}

	@Override
	public Long getPersistKey() {
		return id;
	}

	@Override
	public Long getGroupingId() {
		return legionId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCode(legionId, rank, group, order);
	}

	@Override
	public boolean equalsImpl(Object obj) {
		LegionOfficial legionOfficial = (LegionOfficial) obj;
		return equals(legionId, legionOfficial.getLegionId()) && rank == legionOfficial.getRank() && group == legionOfficial.getGroup() && order == legionOfficial.getOrder();
	}

	public Long getLegionId() {
		return legionId;
	}

	public void setLegionId(Long legionId) {
		this.legionId = legionId;
	}

	public int getRank() {
		return rank;
	}

	public void setRank(int rank) {
		this.rank = rank;
	}

	public int getGroup() {
		return group;
	}

	public void setGroup(int group) {
		this.group = group;
	}

	public int getOrder() {
		return order;
	}

	public void setOrder(int order) {
		this.order = order;
	}

	public Long getRoleId() {
		return roleId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

}
