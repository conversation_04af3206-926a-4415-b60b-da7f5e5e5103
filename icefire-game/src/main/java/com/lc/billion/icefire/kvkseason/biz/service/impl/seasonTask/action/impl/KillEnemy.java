package com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.SoldierConfig;
import com.lc.billion.icefire.game.biz.config.kvk.SeasonTaskConfig;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.RoleSeasonTaskRecord;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.AbstractSeasonTaskAction;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.SeasonTaskActionType;
import com.longtech.ls.config.ServerType;
import com.simfun.sgf.utils.JavaUtils;

/**
 * 玩家个人在狂野之地重伤/击杀X个Y级及以上的其他服务器玩家的战斗单位
 * 
 * <AUTHOR>
 * @date 2021/11/17
 */
@Service
public class KillEnemy extends AbstractSeasonTaskAction {
	@Override
	public SeasonTaskActionType getType() {
		return SeasonTaskActionType.KILL_ENEMY;
	}

	@Override
	public long getProgress(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		RoleSeasonTaskRecord record = taskRecordDao.getRecordByRoleAndMetaId(role, meta.getId());
		if (record == null) {
			return 0;
		}
		return record.getProgress(getType().getId());
	}

	@Override
	protected void updateProgress(Role role, RoleSeasonTaskRecord taskRecord, Object... params) {
		if (Application.getConfigCenter().getServerType(role.getCurrentServerId()) != ServerType.KVK_SEASON) {
			return;// 非赛季服 K服，直接返回不处理
		}
		Map<String, Integer> killDetail = (HashMap<String, Integer>) params[0];
		if (!JavaUtils.bool(killDetail)) {
			return;
		}
		//
		//
		SeasonTaskConfig.SeasonTaskMeta meta = getMeta(taskRecord.getMetaId());
		if (meta == null) {
			return;
		}
		if (taskRecord.isFinish()) {
			return;
		}
		int arrIndex = meta.getConditions().indexOf(getType().getId());
		int parma1 = meta.getParam1s().get(arrIndex);
		int parma2 = meta.getParam2s().get(arrIndex);
		for (Map.Entry<String, Integer> entry : killDetail.entrySet()) {
			SoldierConfig.SoldierMeta soldierMeta = configService.getConfig(SoldierConfig.class).get(entry.getKey());
			if (soldierMeta == null)
				continue;
			long currentProgress = getProgress(role, meta);
			if (currentProgress >= parma1) {
				continue;
			}
			if (soldierMeta.getEra() >= parma2) {
				taskRecord.updateProgress(getType().getId(), currentProgress + entry.getValue() > parma1 ? parma1 : currentProgress + entry.getValue());
				taskRecordDao.save(taskRecord);
			}
		}

	}

	@Override
	public boolean actionFinishCheck(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		int i = meta.getConditions().indexOf(getType().getId());
		int param1 = meta.getParam1s().get(i);
		return getProgress(role, meta) >= param1;
	}
}
