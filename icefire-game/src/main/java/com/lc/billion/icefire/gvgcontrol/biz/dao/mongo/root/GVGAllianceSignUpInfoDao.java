package com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root;

import java.util.*;

import com.simfun.sgf.utils.JavaUtils;
import lombok.Getter;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGAllianceSignUpInfo;

/**
 * <AUTHOR>
 * 
 */
@Repository
public class GVGAllianceSignUpInfoDao extends RootDao<GVGAllianceSignUpInfo> {

	private final Map<Integer, List<GVGAllianceSignUpInfo>> dataByServerId = new MyConcurrentMap<>();
	@Getter
    private final Map<Long, List<GVGAllianceSignUpInfo>> dataByAllianceId = new MyConcurrentMap<>();
	@Getter
    private final Map<Long, Map<GvgMatchType, GVGAllianceSignUpInfo>> dataByAllianceIdAndMatchType = new MyConcurrentMap<>();

	public GVGAllianceSignUpInfoDao() {
		super(GVGAllianceSignUpInfo.class, true);
	}

	@Override
	protected MongoCursor<GVGAllianceSignUpInfo> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(GVGAllianceSignUpInfo entity) {
		//老数据， 没有这个字段
		if (entity.getMatchType() == null) {
			entity.setMatchType(GvgMatchType.FRIENDLY);
		}
		dataByServerId.compute(entity.getServerId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
		dataByAllianceId.compute(entity.getAllianceId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
		dataByAllianceIdAndMatchType.compute(entity.getAllianceId(), (k, v) -> v == null ? new MyConcurrentMap<>() : v).put(entity.getMatchType(), entity);
	}

	@Override
	protected void removeMemoryIndexes(GVGAllianceSignUpInfo entity) {
		List<GVGAllianceSignUpInfo> set = dataByServerId.get(entity.getServerId());
		if (set != null) {
			set.remove(entity);
		}

		List<GVGAllianceSignUpInfo> set2 = dataByAllianceId.get(entity.getAllianceId());
		if (set2 != null) {
			set2.remove(entity);
		}

		Map<GvgMatchType, GVGAllianceSignUpInfo> map = dataByAllianceIdAndMatchType.get(entity.getAllianceId());
		if (map != null) {
			map.remove(entity.getMatchType());
		}

	}


	public GVGAllianceSignUpInfo create(Long allianceId, int serverId, GvgMatchType matchType,int warZoneId) {
		return create(allianceId, serverId, matchType, 0,0,warZoneId);
	}

	public GVGAllianceSignUpInfo create(Long allianceId, int serverId, GvgMatchType matchType, int gameRound,int round,int warZoneId) {
		int db = Application.getServerId();
		GVGAllianceSignUpInfo gvgAllianceSignUpInfo = newEntityInstance();
		gvgAllianceSignUpInfo.setAllianceId(allianceId);
		gvgAllianceSignUpInfo.setServerId(serverId);
		gvgAllianceSignUpInfo.setMatchType(matchType);
		gvgAllianceSignUpInfo.setGameRound(gameRound);
		gvgAllianceSignUpInfo.setRound(round);
		gvgAllianceSignUpInfo.setWarZoneId(warZoneId);
		return createEntity(db, gvgAllianceSignUpInfo);
	}

	public List<GVGAllianceSignUpInfo> findByServerId(int gameServerId) {
		return dataByServerId.get(gameServerId);
	}

    public Map<Integer, List<GVGAllianceSignUpInfo>> findByServerId() {
		return dataByServerId;
	}

	public void deleteAll() {
		delete(findAll());
	}

	public GVGAllianceSignUpInfo findByMatchType(GvgMatchType matchType, Long allianceId) {

		if (allianceId == null) {
			return null;
		}

		List<GVGAllianceSignUpInfo> all = getDataByAllianceId().get(allianceId);
		if (all != null && !all.isEmpty()) {
			for (GVGAllianceSignUpInfo one : all) {
				if (one.getMatchType() == matchType) {
					return one;
				}
			}
		}
		return null;
	}

    public GVGAllianceSignUpInfo findByRole(Long allianceId, Long roleId){
		var infoMap = dataByAllianceIdAndMatchType.get(allianceId);
		if (infoMap == null) {
			return null;
		}

		for(GVGAllianceSignUpInfo info : infoMap.values()){
			if(info.getGvgAllianceLineUpInfo().containsRole(roleId)){
				return info;
			}
		}

		return null;
	}

	public GVGAllianceSignUpInfo findByAllianceIdAndType(Long allianceId, GvgMatchType type){
		if (!JavaUtils.bool(allianceId)) {
			return null;
		}
		var infoMap = dataByAllianceIdAndMatchType.get(allianceId);
		if (infoMap == null) {
			return null;
		}

		return infoMap.get(type);
	}
}
