package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.processor;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.graph.Position;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.BizTime;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.ArmyDao;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.alliance.war.AllianceWar;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.MapRoute;
import com.lc.billion.icefire.game.biz.model.scene.MoveContext;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.login.LoginServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.notice.NoticeConstants;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import com.lc.billion.icefire.protocol.CgArmySetout;
import com.lc.billion.icefire.protocol.constant.PsAllianceWarInfoUpdateReason;
import com.lc.billion.icefire.protocol.structure.PsArmyProgress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/26
 */
@Service
public class GVGJoinRallyProcessor extends AbstractGvgArmyProcessor {
	@Autowired
	private ArmyDao armyDao;
	@Autowired
	private ArmyServiceImpl armyService;
	@Autowired
	private GVGStrongHoldService gvgStrongHoldService;

	@Override
	public ArmyType getArmyType() {
		return ArmyType.GVG_JOIN;
	}

	@Override
	protected boolean check(Role role, SceneNode targetNode, ArmySetoutParam param) {
		logger.info("[GVG]check, role:{}, targetNode:{}, armyType:{}", role.getId(), targetNode.getPersistKey(), param.getArmyType());
		var rallyArmyId = param.getMassId();
		AllianceWar war = allianceWarDao.findById(rallyArmyId);
		if (war == null) {
			ErrorLogUtil.errorLog("[GVG]check war is null", "roleId",role.getId(), "targetNode",targetNode.getPersistKey(),
					"armyType",param.getArmyType());
			return false;
		}

		ArmyInfo rallyArmy = armyManager.findById(war.getArmyId());
		if (rallyArmy == null) {
			ErrorLogUtil.errorLog("[GVG]check rallyArmy is null", "roleId",role.getId(), "targetNode",targetNode.getPersistKey(),
					"armyType",param.getArmyType(), "armyId",war.getArmyId());
			return false;
		}


		long totalSoldierCount = rallyArmy.getSoldierCount();
		for (var value : param.getArmySoldiers().values()) {
			totalSoldierCount += value.getCount();
		}

		// 已经在集结中了
		List<Long> joinArmyIdList = rallyArmy.getRallyContext().getJoinArmyIdList();
		for (Long joinArmyId : joinArmyIdList) {
			ArmyInfo joinArmy = armyManager.findById(joinArmyId);
			totalSoldierCount += joinArmy.getSoldierCount();
			if (joinArmy.getRoleId().equals(role.getPersistKey())) {
				ErrorLogUtil.errorLog("[GVG]gvgJoinCommonCheck already in rally",
						"roleId",role.getId(), "targetNode",targetNode.getPersistKey(), "rallyArmyId",rallyArmy.getPersistKey(),
						"rallyArmyType",rallyArmy.getArmyType(), "rallyWorkType",rallyArmy.getWorkType(),
						"allianceId",role.getAllianceId(), "targetNodeId",rallyArmy.getTargetNodeId(),
						"attackerAllianceId",war.getAttackerAllianceId(), "leaderRoleId",rallyArmy.getRoleId());
				return false;
			}
		}

		if (rallyArmy.getRallyContext().getJoinArmyIdList().size() + 1 >= rallyArmy.getRallyContext().getMaxRallyNum()) {
			noticeService.notice(role, NoticeConstants.JOIN_RALLY_FAIL_ARMY_NUM_LIMIT, true);
			return false;
		}
		if (totalSoldierCount > rallyArmy.getRallyContext().getMaxRallySoldierNum()) {
			noticeService.notice(role, NoticeConstants.JOIN_RALLY_FAIL_SOLDIER_NUM_LIMIT, true);
			return false;
		}

		if(!gvgStrongHoldService.gvgJoinCommonCheck(role, targetNode, rallyArmy, war)){
			ErrorLogUtil.errorLog("[GVG]check gvgJoinCommonCheck fail",
					"roleId",role.getId(), "targetNode",targetNode.getPersistKey(), "armyType",param.getArmyType(),
					"armyId",war.getArmyId());
			return false;
		}

		return true;
	}

	private void commonRecall(ArmyInfo army){
		if(army.getRallyContext() == null) {
			ErrorLogUtil.errorLog("[GVG]commonRecall army context null", "armyId",army.getPersistKey());
			return;
		}
		// 从车头部队中删除自己
		Long leaderArmyId = army.getRallyContext().getLeaderArmyId();
		ArmyInfo leaderArmy = armyManager.findById(leaderArmyId);
		if(leaderArmy == null) {
			ErrorLogUtil.errorLog("[GVG]commonRecall leaderArmy null",  "armyId",army.getPersistKey(), "leaderArmyId",leaderArmyId);
			return;
		}

		if(leaderArmy.getRallyContext() == null) {
			ErrorLogUtil.errorLog("[GVG]commonRecall leaderArmy context null",  "armyId",army.getPersistKey(), "leaderArmyId",leaderArmyId);
			return;
		}

		leaderArmy.getRallyContext().joinerLeave(army.getPersistKey());

		// 删除后，如果车头还没有出发，则检查是否应该出发
		if (leaderArmy.getWorkType() == ArmyWorkType.RALLYING) {
			logger.info("[GVG]commonRecall checkRallySetout, army: {}, leaderArmy: {}", army.getPersistKey(), leaderArmyId);
			warService.checkRallySetout(leaderArmyId);
		}

		// 车头部队在集结中或路上，没有被召回(doRecall或recallArmyImmediately)
		if (leaderArmy.getWorkType() == ArmyWorkType.RALLYING || leaderArmy.getWorkType() == ArmyWorkType.SETOUT) {
			if (!leaderArmy.getRallyContext().isRallyArmyOver()) {
				warService.broadcastUpdate(allianceWarDao.getWarByArmyId(leaderArmyId), PsAllianceWarInfoUpdateReason.JOIN_RECALL);
			}
		}
	}

	@Override
	protected void doRecall(ArmyInfo army) {
		logger.info("[GVG]doRecall, army: {}", army.getPersistKey());
		commonRecall(army);
	}

	@Override
	public void recallArmyImmediately(ArmyInfo army) {
		// leaderArmy有可能为空。比如A参与B发起的集结行军
		// 1：在停服召回所有玩家的行军的时候，先处理B发起的行军。B行军秒回。当处理A的时候，这个leaderArmy就是空
		// 2：启动服务器召回玩家行军时候，同上
		logger.info("[GVG]recallArmyImmediately, army: {}", army.getPersistKey());
		commonRecall(army);
		armyManager.takeBackArmy(army);
	}

	@Override
	protected void start(ArmyInfo army) {
		logger.info("[GVG]start, army: {}", army.getPersistKey());
		armyService.addArmyToAoi(army, addToAoi());

		if(army.getRallyContext() == null) {
			ErrorLogUtil.errorLog("[GVG]start army context null",  "armyId",army.getPersistKey());
			return;
		}

		// 更新联盟战争
		ArmyInfo leaderArmy = armyManager.findById(army.getRallyContext().getLeaderArmyId());
		if(leaderArmy == null){
			ErrorLogUtil.errorLog("[GVG]start leaderArmy null",  "armyId",army.getPersistKey(), "leaderArmyId",army.getRallyContext().getLeaderArmyId());
			return;
		}

		if(leaderArmy.getRallyContext() == null){
			ErrorLogUtil.errorLog("[GVG]start leaderArmy context null",  "armyId",army.getPersistKey(), "leaderArmyId",army.getRallyContext().getLeaderArmyId());
			return;
		}

		leaderArmy.getRallyContext().joinerAdd(army.getPersistKey());
		armyManager.saveArmy(leaderArmy);

		var war = allianceWarDao.getWarByArmyId(leaderArmy.getPersistKey());
		if(war == null) {
			ErrorLogUtil.errorLog("[GVG]start war null",  "armyId",army.getPersistKey(), "leaderArmyId",army.getRallyContext().getLeaderArmyId());
			return;
		}

		warService.broadcastUpdate(war, PsAllianceWarInfoUpdateReason.JOIN_ADD);
	}

	@Override
	protected void prepareArmyReturn(ArmyInfo army) {
		// 做一下容错
		if(army.getRallyContext() == null) {
			ErrorLogUtil.errorLog("[GVG]prepareArmyReturn rallyContext null",  "armyId", army.getPersistKey(), "roleId", army.getRoleId());
			super.prepareArmyReturn(army);
			return;
		}

		ArmyInfo leaderArmy = armyManager.findById(army.getRallyContext().getLeaderArmyId());
		if (leaderArmy == null || leaderArmy.getWorkType() == ArmyWorkType.RALLYING) {
			logger.info("[GVG]prepareArmyReturn rallying, army: {}, leaderArmy: {}, leaderArmyNull: {}", army.getPersistKey(), army.getRallyContext().getLeaderArmyId(), leaderArmy == null);
			// leaderArmy有可能为null
			// 参与集结 ，在doRecall里面触发了 rallySetOut，但是失败了，集结直接解散。leaderArmy就不存在了
			// 情况：1: 集结世界boss A参加，进入等待阶段。 队长踢走这个还在路上的参与者A，在doRecall中
			// 调用了rallSetOut，由于集结人数不足，集结队伍解散了，清理了相关信息
			super.prepareArmyReturn(army);
		} else {
			logger.info("[GVG]prepareArmyReturn setout, army: {}, leaderArmy: {}", army.getPersistKey(), army.getRallyContext().getLeaderArmyId());

			// 将army跟集结终点位置挂钩，并与车头城市位置脱钩
			Position currentDoublePos = leaderArmy.getPos();
			Point currentIntPoint = Point.toCeilPoint(currentDoublePos);
			army.setEndX(currentIntPoint.getX());
			army.setEndY(currentIntPoint.getY());
			armyDao.updateArmyTargetIndex(army.getCurrentServerId(), Point.getInstance(army.getEndX(), army.getEndY()), army);

			// 修改终点位置，并调用基类方法来回城
			army.setPos(currentDoublePos);
			army.getMapRoute().setEnd(currentIntPoint);
			super.prepareArmyReturn(army);
		}
	}

	@Override
	protected PsArmyProgress toArmyProgressInfo(ArmyInfo army, PsArmyProgress info) {
		if (army.getWorkType() == ArmyWorkType.RALLYING) {
			if(army.getRallyContext() == null) {
				ErrorLogUtil.errorLog("[GVG]toArmyProgressInfo rallyContext null",  "armyId", army.getPersistKey(), "armyRoleId", army.getRoleId());
				return info;
			}

			// 如果在集结中，同步 集结车头的行军 时间进度
			ArmyInfo leaderArmy = armyManager.findById(army.getRallyContext().getLeaderArmyId());
			if(leaderArmy == null) {
				ErrorLogUtil.errorLog("[GVG]toArmyProgressInfo leaderArmy null",  "armyId",army.getPersistKey(),
						"armyRoleId",army.getRoleId(), "leaderArmyId",army.getRallyContext().getLeaderArmyId());
				return info;
			}
			long now = BizTime.now();
			info.setStartTime(leaderArmy.getStartWorkTime());
			info.setUpdateTime(now);
			info.setRemainTime(leaderArmy.getStartWorkTime() + leaderArmy.getTotalWorkTime() - now);
			info.setOriginalTotalTime(leaderArmy.getOriginalTotalWorkTime());
			info.setSpeedRatio(leaderArmy.getSpeedup());

			if (leaderArmy.getWorkType() == ArmyWorkType.SETOUT) {
				info.setStartX(leaderArmy.getMapRoute().getStart().getX());
				info.setStartY(leaderArmy.getMapRoute().getStart().getY());
				info.setEndX(leaderArmy.getMapRoute().getEnd().getX());
				info.setEndY(leaderArmy.getMapRoute().getEnd().getY());
				info.setRallyArmyWorkType(leaderArmy.getWorkType().getPsWorkType());
			} else if (leaderArmy.getWorkType() == ArmyWorkType.RALLYING) {
				info.setStartX(leaderArmy.getStartX());
				info.setStartY(leaderArmy.getStartY());
				info.setEndX(leaderArmy.getEndX());
				info.setEndY(leaderArmy.getEndY());
			}
		}
		ArmyInfo leaderArmy = armyManager.findById(army.getRallyContext().getLeaderArmyId());
		if (leaderArmy != null) {
			info.setTargetNodeType(leaderArmy.getTargetNodeType().getPsEnum());
		}
		return info;
	}

	@Override
	protected Map<String, Object> wrapperBILogParam(ArmyInfo army) {
		Map<String, Object> param = super.wrapperBILogParam(army);
		param.put("leaderMarchId", army.getRallyContext().getLeaderArmyId().toString());
		Long leaderArmyId = army.getRallyContext().getLeaderArmyId();
		ArmyInfo leaderArmy = armyManager.findById(leaderArmyId);
		param.put("joinRallyType", leaderArmy.getArmyType().name());
		param.put("rallyTargetConfigId", leaderArmy.getOriginMetaId() == null ? "" : leaderArmy.getOriginMetaId());
		return param;
	}

	@Override
	public boolean recallFreeCheck(ArmyInfo army) {
		logger.info("[GVG]recallFreeCheck, army: {}, workType: {}", army.getPersistKey(), army.getWorkType());
		if (army.getWorkType() == ArmyWorkType.RALLYING) {
			// 本人已到达，且集结发起者还没有出发
			ArmyInfo leaderArmy = armyManager.findById(army.getRallyContext().getLeaderArmyId());
			return leaderArmy.getWorkType() == ArmyWorkType.RALLYING;
		}
		return false;
	}

	@Override
	public boolean clientParamValidate(CgArmySetout message) {
		if(!super.clientParamValidate(message)){
			ErrorLogUtil.errorLog("[GVG]clientParamValidate: fail", "rallyId",message.getRallyId(), "setOutType",message.getSetoutType());
			return false;
		}

		if (!message.isSetRallyId()) {
			ErrorLogUtil.errorLog("[GVG]clientParamValidate: not set rallyId", "rallyId",message.getRallyId(), "setOutType",message.getSetoutType());
			return false;
		}

		AllianceWar war = allianceWarDao.findById(message.getRallyId());
		if(war == null) {
			ErrorLogUtil.errorLog("[GVG]clientParamValidate: war null", "rallyId",message.getRallyId(), "setOutType",message.getSetoutType());
			return false;
		}

		return true;
	}
}
