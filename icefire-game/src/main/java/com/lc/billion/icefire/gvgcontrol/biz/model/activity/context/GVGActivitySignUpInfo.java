package com.lc.billion.icefire.gvgcontrol.biz.model.activity.context;

import com.lc.billion.icefire.protocol.structure.PsCreateRoomTime;
import com.lc.billion.icefire.protocol.structure.PsGVGSignUpTime;

/**
 * <AUTHOR>
 *
 */
public class GVGActivitySignUpInfo {

	// 索引
	private int index;
	// 报名时间
	private long signUpTime;
	// 当前多少联盟：该时间段当前报名联盟数
	private int signUpNum = 0;
	// 最大多少联盟
	private int signUpMax;

	public PsGVGSignUpTime toPsGVGSignUpTime() {
		PsGVGSignUpTime psGVGSignUpTime = new PsGVGSignUpTime();
		psGVGSignUpTime.setIndex(index);
		psGVGSignUpTime.setSignUpTime(signUpTime);
		psGVGSignUpTime.setCurrentNum(signUpNum);
		psGVGSignUpTime.setMaxNum(signUpMax);
		return psGVGSignUpTime;
	}

	public PsCreateRoomTime toPsCreateRoomTime(){
		PsCreateRoomTime psCreateRoomTime = new PsCreateRoomTime();
		psCreateRoomTime.setIndex(index);
		psCreateRoomTime.setSignUpTime(signUpTime);
		psCreateRoomTime.setCurrentNum(signUpNum);
		psCreateRoomTime.setMaxNum(signUpMax);
		return psCreateRoomTime;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public long getSignUpTime() {
		return signUpTime;
	}

	public void setSignUpTime(long signUpTime) {
		this.signUpTime = signUpTime;
	}

	public int getSignUpNum() {
		return signUpNum;
	}

	public void setSignUpNum(int signUpNum) {
		this.signUpNum = signUpNum;
	}

	public int getSignUpMax() {
		return signUpMax;
	}

	public void setSignUpMax(int signUpMax) {
		this.signUpMax = signUpMax;
	}

	public boolean hasPosition() {
		return signUpNum < signUpMax;
	}

}
