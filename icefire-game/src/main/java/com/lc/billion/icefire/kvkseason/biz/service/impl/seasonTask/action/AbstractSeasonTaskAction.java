package com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.kvk.SeasonTaskConfig;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.alliances.AllianceSeasonTaskProgressDao;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.roles.RoleSeasonTaskRecordDao;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionSeasonTaskProgressDao;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.ServerSeasonTaskProgressDao;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.RoleSeasonTaskRecord;
import com.lc.billion.icefire.kvkseason.biz.service.impl.legion.LegionService;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.SeasonTaskService;
import com.lc.billion.icefire.protocol.GcSeasonTaskList;
import com.longtech.ls.config.ServerType;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 * 赛季任务，更新进度的行为,1个任务，策划可以配置多个行为
 * 
 * <AUTHOR>
 * @date 2021/11/15
 */
@Service
public abstract class AbstractSeasonTaskAction {

	private static final Logger logger = LoggerFactory.getLogger(AbstractSeasonTaskAction.class);

	@Autowired
	protected AllianceSeasonTaskProgressDao allianceSeasonTaskProgressDao;
	@Autowired
	protected ServerSeasonTaskProgressDao serverSeasonTaskProgressDao;
	@Autowired
	protected RoleSeasonTaskRecordDao taskRecordDao;
	@Autowired
	protected SeasonTaskService seasonTaskService;
	@Autowired
	protected ConfigServiceImpl configService;
	@Autowired
	protected AllianceServiceImpl allianceService;
	@Autowired
	protected LegionSeasonTaskProgressDao legionSeasonTaskProgressDao;
	@Autowired
	protected LegionService legionService;
	@Autowired
	protected ServiceDependency sdp;

	/**
	 * 获取对应的行为类型
	 * 
	 * @return
	 */
	public abstract SeasonTaskActionType getType();

	/**
	 * 获取进度
	 * 
	 * @param role
	 * @param meta
	 * @return
	 */
	public abstract long getProgress(Role role, SeasonTaskConfig.SeasonTaskMeta meta);

	/**
	 * 更新进度
	 * 
	 * @param role
	 *            非个人相关的，可能为null
	 * @param taskRecord
	 *            个人相关的，不为空，非个人的，一定为空
	 * @param params
	 * @return
	 */
	protected abstract void updateProgress(Role role, RoleSeasonTaskRecord taskRecord, Object... params);

	public void update(Role role, Object... params) {
		if (Application.getServerType() != ServerType.KVK_SEASON) {// 非赛季服不处理
			return;
		}
		if (getType().getTaskType() == SeasonTaskType.PERSONAL) {
			updatePersonalTask(role, params);
		} else if (getType().getTaskType() == SeasonTaskType.ALLIANCE) {
			updateAllianceTask(role, params);
		} else if (getType().getTaskType() == SeasonTaskType.SERVER) {
			updateServerTask(role, params);
		} else if (getType().getTaskType() == SeasonTaskType.LEGION) {
			updateLegionTask(role, params);
		}
	}

	/**
	 * 这次触发是个人类型， 找到这个action相关的所有任务，更新触发
	 * 
	 * @param role
	 * @param params
	 */
	private void updatePersonalTask(Role role, Object... params) {
		// 找到这个action类型，相关的所有任务记录
		List<RoleSeasonTaskRecord> records = taskRecordDao.getRecordByRoleAndActionType(role, getType().getId());
		if (!JavaUtils.bool(records)) {
			return;
		}
		GcSeasonTaskList gcMsg = new GcSeasonTaskList();
		//
		for (RoleSeasonTaskRecord record : records) {
			updateProgress(role, record, params);
			// 如果当前action是个人类型， 且这个任务的所有conditions都是个人类型，检测是否完成并推送
			if (record.isFinish() || record.isReward() || record.isExpire()) {
				continue;
			}
			SeasonTaskConfig.SeasonTaskMeta meta = getMeta(record.getMetaId());
			if (meta == null) {
				ErrorLogUtil.errorLog("找不到赛季任务配置", "metaId",record.getMetaId());
				continue;
			}
			if (meta.getSettlementType() == 1) { // 结算类型 限时的。不需要检测 不需要推送
				continue;
			}
			boolean needCheckFinish = true;
			for (Integer condition : record.getConditions()) {
				if (SeasonTaskActionType.findById(condition).getTaskType() != SeasonTaskType.PERSONAL) {
					needCheckFinish = false;
					break;
				}
			}
			if (needCheckFinish) {
				boolean finish = seasonTaskService.taskFinishCheck(role, meta);
				if (finish) {
					gcMsg.addToRecords(seasonTaskService.toPsInfo(role, meta));
				}
			}

		}
		// 推送
		if (JavaUtils.bool(gcMsg.getRecords())) {
			role.send(gcMsg);
		}
	}

	/**
	 * 这次触发是联盟类型， 更新AllianceSeasonTaskProgress
	 * 
	 * @param role
	 * @param params
	 */
	private void updateAllianceTask(Role role, Object... params) {
		updateProgress(role, null, params);
	}

	/**
	 * 这次触发是服务器类型， 更新ServerSeasonTaskProgress
	 * 
	 * @param role
	 * @param params
	 */
	private void updateServerTask(Role role, Object... params) {
		updateProgress(role, null, params);
	}

	/**
	 * 这次触发是服务器类型， 更新LegionSeasonTaskProgress
	 *
	 * @param role
	 * @param params
	 */
	private void updateLegionTask(Role role, Object... params) {
		updateProgress(role, null, params);
	}

	/**
	 * 这个类型的进度是否完成
	 * 
	 * @param role
	 * @param meta
	 * @return
	 */
	public abstract boolean actionFinishCheck(Role role, SeasonTaskConfig.SeasonTaskMeta meta);

	protected SeasonTaskConfig.SeasonTaskMeta getMeta(String metaId) {
		return configService.getConfig(SeasonTaskConfig.class).getMetaMap().get(metaId);
	}

}
