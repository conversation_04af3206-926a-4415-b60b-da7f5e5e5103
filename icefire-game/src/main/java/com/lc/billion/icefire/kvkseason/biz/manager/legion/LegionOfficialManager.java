package com.lc.billion.icefire.kvkseason.biz.manager.legion;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.config.legion.LegionOfficialConfig;
import com.lc.billion.icefire.game.biz.config.legion.LegionOfficialConfig.LegionOfficialMeta;
import com.lc.billion.icefire.game.biz.config.legion.LegionRankConfig;
import com.lc.billion.icefire.game.biz.manager.IRoleAllianceManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.role.AbstractRole;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionOfficialDao;
import com.lc.billion.icefire.kvkseason.biz.model.legion.Legion;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionOfficial;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionRankAuthEnum;
import com.lc.billion.icefire.kvkseason.biz.model.legion.log.LegionLogType;
import com.lc.billion.icefire.kvkseason.biz.service.impl.legion.LegionConstants;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 *
 */
@Component
public class LegionOfficialManager implements IRoleAllianceManager {

	private static final Logger log = LoggerFactory.getLogger(LegionOfficialManager.class);

	private static final int LEGION_OFFICIAL_LEADER_GROUP = 0;
	private static final int LEGION_OFFICIAL_LEADER_ORDER = 1;

	@Autowired
	private LegionOfficialDao legionOfficialDao;
	@Autowired
	private ConfigServiceImpl configService;
	@Autowired
	private LegionManager legionManager;
	@Autowired
	private RoleManager roleManager;
	@Autowired
	private WorldServiceImpl worldService;

	public void initLegionOfficial(Legion legion) {
		// 特殊处理军团长
		legionOfficialDao.create(legion.getDB(), legion.getId(), LegionConstants.RANK_10, LEGION_OFFICIAL_LEADER_GROUP, LEGION_OFFICIAL_LEADER_ORDER);
		// 初始化其他官员位置
		LegionOfficialConfig legionOfficialConfig = configService.getConfig(LegionOfficialConfig.class);
		List<LegionOfficialMeta> metaList = legionOfficialConfig.getList();
		for (LegionOfficialMeta legionOfficialMeta : metaList) {
			legionOfficialDao.create(legion.getDB(), legion.getId(), legionOfficialMeta.getRank(), legionOfficialMeta.getGroup(), legionOfficialMeta.getOrder());
		}
	}

	public List<LegionOfficial> findByLegionId(Long legionId) {
		return legionOfficialDao.findByLegionId(legionId);
	}

	public void updateLegionOfficialForLeader(Long legionId, Long roleId) {
		LegionOfficial legionOfficial = getLegionOfficial(legionId, LEGION_OFFICIAL_LEADER_GROUP, LEGION_OFFICIAL_LEADER_ORDER);
		updateLegionOfficial(legionOfficial, roleId);

	}

	/**
	 * @param legionOfficial
	 * @param roleId
	 * @return 0-升，1-降，2-释放，3-不变
	 */
	public LegionLogType updateLegionOfficial(LegionOfficial legionOfficial, Long roleId) {
		LegionLogType ret = LegionLogType.OFFICE_NEW;
		if (!JavaUtils.bool(roleId)) {
			legionOfficial.setRoleId(null);
			log.info("玩家释放联盟官衔，{}", legionOfficial);
			ret = LegionLogType.OFFICE_NO;
		} else {
			// roleId老的位置空出来
			List<LegionOfficial> legionOfficials = legionOfficialDao.findByLegionId(legionOfficial.getLegionId());
			if (JavaUtils.bool(legionOfficials)) {
				for (LegionOfficial legionOfficial2 : legionOfficials) {
					if (legionOfficial2.getRoleId() != null && legionOfficial2.getRoleId().longValue() == roleId.longValue()) {
						legionOfficial2.setRoleId(null);
						legionOfficialDao.save(legionOfficial2);
						log.info("玩家释放联盟官衔，{}", legionOfficial2);
						ret = LegionLogType.OFFICE_UP;
						if (legionOfficial2.getRank() > legionOfficial.getRank()) {
							ret = LegionLogType.OFFICE_DOWN;
						}
					}
				}
			}
			legionOfficial.setRoleId(roleId);
		}
		legionOfficialDao.save(legionOfficial);
		return ret;
	}

	public LegionOfficial getLegionOfficial(AbstractRole role) {
		Legion legion = legionManager.findLegionByRole(role);
		if (legion == null) {
			// 没有联盟
			return null;
		}
		List<LegionOfficial> legionOfficials = findByLegionId(legion.getId());
		for (LegionOfficial legionOfficial : legionOfficials) {
			if (legionOfficial.getRoleId() != null && legionOfficial.getRoleId().longValue() == role.getId().longValue()) {
				return legionOfficial;
			}
		}
		return null;
	}

	public LegionOfficial getLegionOfficial(Long legionId, int group, int order) {
		return legionOfficialDao.findByLegionId(legionId, group, order);
	}

	public int getLegionOfficialRank(Role role) {
		LegionOfficial legionOfficial = getLegionOfficial(role);
		if (legionOfficial == null) {
			return LegionConstants.RANK_1;
		}
		return legionOfficial.getRank();
	}

	public boolean checkLegionOfficialAuth(LegionRankAuthEnum legionRankAuthEnum, Role role) {
		int legionOfficialRank = getLegionOfficialRank(role);
		LegionRankConfig legionRankConfig = configService.getConfig(LegionRankConfig.class);
		return legionRankConfig.checkRankAuth(legionOfficialRank, legionRankAuthEnum);
	}

	@Override
	public void onAllianceJoin(Long roleId, Long allianceId) {
		// TODO Auto-generated method stub

	}

	@Override
	public void onAllianceLeave(Long roleId, Long allianceId) {
		// 退盟删官职
		Role role = roleManager.getRole(roleId);
		Legion legion = legionManager.findLegionByRole(role);
		if (legion != null) {
			LegionOfficial legionOfficial = getLegionOfficial(role);
			if (legionOfficial != null) {
				updateLegionOfficial(legionOfficial, null);
			}
		}
	}

	@Override
	public void onLegionJoin(Long allianceId, Long legionId) {
		// TODO Auto-generated method stub

	}

	@Override
	public void onLegionLeave(Long allianceId, Long legionId) {
		log.info("联盟退出军团，删掉对应的军衔");
		// 退军团删官职
		Legion legion = legionManager.findLegionByLegionId(legionId);
		if (legion == null) {
			return;
		}
		List<LegionOfficial> legionOfficials = findByLegionId(legionId);
		if (JavaUtils.bool(legionOfficials)) {
			for (LegionOfficial legionOfficial : legionOfficials) {
				if (!JavaUtils.bool(legionOfficial.getRoleId())) {
					continue;
				}
				AbstractRole abstractRole = worldService.getWorld().getAbstractRole(legionOfficial.getRoleId());
				if (abstractRole != null && abstractRole.getAllianceId() != null && abstractRole.getAllianceId().longValue() == allianceId.longValue()) {
					updateLegionOfficial(legionOfficial, null);
				}
			}
		}
	}

	public void onLegionDismiss(Legion legion) {
		// 删除军团
		List<LegionOfficial> legionOfficials = findByLegionId(legion.getId());
		if (JavaUtils.bool(legionOfficials)) {
			legionOfficialDao.delete(legionOfficials);
		}
	}
}
