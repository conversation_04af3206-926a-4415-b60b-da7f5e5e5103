package com.lc.billion.icefire.gvgbattle.biz.service.impl;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.config.BuffConfig;
import com.lc.billion.icefire.game.biz.model.prop.AttributeValue;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgBuildingConfig;
import com.simfun.sgf.utils.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class GVGBuildingService {

    @Autowired
    private ConfigServiceImpl configService;

    public List<AttributeValue> getGVGBuildingAttribute(String buildingId) {
        List<AttributeValue> list = new ArrayList<>();
        if (!JavaUtils.bool(buildingId)) {
            return list;
        }

        var buildingMeta = configService.getConfig(GvgBuildingConfig.class).getMetaMap().get(buildingId);
        if (buildingMeta == null) {
            return list;
        }

        return getGVGBuildingAttribute(buildingMeta);
    }

    public List<AttributeValue> getGVGBuildingAttribute(GvgBuildingConfig.GvgBuildingMeta buildingMeta) {
        List<AttributeValue> list = new ArrayList<>();
        List<String> buildingBuffIdList = buildingMeta.getBuildingBuffIdList();
        if (!JavaUtils.bool(buildingBuffIdList)) {
            return list;
        }


        for (String buffId : buildingBuffIdList) {
            BuffConfig.BuffMeta buffMeta = configService.getConfig(BuffConfig.class).getBuffMeta(buffId);
            if (buffMeta == null) {
                continue;
            }
            AttributeValue[] proertyTypePara1s = buffMeta.getProertyTypePara1s();
            if (proertyTypePara1s == null || proertyTypePara1s.length == 0) {
                continue;
            }
            list.addAll(Arrays.asList(proertyTypePara1s));
        }
        return list;
    }
}
