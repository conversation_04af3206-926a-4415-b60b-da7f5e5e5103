package com.lc.billion.icefire.csacontrol.biz.model.activity;

import com.lc.billion.icefire.game.biz.model.csa.CSAPlayerHistoryInfo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class CSAActivityGroupContext implements Serializable {

	private static final long serialVersionUID = 7758977302446688385L;

	// 服务器ID
	private int selfServerId;
	// 当前阶段
	private CSAActivityStatus csaActivityStatus;
	// 下一阶段时间
	private long nextCsaActivityStatusTime;
	// 当前阶段事件触发次数
	private int currStageEventTriggerTime;
	// 当前阶段事件完成次数
	private int currStageEventCompleteTime;
	// 活动子类型
	private CSAActivitySubType subType;
	// 开启活动时在哪个赛季
	private int season;

	// 对抗服
	private int againstServerId;
	// 是否为上半场进攻方
	private boolean firstHalfAttack;
	// 分组数值ID CrossSeverAttackMatchMeta
	private String csaMatchMetaId;
	// rankId
	private String rankId;
	// 是否轮空
	private boolean bye = true;
	// 推送失败
	private boolean pushGameFailed;

	// 服务器战力
	private double serverCombat;
	// 服务器当前分数
	private int serverScore;
	// 服务器上半场分数
	private int firstHalfScore;
	// 服务器下半场分数
	private int secondHalfScore;
	// 是否取得本次活动胜利
	private boolean activityWin;
	// 服务器主事人
	private long kingRoleId;
	// 已选取的对战时间
	private List<Integer> selectedBattleTimeList = new ArrayList<>();
	// 对战开始时间
	private long battleStartTime;
	// 对战服国王信息
	private CSAPlayerHistoryInfo enemyKingInfo;

	public CSAActivityStatus getCsaActivityStatus() {
		return csaActivityStatus;
	}

	public void setCsaActivityStatus(CSAActivityStatus csaActivityStatus) {
		this.csaActivityStatus = csaActivityStatus;
	}

	public long getNextCsaActivityStatusTime() {
		return nextCsaActivityStatusTime;
	}

	public void setNextCsaActivityStatusTime(long nextCsaActivityStatusTime) {
		this.nextCsaActivityStatusTime = nextCsaActivityStatusTime;
	}

	public int getAgainstServerId() {
		return againstServerId;
	}

	public void setAgainstServerId(int againstServerId) {
		this.againstServerId = againstServerId;
	}

	public boolean isFirstHalfAttack() {
		return firstHalfAttack;
	}

	public void setFirstHalfAttack(boolean firstHalfAttack) {
		this.firstHalfAttack = firstHalfAttack;
	}

	public String getCsaMatchMetaId() {
		return csaMatchMetaId;
	}

	public void setCsaMatchMetaId(String csaMatchMetaId) {
		this.csaMatchMetaId = csaMatchMetaId;
	}

	public int getServerScore() {
		return serverScore;
	}

	public void setServerScore(int serverScore) {
		this.serverScore = serverScore;
	}

	public int getSelfServerId() {
		return selfServerId;
	}

	public void setSelfServerId(int selfServerId) {
		this.selfServerId = selfServerId;
	}

	public boolean isActivityWin() {
		return activityWin;
	}

	public void setActivityWin(boolean activityWin) {
		this.activityWin = activityWin;
	}

	public double getServerCombat() {
		return serverCombat;
	}

	public void setServerCombat(double serverCombat) {
		this.serverCombat = serverCombat;
	}

	public int getCurrStageEventTriggerTime() {
		return currStageEventTriggerTime;
	}

	public void setCurrStageEventTriggerTime(int currStageEventTriggerTime) {
		this.currStageEventTriggerTime = currStageEventTriggerTime;
	}

	public int getCurrStageEventCompleteTime() {
		return currStageEventCompleteTime;
	}

	public void setCurrStageEventCompleteTime(int currStageEventCompleteTime) {
		this.currStageEventCompleteTime = currStageEventCompleteTime;
	}

	public int getFirstHalfScore() {
		return firstHalfScore;
	}

	public void setFirstHalfScore(int firstHalfScore) {
		this.firstHalfScore = firstHalfScore;
	}

	public int getSecondHalfScore() {
		return secondHalfScore;
	}

	public void setSecondHalfScore(int secondHalfScore) {
		this.secondHalfScore = secondHalfScore;
	}

	public String getRankId() {
		return rankId;
	}

	public void setRankId(String rankId) {
		this.rankId = rankId;
	}

	public long getKingRoleId() {
		return kingRoleId;
	}

	public void setKingRoleId(long kingRoleId) {
		this.kingRoleId = kingRoleId;
	}

	public List<Integer> getSelectedBattleTimeList() {
		return selectedBattleTimeList;
	}

	public void setSelectedBattleTimeList(List<Integer> selectedBattleTimeList) {
		this.selectedBattleTimeList = selectedBattleTimeList;
	}

	public long getBattleStartTime() {
		return battleStartTime;
	}

	public void setBattleStartTime(long battleStartTime) {
		this.battleStartTime = battleStartTime;
	}

	public boolean isBye() {
		return bye;
	}

	public void setBye(boolean bye) {
		this.bye = bye;
	}

	public boolean isPushGameFailed() {
		return pushGameFailed;
	}

	public void setPushGameFailed(boolean pushGameFailed) {
		this.pushGameFailed = pushGameFailed;
	}

	public CSAPlayerHistoryInfo getEnemyKingInfo() {
		return enemyKingInfo;
	}

	public void setEnemyKingInfo(CSAPlayerHistoryInfo enemyKingInfo) {
		this.enemyKingInfo = enemyKingInfo;
	}

	public CSAActivitySubType getSubType() {
		return subType;
	}

	public void setSubType(CSAActivitySubType subType) {
		this.subType = subType;
	}

	public int getSeason() {
		return season;
	}

	public void setSeason(int season) {
		this.season = season;
	}

	@Override
	public String toString() {
		return "CSAActivityGroupContext{" + "selfServerId=" + selfServerId + ", csaActivityStatus=" + csaActivityStatus + ", nextCsaActivityStatusTime=" + nextCsaActivityStatusTime
				+ ", currStageEventTriggerTime=" + currStageEventTriggerTime + ", currStageEventCompleteTime=" + currStageEventCompleteTime + ", againstServerId=" + againstServerId
				+ ", firstHalfAttack=" + firstHalfAttack + ", csaMatchMetaId='" + csaMatchMetaId + '\'' + ", serverCombat=" + serverCombat + ", serverScore=" + serverScore
				+ ", firstHalfScore=" + firstHalfScore + ", secondHalfScore=" + secondHalfScore + ", activityWin=" + activityWin + '}';
	}
}
