package com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgBattleServerDispatchRecord;

/**
 * 战斗服分配记录 Dao
 *
 * <AUTHOR>
 * @date 2020/12/10
 */
@Repository
public class GvgBattleServerDispatchRecordDao extends RootDao<GvgBattleServerDispatchRecord> {

    private Map<Integer, List<GvgBattleServerDispatchRecord>> datasByServerId = new MyConcurrentMap<>();

    private Map<Long, List<GvgBattleServerDispatchRecord>> datasByTime = new MyConcurrentMap<>();

    private Map<GvgMatchType, List<GvgBattleServerDispatchRecord>> datasByMatchType = new MyConcurrentMap<>();

    private Map<String, GvgBattleServerDispatchRecord> datasByObId = new MyConcurrentMap<>();

    private Map<Long, GvgBattleServerDispatchRecord> datasByBattleServerId = new MyConcurrentMap<>();

    protected GvgBattleServerDispatchRecordDao() {
        super(GvgBattleServerDispatchRecord.class, false);
    }

    @Override
    protected MongoCursor<GvgBattleServerDispatchRecord> doFindAll(int db) {
        return dbFindAllForWorldEntity(db);
    }

    @Override
    protected void putMemoryIndexes(GvgBattleServerDispatchRecord entity) {
        //老数据， 没有这个字段
        if (entity.getMatchType() == null) {
            entity.setMatchType(GvgMatchType.FRIENDLY);
        }
        datasByServerId.compute(entity.getAlliance1ServerId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
        datasByServerId.compute(entity.getAlliance2ServerId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
        datasByTime.compute(entity.getBattleStartTime(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
        datasByMatchType.compute(entity.getMatchType(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
        if (entity.getObId() != null) {
            datasByObId.put(entity.getObId(), entity);
        }
        datasByBattleServerId.put(entity.getBattleServerId(), entity);
    }

    @Override
    protected void removeMemoryIndexes(GvgBattleServerDispatchRecord entity) {
        List<GvgBattleServerDispatchRecord> list = datasByServerId.get(entity.getAlliance1ServerId());
        if (list != null) {
            list.remove(entity);
        }
        list = datasByServerId.get(entity.getAlliance2ServerId());
        if (list != null) {
            list.remove(entity);
        }
        list = datasByTime.get(entity.getBattleStartTime());
        if (list != null) {
            list.remove(entity);
        }
        list = datasByMatchType.get(entity.getMatchType());
        if (list != null) {
            list.remove(entity);
        }

        if (entity.getObId() != null) {
            datasByObId.remove(entity.getObId());
        }

        datasByBattleServerId.remove(entity.getBattleServerId());
    }

    public GvgBattleServerDispatchRecord create(Long battleServerId, Long allianceId1, int alliance1ServerId, Long allianceId2, int alliance2ServerId, long battleStartTime,
                                                long battleDestroyTime, int battleTurn, int gameRound, GvgMatchType matchType, int round, int warZoneId,String obId) {
        GvgBattleServerDispatchRecord record = newEntityInstance();
        record.setBattleServerId(battleServerId);
        record.setAllianceId1(allianceId1);
        record.setAlliance1ServerId(alliance1ServerId);
        record.setAllianceId2(allianceId2);
        record.setAlliance2ServerId(alliance2ServerId);
        record.setBattleStartTime(battleStartTime);
        record.setBattleTurn(battleTurn);
        record.setBattleDestroyTime(battleDestroyTime);
        record.setGameRound(gameRound);
        record.setMatchType(matchType);
        record.setRound(round);
        record.setWarZoneId(warZoneId);
        record.setObId(obId);
        return createEntity(Application.getServerId(), record);
    }

    public GvgBattleServerDispatchRecord create(Long battleServerId, long battleStartTime, long battleDestroyTime, int status, GvgMatchType matchType) {
        GvgBattleServerDispatchRecord record = newEntityInstance();
        record.setBattleServerId(battleServerId);
        record.setBattleStartTime(battleStartTime);
        record.setBattleTurn(0);
        record.setStatus(status);
        record.setBattleDestroyTime(battleDestroyTime);
        record.setMatchType(matchType);
        return createEntity(Application.getServerId(), record);
    }

    public List<GvgBattleServerDispatchRecord> findByServerId(int gameServerId) {
        return datasByServerId.get(gameServerId);
    }

    public Map<Integer, List<GvgBattleServerDispatchRecord>> findByServerId() {
        return datasByServerId;
    }

    public List<GvgBattleServerDispatchRecord> findByTime(long time) {
        return datasByTime.get(time);
    }

    public Set<Long> findBattleTimes() {
        return datasByTime.keySet();
    }

    public List<GvgBattleServerDispatchRecord> findByMatchType(GvgMatchType matchType) {
        return datasByMatchType.get(matchType);
    }

    /**
     * dispatchRecord 内存 数据， 外部修改entity时间后， 需要调用本方法替换内存中的索引
     *
     * @param oriBattleStartTime 老的
     * @param newBattleStartTime 新的
     * @param entity
     */
    public void changeBattleStartTime(Long oriBattleStartTime, Long newBattleStartTime, GvgBattleServerDispatchRecord entity) {
        List<GvgBattleServerDispatchRecord> all = datasByTime.get(oriBattleStartTime);
        if (all != null) {
            all.remove(entity);
        }
        datasByTime.compute(newBattleStartTime, (k, v) -> v == null ? new ArrayList<>() : v).add(entity);

    }

    public GvgBattleServerDispatchRecord findByObId(String obId) {
        return datasByObId.get(obId);
    }

    public GvgBattleServerDispatchRecord findByBattleServerId(String battleServerId) {
        return datasByBattleServerId.get(Long.valueOf(battleServerId));
    }
}
