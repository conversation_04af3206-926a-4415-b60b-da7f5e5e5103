package com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgCupApplyInfo;

/**
 * 存储各服务器各联盟报名信息。
 *
 * <AUTHOR>
 * @created 2021-08-23
 */
@Repository
public class GvgCupApplyInfoDao extends RootDao<GvgCupApplyInfo> {

    private Map<Integer, List<GvgCupApplyInfo>> dataByServerId = new MyConcurrentMap<>();
    //<WarZoneId,<ServerId,List<GvgCupApplyInfo>>>;
    private Map<Integer, Map<Integer, List<GvgCupApplyInfo>>> dataByWarZoneIdAndServerId = new MyConcurrentMap<>();

    protected GvgCupApplyInfoDao() {
        super(GvgCupApplyInfo.class, false);
    }

    @Override
    protected MongoCursor<GvgCupApplyInfo> doFindAll(int db) {
        return dbFindAllForWorldEntity(db);
    }

    @Override
    protected void putMemoryIndexes(GvgCupApplyInfo entity) {
        dataByServerId.compute(entity.getServerId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
        dataByWarZoneIdAndServerId.compute(entity.getWarZoneId(), (k, v) -> v == null ? new HashMap<>() : v).compute(entity.getServerId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
    }

    @Override
    protected void removeMemoryIndexes(GvgCupApplyInfo entity) {
        List<GvgCupApplyInfo> set = dataByServerId.get(entity.getServerId());
        if (set != null) {
            set.remove(entity);
        }

    }

    public GvgCupApplyInfo create(int serverId, Long allianceId, String name, String aliasName, long ranking, long rating, String banner, int warZoneId) {
        int db = Application.getServerId();
        GvgCupApplyInfo entity = newEntityInstance();
        entity.setPersistKey(allianceId);
        entity.setServerId(serverId);
        entity.setName(name);
        entity.setAliasName(aliasName);
        entity.setRanking(ranking);
        entity.setRating(rating);
        entity.setBanner(banner);
        entity.setWarZoneId(warZoneId);
        return createEntity(db, entity);
    }

//	public GvgCupApplyInfo create(int serverId, Long allianceId) {
//		return create(serverId, allianceId, "", "", 0, 0, "");
//	}

    public List<GvgCupApplyInfo> findByServerId(int gameServerId) {
        return dataByServerId.get(gameServerId);
    }

    public Map<Integer, List<GvgCupApplyInfo>> findByWarZoneId(int warZoneId) {
        return dataByWarZoneIdAndServerId.get(warZoneId);
    }

    public Map<Integer, List<GvgCupApplyInfo>> getDataByServerId() {
        return dataByServerId;
    }

}
