package com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGBattleRecord;

/**
 * <AUTHOR>
 *
 */
@Repository
public class GVGBattleRecordDao extends RootDao<GVGBattleRecord> {

	private Map<Integer, List<GVGBattleRecord>> datas = new MyConcurrentMap<>();

	public GVGBattleRecordDao() {
		super(GVGBattleRecord.class, true);
	}

	@Override
	protected MongoCursor<GVGBattleRecord> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(GVGBattleRecord entity) {
		datas.compute(entity.getFailAllianceServerId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
		datas.compute(entity.getVictoryAllianceServerId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
	}

	@Override
	protected void removeMemoryIndexes(GVGBattleRecord entity) {
		List<GVGBattleRecord> list = datas.get(entity.getFailAllianceServerId());
		if (list != null) {
			list.remove(entity);
		}
		list = datas.get(entity.getVictoryAllianceServerId());
		if (list != null) {
			list.remove(entity);
		}
	}

	/**
	 * 在战斗服创建记录
	 * 
	 * @param failAllianceId
	 * @param failAlliancePoint
	 * @param failAllianceServerId
	 * @param victoryAllianceId
	 * @param victoryAlliancePoint
	 * @param victoryAllianceServerId
	 * @return
	 */
	public GVGBattleRecord create(Long failAllianceId, int failAlliancePoint, int failAllianceServerId, Long victoryAllianceId, int victoryAlliancePoint,
			int victoryAllianceServerId, long activityId, int activityRound,int gameRound,GvgMatchType matchType,int round,int warZoneId) {
		int db = Application.getServerId();
		GVGBattleRecord gvgBattleRecord = newEntityInstance();
		gvgBattleRecord.setFailAllianceId(failAllianceId);
		gvgBattleRecord.setFailAlliancePoint(failAlliancePoint);
		gvgBattleRecord.setFailAllianceServerId(failAllianceServerId);
		gvgBattleRecord.setVictoryAllianceId(victoryAllianceId);
		gvgBattleRecord.setVictoryAlliancePoint(victoryAlliancePoint);
		gvgBattleRecord.setVictoryAllianceServerId(victoryAllianceServerId);
		gvgBattleRecord.setBattleServerId(db);
		gvgBattleRecord.setActivityId(activityId);
		gvgBattleRecord.setActivityRound(activityRound);
		gvgBattleRecord.setGameRound(gameRound);
		gvgBattleRecord.setMatchType(matchType);
		gvgBattleRecord.setRound(round);
		gvgBattleRecord.setWarZoneId(warZoneId);
		return createEntity(db, gvgBattleRecord);
	}

	/**
	 * 在中控服创建记录
	 * 
	 * @param gvgBattleRecord
	 * @return
	 */
	public GVGBattleRecord create(GVGBattleRecord gvgBattleRecord) {
		int db = Application.getServerId();
		return createEntity(db, gvgBattleRecord);
	}

	public List<GVGBattleRecord> findByServerId(int serverId) {
		return datas.get(serverId);
	}

	public Map<Integer, List<GVGBattleRecord>> findByServerId() {
		return datas;
	}
}
