package com.lc.billion.icefire.kvkseason.biz.service.impl.legion.async;

import java.text.MessageFormat;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.support.HttpUtil;
import com.lc.billion.icefire.game.biz.async.asyncIO.AsyncIOThreadOperation;
import com.lc.billion.icefire.game.biz.async.asyncIO.IOConstant;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.CheckStringServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.kvkseason.biz.service.impl.legion.LegionMarkService;
import com.simfun.sgf.utils.MessageDigestUtils;

/**
 * <AUTHOR>
 *
 */
public class LegionMarkCheckContentOperation extends AsyncIOThreadOperation {

	private LegionMarkService legionMarkService;
	private CheckStringServiceImpl checkStringService;
	private Role role;
	private String metaId;
	private int x;
	private int y;
	private String content;

	private int action = -1;

	public LegionMarkCheckContentOperation(LegionMarkService legionMarkService, CheckStringServiceImpl checkStringService, Role role, String metaId, int x, int y, String content) {
		super();
		this.legionMarkService = legionMarkService;
		this.checkStringService = checkStringService;
		this.role = role;
		this.metaId = metaId;
		this.x = x;
		this.y = y;
		this.content = content;
	}

	@Override
	public boolean run() {
		String time = System.currentTimeMillis() + "";
		String md5 = MessageDigestUtils.md5(IOConstant.APP_ID + time + IOConstant.Key);
		String url = MessageFormat.format(IOConstant.URL, IOConstant.APP_ID, time, md5);
		JSONObject params = new JSONObject();
		params.put("message", content);
		try {
			String result = HttpUtil.postJSON(url, params.toString());
			JSONObject resultJson = JSONObject.parseObject(result);
			JSONObject resultData = JSONObject.parseObject(resultJson.getString("data"));
			if (resultData != null && resultData.containsKey("action")) {
				this.action = resultData.getInteger("action");
			}
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("LegionMarkCheckContentOperation send", e,
					"url",url, "content",content);
		}

		return true;
	}

	@Override
	public void finish() {
		String pureContent = content;
		if (action == -1 || action == 2) {
			pureContent = checkStringService.clean(content);
			if (pureContent.length() > 50) {
				ErrorLogUtil.errorLog("设置军团旗帜,内容非法", "roleId",role.getId(), "content",pureContent);
				legionMarkService.sendGcLegionMarkSet(role, 2, metaId);
				return;
			}
		}

		legionMarkService.legionMarkSet2Step(role, metaId, x, y, pureContent);
	}

}
