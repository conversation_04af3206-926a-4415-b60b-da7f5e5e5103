package com.lc.billion.icefire.kvkseason.biz.dao.mongo.root;

import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.kvkseason.biz.model.legion.Legion;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionLeader;

/**
 * <AUTHOR>
 *
 */
@Repository
public class LegionDao extends RootDao<Legion> {

	private Queue<String> legionNames = new ConcurrentLinkedQueue<>();

	public LegionDao() {
		super(Legion.class, true);
	}

	@Override
	protected MongoCursor<Legion> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(Legion entity) {
		legionNames.add(entity.getName());
	}

	@Override
	protected void removeMemoryIndexes(Legion entity) {
		legionNames.remove(entity.getName());
	}

	public boolean containsName(String name) {
		return legionNames.contains(name);
	}

	public Legion create(Alliance alliance, String badge, int badgeColor, String banner, int bannerColor, String declaration, String language, LegionLeader leader, String name,
			String country) {
		int db = alliance.getDB();
		Legion legion = newEntityInstance();
		legion.setBadge(badge);
		legion.setBadgeColor(badgeColor);
		legion.setBanner(banner);
		legion.setBannerColor(bannerColor);
		legion.setDeclaration(declaration);
		legion.setLanguage(language);
		legion.setLeader(leader);
		legion.setName(name);
		legion.setServerId(alliance.getoServerId());
		legion.setCountry(country);
		return createEntity(db, legion);
	}
}
