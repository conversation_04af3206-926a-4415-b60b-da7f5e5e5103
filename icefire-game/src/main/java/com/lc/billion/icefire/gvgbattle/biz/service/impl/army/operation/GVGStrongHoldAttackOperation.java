package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.operation;

import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.node.RegionCapitalNode;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.StrongHoldNode;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/1/13
 */
@Service
public class GVGStrongHoldAttackOperation extends AbstractGvgFightOperation {
	@Autowired
	private GVGStrongHoldService gvgStrongHoldService;

	@Override
	public ArmyType getArmyType() {
		return ArmyType.PVP_STRONGHOLD;
	}

	@Override
	public void armyArrive(ArmyInfo army) {
		logger.info("[GVG]armyArrive, army: {}", army.getPersistKey());
		try {
			if (gvgStrongHoldService.attackerArriveStrongHold(army)) {
				logger.info("[GVG]armyArrive, begin fight, army: {}", army.getPersistKey());
				super.armyArrive(army);
			}
		} catch(ExpectedException ignored) {

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("[GVG]armyArrive error", e);
			armyManager.returnArmy(army);
		}
	}

	@Override
	public void finishWork(ArmyInfo army) {
		super.finishWork(army);
		// 遣返进攻方部队
		if(!army.getFightContext().isWin()) { // 没打赢直接回城
			logger.info("[GVG]官渡 进攻官渡建筑失败 队列返回 玩家: {} 部队: {}",army.getRoleId(), army.getPersistKey());
			armyManager.returnArmy(army);
		}
		logger.info("[GVG]finishWork, army: {}", army.getPersistKey());
		armyManager.marchRetBILog(army);
	}

	@Override
	protected void endBattle(ArmyInfo army) {
		logger.info("[GVG]endBattle, army: {}", army.getPersistKey());
		gvgStrongHoldService.onStrongHoldBattleEnd(army);
	}

	@Override
	protected void handleRefineOrDefendArmies(SceneNode armyTargetNode) {
		if (armyTargetNode == null) {
			logger.info("官渡, 目标点为空");
			return;
		}
		if (armyTargetNode instanceof StrongHoldNode) {
			var armys = sceneService.getNodeArriveArmies(armyTargetNode);
			for (var army : armys) {
				if (army.getWorkType() == ArmyWorkType.DEFENDING && army.getSoldierCount() <= 0) {
					armyManager.returnArmy(army);
					logger.info("官渡 驻防{}的部队已经全部损失，现在遣返 armyId={} 增援方{}", armyTargetNode.getMetaId(), army.getPersistKey(), army.getRoleId());
				}
			}
		} else {
			super.handleRefineOrDefendArmies(armyTargetNode);
		}
	}
}
