package com.lc.billion.icefire.gvgcontrol.biz.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.AllianceBaseDataDao;
import com.lc.billion.icefire.protocol.structure.PsAllianceFlagInfo;
import com.lc.billion.icefire.protocol.structure.PsMatchRoomInfo;
import com.lc.billion.icefire.protocol.structure.PsRoomInfo;
import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.IAlliancesEntity;

/**
 * <AUTHOR>
 *
 */
public class RZERoomInfo extends AbstractEntity implements IAlliancesEntity {
	@MongoId
	private Long id;
	private Long allianceId;
	private int serverId;
	private int roomType;
	private int status;												// 状态
	private long fightPower;											// 分数
	private int signUpTimeIndex;										// 选择的时间点
	private List<Long> formalMemberIds = new ArrayList<>();			// 正式工
	private List<Long> tempMemberIds = new ArrayList<>();				// 临时工
	private Map<Long, Integer> applyList = new HashMap<>();			// 申请列表
	private Map<Long, Integer> inviteList = new HashMap<>();			// 邀请列表
	private Long enemyAllianceId;										// 应战联盟Id
	private int enemyServerId;											// 应战联盟服Id
	private List<Long> enemyFormalMemberIds = new ArrayList<>();		// 应战联盟正式工
	private List<Long> enemyTempMemberIds = new ArrayList<>();		// 应战临时工

	public void setId(Long id) {
		this.id = id;
	}
	public Long getId() {
		return id;
	}

	public void setStatus(int status) {
		this.status = status;
	}
	public int getStatus() {
		return status;
	}

	public int getServerId() {
		return serverId;
	}
	public void setServerId(int serverId) {
		this.serverId = serverId;
	}

	public int getRoomType() {
		return roomType;
	}
	public void setRoomType(int roomType) {
		this.roomType = roomType;
	}

	public void setSignUpTimeIndex(int signUpTimeIndex) {
		this.signUpTimeIndex = signUpTimeIndex;
	}
	public int getSignUpTimeIndex() {
		return signUpTimeIndex;
	}

	public void setFormalMemberIds(List<Long> formalMemberIds) {
		this.formalMemberIds = formalMemberIds;
	}
	public List<Long> getFormalMemberIds() {
		return formalMemberIds;
	}

	public void setTempMemberIds(List<Long> tempMemberIds) {
		this.tempMemberIds = tempMemberIds;
	}
	public List<Long> getTempMemberIds() {
		return tempMemberIds;
	}

	public void setEnemyAllianceId(Long enemyAllianceId) {
		this.enemyAllianceId = enemyAllianceId;
	}
	public Long getEnemyAllianceId() {
		return enemyAllianceId;
	}

	public long getFightPower() {
		return fightPower;
	}
	public void setFightPower(long fightPower) {
		this.fightPower = fightPower;
	}

	public void setEnemyServerId(int enemyServerId) {
		this.enemyServerId = enemyServerId;
	}
	public int getEnemyServerId() {
		return enemyServerId;
	}

	public void setEnemyFormalMemberIds(List<Long> enemyFormalMemberIds) {
		this.enemyFormalMemberIds = enemyFormalMemberIds;
	}
	public List<Long> getEnemyFormalMemberIds() {
		return enemyFormalMemberIds;
	}

	public Map<Long, Integer> getApplyList() {
		return applyList;
	}
	public void setApplyList(Map<Long, Integer> applyList) {
		this.applyList = applyList;
	}

	public List<Long> getEnemyTempMemberIds() {
		return enemyTempMemberIds;
	}
	public void setEnemyTempMemberIds(List<Long> enemyTempMemberIds) {
		this.enemyTempMemberIds = enemyTempMemberIds;
	}

	public Map<Long, Integer> getInviteList() {
		return inviteList;
	}
	public void setInviteList(Map<Long, Integer> inviteList) {
		this.inviteList = inviteList;
	}

	@Override
	public void setPersistKey(Long id) {
		this.id = id;
	}

	@Override
	public Long getPersistKey() {
		return id;
	}

	@Override
	public Long getGroupingId() {
		return allianceId;
	}

	@Override
	public Long getAllianceId() {
		return allianceId;
	}

	@Override
	public void setAllianceId(Long allianceId) {
		this.allianceId = allianceId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public PsRoomInfo toPsRoomInfo(){
		PsRoomInfo roomInfo = new PsRoomInfo();
		roomInfo.setRoomId(this.getId());
		roomInfo.setIndex(this.getSignUpTimeIndex());
		roomInfo.setAllianceId(String.valueOf(this.getAllianceId()));

		AllianceBaseData allianceBaseData = Application.getBean(AllianceBaseDataDao.class).findById(this.getAllianceId());
		if (allianceBaseData != null) {
			roomInfo.setName(allianceBaseData.getAllianceName());
			roomInfo.setAliasName(allianceBaseData.getAllianceAlias());
			roomInfo.setCountry(allianceBaseData.getCountry());

			// 匹配分数
			roomInfo.setMatchScore(allianceBaseData.getFightPower());

			// 旗帜
			PsAllianceFlagInfo flagInfo = new PsAllianceFlagInfo();
			flagInfo.setBadge(allianceBaseData.getBadge());
			flagInfo.setBadgeColor(allianceBaseData.getBadgeColor());
			flagInfo.setBanner(allianceBaseData.getBanner());
			flagInfo.setBannerColor(allianceBaseData.getBannerColor());
			roomInfo.setFlagInfo(flagInfo);
		}

		return roomInfo;
	}

	public PsMatchRoomInfo toPsMatchRoomInfo(){
		PsMatchRoomInfo matchRoomInfo = new PsMatchRoomInfo();
		matchRoomInfo.setRoomId(this.getId());
		matchRoomInfo.setIndex(this.getSignUpTimeIndex());

		matchRoomInfo.setAllianceId1(String.valueOf(this.getAllianceId()));
		AllianceBaseData allianceBaseData1 = Application.getBean(AllianceBaseDataDao.class).findById(this.getAllianceId());
		if (allianceBaseData1 != null) {
			matchRoomInfo.setName1(allianceBaseData1.getAllianceName());
			matchRoomInfo.setAliasName1(allianceBaseData1.getAllianceAlias());
			matchRoomInfo.setCountry1(allianceBaseData1.getCountry());

			// 匹配分数
			matchRoomInfo.setMatchScore1(allianceBaseData1.getFightPower());

			// 旗帜
			PsAllianceFlagInfo flagInfo = new PsAllianceFlagInfo();
			flagInfo.setBadge(allianceBaseData1.getBadge());
			flagInfo.setBadgeColor(allianceBaseData1.getBadgeColor());
			flagInfo.setBanner(allianceBaseData1.getBanner());
			flagInfo.setBannerColor(allianceBaseData1.getBannerColor());
			matchRoomInfo.setFlagInfo1(flagInfo);
		}

		matchRoomInfo.setAllianceId2(String.valueOf(this.getEnemyAllianceId()));
		AllianceBaseData allianceBaseData2 = Application.getBean(AllianceBaseDataDao.class).findById(this.getEnemyAllianceId());
		if (allianceBaseData2 != null) {
			matchRoomInfo.setName2(allianceBaseData2.getAllianceName());
			matchRoomInfo.setAliasName2(allianceBaseData2.getAllianceAlias());
			matchRoomInfo.setCountry2(allianceBaseData2.getCountry());

			// 匹配分数
			matchRoomInfo.setMatchScore2(allianceBaseData2.getFightPower());

			// 旗帜
			PsAllianceFlagInfo flagInfo = new PsAllianceFlagInfo();
			flagInfo.setBadge(allianceBaseData2.getBadge());
			flagInfo.setBadgeColor(allianceBaseData2.getBadgeColor());
			flagInfo.setBanner(allianceBaseData2.getBanner());
			flagInfo.setBannerColor(allianceBaseData2.getBannerColor());
			matchRoomInfo.setFlagInfo2(flagInfo);
		}

		return matchRoomInfo;
	}

	public GvgMatchType getMatchType(Long time){
		int day = TimeUtil.getDayOfWeek(time);
		return GvgMatchType.findById(day);
	}
}
