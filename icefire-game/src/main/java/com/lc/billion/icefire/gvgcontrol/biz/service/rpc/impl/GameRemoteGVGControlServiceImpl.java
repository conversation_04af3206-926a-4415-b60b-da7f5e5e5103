package com.lc.billion.icefire.gvgcontrol.biz.service.rpc.impl;

import java.util.*;
import java.util.concurrent.TimeUnit;

import com.lc.billion.icefire.game.biz.model.gvg.GVGAllianceLineUpInfo;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.*;
import com.lc.billion.icefire.gvgcontrol.biz.model.*;
import com.lc.billion.icefire.gvgcontrol.biz.service.gm.ControllerTestGM;
import com.lc.billion.icefire.gvgcontrol.biz.service.impl.activity.handler.GVGActivityHandler;
import com.lc.billion.icefire.gvgcontrol.biz.service.impl.activity.handler.RZEActivityHandler;
import com.lc.billion.icefire.protocol.structure.*;
import com.lc.billion.icefire.rpc.vo.gvg.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.context.GVGActivityContext;
import com.lc.billion.icefire.gvgcontrol.biz.service.GVGControlService;
import com.lc.billion.icefire.rpc.service.gvg.IGameRemoteGVGControlService;
import com.simfun.sgf.utils.JavaUtils;

/**
 * game服到GVG中控
 *
 * <AUTHOR>
 */
@Service
public class GameRemoteGVGControlServiceImpl implements IGameRemoteGVGControlService {

    private static final Logger logger = LoggerFactory.getLogger(GameRemoteGVGControlServiceImpl.class);

    @Autowired
    private GvgBattleServerDispatchRecordDao gvgBattleServerDispatchRecordDao;
    @Autowired
    private GVGControlService gvgControlService;
    @Autowired
    private GVGBattleRecordDao gvgBattleRecordDao;
    @Autowired
    private GVGActivityHandler gvgActivityHandler;
    @Autowired
    private ActivityDao activityDao;
    @Autowired
    private AllianceBaseDataDao allianceBaseDataDao;
    @Autowired
    private GvgAllianceRecordDao gvgAllianceRecordDao;
    @Autowired
    private GVGAllianceSignUpInfoDao gvgAllianceSignUpInfoDao;
    @Autowired
    private GVGQualifiedServerDao gvgQualifiedServerDao;
    @Autowired
    private GvgCupApplyInfoDao gvgCupApplyInfoDao;
    @Autowired
    private RZEActivityHandler rzeActivityHandler;
    @Autowired
    private ControllerTestGM controllerTestGM;

    @Override
    public List<GvgBattleServerDispatchRecordVo> findGvgBattleServerDispatchRecords(Set<Integer> serverIds) {
        List<GvgBattleServerDispatchRecordVo> ret = new ArrayList<>();
        for (Integer serverId : serverIds) {
            List<GvgBattleServerDispatchRecord> gvgBattleServerDispatchRecords = gvgBattleServerDispatchRecordDao.findByServerId(serverId);
            if (JavaUtils.bool(gvgBattleServerDispatchRecords)) {
                for (GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord : gvgBattleServerDispatchRecords) {
                    if (gvgBattleServerDispatchRecord.getBattleTurn() > 0) {
                        ret.add(new GvgBattleServerDispatchRecordVo(gvgBattleServerDispatchRecord));
                    }
                }
            }
        }
        return ret;
    }

    @Override
    public List<GVGBattleRecordVo> findGVGBattleRecords(Set<Integer> serverIds) {
        List<GVGBattleRecordVo> ret = new ArrayList<>();
        for (Integer serverId : serverIds) {
            List<GVGBattleRecord> gvgBattleRecords = gvgBattleRecordDao.findByServerId(serverId);
            if (JavaUtils.bool(gvgBattleRecords)) {
                Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG);
                long activityId = 0L;
                int activityRound = 0;
                if (activity != null) {
                    activityId = activity.getPersistKey();
                    activityRound = ((GVGActivityContext) activity.getActivityContext()).getRound();
                }
                for (GVGBattleRecord gvgBattleRecord : gvgBattleRecords) {
                    if (gvgBattleRecord.getActivityId() != activityId) {
                        continue;
                    }
                    if (gvgBattleRecord.getActivityRound() != activityRound) {
                        continue;
                    }
                    if (gvgBattleRecord.getFailAllianceServerId() != serverId && gvgBattleRecord.getVictoryAllianceServerId() != serverId) {
                        continue;
                    }
                    ret.add(new GVGBattleRecordVo(gvgBattleRecord));
                }
            }
        }
        return ret;
    }

    @Override
    public ActivityVo findActivityVo() {
        Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG);
        if (activity == null) {
            return null;
        }
        return new ActivityVo(activity);
    }

    @Override
    public List<GVGAllianceSignUpInfoVo> findGVGAllianceSignUpInfo(Set<Integer> serverIds) {
        List<GVGAllianceSignUpInfoVo> ret = new ArrayList<>();
        for (Integer serverId : serverIds) {
            List<GVGAllianceSignUpInfo> gvgAllianceSignUpInfos = gvgAllianceSignUpInfoDao.findByServerId(serverId);
            if (JavaUtils.bool(gvgAllianceSignUpInfos)) {
                for (GVGAllianceSignUpInfo gvgAllianceSignUpInfo : gvgAllianceSignUpInfos) {
                    ret.add(new GVGAllianceSignUpInfoVo(gvgAllianceSignUpInfo));
                }
            }
        }
        return ret;
    }


    @Override
    public void uploadAllianceBaseData(AllianceBaseDataVo allianceBaseDataVo) {
        gvgControlService.updateAllianceBaseData(allianceBaseDataVo);
    }

    @Override
    public List<AllianceBaseDataVo> findAllianceBaseDataVos(Set<Integer> serverIds) {
        List<AllianceBaseDataVo> ret = new ArrayList<>();
        for (Integer serverId : serverIds) {
            List<GvgBattleServerDispatchRecord> gvgBattleServerDispatchRecords = gvgBattleServerDispatchRecordDao.findByServerId(serverId);
            Set<Long> allianceIds = new HashSet<>();
            if (JavaUtils.bool(gvgBattleServerDispatchRecords)) {
                gvgBattleServerDispatchRecords.forEach(gvgBattleServerDispatchRecord -> {
                    if (JavaUtils.bool(gvgBattleServerDispatchRecord.getAllianceId1())) {
                        allianceIds.add(gvgBattleServerDispatchRecord.getAllianceId1());
                    }
                    if (JavaUtils.bool(gvgBattleServerDispatchRecord.getAllianceId2())) {
                        allianceIds.add(gvgBattleServerDispatchRecord.getAllianceId2());
                    }
                });
            }
            if (JavaUtils.bool(allianceIds)) {
                for (Long allianceId : allianceIds) {
                    AllianceBaseData allianceBaseData = allianceBaseDataDao.findById(allianceId);
                    ret.add(new AllianceBaseDataVo(allianceBaseData));
                }
            }
        }
        return ret;
    }

    @Override
    public String test(Set<Integer> serverIds) {
        logger.info("测试game发送rpc到中控服堵住开始");
        try {
            TimeUnit.SECONDS.sleep(0);
            logger.info("测试game发送rpc到中控服结束");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return "end";
    }

    @Override
    public boolean findGVGServerQualified(int serverId) {
        GVGQualifiedServer gvgQualifiedServer = gvgQualifiedServerDao.findById(Long.valueOf(serverId));
        return gvgQualifiedServer != null;
    }

    @Override
    public GvgCupApplyInfoVO applyGvgCup(int warZoneId, int serverId, Long allianceId, String name, String aliasName, long allianceFightPowerRanking, long power, String banner) {
        GvgCupApplyInfo entity = gvgCupApplyInfoDao.findById(allianceId);
        GvgCupApplyInfoVO res = null;
        if (entity == null) {
            logger.info("收到GvgCup报名信息 服务器{} 联盟{}", serverId, allianceId);
            entity = gvgCupApplyInfoDao.create(serverId, allianceId, name, aliasName, allianceFightPowerRanking, power, banner, warZoneId);
            entity.setHasApply(1);
            res = new GvgCupApplyInfoVO(entity);
        } else {
            logger.info("收到GvgCup报名信息 服务器{} 联盟{},【已经报过了不能重复报名！】", serverId, allianceId);
        }
        return res;
    }

    @Override
    public List<GvgCupApplyInfoVO> findGvgCupApplyInfo(Set<Integer> serverIds) {
        List<GvgCupApplyInfoVO> ret = new ArrayList<>();
        for (Integer serverId : serverIds) {
            List<GvgCupApplyInfo> infos = gvgCupApplyInfoDao.findByServerId(serverId);
            if (JavaUtils.bool(infos)) {
                for (GvgCupApplyInfo info : infos) {
                    ret.add(new GvgCupApplyInfoVO(info));
                }
            }
        }
        return ret;
    }

    @Override
    public Map<Integer, List<GvgObMatchInfoVo>> fetchGvgCupObMatchList() {
        return gvgControlService.getGvgObMatchList();
    }

    @Override
    public int queryBattleServerStatus(int battleServerId) {
        if (battleServerId <= 0) {
            return 0;
        }
        GvgBattleServerDispatchRecord record = gvgBattleServerDispatchRecordDao.findById(Long.valueOf(battleServerId));
        if (record != null) {
            return record.getStatus();
        }
        return 0;
    }

    @Override
    public List<RZERoomInfoVo> updateCreateRoom(Long roleId, Long allianceId, int serverId, int roomType, List<Integer> indexs) {
        return gvgControlService.updateRZECreateRoomInfo(roleId, allianceId, serverId, roomType, indexs);
    }

    @Override
    public List<RZERoomInfoVo> updateModifyRoom(Long roleId, Long allianceId, int serverId, List<Integer> indexs, Map<Long, Integer> delRoomList) {
        return gvgControlService.updateRZEModifyRoomInfo(roleId, allianceId, serverId, indexs, delRoomList);
    }

    @Override
    public RZERoomInfoVo updateJoinRoom(Long roleId, Long allianceId, int serverId, Long roomId, String comment, Set<Long> removeRoomIds) {
        return gvgControlService.updateRZEJoinRoom(roleId, allianceId, serverId, roomId, comment, removeRoomIds);
    }

    @Override
    public List<PsRoomApplyInfo> getApplyRoomList(long roleId, int type, Map<Long, Boolean> roomIds, Set<Long> removeRoomIds) {
        return gvgControlService.getApplyRoomList(roleId, type, roomIds, removeRoomIds);
    }

    @Override
    public List<PsRoomInfo> getCreateRoomList(long roleId, Set<Long> removeRoomIds) {
        return gvgControlService.getCreateRoomList(roleId, removeRoomIds);
    }

    @Override
    public RZERoomInfoVo updateRoomAllianceMember(Long roleId, Long roomId, Long allianceId, int serverId, List<Long> formalMemberIds, List<Long> tempMemberIds) {
        return gvgControlService.updateRoomAllianceMemberInfo(roleId, roomId, allianceId, serverId, formalMemberIds, tempMemberIds);
    }

    @Override
    public List<RZERoomInfoVo> updateInviteJoinRoom(Long roleId, Long allianceId, int serverId, List<PsRoomInviteInfo> inviteInfos, Set<Long> removeRoomIds) {
        return gvgControlService.updateInviteJoinRoom(roleId, allianceId, serverId, inviteInfos, removeRoomIds);
    }

    @Override
    public RZERoomInfoVo updateCheckApplyRoom(Long roleId, Long allianceId, int serverId, Long roomId, Long targetAllianceId, int type, Set<Long> removeRoomIds) {
        return gvgControlService.updateCheckApplyRoom(roleId, allianceId, serverId, roomId, targetAllianceId, type, removeRoomIds);
    }

    @Override
    public List<PsMatchRoomInfo> getMatchRoomList(Long roleId, Long allianceId, List<Long> roomIds) {
        return gvgControlService.getMatchRoomList(roleId, allianceId, roomIds);
    }

    @Override
    public void uploadRZEAllianceInfo(Collection<RZEAllianceInfoVo> infos) {
        rzeActivityHandler.handleUploadRZEAllianceInfo(infos);
    }

    @Override
    public boolean findRZEServerQualified(int serverId) {
        return rzeActivityHandler.findRZEServerQualified(serverId);
    }

    @Override
    public ActivityVo findRZEActivityVo() {
        Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG_ENGAGEMENT);
        if (activity == null) {
            return null;
        }
        return new ActivityVo(activity);
    }

    @Override
    public Collection<RZEAllianceInfoVo> findRZEAllianceInfoVos() {
        return rzeActivityHandler.findAllRZEAllianceInfoVos();
    }


    /**
     * 只有在GVGUpdateAllianceLineUpInfoOperation可以调用
     * @param roleId
     * @param allianceId
     * @param serverId
     * @param gvgAllianceLineUpInfo
     * @param matchType
     * @return
     */
    @Override
    public GVGAllianceLineUpInfo updateGVGAllianceLineUpInfo(Long roleId, Long allianceId, int serverId, GVGAllianceLineUpInfo gvgAllianceLineUpInfo, GvgMatchType matchType) {
        GVGAllianceLineUpInfo gvgAllianceLineUpInfo_res = gvgControlService.updateGVGAllianceLineUpInfo(roleId, allianceId, serverId, gvgAllianceLineUpInfo, matchType);
        return gvgAllianceLineUpInfo_res;
    }


    @Override
    public void setControlServerStatusGM(boolean restart, int status) {
        gvgActivityHandler.setGvgStatusByGm(restart, status);
    }

    @Override
    public void gm(String[] args){
        controllerTestGM.process(args);
    }
}
