package com.lc.billion.icefire.gvgcontrol.biz.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.support.HttpUtil;
import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.async.asyncIO.AsyncIOThreadOperation;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.manager.gvg.GVGGameDataVoManager;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.game.biz.model.gvg.GVGAllianceLineUpInfo;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.gvg.GVGGameService;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.manager.gvg.GVGBattleDataVoManager;
import com.lc.billion.icefire.gvgcontrol.biz.GVGBattleServerCreateConfig;
import com.lc.billion.icefire.gvgcontrol.biz.config.GvgCupGroupListConfig;
import com.lc.billion.icefire.gvgcontrol.biz.config.GvgCupGroupStageAutoMatchConfig;
import com.lc.billion.icefire.gvgcontrol.biz.config.GvgLeagueGroupSettingConfig;
import com.lc.billion.icefire.gvgcontrol.biz.config.GvgLeagueGroupSettingConfig.GvgCupGroupSettingMeta;
import com.lc.billion.icefire.gvgcontrol.biz.config.GvgObServerConfig;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.*;
import com.lc.billion.icefire.gvgcontrol.biz.model.*;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.context.GVGActivityContext;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.context.GVGActivitySignUpInfo;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.engagement.RZEActivityContext;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.engagement.RZEActivityStatus;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.gvg.GVGActivityStatus;
import com.lc.billion.icefire.gvgcontrol.biz.service.impl.activity.handler.GVGActivityHandler;
import com.lc.billion.icefire.protocol.constant.PsRZEErrorCode;
import com.lc.billion.icefire.protocol.structure.*;
import com.lc.billion.icefire.rpc.vo.gvg.*;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.config.WorldMapConfig;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.simfun.sgf.common.tuple.TwoTuple;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class GVGControlService {

    private static final Logger logger = LoggerFactory.getLogger(GVGControlService.class);

    @Autowired
    private GVGBattleDataVoManager gvgBattleDataVoManager;
    @Autowired
    private GVGControlBroadcastToGameService gvgControlBroadcastToGameService;
    @Autowired
    private GVGBattleRecordDao gvgBattleRecordDao;
    @Autowired
    private GVGQualifiedServerDao gvgQualifiedServerDao;
    @Autowired
    private GvgBattleServerDispatchRecordDao gvgBattleServerDispatchRecordDao;
    @Autowired
    private GVGAllianceSignUpInfoDao gvgAllianceSignUpInfoDao;
    @Autowired
    private ActivityDao activityDao;
    @Autowired
    private GVGControlBroadcastToGVGBattleService gvgControlBroadcastToGVGBattleService;
    @Autowired
    private AllianceBaseDataDao allianceBaseDataDao;
    @Autowired
    private GvgAllianceRecordDao gvgAllianceRecordDao;
    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private GvgCupApplyInfoDao gvgCupApplyInfoDao;
    @Autowired
    private GVGActivityTurnDao gvgActivityTurnDao;
    @Autowired
    private GVGGameService gvgGameService;
    @Autowired
    private GVGGameDataVoManager gvgGameDataVoManager;
    @Autowired
    private AsyncOperationServiceImpl asyncOperationService;
    @Autowired
    private GvgObserveMatchDao gvgObserveMatchDao;
    @Autowired
    private RZERoomInfoDao rzeRoomInfoDao;
    @Autowired
    private RZEAllianceInfoDao rzeAllianceInfoDao;
    @Autowired
    private RZEQualifiedServerDao rzeQualifiedServerDao;
    //<warZoneId,List<>>
    Map<Integer, List<GvgObMatchInfoVo>> gvgObMatchList = new MyConcurrentMap<>();

    public Map<Integer, List<GvgObMatchInfoVo>> getGvgObMatchList() {
        return gvgObMatchList;
    }

    public void startService() {
        WorldMapConfig worldMapConfig = ServerConfigManager.getInstance().getWorldMapConfig();
        ServerType serverType = worldMapConfig.getServerType();
        if (serverType == ServerType.GVG_CONTROL) {
            // gvgControlBroadcastToGameService.noticeGameServerQualifieds();
            // 下发已报名玩家的信息
            gvgControlBroadcastToGameService.broadcastGVGAllianceSignUpInfos();
            Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG);
            long activityId = 0L;
            int activityRound = 0;
            if (activity != null) {
                activityId = activity.getPersistKey();
                activityRound = ((GVGActivityContext) activity.getActivityContext()).getRound();
            }
            Collection<GVGBattleRecord> gvgBattleRecords = gvgBattleRecordDao.findAll();
            for (GVGBattleRecord gvgBattleRecord : gvgBattleRecords) {
                if (gvgBattleRecord.getActivityId() != activityId) {
                    continue;
                }
                if (gvgBattleRecord.getActivityRound() != activityRound) {
                    continue;
                }
                if (!gvgBattleRecord.isStop()) {
                    continue;
                }
                gvgControlBroadcastToGameService.broadcastGVGBattleRecord(new GVGBattleRecordVo(gvgBattleRecord));
            }
            // 广播匹配信息给game和battle
            Collection<GvgBattleServerDispatchRecord> gvgBattleServerDispatchRecords = gvgBattleServerDispatchRecordDao.findAll();
            if (JavaUtils.bool(gvgBattleServerDispatchRecords)) {
                for (GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord : gvgBattleServerDispatchRecords) {
                    if (gvgBattleServerDispatchRecord.getStatus() != GvgBattleServerDispatchRecord.STATUS_END) {
                        // 结束的不广播给战斗服
                        gvgControlBroadcastToGVGBattleService.broadcastGvgBattleServerDispatchRecord(gvgBattleServerDispatchRecord);
                    }
                    Long battleServerId = gvgBattleServerDispatchRecord.getBattleServerId();
                    gvgControlBroadcastToGameService.broadcastGvgBattleServerDispatchRecord(gvgBattleServerDispatchRecord);
                    List<GVGAllianceSignUpInfo> gvgAllianceSignUpInfos = new ArrayList<>();
                    GvgMatchType matchType = gvgBattleServerDispatchRecord.getMatchType();
                    GVGAllianceSignUpInfo gvgAllianceSignUpInfo = gvgAllianceSignUpInfoDao.findByMatchType(matchType, gvgBattleServerDispatchRecord.getAllianceId1());
                    gvgAllianceSignUpInfos.add(gvgAllianceSignUpInfo);
                    gvgAllianceSignUpInfo = gvgAllianceSignUpInfoDao.findByMatchType(matchType, gvgBattleServerDispatchRecord.getAllianceId2());
                    gvgAllianceSignUpInfos.add(gvgAllianceSignUpInfo);
                    gvgControlBroadcastToGVGBattleService.broadcastGVGAllianceSignUpInfo(battleServerId.intValue(), gvgAllianceSignUpInfos);
                }
            }
            // 广播联盟信息给game
            gvgControlBroadcastToGameService.broadcastAllianceBaseData();

            // 广播gvg杯赛报名信息给game
            gvgControlBroadcastToGameService.broadcastGvgCupApplyInfo();

            // 生成并广播杯赛观战数据列表给game
            this.genGvgObserveDataFromDBThenBroadcastToGame();
        }
    }

    /**
     * 处理战斗服上传的战斗记录信息
     * <p>
     * 本地持久化，奖励
     */
    public void uploadGVGBattleRecord(GVGBattleRecordVo recordVo) {
        logger.info("[GVG] uploadGVGBattleRecord battleServerId: {}, victoryServer: {}, victoryAlliance: {}, failServer: {}, failAlliance: {}",
                recordVo.getBattleServerId(), recordVo.getVictoryAllianceServerId(), recordVo.getVictoryAllianceId(), recordVo.getFailAllianceServerId(), recordVo.getFailAllianceId());
        GVGBattleRecord gvgBattleRecord = gvgBattleRecordDao.findById(recordVo.getId());
        if (gvgBattleRecord == null) {
            logger.info("[GVG] uploadGVGBattleRecord createBattleRecord: {}", recordVo.getBattleServerId());
            gvgBattleRecord = GVGBattleRecord.copyGVGBattleRecord(recordVo);
            gvgBattleRecordDao.create(gvgBattleRecord);
        }

        // 数据存库
        // 计算胜负并结算净胜分
        Long victoryAllianceId = gvgBattleRecord.getVictoryAllianceId();
        int victoryAllianceServerId = gvgBattleRecord.getVictoryAllianceServerId();
        int victoryAlliancePoint = gvgBattleRecord.getVictoryAlliancePoint();
        Long failAllianceId = gvgBattleRecord.getFailAllianceId();
        int failAllianceServerId = gvgBattleRecord.getFailAllianceServerId();
        int failAlliancePoint = gvgBattleRecord.getFailAlliancePoint();
        try {
            handleGvgAllianceRecord(recordVo.getMatchType(), true, victoryAllianceId, victoryAllianceServerId, victoryAlliancePoint - failAlliancePoint);
            handleGvgAllianceRecord(recordVo.getMatchType(), false, failAllianceId, failAllianceServerId, failAlliancePoint - victoryAlliancePoint);
            if (!gvgBattleRecord.isSendAwardReady()) {
                // 更新给game服
                gvgBattleRecord.setSendAwardReady(true);
                gvgControlBroadcastToGameService.broadcastGVGBattleRecord(recordVo);
            }
        } catch (ExpectedException ignored){

        } catch (Exception e) {
            ErrorLogUtil.errorLog("[GVG] uploadGVGBattleRecord", e);
        } finally {
            gvgBattleRecord.setSendAward(true);
            gvgBattleRecordDao.save(gvgBattleRecord);
        }
    }

    public void updateGvgBattleServerDispatchRecord(int battleServerId) {
        GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord = gvgBattleServerDispatchRecordDao.findById((long) battleServerId);
        if (gvgBattleServerDispatchRecord != null) {
            logger.info("[GVG] updateGvgBattleServerDispatchRecord, battleServerId: {}, {} <-> {}, status: {}", battleServerId, gvgBattleServerDispatchRecord.getAllianceId1(), gvgBattleServerDispatchRecord.getAllianceId1(), gvgBattleServerDispatchRecord.getStatus());
            gvgBattleServerDispatchRecord.setStatus(GvgBattleServerDispatchRecord.STATUS_END);
            gvgBattleServerDispatchRecordDao.save(gvgBattleServerDispatchRecord);
        } else {
            ErrorLogUtil.errorLog("[GVG] updateGvgBattleServerDispatchRecord noDispatchRecord", "battleServerId",battleServerId);
            // 可能是战斗服启服销毁
            Calendar calendar = Calendar.getInstance();
            int minute = calendar.get(Calendar.MINUTE);
            calendar.add(Calendar.MINUTE, 10 - minute % 10);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            // 随便创建一个
            gvgBattleServerDispatchRecordDao.create((long) battleServerId, calendar.getTimeInMillis(), calendar.getTimeInMillis(),
                    GvgBattleServerDispatchRecord.STATUS_END, GvgMatchType.FRIENDLY);
        }
    }

    /**
     * GM调用 再次处理战斗记录--可能有发奖失败的记录
     */
    public void uploadGVGBattleRecordAndDeleteDispatchRecordAgain() {
        logger.info("GM触发重发奖");
        Collection<GVGBattleRecord> gvgBattleRecords = gvgBattleRecordDao.findAll();
        if (JavaUtils.bool(gvgBattleRecords)) {
            for (GVGBattleRecord gvgBattleRecord : gvgBattleRecords) {
                if (gvgBattleRecord.isSendAward()) {
                    continue;
                }
                uploadGVGBattleRecord(new GVGBattleRecordVo(gvgBattleRecord));
            }
        } else {
            logger.info("没有GVGBattleRecord记录");
        }
    }

    public void handleGvgAllianceRecord(GvgMatchType matchType, boolean isWin, Long allianceId, int serverId, long score) {
        if(allianceId <= 0){
            return;
        }

        GvgAllianceRecord gvgAllianceRecord = gvgAllianceRecordDao.findById(allianceId);
        if (gvgAllianceRecord == null) {
            logger.info("[GVG] handleGvgAllianceRecord createAllianceRecord: {}, serverId: {}, score: {}", allianceId, serverId, score);
            gvgAllianceRecord = gvgAllianceRecordDao.create(allianceId, serverId);
        } else {
            logger.info("[GVG] handleGvgAllianceRecord findAllianceRecord: {}, serverId: {}, score: {}", allianceId, gvgAllianceRecord.getServerId(), gvgAllianceRecord.getScore(matchType));
        }
        gvgAllianceRecord.addResult(matchType, isWin, score);
        gvgAllianceRecordDao.save(gvgAllianceRecord);
        gvgControlBroadcastToGameService.broadcastGvgAllianceRecord(gvgAllianceRecord);
    }

    /**
     * 获得指定数量的战斗服serverId列表
     *
     * @return
     */
    public List<Integer> getCanUseBattleServerIds(int needNum) {
        List<Integer> ret = new ArrayList<>();
        WorldMapConfig worldMapConfig = ServerConfigManager.getInstance().getWorldMapConfig();
        List<Long> dispatchBattleServerIds = gvgBattleServerDispatchRecordDao.findAll().stream().map(GvgBattleServerDispatchRecord.class::cast)
                .collect(Collectors.mapping(GvgBattleServerDispatchRecord::getBattleServerId, Collectors.toList()));
        int gvgBattleServerStartId = worldMapConfig.getGvgBattleServerStartId() + dispatchBattleServerIds.size();
        int index = 0;
        while (ret.size() < needNum) {
            int serverId = gvgBattleServerStartId + index;
            index ++;
            boolean contains = dispatchBattleServerIds.contains(Long.valueOf(serverId));
            if (contains) {
                continue;
            }
            ret.add(serverId);
        }
        //            for (GameServerConfig gsc : configCenter.getLsConfig().getGameServers().values()) {
//                if (gsc.serverType() == ServerType.GVG_BATTLE) {
//                    int gameServerId = gsc.getGameServerId();
//                    boolean contains = dispatchBattleServerIds.contains(Long.valueOf(gameServerId));
//                    if (contains) {
//                        continue;
//                    }
//                    ret.add(gsc.getGameServerId());
//                    logger.info("查找到测试环境下的GVG战斗服， serverId：{}，", gsc.getGameServerId());
//                }
//            }
//            if (ret.size() == 0) {
//                throw new AlertException("没找到测试环境下的GVG战斗服");
//            }
//
//            return ret;
//        }

        return ret;

//        GVGBattleServerCreateConfig gvgBattleServerCreateConfig = ServerConfigManager.getInstance().getGvgBattleServerCreateConfig();
//        String createUrl = gvgBattleServerCreateConfig.getCreateUrl();
//
//        List<Long> dispatchBattleServerIds = gvgBattleServerDispatchRecordDao.findAll().stream().map(GvgBattleServerDispatchRecord.class::cast)
//                .collect(Collectors.mapping(GvgBattleServerDispatchRecord::getBattleServerId, Collectors.toList()));
//
//        if (createUrl == null) {
//            //从zookeeper中查找，serverType为GVG_BATTLE的服务器是战斗服。 目前写死，只返回查找到的第一个。
//            for (GameServerConfig gsc : configCenter.getLsConfig().getGameServers().values()) {
//                if (gsc.serverType() == ServerType.GVG_BATTLE) {
//                    int gameServerId = gsc.getGameServerId();
//                    boolean contains = dispatchBattleServerIds.contains(Long.valueOf(gameServerId));
//                    if (contains) {
//                        continue;
//                    }
//                    ret.add(gsc.getGameServerId());
//                    logger.info("查找到测试环境下的GVG战斗服， serverId：{}，", gsc.getGameServerId());
//                }
//            }
//            if (ret.size() == 0) {
//                throw new AlertException("没找到测试环境下的GVG战斗服");
//            }
//
//            return ret;
//        }

    }

    /**
     * 创建战斗服
     */
    public void noticeCreateBattleServer(List<Integer> serverIds, int dbIndex) {
        if (!JavaUtils.bool(serverIds)) {
            return;
        }
        logger.info("创建战斗服请求发送：{}", serverIds);
        GVGBattleServerCreateConfig gvgBattleServerCreateConfig = ServerConfigManager.getInstance().getGvgBattleServerCreateConfig();
        String createUrl = gvgBattleServerCreateConfig.getCreateUrl();
        if (createUrl == null) {
            serverIds.forEach(serverId -> configCenter.updateGameServerRestartTimeMs(serverId, TimeUtil.getNow()));
        } else {
            // {"status": "ok", "sids": ["90001"], "message": "Commit create twd gvg
            // success"}
            JSONObject sendBattleServerRequest = sendBattleServerRequest(createUrl, gvgBattleServerCreateConfig.getProjectName(), serverIds, dbIndex);
            String status = sendBattleServerRequest.getString("status");
            if ("ok".equals(status)) {
                logger.info("创建战斗服请求返回成功{}", sendBattleServerRequest);
            } else {
                logger.info("创建战斗服请求返回失败{}", sendBattleServerRequest);
            }
        }
    }

    private JSONObject sendBattleServerRequest(String url, String projectName, List<Integer> serverIds, int dbIndex) {
        JSONObject ret = new JSONObject();
        if (!JavaUtils.bool(serverIds)) {
            ErrorLogUtil.errorLog("创建战斗服serverIds==null", new RuntimeException());
            return ret;
        }
        StringJoiner joiner = new StringJoiner(",");
        serverIds.forEach(serverId -> joiner.add(String.valueOf(serverId)));
        Map<String, String> params = new HashMap<>();
        params.put(GVGBattleServerCreateConfig.PROJECT_NAME_KEY, projectName);
        params.put(GVGBattleServerCreateConfig.SID_KEY, joiner.toString());
        params.put(GVGBattleServerCreateConfig.DB_INDEX_KEY, String.valueOf(dbIndex));
        try {
            String post = HttpUtil.post(url, params);
            ret = JSONObject.parseObject(post);
        } catch (ExpectedException ignored){

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("发送GVG战斗服指令报错", e);
        }
        return ret;
    }

    /**
     * 第一次失败再次请求
     */
    public JSONObject reNoticeCreateBattleServer(List<Integer> serverIds, int dbIndex) {
        if (!JavaUtils.bool(serverIds)) {
            return new JSONObject();
        }
        logger.info("创建失败的战斗服查看状态：{}", serverIds);
        GVGBattleServerCreateConfig gvgBattleServerCreateConfig = ServerConfigManager.getInstance().getGvgBattleServerCreateConfig();
        String statusUrl = gvgBattleServerCreateConfig.getStatusUrl();
        if (statusUrl != null) {
            // {"status": "ok", "running": ["90001"], "pending": [], "destroy": []}
            JSONObject sendBattleServerRequest = sendBattleServerRequest(statusUrl, gvgBattleServerCreateConfig.getProjectName(), serverIds, dbIndex);
            logger.info("战斗服状态查看{}", sendBattleServerRequest);
            return sendBattleServerRequest;
        } else {
            logger.info("没有statusUrl，不用查状态");
            return new JSONObject();
        }
    }

    /**
     * 通知销毁战斗服
     */
    public void noticeDestroy(List<Integer> serverIds, int dbIndex) {
        if (!JavaUtils.bool(serverIds)) {
            return;
        }
        // 通知销毁
        logger.info("通知销毁战斗服{}", serverIds);
        GVGBattleServerCreateConfig gvgBattleServerCreateConfig = ServerConfigManager.getInstance().getGvgBattleServerCreateConfig();
        String destroyUrl = gvgBattleServerCreateConfig.getDestroyUrl();
        if (destroyUrl != null) {
            // {"status": "ok", "message": ["90001"]}
            // {"message":"deploy task running...","status":"error"}
            JSONObject sendBattleServerRequest = sendBattleServerRequest(destroyUrl, gvgBattleServerCreateConfig.getProjectName(), serverIds, dbIndex);
            logger.info("战斗服销毁{},{}", serverIds, sendBattleServerRequest);
            JSONObject jsonObject = reNoticeCreateBattleServer(serverIds, dbIndex);
            if (jsonObject.containsKey("status")) {
                JSONArray jsonArray = jsonObject.getJSONArray("destroy");
                if (jsonArray != null && jsonArray.size() > 0) {
                    for (Object obj : jsonArray) {
                        Long battleServerId = Long.valueOf(obj.toString());
                        gvgBattleServerDispatchRecordDao.delete(battleServerId);
                        logger.info("删除GvgBattleServerDispatchRecord记录{}", battleServerId);
                    }
                }
            }
        } else {
            logger.info("没有destroyUrl，不用查状态");
            serverIds.forEach(battleServerId -> gvgBattleServerDispatchRecordDao.delete(Long.valueOf(battleServerId)));
        }
    }

    /**
     * 更新分配服务器信息并广播，战斗服启服反注册
     * <p>
     * 注意：这时战斗服rpc还没有建立，所以不要去连
     *
     * @param battleServerId
     */
    public GvgBattleServerDispatchRecordVo updateAndBroadcastGvgBattleServerDispatchRecord(int battleServerId) {
        GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord = gvgBattleServerDispatchRecordDao.findById(Long.valueOf(battleServerId));
        if (gvgBattleServerDispatchRecord == null) {
            // 战斗服起来注册，发现中控服没有信息
            ErrorLogUtil.errorLog("战斗服起来注册，发现中控服没有信息", "battleServerId",battleServerId);
            return null;
        } else {
            logger.info("战斗服启动注册:{}", battleServerId);
            gvgBattleServerDispatchRecord.setStatus(GvgBattleServerDispatchRecord.STATUS_START);
            gvgBattleServerDispatchRecordDao.save(gvgBattleServerDispatchRecord);
            // 广播分配的服务器信息给game服
            gvgControlBroadcastToGameService.broadcastGvgBattleServerDispatchRecord(gvgBattleServerDispatchRecord);
            return new GvgBattleServerDispatchRecordVo(gvgBattleServerDispatchRecord);
        }
    }


    private GVGAllianceSignUpInfoVo checkGVGAllianceSignUp(int serverId, GvgMatchType matchType, boolean skipStatus) {
        GVGAllianceSignUpInfoVo gvgAllianceSignUpInfoVo = new GVGAllianceSignUpInfoVo();
        gvgAllianceSignUpInfoVo.setMatchType(matchType);
        if (matchType == GvgMatchType.FRIENDLY) {
            GVGQualifiedServer gvgQualifiedServer = gvgQualifiedServerDao.findById((long) serverId);
            if (gvgQualifiedServer == null) {
                ErrorLogUtil.errorLog("没有资格报名", "serverId",serverId);
                // 这个服务器不允许参赛
                gvgAllianceSignUpInfoVo.setErrorCode(3);
                return gvgAllianceSignUpInfoVo;
            }
        }

        // 判断报名状态
        Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG);
        if (null == activity) {
            ErrorLogUtil.errorLog("活动不存在", "serverId",serverId);
            gvgAllianceSignUpInfoVo.setErrorCode(3);
            return gvgAllianceSignUpInfoVo;
        }

        GVGActivityContext activityContext = activity.getActivityContext();
        GVGActivityStatus gvgActivityStatus = activityContext.getGvgActivityStatus();
        if (matchType == GvgMatchType.FRIENDLY) {
            if (gvgActivityStatus == null || (gvgActivityStatus != GVGActivityStatus.REGISTER && !skipStatus)) {
                ErrorLogUtil.errorLog("不在报名阶段", "gvgActivityStatus",gvgActivityStatus);
                gvgAllianceSignUpInfoVo.setErrorCode(4);
                return gvgAllianceSignUpInfoVo;
            }
        } else {
            if (gvgActivityStatus == null
                    || (gvgActivityStatus != GVGActivityStatus.SIGNUP && gvgActivityStatus != GVGActivityStatus.READY && gvgActivityStatus != GVGActivityStatus.REGISTER && gvgActivityStatus != GVGActivityStatus.ADMITTANCE && gvgActivityStatus != GVGActivityStatus.ADMITTANCE_ADVANCE)) {
                ErrorLogUtil.errorLog("不在报名阶段", "gvgActivityStatus",gvgActivityStatus);
                gvgAllianceSignUpInfoVo.setErrorCode(4);
                return gvgAllianceSignUpInfoVo;
            }
        }

        return gvgAllianceSignUpInfoVo;
    }

    /**
     * 更新或新建联盟基础信息
     *
     * @param allianceBaseDataVo
     */
    public void updateAllianceBaseData(AllianceBaseDataVo allianceBaseDataVo) {
        logger.info("GVG 收到联盟基本信息 [{}]", allianceBaseDataVo.getAllianceId());
        // 存库
        AllianceBaseData allianceBaseData = allianceBaseDataDao.findById(allianceBaseDataVo.getAllianceId());
        if (allianceBaseData == null) {
            allianceBaseData = allianceBaseDataDao.create(allianceBaseDataVo);
        } else {
            allianceBaseData.copyAllianceBaseData(allianceBaseDataVo);
            allianceBaseDataDao.save(allianceBaseData);
        }
    }

    public boolean findGVGServerQualified(int serverId) {
        GVGQualifiedServer gvgQualifiedServer = gvgQualifiedServerDao.findById(Long.valueOf(serverId));
        return gvgQualifiedServer != null;
    }

    /**
     * 读表，获取gvg杯赛入围联盟名单 处理apply信息中的状态。 通知各game服
     */
    public void updateGvgCupGroupListFromMeta() {
        // 按服务器分组的入围联盟id数据， <服务器ID,List<vo>>
        Map<Integer, List<GvgCupApplyInfoVO>> res = new HashMap<>();
        logger.info("GvgCup GVG杯赛入围数据读取成功，处理中。。。");
        // 1.取数据表数据
        Map<Integer, List<TwoTuple<Integer, Long>>> all = configService.getConfig(GvgCupGroupListConfig.class).getAll();
        // 2.遍历表中联盟，将其参赛状态更改为已入围。/或者已淘汰
        for (Integer status : all.keySet()) {
            for (TwoTuple<Integer, Long> one : all.get(status)) {
                GvgCupApplyInfo entity = gvgCupApplyInfoDao.findById(one.getSecond());
                if (entity == null) {
                    //热更报名入围信息，如果库里不存在，说明没报名，不予处理。
                    ErrorLogUtil.errorLog("GvgCup GVG杯赛入围数据处理,联盟没有报名记录不予处理", "allianceId",one.getSecond());
//                    entity = gvgCupApplyInfoDao.create(one.getFirst(), one.getSecond());
                    continue;
                } else {
                    // 跳过状态没变化的
                    if (entity.getStatusOfParticipation() == status) {
                        logger.info("GvgCup 报名信息存在，但是状态没变 跳过 联盟id {}", entity.getAllianceId());
                        continue;
                    }
                }
                // 设置已入围
                entity.setStatusOfParticipation(status);
                gvgCupApplyInfoDao.save(entity);

                // 按照联盟id分组， 将入围数据存进res，供后续使用
                if (!res.containsKey(one.getFirst())) {
                    res.put(one.getFirst(), new ArrayList<>());
                }
                res.get(one.getFirst()).add(new GvgCupApplyInfoVO(entity));
            }
        }
        // 3.通知game服更新其apply数据
        gvgControlBroadcastToGameService.broadcastGvgCupApplyInfo(res);
        logger.info("GvgCup GVG杯赛入围数据，处理完毕。");
    }

    //轮空时 策划填写的假联盟id
    private final int BYE_FAKE_ALLIANCE_ID = -404;

    /**
     * 人工上传表，初始化本周比赛的对阵数据。
     * <p>
     * TODO 评估这个操作是否需要做成异步的
     */
    public void updateGvgCupMatchScheduleFromMeta() {
        logger.info("GvgCup 开始处理。GVG杯赛对阵数据");
        // if (!gvgGameService.isGvgCupStart()) {
        // return;
        // }
        // 本动作在一周内只能执行一次，
        // 因为和自动生成对阵数据有冲突， 暂时屏蔽检测。 后期需要在signupInfo增加字段用于检测。
//        Collection<GVGAllianceSignUpInfo> allSignUpInfo = gvgAllianceSignUpInfoDao.findAll();
//        // 是否执行过读表生成报名数据
//        boolean executed = false;
//        for (GVGAllianceSignUpInfo info : allSignUpInfo) {
//            if (info.getMatchType() != GvgMatchType.FRIENDLY) {
//                executed = true;
//                break;
//            }
//        }
//        if (executed) {
//            logger.info("GVG杯赛 人工导表《本周对阵数据》,已经执行过，不能重复执行。。。。。。。。。。。。");
//            return;
//        } else {
//            logger.info("GVG杯赛 人工导表 本周第一次加载处理 开始。");
//
//        }
        // 读表
        Collection<GvgCupGroupSettingMeta> all = configService.getConfig(GvgLeagueGroupSettingConfig.class).getAll();
        if (all == null || all.size() == 0) {
            logger.info("GvgCupGroupSettingMeta表中没有读到新数据！！！");
            return;
        }
        // 校验是否有联盟配置了重复比赛
        if (!validateGvgCupGroupSettingMeta(all)) {
            ErrorLogUtil.errorLog("GvgCup 热更新对阵表失败");
            return;
        }
        // 此处create 对普通gvg那边的影响
        // 按周取， 如果没有再创建 保证 gvg杯赛 和 gvg 同一周的turn相同
        int week = GVGActivityHandler.getGVGTurnByYearAndWeek();
        GVGActivityTurn gvgActivityTurn = gvgActivityTurnDao.findByWeek(week);
        if (gvgActivityTurn == null) {
            logger.info("GvgCup turn不存在 创建");
            gvgActivityTurn = gvgActivityTurnDao.create(week);
        } else {
            logger.info("GvgCup turn已存在 直接使用");
        }
        // 比赛总场次
        int size = all.size();
        logger.info("GvgCup 本周共：" + size + "场比赛 ");
        List<Integer> canUseBattleServerIds = getCanUseBattleServerIds(size);
        logger.info("GvgCup 预分配战斗服id{}", canUseBattleServerIds);
        List<Integer> tempBattleServerIds = new ArrayList<>(canUseBattleServerIds);
        List<Integer> battleServerIds = new ArrayList<>();
        // 遍历数据为每条数据生成红蓝双方两个SignUpInfo，以及battleServerDispatchRecord
        int counter = 0;
        // 设置比较靠后的时间， 防止record被 ticker 清掉
        long delayBattleTime = TimeUtil.getNow() + (7 * 24 * 3600 * 1000L);
        long delayDestoryTime = delayBattleTime + (24 * 3600 * 1000L);
        Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG);
        for (GvgCupGroupSettingMeta meta : all) {
            logger.info("GvgCup,第 {} 场,redId:{}, blueId:{}, warZoneId:{}, matchType:{}", ++counter, meta.getRedAlliance(), meta.getBlueAlliance(), meta.getWarZoneId(), meta.getWeek());
            // 轮空情况处理。
            if (meta.getBlueAlliance().intValue() == BYE_FAKE_ALLIANCE_ID) {
                logger.info("GvgCup -404 轮空处理");
                GVGAllianceSignUpInfo redInfo = gvgAllianceSignUpInfoDao.create(meta.getRedAlliance(), meta.getRedServerId(), meta.getWeek(), meta.getGameRound(), meta.getRound(), meta.getWarZoneId());
                redInfo.setBye(true);
                gvgAllianceSignUpInfoDao.save(redInfo);
                GVGActivityContext gvgActivityContext = activity.getActivityContext();
                GVGBattleRecord battleRecord = gvgBattleRecordDao.create(-404L, 0, 0, meta.getRedAlliance(), 0, meta.getRedServerId(), activity.getId(), gvgActivityContext.getRound(),
                        meta.getGameRound(), meta.getWeek(), meta.getRound(), meta.getWarZoneId());
                // 广播
                gvgControlBroadcastToGameService.broadcastGVGBattleRecord(new GVGBattleRecordVo(battleRecord));
                battleRecord.setBye(true);
                gvgBattleRecordDao.save(battleRecord);
                logger.info("GvgCup 对阵 联盟{}轮空，直接胜利。。。 ", meta.getRedAlliance());
                //轮空处理完毕跳出当前循环
                continue;
            }
            GVGAllianceSignUpInfo redInfo = gvgAllianceSignUpInfoDao.create(meta.getRedAlliance(), meta.getRedServerId(), meta.getWeek(), meta.getGameRound(), meta.getRound(), meta.getWarZoneId());
            GVGAllianceSignUpInfo blueInfo = gvgAllianceSignUpInfoDao.create(meta.getBlueAlliance(), meta.getBlueServerId(), meta.getWeek(), meta.getGameRound(), meta.getRound(), meta.getWarZoneId());
            Integer battleServerId = tempBattleServerIds.remove(0);
            battleServerIds.add(battleServerId);
            // broadcast allianceBaseInfo，各联盟在apply时上报的base信息，此时进行下发
            AllianceBaseData redAllianceBaseData = allianceBaseDataDao.findById(meta.getRedAlliance());
            gvgControlBroadcastToGameService.broadcastAllianceBaseData(meta.getBlueServerId(), redAllianceBaseData);
            AllianceBaseData blueAllianceBaseData = allianceBaseDataDao.findById(meta.getBlueAlliance());
            logger.info("GvgCup 广播联盟基本信息");
            gvgControlBroadcastToGameService.broadcastAllianceBaseData(meta.getRedServerId(), blueAllianceBaseData);

            GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord = gvgBattleServerDispatchRecordDao.findById(Long.valueOf(battleServerId));
            if (gvgBattleServerDispatchRecord != null) {
                ErrorLogUtil.errorLog("GvgCup battleServerDispatchRecord已经存在,战斗服已分配,注意",new RuntimeException(),
                "battleServerId",battleServerId);
                gvgBattleServerDispatchRecordDao.delete(Long.valueOf(battleServerId));
            } else {
                logger.info("GvgCup battleServerDispatchRecord不存在 创建");
            }
            gvgBattleServerDispatchRecord = gvgBattleServerDispatchRecordDao.create(Long.valueOf(battleServerId), redInfo.getAllianceId(), redInfo.getServerId(),
                    blueInfo.getAllianceId(), blueInfo.getServerId(), delayBattleTime, delayDestoryTime, gvgActivityTurn.getTurn(), meta.getGameRound(), meta.getWeek(), meta.getRound(), meta.getWarZoneId(), meta.getObId());

            logger.info("GvgCup 广播战斗分配记录 ");
            gvgControlBroadcastToGameService.broadcastGvgBattleServerDispatchRecord(gvgBattleServerDispatchRecord);

        }
        if (tempBattleServerIds.size() > 0) {
            // 申请多了
            canUseBattleServerIds.removeAll(tempBattleServerIds);
        }
        gvgActivityTurn.setBattleServerIds(battleServerIds);
        gvgActivityTurnDao.save(gvgActivityTurn);
        // 广播signUpInfo
        logger.info("GvgCup 广播报名信息 ");
        gvgControlBroadcastToGameService.broadcastGVGAllianceSignUpInfos();
        logger.info("GvgCup 读表自动分配对手，自动报名结束。");
    }

    /**
     * 校验对阵表， 同一天的比赛是否有重复的联盟id
     *
     * @param all
     * @return true-校验通过 false-校验失败
     */
    private boolean validateGvgCupGroupSettingMeta(Collection<GvgCupGroupSettingMeta> all) {
        // 数据校验， 联盟是否存在重复配置的情况 <比赛类型, 联盟id集合>
        Map<GvgMatchType, Set<Long>> validator = new HashMap<>();
        validator.put(GvgMatchType.CUP_SAT, new HashSet<>());
        validator.put(GvgMatchType.CUP_SUN, new HashSet<>());
        for (GvgCupGroupSettingMeta m : all) {
            Set<Long> val = validator.get(m.getWeek());

            if (!val.add(m.getRedAlliance())) {
                ErrorLogUtil.errorLog("GvgCup : 注意!GvgCupGroupSettingMeta表 比赛配重了","allianceId",m.getRedAlliance());
                return false;
            }
            if (m.getBlueAlliance().intValue() != BYE_FAKE_ALLIANCE_ID && !val.add(m.getBlueAlliance())) {
                ErrorLogUtil.errorLog("GvgCup : 注意!GvgCupGroupSettingMeta表 比赛配重了","allianceId",m.getBlueAlliance());
                return false;
            }
        }
        return true;
    }

    /**
     * 读表生成本周的杯赛对阵数据, 半自动
     */
    public void updateGvgCupGroupStageMatchList() {
        logger.info("GvgCup:读表生成本周杯赛小组赛对阵数据 开始.");
        long timer = TimeUtil.getNow();
        int week = GVGActivityHandler.getGVGTurnByYearAndWeek();
        GVGActivityTurn gvgActivityTurn = gvgActivityTurnDao.findByWeek(week);
        if (gvgActivityTurn == null) {
            logger.info("GvgCup: turn不存在 创建");
            gvgActivityTurn = gvgActivityTurnDao.create(week);
        } else {
            logger.info("GvgCup: turn已存在 直接使用");
        }
        GvgCupGroupListConfig groupConfig = configService.getConfig(GvgCupGroupListConfig.class);
        GvgCupGroupStageAutoMatchConfig autoMatchConfig = configService.getConfig(GvgCupGroupStageAutoMatchConfig.class);
        // 比赛总场次
        int size = 0;
        Map<Integer, Set<GvgCupGroupStageAutoMatchConfig.GvgCupGroupStageAutoMatchMeta>> warZoneMapper = autoMatchConfig.getWarZoneMapper();
        for (int warZoneId : warZoneMapper.keySet()) {
            int allianceMatchCount = warZoneMapper.get(warZoneId).size();
            Set<Integer> groupIdsInWarZone = groupConfig.getAllGroupsByWarZone(warZoneId);
            if (groupIdsInWarZone == null || groupIdsInWarZone.size() < 1) {
                continue;
            }
            int foo = allianceMatchCount * groupIdsInWarZone.size();
            size += foo;
            logger.info("GvgCup:本轮小组赛 {} 战区 有 {} 个小组 每组 {} 场比赛 小记 {} 场比赛/总记 {} 场比赛 ", warZoneId, groupIdsInWarZone.size(), allianceMatchCount, foo, size);
        }

        logger.info("GvgCup 本周共：" + size + "场比赛 ");
        if (size < 1) {
            ErrorLogUtil.errorLog("GvgCup 0场小组赛,不必生成了,return");
            return;
        }
        List<Integer> canUseBattleServerIds = getCanUseBattleServerIds(size);
        if (canUseBattleServerIds.size() < size) {
            throw new AlertException("GvgCup 预分配战斗服数量不足","requestNum",canUseBattleServerIds.size(),"needNum",size);
        }
        logger.info("GvgCup 预分配战斗服id{}", canUseBattleServerIds);
        List<Integer> tempBattleServerIds = new ArrayList<>(canUseBattleServerIds);
        List<Integer> battleServerIds = new ArrayList<>();
        // 遍历数据为每条数据生成红蓝双方两个SignUpInfo，以及battleServerDispatchRecord
        int counter = 0;
        // 设置比较靠后的时间， 防止record被 ticker 清掉
        long delayBattleTime = TimeUtil.getNow() + (7 * 24 * 3600 * 1000L);
        long delayDestoryTime = delayBattleTime + (24 * 3600 * 1000L);
        Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG);
        for (int warZoneId : warZoneMapper.keySet()) {
            for (GvgCupGroupStageAutoMatchConfig.GvgCupGroupStageAutoMatchMeta meta : warZoneMapper.get(warZoneId)) {
                int red = meta.getRed();
                int blue = meta.getBlue();
                for (int groupId : groupConfig.getAllGroupsByWarZone(warZoneId)) {
                    TwoTuple<Integer, Long> redAlliance = groupConfig.findAllianceByGroupAndSn(warZoneId, groupId, red);
                    TwoTuple<Integer, Long> blueAlliance = groupConfig.findAllianceByGroupAndSn(warZoneId, groupId, blue);
                    if (redAlliance == null || blueAlliance == null) {
                        //TODO 此处应该抛出异常， 打断操作。
                        ErrorLogUtil.errorLog("GvgCup: 一方不存在,跳过这场比赛", "warZone",warZoneId, "groupId",groupId,
                                "red",red, "blue",blue);
                        continue;
                    }
                    GVGAllianceSignUpInfo redInfo = gvgAllianceSignUpInfoDao.create(redAlliance.getSecond(), redAlliance.getFirst(), meta.getWeek(), meta.getGameRound(), meta.getRound(), meta.getWarZoneId());
                    GVGAllianceSignUpInfo blueInfo = gvgAllianceSignUpInfoDao.create(blueAlliance.getSecond(), blueAlliance.getFirst(), meta.getWeek(), meta.getGameRound(), meta.getRound(), meta.getWarZoneId());

                    Integer battleServerId = tempBattleServerIds.remove(0);
                    battleServerIds.add(battleServerId);
                    // broadcast allianceBaseInfo，各联盟在apply时上报的base信息，此时进行下发
                    AllianceBaseData redAllianceBaseData = allianceBaseDataDao.findById(redAlliance.getSecond());
                    AllianceBaseData blueAllianceBaseData = allianceBaseDataDao.findById(blueAlliance.getSecond());
                    gvgControlBroadcastToGameService.broadcastAllianceBaseData(redAlliance.getFirst(), blueAllianceBaseData);
                    gvgControlBroadcastToGameService.broadcastAllianceBaseData(blueAlliance.getFirst(), redAllianceBaseData);
                    logger.info("GvgCup 自动生成小组赛: battleServerId {}, red[{}] vs blue[{}] @{}", battleServerId, redAlliance.getSecond(), blueAlliance.getSecond(), meta.getWeek().name());

                    GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord = gvgBattleServerDispatchRecordDao.findById(Long.valueOf(battleServerId));
                    if (gvgBattleServerDispatchRecord != null) {
                        ErrorLogUtil.errorLog("GvgCup battleServerDispatchRecord已经存在,战斗服已分配,注意",new RuntimeException(),
                                "battleServerId",battleServerId);
                        gvgBattleServerDispatchRecordDao.delete(Long.valueOf(battleServerId));
                    } else {
                        logger.info("GvgCup battleServerDispatchRecord不存在 创建");
                    }
                    gvgBattleServerDispatchRecord = gvgBattleServerDispatchRecordDao.create(Long.valueOf(battleServerId), redInfo.getAllianceId(), redInfo.getServerId(),
                            blueInfo.getAllianceId(), blueInfo.getServerId(), delayBattleTime, delayDestoryTime, gvgActivityTurn.getTurn(), meta.getGameRound(), meta.getWeek(), meta.getRound(), meta.getWarZoneId(), "0");

                    logger.info("GvgCup  广播战斗分配记录 ");
                    gvgControlBroadcastToGameService.broadcastGvgBattleServerDispatchRecord(gvgBattleServerDispatchRecord);
                }
            }
        }
        if (tempBattleServerIds.size() > 0) {
            // 申请多了
            canUseBattleServerIds.removeAll(tempBattleServerIds);
        }
        gvgActivityTurn.setBattleServerIds(battleServerIds);
        gvgActivityTurnDao.save(gvgActivityTurn);
        // 广播signUpInfo
        logger.info("GvgCup 广播报名信息 ");
        gvgControlBroadcastToGameService.broadcastGVGAllianceSignUpInfos();

        logger.info("GvgCup:读表生成本周杯赛小组赛对阵数据 结束，耗时 {} ms.", TimeUtil.getNow() - timer);
    }


    /**
     * 生成杯赛小组赛入围数据， 异步处理入口
     *
     * @param warZoneId 目标战区id
     * @param size      小组赛入围联盟总数
     */
    public void settleGvgTournamentGroupList(int warZoneId, int size) {
        GVGControlService gvgControlService = this;
        asyncOperationService.execute(new AsyncIOThreadOperation() {
            @Override
            public boolean run() {
                gvgControlService.settleGvgTournamentGroupListHelper(warZoneId, size);
                return false;
            }

            @Override
            public void finish() {
            }
        });
    }

    /**
     * 生成杯赛小组赛入围数据-settle
     * 规则：战区内报名联盟按照服务器分组，按联盟战斗力倒序排名，取每组（也就是每个服务器）的前两名直接入围。已入围总数记为T；
     * 各组第二名以后的联盟重新根据战斗力倒序排列， 取前 size-T 个联盟 入围。
     * 策划人为控制，保证报名联盟总数必定>=size
     *
     * @param warZoneId 目标战区id
     * @param size      小组赛入围联盟总数
     */
    private void settleGvgTournamentGroupListHelper(int warZoneId, int size) {
        if (warZoneId <= 0 || size <= 0) {
            ErrorLogUtil.errorLog("GvgCup 小组赛入围名单处理：输入参数错误,,无法继续处理,退出",
                    "warZoneId",warZoneId, "size",size);
            throw new RuntimeException("GvgCup 小组赛入围资格：输入参数错误");
        }
        logger.info("GvgCup 小组赛入围名单处理：开始处理第{}战区。", warZoneId);
        long startTime = TimeUtil.getNow();
        Map<Integer, List<GvgCupApplyInfo>> all = gvgCupApplyInfoDao.findByWarZoneId(warZoneId);
        if (all == null || all.size() < 1) {
            ErrorLogUtil.errorLog("GvgCup 小组赛入围名单处理：报名数据不存在,无法继续处理,退出", "warZoneId",warZoneId);
            throw new AlertException("GvgCup 小组赛入围资格：报名数据不存在");
        }
        int appliedCount = all.size();
        //5.check if applied Alliances is enough to fill.
        if (size > appliedCount) {
            ErrorLogUtil.errorLog("GvgCup 小组赛入围资格：报名联盟数量不足", "curNum",appliedCount, "needNum",size);
            throw new AlertException("GvgCup 小组赛入围资格：报名联盟数量不足");
        }
        Map<Integer, Set<Long>> shortlist = new HashMap<>();
        //已入围联盟计数器
        int inCounter = 0;
        //1.rpc notice game servers to recalculate alliance battle power.
        //not top2 alliances,sort by battle power desc
        PriorityQueue<Long[]> sortedAllianceFightPowerData = new PriorityQueue<>((d1, d2) -> d2[1].compareTo(d1[1]));
        for (int serverId : all.keySet()) {
            Set<Long> allianceIds = all.get(serverId).stream().map(GvgCupApplyInfo::getAllianceId).collect(Collectors.toSet());
            Map<Long, Long[]> data = this.gvgControlBroadcastToGameService.getAllianceFightPower(serverId, allianceIds);
            shortlist.put(serverId, new HashSet<>());
            //排行前两名直接入围
            //3.get top 2 of each group(server), mark a count;
            for (int i = 0; i <= 1; ++i) {
                Long ranking = Long.valueOf(i);
                if (data.containsKey(ranking)) {
                    long allianceId = data.get(ranking)[0];
                    logger.info("GvgCup 小组赛入围资格：服务器 {},联盟 {}, 战力排名 {}, 直接入围", allianceId, (ranking + 1));
                    shortlist.get(serverId).add(allianceId);
                    ++inCounter;
                    data.remove(ranking);
                }
            }
            //4.group all other clan not in top2  then sort by battle power.
            data.values().forEach(sortedAllianceFightPowerData::offer);
        }

        //补齐入围联盟到size个。
        for (; inCounter < size; ++inCounter) {
            Long[] foo = sortedAllianceFightPowerData.poll();
            int serverId = foo[2].intValue();
            Long allianceId = foo[0];
            Long fightPower = foo[1];
            shortlist.get(serverId).add(allianceId);
            logger.info("GvgCup 小组赛入围资格：服务器 {},联盟 {}, 战力 {}, 入围", serverId, allianceId, fightPower);
        }
        //6.rpc notice game servers to update clan participation status."in"
        // 按服务器分组的联盟报名信息， <ServerId,List<GvgCupApplyInfoVO>>
        Map<Integer, List<GvgCupApplyInfoVO>> broadcastDataIn = new HashMap<>();
        for (int serverId : shortlist.keySet()) {
            List<GvgCupApplyInfoVO> voList = new ArrayList<>();
            broadcastDataIn.put(serverId, new ArrayList<>());
            shortlist.get(serverId).forEach(allianceId -> {
                GvgCupApplyInfo info = gvgCupApplyInfoDao.findById(allianceId);
                if (info != null) {
                    info.setStatusOfParticipation(1);
                    gvgCupApplyInfoDao.save(info);
                    voList.add(new GvgCupApplyInfoVO(info));
                }
            });
            broadcastDataIn.put(serverId, voList);
        }
        logger.info("GvgCup 小组赛入围资格：rpc 入围联盟报名信息");
        gvgControlBroadcastToGameService.broadcastGvgCupApplyInfo(broadcastDataIn);

        //rpc notice game "out"
        Map<Integer, List<GvgCupApplyInfoVO>> broadcastDataOut = new HashMap<>();
        //未入围联盟计数器
        int outCounter = 0;
        while (!sortedAllianceFightPowerData.isEmpty()) {
            Long[] foo = sortedAllianceFightPowerData.poll();
            int serverId = foo[2].intValue();
            Long allianceId = foo[0];
            Long fightPower = foo[1];
            GvgCupApplyInfo info = gvgCupApplyInfoDao.findById(allianceId);
            if (info != null) {
                info.setStatusOfParticipation(2);
                gvgCupApplyInfoDao.save(info);
                broadcastDataOut.compute(serverId, (k, v) -> v == null ? new ArrayList<>() : v).add(new GvgCupApplyInfoVO(info));
                ++outCounter;
            }
            logger.info("GvgCup 小组赛入围资格：服务器 {},联盟 {}, 战力 {}, 未入围", serverId, allianceId, fightPower);
        }
        logger.info("GvgCup 小组赛入围资格：rpc 未入围联盟报名信息");
        gvgControlBroadcastToGameService.broadcastGvgCupApplyInfo(broadcastDataOut);
        logger.info("GvgCup 小组赛入围资格：第{}战区处理完成, 报名{},晋级{},未晋级{},耗时 {} ms", warZoneId, appliedCount, inCounter, outCounter, TimeUtil.getNow() - startTime);
    }

    /**
     * 生成本周观战列表
     * <p>
     * 热更表[GvgObServer]  时会执行
     */
    public void createGvgObserveMatchList() {
        logger.info("GvgCup 开始执行生成本周观赛列表");
        GvgObServerConfig config = configService.getConfig(GvgObServerConfig.class);
        if (config == null) {
            ErrorLogUtil.errorLog("GvgCup GvgObServerConfig 为空");
            return;
        }

        //第1部分 通过ObId
        String[] obIds = config.getMatchFromObId();
        int counter = 0;
        if (obIds == null || obIds.length < 1) {
            logger.info("GvgCup obId 配置的观战场次数量 0");
        } else {
            for (String obId : obIds) {
                GvgBattleServerDispatchRecord record = gvgBattleServerDispatchRecordDao.findByObId(obId);
                if (record == null) {
                    logger.info("GvgCup obId:{} 未找到对应的战斗分配记录. 跳过这一场", obId);
                    continue;
                }
                GvgObserveMatch entity = gvgObserveMatchDao.findById(record.getBattleServerId());
                if (entity != null) {
                    gvgObserveMatchDao.delete(entity);
                    logger.info("GvgCup obId:{} 观赛数据已存在，删除旧的", obId);

                }
                gvgObserveMatchDao.create(record.getBattleServerId(), record.getWarZoneId());
                ++counter;
            }
            logger.info("GvgCup 通过obId 生成{}场观战比赛信息", counter);
        }
        //第2部分 通过battleServerId
        String[] battleServerIds = config.getMatchFromBattleServerId();
        int counter2 = 0;
        if (battleServerIds == null || battleServerIds.length < 1) {
            logger.info("GvgCup 通过battleServerId 配置的观战场次数量 0");
        } else {
            for (String battleServerId : battleServerIds) {
                GvgBattleServerDispatchRecord record = gvgBattleServerDispatchRecordDao.findByBattleServerId(battleServerId);
                if (record == null) {
                    logger.info("GvgCup battleServerId:{} 未找到对应的战斗分配记录. 跳过这一场", battleServerId);
                    continue;
                }
                GvgObserveMatch entity = gvgObserveMatchDao.findById(record.getBattleServerId());
                if (entity != null) {
                    gvgObserveMatchDao.delete(entity);
                    logger.info("GvgCup battleServerId:{} 观赛数据已存在，删除旧的", battleServerId);

                }
                gvgObserveMatchDao.create(record.getBattleServerId(), record.getWarZoneId());
                ++counter2;
            }

            logger.info("GvgCup 通过battleServerId 生成{}场观战比赛信息", counter2);
        }
        logger.info("GvgCup 总计本周共{}场观战", counter + counter2);
        //根据db中的场次 生成 vo；
        genGvgObserveDataFromDBThenBroadcastToGame();
    }

    public void genGvgObserveDataFromDBThenBroadcastToGame() {
        Map<Integer, List<GvgObMatchInfoVo>> res = new HashMap<>();
        GvgLeagueGroupSettingConfig config = configService.getConfig(GvgLeagueGroupSettingConfig.class);
        for (GvgObserveMatch match : gvgObserveMatchDao.findAll()) {
            GvgBattleServerDispatchRecord record = gvgBattleServerDispatchRecordDao.findByBattleServerId(String.valueOf(match.getPersistKey()));
            if (record == null) {
                ErrorLogUtil.errorLog("GvgCup: 没找到战斗服分配记录",
                        "battleServerId",match.getPersistKey(), "warZoneId",match.getWarZoneId());
                continue;
            }
            GvgObMatchInfoVo vo = getGvgObMatchInfoVo(config, record);
            if (vo == null) {
                ErrorLogUtil.errorLog("GvgCup:生成观战数据失败", "warZoneId",record.getWarZoneId(),
                        "battleServerId",record.getBattleServerId());
                continue;
            }
            res.compute(match.getWarZoneId(), (k, v) -> v == null ? new ArrayList<>() : v).add(vo);
        }
        this.gvgObMatchList = res;
        logger.info("GvgCup:gvg观赛内存数据 准备完毕");
        if (this.gvgObMatchList != null && this.gvgObMatchList.size() > 0) {
            logger.info("GvgCup 开始RPC广播观战数据列表到各个game服务");
            gvgControlBroadcastToGameService.noticeGameUpdateGvgObMatchList(this.gvgObMatchList);
        }
    }

    private GvgObMatchInfoVo getGvgObMatchInfoVo(GvgLeagueGroupSettingConfig gvgLeagueGroupSettingConfig, GvgBattleServerDispatchRecord record) {

        GvgObMatchInfoVo vo = new GvgObMatchInfoVo();
        vo.setBattleServerId(record.getBattleServerId().intValue());
        vo.setWarZoneId(record.getWarZoneId());
        vo.setBattleTime(record.getBattleStartTime());
        AllianceBaseData redAllianceBaseData = allianceBaseDataDao.findById(record.getAllianceId1());
        if (redAllianceBaseData == null) {
            logger.info("GvgCup  allianceId {} BaseData 找不到. 跳过这一场", record.getAllianceId1());
            return null;
        }
        vo.setRedInfo(new AllianceBaseDataVo(redAllianceBaseData));

        AllianceBaseData blueAllianceBaseData = allianceBaseDataDao.findById(record.getAllianceId2());
        if (blueAllianceBaseData == null) {
            logger.info("GvgCup  allianceId {} BaseData 找不到. 跳过这一场", record.getAllianceId2());
            return null;
        }
        vo.setBlueInfo(new AllianceBaseDataVo(blueAllianceBaseData));
        GvgCupGroupSettingMeta matchMeta = gvgLeagueGroupSettingConfig.findByObId(record.getObId());
        if (matchMeta == null) {
            logger.info("GvgCup  obId {}  找不到 对应meta 跳过这一场", record.getObId());
            return null;
        }
        vo.setMatchName(matchMeta.getMatchName());
        vo.setGameRound(record.getGameRound());
        GVGAllianceSignUpInfo redSignUpInfo = gvgAllianceSignUpInfoDao.findByMatchType(record.getMatchType(), record.getAllianceId1());
        if (redSignUpInfo == null) {
            logger.info("GvgCup allianceId {} ,matchType{} ,找不到报名信息", record.getAllianceId1(), record.getMatchType());
            return null;
        }
        vo.setRedSignupNumber(redSignUpInfo.getGvgAllianceLineUpInfo().getFormalMemberIds().size());

        GVGAllianceSignUpInfo blueSignUpInfo = gvgAllianceSignUpInfoDao.findByMatchType(record.getMatchType(), record.getAllianceId2());
        if (blueSignUpInfo == null) {
            logger.info("GvgCup allianceId {} ,matchType{} ,找不到报名信息", record.getAllianceId1(), record.getMatchType());
            return null;
        }
        vo.setBlueSignupNumber(blueSignUpInfo.getGvgAllianceLineUpInfo().getFormalMemberIds().size());
        vo.setBlueScore(0L);
        vo.setRedScore(0L);
        return vo;
    }

    public void handleGvgObserveResetWeekly() {
        gvgObserveMatchDao.deleteAll();
        gvgControlBroadcastToGameService.noticeGameDeletegvgObMatchList();
    }

    public void createGvgAdmissionPush() {
        gvgControlBroadcastToGameService.broadcastGvgadmissionPushCreate();
    }

    /*******************约战逻辑开始*********************/

    public PsRZEErrorCode checkRZECommonCondition(int serverId, Long allianceId) {
        // 判断服务器资格
        if (rzeQualifiedServerDao.findById(Long.valueOf(serverId)) == null) {
            return PsRZEErrorCode.SERVER_NOT_AUTH;
        }

        // 判断联盟势力值
        if (rzeAllianceInfoDao.findById(allianceId) == null) {
            return PsRZEErrorCode.PROSPERITY_NOT_EOUGH;
        }

        // 判断约战状态
        Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG_ENGAGEMENT);
        RZEActivityContext rzeActivityContext = activity.getActivityContext();
        RZEActivityStatus rzeActivityStatus = rzeActivityContext.getStatus();
        if (rzeActivityStatus == null || rzeActivityStatus != RZEActivityStatus.ENGAGE) {
            ErrorLogUtil.errorLog("不在约战阶段", "rzeActivityStatus",rzeActivityStatus == null ? "null" : rzeActivityStatus);
            return PsRZEErrorCode.NOT_ENGAGE_STATUS;
        }

        return PsRZEErrorCode.SUCCESS;
    }

    /**
     * 检查创建房间信息
     */
    private RZERoomInfoVo checkRZECreateRoom(RZEActivityContext rzeActivityContext, int serverId, boolean checkNum, int index) {
        RZERoomInfoVo rzeRoomInfoVo = new RZERoomInfoVo();
        rzeRoomInfoVo.setSignUpTimeIndex(index);

        // 该时间段是否已满
        if (checkNum) {
            Map<Integer, GVGActivitySignUpInfo> signUpInfos = rzeActivityContext.getSignUpInfos();
            GVGActivitySignUpInfo gvgActivitySignUpInfo = signUpInfos.get(index);
            if (gvgActivitySignUpInfo == null) {
                ErrorLogUtil.errorLog("约战的时间不是开战时间", "index",index);
                rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.ENGAGE_TIME_NULL);
                return rzeRoomInfoVo;
            }

            int signUpMax = gvgActivitySignUpInfo.getSignUpMax();
            if (signUpMax <= 0) {
                ErrorLogUtil.errorLog("约战时间人数上限小于0", "index",index, "signUpMax",signUpMax);
                rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.ENGAGE_NUM_ZERO);
                return rzeRoomInfoVo;
            }

            if (gvgActivitySignUpInfo.getSignUpNum() >= signUpMax) {
                ErrorLogUtil.errorLog("约战时间段已经满了-1", "signUpNum",gvgActivitySignUpInfo.getSignUpNum(),
                        "signUpMax",signUpMax, "index",index);
                rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.ENGAGE_MAX);
                return rzeRoomInfoVo;
            }

            List<RZERoomInfo> roomInfos = rzeRoomInfoDao.findBySelectTime(index);
            if (roomInfos.size() >= signUpMax) {
                ErrorLogUtil.errorLog("约战时间段已经满了-2", "roomInfosSize",roomInfos.size(),
                        "signUpMax",signUpMax, "index",index);
                rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.ENGAGE_MAX);
                return rzeRoomInfoVo;
            }

            // 占坑
            gvgActivitySignUpInfo.setSignUpNum(gvgActivitySignUpInfo.getSignUpNum() + 2);
        }

        return rzeRoomInfoVo;
    }

    /**
     * 上传创建房间信息
     *
     * @param allianceId
     * @param serverId
     * @return
     */
    public List<RZERoomInfoVo> updateRZECreateRoomInfo(Long roleId, Long allianceId, int serverId, int roomType, List<Integer> indexs) {
        List<RZERoomInfoVo> ret = new ArrayList<>();
        PsRZEErrorCode errorCode = checkRZECommonCondition(serverId, allianceId);
        if (errorCode != PsRZEErrorCode.SUCCESS) {
            RZERoomInfoVo rzeRoomInfoVo = new RZERoomInfoVo();
            rzeRoomInfoVo.setErrorCode(errorCode);
            ret.add(rzeRoomInfoVo);
            return ret;
        }

        Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG_ENGAGEMENT);
        RZEActivityContext rzeActivityContext = activity.getActivityContext();
        for (Integer index : indexs) {
            RZERoomInfoVo rzeRoomInfoVo = checkRZECreateRoom(rzeActivityContext, serverId, true, index);
            if (rzeRoomInfoVo.getErrorCode() == PsRZEErrorCode.SUCCESS) {
                RZERoomInfo rzeRoomInfo = rzeRoomInfoDao.create(allianceId, serverId);
                rzeRoomInfo.setSignUpTimeIndex(index);
                rzeRoomInfo.setRoomType(roomType);
                rzeRoomInfoDao.save(rzeRoomInfo);
                rzeRoomInfoVo.copy(rzeRoomInfo);
            }

            ret.add(rzeRoomInfoVo);
        }

        activityDao.save(activity);

        // 同步
        gvgControlBroadcastToGameService.broadcastRZEActivity(activity);
        return ret;
    }

    /**
     * 上传申请加入房间
     *
     * @param allianceId
     * @param serverId
     * @return
     */
    public RZERoomInfoVo updateRZEJoinRoom(Long roleId, Long allianceId, int serverId, Long roomId, String comment, Set<Long> removeRoomIds) {
        RZERoomInfoVo rzeRoomInfoVo = new RZERoomInfoVo();
        PsRZEErrorCode errorCode = checkRZECommonCondition(serverId, allianceId);
        if (errorCode != PsRZEErrorCode.SUCCESS) {
            rzeRoomInfoVo.setErrorCode(errorCode);
            return rzeRoomInfoVo;
        }

        // 判断房间记录是否存在
        RZERoomInfo rzeRoomInfo = rzeRoomInfoDao.findById(roomId);
        if (rzeRoomInfo == null) {
            rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.ROOM_NULL);
            return rzeRoomInfoVo;
        }

        // 判断房间状态
        if (rzeRoomInfo.getStatus() == 1) {
            // 已经匹配完成，不可在在申请
            rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.ROOM_IS_ENGAGE);
            return rzeRoomInfoVo;
        }

        rzeRoomInfo.getApplyList().put(allianceId, serverId);
        rzeRoomInfoDao.save(rzeRoomInfo);
        rzeRoomInfoVo.copy(rzeRoomInfo);

        // 把申请信息同步给创建者
        gvgControlBroadcastToGameService.broadcastRoomInfoToServer(rzeRoomInfoVo, rzeRoomInfo.getAllianceId(), rzeRoomInfo.getServerId());

        return rzeRoomInfoVo;
    }

    /**
     * 获取申请列表
     */
    public List<PsRoomApplyInfo> getApplyRoomList(long roleId, int type, Map<Long, Boolean> roomIds, Set<Long> removeRoomIds) {
        List<PsRoomApplyInfo> ret = new ArrayList<>();
        for (Long roomId : roomIds.keySet()) {
            RZERoomInfo rzeRoomInfo = rzeRoomInfoDao.findById(roomId);
            if (rzeRoomInfo == null) {
                ErrorLogUtil.errorLog("GVG约战：获取申请列表-房间信息为空", "roleId",roleId, "roomId",roomId);
                continue;
            }

            if (rzeRoomInfo.getStatus() != 0) {
                ErrorLogUtil.errorLog("GVG约战：获取申请列表-房间已经匹配", "roleId",roleId, "roomId",roomId,
                        "rzeRoomStatus",rzeRoomInfo.getStatus());
                continue;
            }

            if (type == 1) {
                if (roomIds.get(roomId)) {
                    // 1:他人申请自己创建的场次的
                    if (!JavaUtils.bool(rzeRoomInfo.getApplyList())) {
                        ErrorLogUtil.errorLog("GVG约战：获取申请列表-申请信息为空", "roleId",roleId, "roomId",roomId);
                        continue;
                    }

                    for (Long allianceId : rzeRoomInfo.getApplyList().keySet()) {
                        ret.add(toPsRoomApplyInfo(rzeRoomInfo.getId(), rzeRoomInfo.getSignUpTimeIndex(), allianceId));
                    }
                } else {
                    // 2:别人邀请我的
                    ret.add(toPsRoomApplyInfo(rzeRoomInfo.getId(), rzeRoomInfo.getSignUpTimeIndex(), rzeRoomInfo.getAllianceId()));
                }
            } else {
                // 我申请别人的
                ret.add(toPsRoomApplyInfo(rzeRoomInfo.getId(), rzeRoomInfo.getSignUpTimeIndex(), rzeRoomInfo.getAllianceId()));
            }
        }

        return ret;
    }

    private PsRoomApplyInfo toPsRoomApplyInfo(Long roomId, int timeIndex, Long allianceId) {
        PsRoomApplyInfo applyInfo = new PsRoomApplyInfo();
        applyInfo.setRoomId(roomId);
        applyInfo.setIndex(timeIndex);
        applyInfo.setAllianceId(String.valueOf(allianceId));

        AllianceBaseData allianceBaseData = allianceBaseDataDao.findById(allianceId);
        if (allianceBaseData != null) {
            applyInfo.setName(allianceBaseData.getAllianceName());
            applyInfo.setAliasName(allianceBaseData.getAllianceAlias());
            applyInfo.setCountry(allianceBaseData.getCountry());
            // 匹配分数
            applyInfo.setMatchScore(allianceBaseData.getFightPower());

            // 旗帜
            PsAllianceFlagInfo flagInfo = new PsAllianceFlagInfo();
            flagInfo.setBadge(allianceBaseData.getBadge());
            flagInfo.setBadgeColor(allianceBaseData.getBadgeColor());
            flagInfo.setBanner(allianceBaseData.getBanner());
            flagInfo.setBannerColor(allianceBaseData.getBannerColor());
            applyInfo.setFlagInfo(flagInfo);
        }

        return applyInfo;
    }

    /**
     * 获取创建房间列表
     */
    public List<PsRoomInfo> getCreateRoomList(long roleId, Set<Long> removeRoomIds) {
        List<PsRoomInfo> ret = new ArrayList<>();
        Collection<RZERoomInfo> roomInfos = rzeRoomInfoDao.findAll();
        if (JavaUtils.bool(roomInfos)) {
            for (RZERoomInfo roomInfo : roomInfos) {
                if (roomInfo.getStatus() != 1)
                    ret.add(roomInfo.toPsRoomInfo());
            }
        }
        return ret;
    }

    /**
     * 上传修改房间信息
     *
     * @param allianceId
     * @param serverId
     * @return
     */
    public List<RZERoomInfoVo> updateRZEModifyRoomInfo(Long roleId, Long allianceId, int serverId, List<Integer> indexs, Map<Long, Integer> delRoomList) {
        List<RZERoomInfoVo> ret = new ArrayList<>();
        PsRZEErrorCode errorCode = checkRZECommonCondition(serverId, allianceId);
        if (errorCode != PsRZEErrorCode.SUCCESS) {
            RZERoomInfoVo rzeRoomInfoVo = new RZERoomInfoVo();
            rzeRoomInfoVo.setErrorCode(errorCode);
            ret.add(rzeRoomInfoVo);
            return ret;
        }

        Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG_ENGAGEMENT);
        RZEActivityContext rzeActivityContext = activity.getActivityContext();
        Map<Integer, GVGActivitySignUpInfo> signUpInfos = rzeActivityContext.getSignUpInfos();

        // 判断修改的场次是否为同一天的
        int weekOfDay = 0;
        for (Integer index : indexs) {
            GVGActivitySignUpInfo gvgActivitySignUpInfo = signUpInfos.get(index);
            if (gvgActivitySignUpInfo == null) {
                ErrorLogUtil.errorLog("约战的时间不是开战时间", "index",index);
                RZERoomInfoVo rzeRoomInfoVo = new RZERoomInfoVo();
                rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.ENGAGE_TIME_NULL);
                ret.add(rzeRoomInfoVo);
                return ret;
            }

            int day = TimeUtil.getDayOfWeek(gvgActivitySignUpInfo.getSignUpTime());
            if (weekOfDay == 0) weekOfDay = day;

            if (weekOfDay != day) {
                ErrorLogUtil.errorLog("约战的时间不一致", "index",index, "weekOfDay",weekOfDay, "day",day);
                RZERoomInfoVo rzeRoomInfoVo = new RZERoomInfoVo();
                rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.NOT_SAME_DAY);
                ret.add(rzeRoomInfoVo);
                return ret;
            }
        }

        // 1:删除
        Collection<RZERoomInfo> roomInfos = rzeRoomInfoDao.findByAllianceId(allianceId);
        if (JavaUtils.bool(roomInfos)) {
            for (RZERoomInfo roomInfo : roomInfos) {
                GVGActivitySignUpInfo gvgActivitySignUpInfo = signUpInfos.get(roomInfo.getSignUpTimeIndex());
                if (TimeUtil.getDayOfWeek(gvgActivitySignUpInfo.getSignUpTime()) == weekOfDay && !indexs.contains(roomInfo.getSignUpTimeIndex())) {
                    delRoomList.put(roomInfo.getPersistKey(), roomInfo.getSignUpTimeIndex());
                }
            }
        }

        for (Long roomId : delRoomList.keySet()) {
            rzeRoomInfoDao.delete(roomId);
            logger.info("GVG约战：修改房间  删除场次:{}/{}   {}/{}", roleId, allianceId, roomId, delRoomList.get(roomId));
        }

        // 2:新增
        for (Integer index : indexs) {
            // 判断改场次是新增还是删除
            RZERoomInfo rzeRoomInfo = rzeRoomInfoDao.findAllianceRoomBySelectTime(allianceId, index);
            if (rzeRoomInfo == null) {
                // 新增
                RZERoomInfoVo rzeRoomInfoVo = checkRZECreateRoom(rzeActivityContext, serverId, true, index);
                if (rzeRoomInfoVo.getErrorCode() == PsRZEErrorCode.SUCCESS) {
                    rzeRoomInfo = rzeRoomInfoDao.create(allianceId, serverId);
                    rzeRoomInfo.setSignUpTimeIndex(index);
                    rzeRoomInfoDao.save(rzeRoomInfo);
                    rzeRoomInfoVo.copy(rzeRoomInfo);
                }

                ret.add(rzeRoomInfoVo);
            } else {
                ErrorLogUtil.errorLog("GVG约战:修改房间 玩家修改场次已经存在",
                        "roleId",roleId, "allianceId",allianceId,
                        "rzeRoomId",rzeRoomInfo.getPersistKey(), "index",index);
            }
        }

        activityDao.save(activity);

        // 同步
        gvgControlBroadcastToGameService.broadcastRZEActivity(activity);
        return ret;
    }

    /**
     * 更新房间选择的成员
     */
    public RZERoomInfoVo updateRoomAllianceMemberInfo(Long roleId, Long roomId, Long allianceId, int serverId, List<Long> formalMemberIds, List<Long> tempMemberIds) {
        RZERoomInfoVo rzeRoomInfoVo = new RZERoomInfoVo();
        PsRZEErrorCode errorCode = checkRZECommonCondition(serverId, allianceId);
        if (errorCode != PsRZEErrorCode.SUCCESS) {
            rzeRoomInfoVo.setErrorCode(errorCode);
            return rzeRoomInfoVo;
        }

        RZERoomInfo rzeRoomInfo = rzeRoomInfoDao.findById(roomId);
        if (rzeRoomInfo == null) {
            logger.info("GVG约战：玩家:{}/{} 选择成员 房间为空:{}", roleId, allianceId, roomId);
            rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.ROOM_NULL);
            return rzeRoomInfoVo;
        }

        if (rzeRoomInfo.getStatus() != 1 || !JavaUtils.bool(rzeRoomInfo.getAllianceId()) || !JavaUtils.bool(rzeRoomInfo.getEnemyAllianceId())) {
            logger.info("GVG约战：玩家:{}/{} 选择成员 当前状态:{} {}/{}未约战成功，不可选择成员",
                    roleId, allianceId, rzeRoomInfo.getStatus(), rzeRoomInfo.getAllianceId(), rzeRoomInfo.getEnemyAllianceId());
            rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.ROOM_NOT_ENGAGE);
            return rzeRoomInfoVo;
        }

        if (!rzeRoomInfo.getEnemyAllianceId().equals(allianceId) && !rzeRoomInfo.getAllianceId().equals(allianceId)) {
            logger.info("GVG约战：玩家:{}/{} 选择成员 场次:{}/{} 不属于该联盟",
                    roleId, allianceId, rzeRoomInfo.getAllianceId(), rzeRoomInfo.getEnemyAllianceId());
            rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.ROOM_NOT_BELONG_ALLIANCE);
            return rzeRoomInfoVo;
        }

        Long broadcastAllianceId;
        int broadcastServerId;
        // 设置参赛人员
        if (rzeRoomInfo.getAllianceId().equals(allianceId)) {
            rzeRoomInfo.setFormalMemberIds(formalMemberIds);
            rzeRoomInfo.setTempMemberIds(tempMemberIds);
            broadcastAllianceId = rzeRoomInfo.getEnemyAllianceId();
            broadcastServerId = rzeRoomInfo.getEnemyServerId();
        } else {
            rzeRoomInfo.setEnemyFormalMemberIds(formalMemberIds);
            rzeRoomInfo.setEnemyTempMemberIds(tempMemberIds);
            broadcastAllianceId = rzeRoomInfo.getAllianceId();
            broadcastServerId = rzeRoomInfo.getServerId();
        }

        rzeRoomInfoDao.save(rzeRoomInfo);
        rzeRoomInfoVo.copy(rzeRoomInfo);

        // 同步给对盟
        gvgControlBroadcastToGameService.broadcastRoomInfoToServer(rzeRoomInfoVo, broadcastAllianceId, broadcastServerId);

        return rzeRoomInfoVo;
    }

    /**
     * 邀请联盟
     */
    public List<RZERoomInfoVo> updateInviteJoinRoom(Long roleId, Long allianceId, int serverId, List<PsRoomInviteInfo> inviteInfos, Set<Long> removeRoomIds) {
        List<RZERoomInfoVo> ret = new ArrayList<>();

        // 通用检查
        PsRZEErrorCode errorCode = checkRZECommonCondition(serverId, allianceId);
        if (errorCode != PsRZEErrorCode.SUCCESS) {
            RZERoomInfoVo rzeRoomInfoVo = new RZERoomInfoVo();
            rzeRoomInfoVo.setErrorCode(errorCode);
            ret.add(rzeRoomInfoVo);
            return ret;
        }

        for (PsRoomInviteInfo inviteInfo : inviteInfos) {
            RZERoomInfoVo rzeRoomInfoVo = new RZERoomInfoVo();
            RZERoomInfo rzeRoomInfo = rzeRoomInfoDao.findById(inviteInfo.getRoomId());
            if (rzeRoomInfo == null) {
                ErrorLogUtil.errorLog("GVG约战：玩家邀请联盟,对应场次不存在",
                        "roleId",roleId, "allianceId",allianceId,
                        "roomId",inviteInfo.getRoomId());
                rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.ROOM_NULL);
                ret.add(rzeRoomInfoVo);
                continue;
            }

            rzeRoomInfoVo.setId(rzeRoomInfo.getId());
            rzeRoomInfoVo.setSignUpTimeIndex(rzeRoomInfo.getSignUpTimeIndex());

            // 判断是否为该联盟的场次
            if (rzeRoomInfo.getAllianceId().equals(allianceId)) {
                ErrorLogUtil.errorLog("GVG约战：玩家邀请联盟,不是玩家场次",
                        "roleId",roleId, "allianceId",allianceId,
                        "roomId",inviteInfo.getRoomId(), "allianceId",rzeRoomInfo.getAllianceId());
                rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.ROOM_NOT_BELONG_ALLIANCE);
                ret.add(rzeRoomInfoVo);
                continue;
            }

            // 状态
            if (rzeRoomInfo.getStatus() == 1 || JavaUtils.bool(rzeRoomInfo.getEnemyAllianceId())) {
                logger.info("GVG约战：玩家:{}/{} 选择成员 当前状态:{} 联盟双方:{}/{}约战成功，不可选择成员",
                        roleId, allianceId, rzeRoomInfo.getStatus(), rzeRoomInfo.getAllianceId(), rzeRoomInfo.getEnemyAllianceId());
                rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.ROOM_NOT_ENGAGE);
                ret.add(rzeRoomInfoVo);
                continue;
            }

            // 记录邀请列表
            rzeRoomInfo.getInviteList().put(inviteInfo.getTargetAllianceId(), inviteInfo.getTargetServerId());
            rzeRoomInfoDao.save(rzeRoomInfo);
            rzeRoomInfoVo.copy(rzeRoomInfo);

            // 邀请列表需要同步在对方服的缓存中
            gvgControlBroadcastToGameService.broadcastBeInviteInfoToServer(rzeRoomInfo.getPersistKey(), inviteInfo.getTargetAllianceId(), inviteInfo.getTargetServerId());

            ret.add(rzeRoomInfoVo);
        }

        return ret;
    }

    /**
     * 更新审核信息
     */
    public RZERoomInfoVo updateCheckApplyRoom(Long roleId, Long allianceId, int serverId, Long roomId, Long targetAllianceId, int type, Set<Long> removeRoomIds) {
        RZERoomInfoVo rzeRoomInfoVo = new RZERoomInfoVo();

        // 通用检查
        PsRZEErrorCode errorCode = checkRZECommonCondition(serverId, allianceId);
        if (errorCode != PsRZEErrorCode.SUCCESS) {
            rzeRoomInfoVo.setErrorCode(errorCode);
            return rzeRoomInfoVo;
        }

        RZERoomInfo rzeRoomInfo = rzeRoomInfoDao.findById(roomId);
        if (rzeRoomInfo == null) {
            ErrorLogUtil.errorLog("GVG约战：玩家审核申请,目标联盟对应场次不存在",
                    "roleId",roleId, "allianceId",allianceId,
                    "targetAllianceId",targetAllianceId, "roomId",roomId);
            rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.ROOM_NULL);
            return rzeRoomInfoVo;
        }

        // 获取审核类型
        if (rzeRoomInfo.getAllianceId().equals(allianceId) && rzeRoomInfo.getApplyList().containsKey(targetAllianceId)) {
            // 审核申请我创建的场次--目标为被操作的联盟
            int targetServerId = rzeRoomInfo.getApplyList().get(targetAllianceId);
            if (type == 1) {
                // 同意
                agree(rzeRoomInfo, targetAllianceId, targetServerId);
                rzeRoomInfoVo.copy(rzeRoomInfo);
            } else {
                // 拒绝
                // 广播
                List<Integer> broadcastServerIds = new ArrayList<>();
                broadcastServerIds.add(targetServerId);
                gvgControlBroadcastToGameService.broadcastRemoveApplyInfoToServer(rzeRoomInfo.getPersistKey(), broadcastServerIds);

                // 从申请列表删除
                rzeRoomInfo.getApplyList().remove(targetAllianceId);
                rzeRoomInfoDao.save(rzeRoomInfo);
                rzeRoomInfoVo.copy(rzeRoomInfo);
            }

            rzeRoomInfoVo.setBelong(0);
        } else if (rzeRoomInfo.getAllianceId().equals(targetAllianceId) && rzeRoomInfo.getInviteList().containsKey(allianceId)) {
            // 审核他人场次邀请我约战的--目标为我方操作的联盟
            int targetServerId = rzeRoomInfo.getInviteList().get(allianceId);
            if (type == 1) {
                // 同意
                agree(rzeRoomInfo, allianceId, serverId);
                rzeRoomInfoVo.copy(rzeRoomInfo);
            } else {
                // 拒绝
                // 从邀请列表删除
                rzeRoomInfo.getInviteList().remove(allianceId);
                rzeRoomInfoDao.save(rzeRoomInfo);
                rzeRoomInfoVo.copy(rzeRoomInfo);

                List<Integer> broadcastServerIds = new ArrayList<>();
                broadcastServerIds.add(serverId);
                gvgControlBroadcastToGameService.broadcastRemoveBeInviteInfoToServer(rzeRoomInfo.getPersistKey(), broadcastServerIds);
            }

            // 广播给创建场次的服
            gvgControlBroadcastToGameService.broadcastRoomInfoToServer(rzeRoomInfoVo, rzeRoomInfo.getAllianceId(), rzeRoomInfo.getServerId());

            rzeRoomInfoVo.setBelong(1);
        } else {
            ErrorLogUtil.errorLog("GVG约战：玩家房间审核类型错误",
                    "roleId",roleId, "allianceId",allianceId,
                    "roomId",roomId, "roomAllianceId",rzeRoomInfo.getAllianceId(), "targetAllianceId",targetAllianceId);
            rzeRoomInfoVo.setId(rzeRoomInfo.getId());
            rzeRoomInfoVo.setSignUpTimeIndex(rzeRoomInfo.getSignUpTimeIndex());
            rzeRoomInfoVo.setErrorCode(PsRZEErrorCode.CHECK_ERROR_TYPE);
            return rzeRoomInfoVo;
        }

        return rzeRoomInfoVo;
    }

    /**
     * 同意申请或者邀请
     */
    private void agree(RZERoomInfo rzeRoomInfo, Long targetAllianceId, int targetServerId) {
        // TODO:这个广播有无必要？尽量减少服务器压力
        // 1.申请列表
        // 同步给各个申请的服
        List<Integer> broadcastServerIds = new ArrayList<>();
        for (Integer applyServerId : rzeRoomInfo.getApplyList().values()) {
            if (!broadcastServerIds.contains(applyServerId)) {
                broadcastServerIds.add(applyServerId);
            }
        }
        gvgControlBroadcastToGameService.broadcastRemoveApplyInfoToServer(rzeRoomInfo.getPersistKey(), broadcastServerIds);

        // 申请列表清空
        rzeRoomInfo.getApplyList().clear();

        // 2.邀请列表
        // 同步给各个邀请的服
        broadcastServerIds.clear();
        for (Integer beInviteServerId : rzeRoomInfo.getInviteList().values()) {
            if (!broadcastServerIds.contains(beInviteServerId)) {
                broadcastServerIds.add(beInviteServerId);
            }
        }
        gvgControlBroadcastToGameService.broadcastRemoveBeInviteInfoToServer(rzeRoomInfo.getPersistKey(), broadcastServerIds);

        // 邀请列表清空
        rzeRoomInfo.getInviteList().clear();

        // 3.设置状态及逻辑
        rzeRoomInfo.setStatus(1);
        rzeRoomInfo.setEnemyAllianceId(targetAllianceId);
        rzeRoomInfo.setEnemyServerId(targetServerId);
        rzeRoomInfoDao.save(rzeRoomInfo);

        // + + + 删除对战双方同一天的场次，- - - 申请、邀请逻辑在再次请求是处理删除逻辑
        removeSameDayRoom(rzeRoomInfo.getSignUpTimeIndex(), rzeRoomInfo.getAllianceId(), rzeRoomInfo.getEnemyAllianceId());

        // 创建战场记录
        List<Integer> canUseBattleServerIds = getCanUseBattleServerIds(rzeRoomInfo.getInviteList().size() / 2);
        if (JavaUtils.bool(canUseBattleServerIds)) {
            logger.info("GVG约战申请战斗服:{}", canUseBattleServerIds.toString());

            int battleServerId = canUseBattleServerIds.get(0);
            logger.info("GVG约战成功，战场:{} 联盟1:{} 联盟2:{}", battleServerId, rzeRoomInfo.getAllianceId(), rzeRoomInfo.getEnemyAllianceId());

            GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord = gvgBattleServerDispatchRecordDao.findById(Long.valueOf(battleServerId));
            if (gvgBattleServerDispatchRecord != null) {
                ErrorLogUtil.errorLog("战斗服已分配,注意" ,new RuntimeException(),"battleServerId",battleServerId);
                gvgBattleServerDispatchRecordDao.delete(Long.valueOf(battleServerId));
            }

            // 战场开始和销毁时间
            Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG_ENGAGEMENT);
            RZEActivityContext rzeActivityContext = activity.getActivityContext();
            Map<Integer, GVGActivitySignUpInfo> signUpInfos = rzeActivityContext.getSignUpInfos();
            GVGActivitySignUpInfo gvgActivitySignUpInfo = signUpInfos.get(rzeRoomInfo.getSignUpTimeIndex());
            long signUpTime = gvgActivitySignUpInfo.getSignUpTime();
            GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
            long battleDestroyTime = gvgSettingConfig.getGVGBattleEndTime(signUpTime) + 10 * TimeUtil.MINUTE_MILLIS;

            // 创建战斗记录
            gvgBattleServerDispatchRecord = gvgBattleServerDispatchRecordDao.create(Long.valueOf(battleServerId), rzeRoomInfo.getAllianceId(),
                    rzeRoomInfo.getServerId(), rzeRoomInfo.getEnemyAllianceId(), rzeRoomInfo.getEnemyServerId(), signUpTime, battleDestroyTime,
                    0, 0, rzeRoomInfo.getMatchType(signUpTime), 0, 0, "");

            gvgBattleServerDispatchRecord.setRoomId(rzeRoomInfo.getPersistKey());
            gvgBattleServerDispatchRecordDao.save(gvgBattleServerDispatchRecord);

            // 广播给对方服务器，自己的联盟信息
            int alliance1ServerId = gvgBattleServerDispatchRecord.getAlliance1ServerId();
            int alliance2ServerId = gvgBattleServerDispatchRecord.getAlliance2ServerId();
            Long allianceId1 = gvgBattleServerDispatchRecord.getAllianceId1();
            AllianceBaseData allianceBaseData1 = allianceBaseDataDao.findById(allianceId1);
            gvgControlBroadcastToGameService.broadcastAllianceBaseData(alliance2ServerId, allianceBaseData1);

            Long allianceId2 = gvgBattleServerDispatchRecord.getAllianceId2();
            AllianceBaseData allianceBaseData2 = allianceBaseDataDao.findById(allianceId2);
            gvgControlBroadcastToGameService.broadcastAllianceBaseData(alliance1ServerId, allianceBaseData2);

            // 广播给游戏服
            gvgControlBroadcastToGameService.broadcastGvgBattleServerDispatchRecord(gvgBattleServerDispatchRecord);
        } else {
            ErrorLogUtil.errorLog("GVG约战成功,战场ID申请失败", "allianceId1",rzeRoomInfo.getAllianceId(),
                    "allianceId2",rzeRoomInfo.getEnemyAllianceId());
        }
    }

    /**
     * 获取匹配好的场次
     */
    public List<PsMatchRoomInfo> getMatchRoomList(Long roleId, Long allianceId, List<Long> roomIds) {
        List<PsMatchRoomInfo> matchRoomInfos = new ArrayList<>();
        for (Long roomId : roomIds) {
            RZERoomInfo rzeRoomInfo = rzeRoomInfoDao.findById(roomId);
            if (rzeRoomInfo == null) {
                ErrorLogUtil.errorLog("GVG约战：获取匹配好的场次信息,房间信息为空", "roleId",roleId, "roomId",roomId);
                continue;
            }

            if (!rzeRoomInfo.getAllianceId().equals(allianceId) && !rzeRoomInfo.getEnemyAllianceId().equals(allianceId)) {
                ErrorLogUtil.errorLog("GVG约战：获取匹配好的场次信息,房间不属于该玩家",
                        "roleId",roleId, "allianceId",allianceId, "roomId",roomId,
                        "roomAllianceId",rzeRoomInfo.getAllianceId(), "roomEnemyAllianceId",rzeRoomInfo.getEnemyAllianceId());
                continue;
            }

            matchRoomInfos.add(rzeRoomInfo.toPsMatchRoomInfo());
        }

        return matchRoomInfos;
    }

    /**
     * 删除同一天的场次
     */
    public void removeSameDayRoom(int roomIndex, Long allianceId1, Long allianceId2) {
        Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG_ENGAGEMENT);
        RZEActivityContext rzeActivityContext = activity.getActivityContext();
        Map<Integer, GVGActivitySignUpInfo> signUpInfos = rzeActivityContext.getSignUpInfos();

        // 获取这一场次的时间
        GVGActivitySignUpInfo gvgActivitySignUpInfo = signUpInfos.get(roomIndex);
        if (gvgActivitySignUpInfo == null) {
            ErrorLogUtil.errorLog("约战:删除同一天的场次 时间不是开战时间", "roomIndex",roomIndex);
            return;
        }
        int weekOfDay = TimeUtil.getDayOfWeek(gvgActivitySignUpInfo.getSignUpTime());

        // 获取这一时间的所有场次索引
        List<Integer> sameDayIndex = new ArrayList<>();
        for (Integer index : signUpInfos.keySet()) {
            gvgActivitySignUpInfo = signUpInfos.get(index);
            if (gvgActivitySignUpInfo == null) {
                ErrorLogUtil.errorLog("约战:删除同一天的场次 时间不是开战时间", "index",index);
                continue;
            }

            if (TimeUtil.getDayOfWeek(gvgActivitySignUpInfo.getSignUpTime()) == weekOfDay) {
                sameDayIndex.add(index);
            }
        }

        for (Integer index : sameDayIndex) {
            List<Long> removeIds = new ArrayList<>();
            Collection<RZERoomInfo> rzeRoomInfos = rzeRoomInfoDao.findByAllianceId(allianceId1);
            for (RZERoomInfo rzeRoomInfo : rzeRoomInfos) {
                if (rzeRoomInfo.getSignUpTimeIndex() == index && rzeRoomInfo.getStatus() != 1) {
                    removeIds.add(rzeRoomInfo.getPersistKey());
                    logger.info("GVG约战：联盟：{} 删除同一天的场次：{} {} {}", allianceId1, rzeRoomInfo.getId(), roomIndex, index);
                }
            }

            removeIds.forEach(roomId -> rzeRoomInfoDao.delete(roomId));
            removeIds.clear();

            if (JavaUtils.bool(allianceId2)) {
                rzeRoomInfos = rzeRoomInfoDao.findByAllianceId(allianceId2);
                for (RZERoomInfo rzeRoomInfo : rzeRoomInfos) {
                    if (rzeRoomInfo.getSignUpTimeIndex() == index && rzeRoomInfo.getStatus() != 1) {
                        removeIds.add(rzeRoomInfo.getPersistKey());
                        logger.info("GVG约战：联盟：{} 删除同一天的场次：{} {} {}", allianceId2, rzeRoomInfo.getId(), roomIndex, index);
                    }
                }

                removeIds.forEach(roomId -> rzeRoomInfoDao.delete(roomId));
            }
        }
    }

    public GVGAllianceLineUpInfo updateGVGAllianceLineUpInfo(Long roleId, Long allianceId, int serverId,
                                                             GVGAllianceLineUpInfo gvgAllianceLineUpInfo, GvgMatchType matchType) {
        GVGAllianceSignUpInfoVo gvgAllianceSignUpInfoVo = checkGVGAllianceSignUp(serverId, matchType, true);
        if (gvgAllianceSignUpInfoVo.getErrorCode() != 0) {
            return null;
        }
        GVGAllianceSignUpInfo gvgAllianceSignUpInfo = gvgAllianceSignUpInfoDao.findByMatchType(matchType, allianceId);
        if (gvgAllianceSignUpInfo == null) {
            return null;
        }

        // 设置参赛人员
        gvgAllianceSignUpInfo.setGvgAllianceLineUpInfo(gvgAllianceLineUpInfo);

        // 设置操作记录
        List<String> memberParam = gvgAllianceSignUpInfo.getSelectMemberParam();
        memberParam.clear();
        memberParam.add(roleId.toString());
        memberParam.add(String.valueOf(TimeUtil.getNow()));
        gvgAllianceSignUpInfoDao.save(gvgAllianceSignUpInfo);

        //返回
        return controlServerLineUpData(gvgAllianceLineUpInfo);
    }

    private GVGAllianceLineUpInfo controlServerLineUpData(GVGAllianceLineUpInfo gvgAllianceLineUpInfo) {
        GVGAllianceLineUpInfo gvgAllianceLineUpInfo_copy = new GVGAllianceLineUpInfo(gvgAllianceLineUpInfo.getAllianceId(), gvgAllianceLineUpInfo.getGvgMatchType());
        gvgAllianceLineUpInfo_copy.copy(gvgAllianceLineUpInfo);
        return gvgAllianceLineUpInfo_copy;
    }
}