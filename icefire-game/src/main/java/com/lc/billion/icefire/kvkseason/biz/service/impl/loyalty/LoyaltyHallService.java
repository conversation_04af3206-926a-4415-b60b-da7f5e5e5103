package com.lc.billion.icefire.kvkseason.biz.service.impl.loyalty;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.kvk.KVKSettingConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.manager.SoldierManager;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.soldier.SoldierUpdateReasonType;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.roles.RoleSeasonInfoDao;
import com.lc.billion.icefire.kvkseason.biz.model.KvkStage;
import com.lc.billion.icefire.kvkseason.biz.service.impl.kvk.KvkSeasonServiceImpl;
import com.lc.billion.icefire.protocol.GcLoyaltyHallInfo;
import com.longtech.ls.config.ServerType;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

// 忠义堂
@Service
public class LoyaltyHallService {
    private static final Logger logger = LoggerFactory.getLogger(LoyaltyHallService.class);

    @Autowired
    private RoleSeasonInfoDao roleSeasonInfoDao;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private RoleDao roleDao;
    @Autowired
    private KvkSeasonServiceImpl seasonService;
    @Autowired
    private SoldierManager soldierManager;

    public void onEnterWorld(Role role) {
        // 非赛季服 不需要做什么
        if (Application.getServerType() != ServerType.KVK_SEASON) {
            return;
        }

        // 登陆下发死兵信息
        sendSoldierList(role);
    }

    // 死兵信息发送
    public void sendSoldierList(Role role) {
        GcLoyaltyHallInfo msg = new GcLoyaltyHallInfo();
        var seasonInfo = roleSeasonInfoDao.findById(role.getRoleId());
        if (null == seasonInfo) {
            return;
        }

        // 本赛季死兵
        int season = Application.getSeason();
        var deads = seasonInfo.getSoldierDeads().get(season);
        if (null != deads) {
            for (var dead : deads.entrySet()) {
                msg.putToSoldierDeads(dead.getKey(), dead.getValue());
            }
        }
        Boolean isReward = seasonInfo.getSoldierReward().get(season);
        msg.setIsReward(null != isReward && isReward);
        role.send(msg);
    }

    // 领取死亡士兵
    public void solderReward(Role role) {
        // 战斗是否结束
        KvkStage kvkStage = seasonService.getKvkStage();
        if (KvkStage.SETTLEMENT != kvkStage) {
            logger.debug("solderReward role:{} dead soldier not settlement.", role.getRoleId());
            return;
        }

        var seasonInfo = roleSeasonInfoDao.findById(role.getRoleId());
        if (null == seasonInfo) {
            return;
        }

        int season = Application.getSeason();
        // 是否已经领取过了
        Boolean isReward = seasonInfo.getSoldierReward().get(season);
        if (null != isReward && isReward) {
            ErrorLogUtil.errorLog("solderReward dead soldier is reward", "roleId",role.getRoleId());
            return;
        }

        // 是否有伤兵记录
        var deads = seasonInfo.getSoldierDeads().get(season);
        if (JavaUtils.bool(deads)) {
            Map<String, Integer> reward = new HashMap<>();
            KVKSettingConfig kvkSettingConfig = configService.getConfig(KVKSettingConfig.class);
            for (var dead : deads.entrySet()) {
                reward.put(dead.getKey(), (int) (1.0 * dead.getValue() * kvkSettingConfig.getKvkDeadSoldiersReturn()));
            }
            soldierManager.add(role, reward, SoldierUpdateReasonType.LOYALTY_HALL_REWARD, Strings.EMPTY, true);
        }
        logger.info("solderReward role:{} dead soldier reward:{}.", role.getRoleId(), deads);

        seasonInfo.getSoldierReward().put(season, true);
        roleSeasonInfoDao.save(seasonInfo);

        // 同步客户端
        sendSoldierList(role);
    }

    // 添加赛季死兵
    public void addSoldierDeads(int serverId, long roleId, String soldierId, int deadCount) {
        if (deadCount <= 0) {
            return;
        }

        Role role = roleDao.findById(roleId);
        if (null == role) {
            return;
        }

        addSoldierDeads(serverId, role, soldierId, deadCount);
    }
    public void addSoldierDeads(int serverId, Role role, String soldierId, int deadCount) {
        if (serverId != Application.getServerId()) {
            // 只处理k服数据
            return;
        }

        // 是否是在战斗期发生
        KvkStage kvkStage = seasonService.getKvkStage();
        if (KvkStage.BATTLE != kvkStage) {
            logger.debug("addSoldierDeads role:{} dead soldier not battle.", role.getRoleId());
            return;
        }

        var seasonInfo = roleSeasonInfoDao.findById(role.getRoleId());
        if (null == seasonInfo) {
            return;
        }

        int season = Application.getSeason();
        var soldiers = seasonInfo.getSoldierDeads().computeIfAbsent(season, o -> new HashMap<>());
        soldiers.merge(soldierId, deadCount, Integer::sum);

        roleSeasonInfoDao.save(seasonInfo);
    }
}
