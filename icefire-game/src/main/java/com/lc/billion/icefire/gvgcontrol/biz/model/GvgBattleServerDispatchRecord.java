package com.lc.billion.icefire.gvgcontrol.biz.model;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;

/**
 * 
 * 战斗服分配记录
 * 
 * 战斗结束，战斗服关闭前通知中控服移除
 * 
 * <AUTHOR>
 * @date 2020/12/10
 */
public class GvgBattleServerDispatchRecord extends AbstractEntity {

	private static final long serialVersionUID = -7416533108333121773L;

	/**
	 * 分配
	 */
	public static final int STATUS_MATCH = 0;
	/**
	 * 通知
	 */
	public static final int STATUS_NOTICE = 1;
	/**
	 * 启动
	 */
	public static final int STATUS_START = 2;
	/**
	 * 结束
	 */
	public static final int STATUS_END = 3;
	/**
	 * 查询状态
	 */
	public static final int STATUS_RENOTICE = 4;

	@MongoId
	private Long battleServerId;// 战斗服id
	private Long allianceId1;// 参战联盟1联盟Id
	private int alliance1ServerId;// 参战联盟1的服务器id
	private Long allianceId2;// 参战联盟2联盟Id
	private int alliance2ServerId;// 参战联盟2的服务器id
	private long battleStartTime;// 战场开启时间
	// 0-分配，1-通知，2-启动，3-结束，4-再次通知
	private int status;
	private int battleTurn;
	// 战场销毁时间，战场结束后半小时
	private long battleDestroyTime;

	private GvgMatchType matchType;
	// 杯赛， 比赛weekofyear
	private int gameRound;
	//杯赛,比赛 轮次
	private int round;
	//杯赛，所属战区
	private int warZoneId;
	//杯赛，观战id
	private String obId;
	// GVG约战用：方便查找自己匹配成功的场次
	private Long roomId;

	@Override
	public String toString() {
		return "[battleServerId=" + battleServerId + ",allianceId1=" + allianceId1 + ",alliance1ServerId=" + alliance1ServerId + ",allianceId2=" + allianceId2
				+ ",alliance2ServerId=" + alliance2ServerId + ",battleStartTime=" + battleStartTime + ",status=" + status + "]";
	}

	@Override
	public void setPersistKey(Long id) {
		this.battleServerId = id;
	}

	@Override
	public Long getPersistKey() {
		return this.battleServerId;
	}

	@Override
	public Long getGroupingId() {
		return this.battleServerId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	public int getWarZoneId() {
		return warZoneId;
	}

	public void setWarZoneId(int warZoneId) {
		this.warZoneId = warZoneId;
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public Long getBattleServerId() {
		return battleServerId;
	}

	public void setBattleServerId(Long battleServerId) {
		this.battleServerId = battleServerId;
	}

	public Long getAllianceId1() {
		return allianceId1;
	}

	public void setAllianceId1(Long allianceId1) {
		this.allianceId1 = allianceId1;
	}

	public int getAlliance1ServerId() {
		return alliance1ServerId;
	}

	public void setAlliance1ServerId(int alliance1ServerId) {
		this.alliance1ServerId = alliance1ServerId;
	}

	public Long getAllianceId2() {
		return allianceId2;
	}

	public void setAllianceId2(Long allianceId2) {
		this.allianceId2 = allianceId2;
	}

	public int getAlliance2ServerId() {
		return alliance2ServerId;
	}

	public void setAlliance2ServerId(int alliance2ServerId) {
		this.alliance2ServerId = alliance2ServerId;
	}

	public boolean validate(Long allianceId) {
		return allianceId != null && (allianceId.equals(allianceId1) || allianceId.equals(allianceId2));
	}

	public long getBattleStartTime() {
		return battleStartTime;
	}

	public void setBattleStartTime(long battleStartTime) {
		this.battleStartTime = battleStartTime;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public int getBattleTurn() {
		return battleTurn;
	}

	public void setBattleTurn(int battleTurn) {
		this.battleTurn = battleTurn;
	}

	public long getBattleDestroyTime() {
		return battleDestroyTime;
	}

	public void setBattleDestroyTime(long battleDestroyTime) {
		this.battleDestroyTime = battleDestroyTime;
	}

	public GvgMatchType getMatchType() {
		return matchType;
	}

	public void setMatchType(GvgMatchType matchType) {
		this.matchType = matchType;
	}

	public int getGameRound() {
		return gameRound;
	}

	public void setGameRound(int gameRound) {
		this.gameRound = gameRound;
	}

	public int getRound() {
		return round;
	}

	public void setRound(int round) {
		this.round = round;
	}

	public String getObId() {
		return obId;
	}

	public void setObId(String obId) {
		this.obId = obId;
	}

	public Long getRoomId() {
		return roomId;
	}

	public void setRoomId(Long roomId) {
		this.roomId = roomId;
	}
}
