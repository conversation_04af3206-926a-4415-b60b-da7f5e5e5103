package com.lc.billion.icefire.gvgcontrol.biz.service.impl.activity.handler;

import com.lc.billion.icefire.core.common.RandomUtils;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.biz.config.ActivityListConfig;
import com.lc.billion.icefire.game.biz.config.ActivityListConfig.ActivityListMeta;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityTurnDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.GVGQualifiedAllianceDao;
import com.lc.billion.icefire.game.biz.manager.gvg.GVGGameDataVoManager;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityContext;
import com.lc.billion.icefire.game.biz.model.activity.ActivityTurn;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.game.biz.model.gvg.GVGAllianceLineUpInfo;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.ServerInfoServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.activity.AbstractActivityHandler;
import com.lc.billion.icefire.game.biz.service.impl.gvg.GVGGameService;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.rpc.impl.battle.BattleServiceImpl;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.msg.GameMessageConfigManager;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig.GvgActivityTime;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.*;
import com.lc.billion.icefire.gvgcontrol.biz.model.*;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.context.GVGActivityContext;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.gvg.GVGActivityStatus;
import com.lc.billion.icefire.gvgcontrol.biz.service.GVGControlBroadcastToGVGBattleService;
import com.lc.billion.icefire.gvgcontrol.biz.service.GVGControlBroadcastToGameService;
import com.lc.billion.icefire.gvgcontrol.biz.service.GVGControlService;
import com.lc.billion.icefire.protocol.GcGVGStageInfo;
import com.lc.billion.icefire.protocol.constant.PsGVGActivityStatus;
import com.lc.billion.icefire.protocol.constant.PsGvgLineUpStatusType;
import com.lc.billion.icefire.protocol.constant.PsGvgMatchType;
import com.lc.billion.icefire.protocol.structure.PsActivityInfo;
import com.lc.billion.icefire.rpc.vo.gvg.*;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.GameServerConfig;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import com.longtech.ls.zookeeper.KvkSeasonsConfig;
import com.simfun.sgf.common.tuple.Tuple;
import com.simfun.sgf.common.tuple.TwoTuple;
import com.simfun.sgf.utils.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
public class GVGActivityHandler extends AbstractActivityHandler<Activity> {
    @Autowired
    private GVGControlBroadcastToGameService gvgControlBroadcastToGameService;
    @Autowired
    private GVGControlBroadcastToGVGBattleService gvgControlBroadcastToGVGBattleService;
    @Autowired
    private GvgBattleServerDispatchRecordDao gvgBattleServerDispatchRecordDao;
    @Autowired
    private GvgAllianceRecordDao gvgAllianceRecordDao;
    @Autowired
    private GVGQualifiedServerDao gvgQualifiedServerDao;
    @Autowired
    private GVGAllianceSignUpInfoDao gvgAllianceSignUpInfoDao;
    @Autowired
    private GVGMatchResultBackupDao gvgMatchResultBackupDao;
    @Autowired
    private GVGControlService gvgControlService;
    @Autowired
    private AllianceBaseDataDao allianceBaseDataDao;
    @Autowired
    private GVGGameDataVoManager gvgDataVoManager;
    @Autowired
    private GVGActivityTurnDao gvgActivityTurnDao;
    @Autowired
    private GVGBattleRecordDao gvgBattleRecordDao;
    @Autowired
    private GvgRegisterInfoDao gvgRegisterInfoDao;
    @Autowired
    private GVGQualifiedAllianceDao gvgQualifiedAllianceDao;
    @Autowired
    private ActivityTurnDao activityTurnDao;
    @Autowired
    private GVGGameDataVoManager gvgGameDataVoManager;
    @Autowired
    private ServerInfoServiceImpl serverInfoService;
    @Autowired
    private GVGGameService gvgGameService;


    public static boolean skipTicker = false;

    @Override
    public void onAdd(Activity t) {
        asyncOperationService.execute(() -> {
            // 广播一次信息给所有game和战斗服
            broadcastGVGActivity(t);
            return false;
        });
    }

    @Override
    public ActivityType getType() {
        return ActivityType.GVG;
    }

    @Override
    public void tick(Activity activity, long now) {
        if (skipTicker) {
            return;
        }

        // GVG阶段处理
        GVGActivityContext gvgActivityContext = activity.getActivityContext();
        GVGActivityStatus gvgActivityStatus = gvgActivityContext.getGvgActivityStatus();
        if (gvgActivityStatus != null) {
            long nextGvgActivityStatusTime = gvgActivityContext.getNextGvgActivityStatusTime();
            if (nextGvgActivityStatusTime <= now) {
                gvgActivityStatusContinue(activity);
            }
        }
    }

    @Override
    public GcGVGStageInfo getActivityInfo(Role role, String activityTypeMetaId) {
        ActivityVo gvgActivityVo = gvgDataVoManager.findGvgActivityVo();
        if (gvgActivityVo == null) {
            return null;
        }

        GcGVGStageInfo gcGVGStageInfo = new GcGVGStageInfo();
        GVGActivityContext gvgActivityContext = gvgActivityVo.getActivityContext();
        if (gvgActivityContext == null || gvgActivityContext.getGvgActivityStatus() == null) {
            return null;
        }

        // 活动基础数据
        gcGVGStageInfo.setStatus(gvgActivityContext.getGvgActivityStatus().getGvgActivityStatus());
        gcGVGStageInfo.setNextStatusTime(gvgActivityContext.getNextGvgActivityStatusTime());
        gcGVGStageInfo.setRound(gvgActivityContext.getRound());

        // 战场开始时间下发。
        Map<GvgMatchType, Long> battleStartTimes = gvgActivityContext.getBattleStartTimes();
        if (JavaUtils.bool(battleStartTimes)) {
            Map<PsGvgMatchType,java.lang.Long> battleStartTimesMap = new HashMap<>();
            for (Entry<GvgMatchType, Long> entry : battleStartTimes.entrySet()) {
                battleStartTimesMap.put(entry.getKey().getPsState(), entry.getValue());
            }
            gcGVGStageInfo.setBattleStartTimes(battleStartTimesMap);
        }

        // 如果有报名数据，则返回阶段
        var signUpInfo = gvgDataVoManager.findGVGAllianceSignUpInfoVoByRole(role);
        if (signUpInfo != null) {
            gcGVGStageInfo.setMatchType(signUpInfo.getMatchType().getPsState());
        }

        fillRoleBattleInfo(gcGVGStageInfo, role, gvgActivityContext.getGvgActivityStatus());

        return gcGVGStageInfo;
    }

    public void fillRoleBattleInfo(GcGVGStageInfo gcGVGStageInfo, Role role, GVGActivityStatus gvgStatus) {
        if (role == null || !JavaUtils.bool(role.getAllianceId())) {
            return;
        }

        var signUpVo = gvgDataVoManager.findGVGAllianceSignUpInfoVoByRole(role);

        var allianceInfo = (signUpVo != null) ? gvgGameService.getQualifiedAlliance(role.getAllianceId(), signUpVo.getMatchType()): null;
        var dispatchVo = gvgDataVoManager.findGvgBattleServerDispatchRecordVo(role.getAllianceId(), role.getRoleId());
        var battleRecordVo = signUpVo != null ? gvgGameDataVoManager.findCurrentGVGBattleRecordVo(role.getAllianceId(), signUpVo.getMatchType()) : null;

        if (signUpVo != null) {
            gcGVGStageInfo.setMatchType(signUpVo.getMatchType().getPsState());
        }
        gcGVGStageInfo.setBattleServerId(dispatchVo != null ? dispatchVo.getBattleServerId(): 0);

        if(gvgStatus == GVGActivityStatus.READY || gvgStatus == GVGActivityStatus.REGISTER) {
            gcGVGStageInfo.setStatusType(PsGvgLineUpStatusType.NORMAL);
        } else {
            gcGVGStageInfo.setStatusType(getPsGvgLineUpStatusType(role, signUpVo));
        }

        gcGVGStageInfo.setIsInGvgEnd(gvgGameService.isGvgEnded(gvgStatus, allianceInfo, signUpVo, battleRecordVo));
    }

    private static PsGvgLineUpStatusType getPsGvgLineUpStatusType(Role role, GVGAllianceSignUpInfoVo signUpVo) {
        if (signUpVo == null) {
            return PsGvgLineUpStatusType.NORMAL;
        }

        GVGAllianceLineUpInfo gvgAllianceLineUpInfo = signUpVo.getGvgAllianceLineUpInfo();
        if (gvgAllianceLineUpInfo != null) {
            if (gvgAllianceLineUpInfo.getFormalMemberIds() != null && gvgAllianceLineUpInfo.getFormalMemberIds().contains(role.getRoleId())) {
                return PsGvgLineUpStatusType.FORMAL;
            } else if (gvgAllianceLineUpInfo.getTempMemberIds() != null && gvgAllianceLineUpInfo.getTempMemberIds().contains(role.getRoleId())) {
                return PsGvgLineUpStatusType.TEMPORARY;
            }
        }

        return PsGvgLineUpStatusType.NORMAL;
    }

    public void clearBattleInfo(GcGVGStageInfo gcGVGStageInfo) {
        gcGVGStageInfo.setBattleServerId(0);
        gcGVGStageInfo.setStatusType(PsGvgLineUpStatusType.NORMAL);
    }

    @Override
    protected void onStartImpl(Activity t) {
        asyncOperationService.execute(() -> {
            initStatusContinue(t);
            return false;
        });
    }

    @Override
    protected void onPreheatImpl(Activity t) {
        asyncOperationService.execute(() -> {
            initStatusContinue(t);
            return false;
        });
    }

    private void initStatusContinue(Activity activity) {
        // GVG阶段处理
        GVGActivityContext gvgActivityContext = activity.getActivityContext();
        GVGActivityStatus gvgActivityStatus = gvgActivityContext.getGvgActivityStatus();
        if (gvgActivityStatus == null) {
            gvgActivityStatusContinue(activity);
        }
    }
    @Override
    protected void onStopImpl(Activity t) {
        asyncOperationService.execute(() -> {
            // 通知战斗服及game服，活动结束
            AsyncOperation.logger.info("GVG活动结束了，广播GVG活动信息给game和战斗服");
//            settleByeAlliance(t);
            broadcastGVGActivity(t);
            clearActivityContext(t);
            return false;
        });
    }

    private void clearActivityContext(Activity t) {
        GVGActivityContext gvgActivityContext = t.getActivityContext();
        gvgActivityContext.setGvgActivityStatus(null);
        activityDao.save(t);
    }

    public void broadcastGVGActivity(Activity activity) {
        logger.info("广播GVG活动信息给game和战斗服");
        gvgControlBroadcastToGameService.broadcastGVGActivity(activity);
        gvgControlBroadcastToGVGBattleService.broadcastGVGActivity(activity);
    }

    private void afterGvgActivityStatusContinue(Activity activity) {
        activityDao.save(activity);
        broadcastGVGActivity(activity);
    }

    public void gvgActivityStatusContinue(Activity activity) {
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        List<GvgActivityTime> gvgActivityTimes = gvgSettingConfig.getGvgActivityTime();
        GVGActivityContext activityContext = activity.getActivityContext();
        // 当前阶段
        GVGActivityStatus currentGVGActivityStatus = activityContext.getGvgActivityStatus();
        // 将要进入的阶段
        GVGActivityStatus gvgActivityStatus = null;
        // 下一次的时间
        GvgActivityTime gvgActivityTime = null;
        // 是否开启新的一轮
        boolean newTurn = false;
        if (currentGVGActivityStatus == null) {
            onActivityStatusStart();
            // 没有状态，新建的
            gvgActivityStatus = GVGActivityStatus.READY;
            gvgActivityTime = gvgActivityTimes.get(0);
        } else {
            PsGVGActivityStatus nextGVGActivityStatus = currentGVGActivityStatus.getNextGvgActivityStatus();
            boolean isAdvanced = gvgSettingConfig.isGvgLegion2MatchStartEarly();
            switch (nextGVGActivityStatus) {
                case READY:// 同时也是gvg杯赛的约战阶段
                    newTurn = true;
                    gvgActivityTime = gvgActivityTimes.get(0);
                    break;
                case REGISTER:
                    gvgActivityTime = gvgActivityTimes.get(1);
                    break;
                case SIGNUP:
                    gvgActivityTime = isAdvanced ? gvgActivityTimes.get(2) : gvgActivityTimes.get(3);
                    break;
                case ADMITTANCE_ADVANCE:
                    gvgActivityTime = gvgActivityTimes.get(3);
                    if (!isAdvanced) {
                        nextGVGActivityStatus = PsGVGActivityStatus.ADMITTANCE;
                        gvgActivityTime = gvgActivityTimes.get(4);
                    }
                    break;
                case ADMITTANCE:
                    gvgActivityTime = gvgActivityTimes.get(4);
                    break;
                default:
                    ErrorLogUtil.errorLog("阶段出错", "nextGVGActivityStatus",nextGVGActivityStatus);
                    break;
            }
            gvgActivityStatus = GVGActivityStatus.findById(nextGVGActivityStatus.getValue());
        }
        Date nextDate = GvgSettingConfig.getGVGActivityNextDate(gvgActivityTime.getWeek(), gvgActivityTime.getLocalTime());
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(nextDate);
        if (newTurn) {
            // 本阶段结束，找下一阶段
            int gvgActivityRestTime = gvgSettingConfig.getGvgActivityRestTime();
            calendar.add(Calendar.WEEK_OF_YEAR, gvgActivityRestTime);
        }
        activityContext.setNextGvgActivityStatusTime(calendar.getTimeInMillis());
        activityContext.setGvgActivityStatus(gvgActivityStatus);
        logger.info("GVG活动流程：status={}，time={}", activityContext.getGvgActivityStatus(), new Date(activityContext.getNextGvgActivityStatusTime()));

        // 处理各个阶段
        handleActivityStatus(activity);
        activityDao.save(activity);
    }

    private void handleActivityStatus(Activity activity) {


        // 内部类，状态处理。
        class GvgActivityStatusOperation implements AsyncOperation {
            @Override
            public boolean run() {
                return true;
            }
            @Override
            public void finish() {
                afterGvgActivityStatusContinue(activity);
            }
        }

        GVGActivityContext activityContext = activity.getActivityContext();
        switch (activityContext.getGvgActivityStatus()) {
            case READY:// 同时也是gvg杯赛的约战阶段
                asyncOperationService.execute(new GvgActivityStatusOperation() {
                    @Override
                    public boolean run() {
                        //gvg 和 杯赛 的开始阶段， 主要进行数据清理和重置工作。
                        onActivityStatusReady(activityContext);
                        return true;
                    }
                });

                break;
            case REGISTER: //普通gvg报名 和 gvg杯赛约战阶段
                asyncOperationService.execute(new GvgActivityStatusOperation() {
                    @Override
                    public boolean run() {
                        handleGVGActivityStatusRegister();
                        return true;
                    }
                });

                break;
            case SIGNUP: //普通gvg约战阶段
                asyncOperationService.execute(new GvgActivityStatusOperation() {
                    @Override
                    public boolean run() {
                        handleGvgActivityStatusSignup(activity);
                        return true;
                    }
                });
                break;
            case ADMITTANCE_ADVANCE: //提前入场阶段
                asyncOperationService.execute(new GvgActivityStatusOperation() {
                    @Override
                    public boolean run() {
                        handleGvgInAdmittanceAdvance(activity);
                        return true;
                    }
                });
                break;
            case ADMITTANCE: //普通gvg 和 杯赛的入场阶段
                asyncOperationService.execute(new GvgActivityStatusOperation() {
                    @Override
                    public boolean run() {
                        handleGvgInAdmittance(activity);
                        // 入场阶段设置推送消息
                        gvgControlService.createGvgAdmissionPush();
                        return true;
                    }

                });
                break;
            default:
                break;
        }
    }

    /**
     * 军团2提前开放
     * @param activity
     */
    private void handleGvgInAdmittanceAdvance(Activity activity) {
        handleGvgInAdmittance(activity, GvgMatchType.FRIENDLY_TEAM2);
    }


    /**
     * 开始阶段的操作
     */
    private void onActivityStatusStart() {
    }

    /**
     * 准备阶段的操作
     */
    private void onActivityStatusReady(GVGActivityContext activityContext) {
        // 清理服务器分配信息--其实战斗结束就已经清了，这里再清一次保险
        Collection<GvgBattleServerDispatchRecord> gvgBattleServerDispatchRecords = gvgBattleServerDispatchRecordDao.findAll();
        if (JavaUtils.bool(gvgBattleServerDispatchRecords)) {
            List<Integer> battleServerIds = new ArrayList<>();
            gvgBattleServerDispatchRecords.forEach(record -> {
                battleServerIds.add(record.getBattleServerId().intValue());
            });
            gvgControlService.noticeDestroy(battleServerIds, 0);
            logger.info("[GVG] onActivityStatusReady, delete server: {}", battleServerIds);
        }

        // 清理上一轮的报名信息
        logger.info("[GVG] onActivityStatusReady delete register: {}", gvgRegisterInfoDao.getAllDatas());
        gvgRegisterInfoDao.deleteAll();

        logger.info("[GVG] onActivityStatusReady delete gvgAllianceSignUpInfo: {}", gvgAllianceSignUpInfoDao.findAll());
        gvgAllianceSignUpInfoDao.deleteAll();

        logger.info("[GVG] onActivityStatusReady broadcastGvgBattleServerDispatchRecordClear");
        gvgControlBroadcastToGameService.broadcastGvgBattleServerDispatchRecordClear(new GvgMatchType[]{GvgMatchType.FRIENDLY, GvgMatchType.FRIENDLY_TEAM2, GvgMatchType.CUP_SAT, GvgMatchType.CUP_SUN});

        logger.info("[GVG] onActivityStatusReady broadcastGvgAdmissionPushClear");
        gvgControlBroadcastToGameService.broadcastGvgAdmissionPushClear();

        logger.info("[GVG] onActivityStatusReady handleGvgObserveResetWeekly");
        gvgControlService.handleGvgObserveResetWeekly();

        ActivityTurn activityTurn = activityTurnDao.findByActivityType(ActivityType.GVG);
        int round = activityTurn == null ? 0 : activityTurn.getTurn();
        activityContext.setRound(round);
        logger.info("[GVG] onActivityStatusReady activityContext.setRound {}", round);

        // 选择本轮参赛服务器
        selectGVGQualifiedServer();

        logger.info("[GVG]onActivityStatusReady end");
    }

    private void selectGVGQualifiedServer() {
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG);
        ActivityListConfig activityListConfig = configService.getConfig(ActivityListConfig.class);
        ActivityListMeta activityListMeta = activityListConfig.getMetaById(activity.getMetaId());
        // 哪些服务器可以参赛
        Map<Integer, GameServerConfig> gameServers = configCenter.getLsConfig().getGameServers();
        KvkSeasonsConfig kvkSeasons = configCenter.getLsConfig().getKvkSeasons();

        for (GameServerConfig gameServerConfig : gameServers.values()) {
            int gameServerId = gameServerConfig.getGameServerId();
            if (!gameServerConfig.isAlive()) {
                ErrorLogUtil.errorLog("[GVG] selectGVGQualifiedServer ServerNotStart", "serverId", gameServerId);
                continue;
            }

            ServerType serverType = ServerConfigManager.getInstance().getServerTypeConfig().getServerType(gameServerId);
            if (serverType != ServerType.GAME && serverType != ServerType.KVK_SEASON) {
                // 非game服跳过
                logger.info("[GVG] selectGVGQualifiedServer skip serverType: {}, serverId: {}", serverType, gameServerId);
                continue;
            }

            // 区分原始服和赛季服
            switch (serverType) {
                case GAME:
                    handleGVGQualifiedServer(gameServerConfig, gvgSettingConfig, activityListMeta);
                    break;
                case KVK_SEASON:
                    KvkSeasonServerGroupConfig serverGroupByKServerId = kvkSeasons.getServerGroupByKServerId(gameServerId);
                    if (serverGroupByKServerId != null) {
                        Set<Integer> oServerIds = serverGroupByKServerId.getOServerIds();
                        if (JavaUtils.bool(oServerIds)) {
                            oServerIds.forEach(sid -> {
                                GameServerConfig gameServerConfig1 = gameServers.get(sid);
                                handleGVGQualifiedServer(gameServerConfig1, gvgSettingConfig, activityListMeta);
                            });
                        }
                    } else {
                        ErrorLogUtil.errorLog("[GVG] selectGVGQualifiedServer serverGroupByKServerId null", "gameServerId",gameServerId);
                    }
                    break;
                default:
                    ErrorLogUtil.errorLog("[GVG] selectGVGQualifiedServer errorType", new RuntimeException(),"gameServerId", gameServerId, "serverType", serverType);
                    break;
            }
        }
    }

    private void handleGVGQualifiedServer(GameServerConfig gameServerConfig, GvgSettingConfig gvgSettingConfig, ActivityListMeta activityListMeta) {
        int gameServerId = gameServerConfig.getGameServerId();

        // 没有资格
        if (!JavaUtils.bool(findGvgMatchGroupByServerId(gameServerId))) {
            logger.info("[GVG] handleGVGQualifiedServer serverNotQualified: serverId: {}", gameServerConfig.getGameServerId());
            return;
        }

        Map<Integer, Set<Long>> testMatchAllianceInfo = gvgSettingConfig.getTestMatchAllianceInfo();
        if (JavaUtils.bool(testMatchAllianceInfo)) {
            if (!testMatchAllianceInfo.containsKey(gameServerId)) {
                logger.info("[GVG] handleGVGQualifiedServer serverNotQualifiedTest: serverId: {}, testServer: {}", gameServerConfig.getGameServerId(), testMatchAllianceInfo.keySet());
                return;
            }
        }

        GVGQualifiedServer gvgQualifiedServer = gvgQualifiedServerDao.findById((long) gameServerId);
        boolean isBlockServer = activityListMeta.isBlockServer(gameServerId);
        boolean enabledServers = activityListMeta.isEnabledServers(gameServerId);
        if (gvgQualifiedServer == null) {
            if (isBlockServer) {
                logger.info("[GVG] handleGVGQualifiedServer serverInBlock {}", gameServerId);
                return;
            }
            if (!enabledServers) {
                logger.info("[GVG] handleGVGQualifiedServer serverNotInEnable {}", gameServerId);
                return;
            }

            gvgQualifiedServerDao.create(gameServerId);
            logger.info("[GVG] handleGVGQualifiedServer newServerQualified {}", gameServerId);
        } else {
            // 之前有资格，但这次可能不在白名单里或者在黑名单里
            if (isBlockServer || !enabledServers) {
                gvgQualifiedServerDao.delete(gvgQualifiedServer);
                logger.info("[GVG] handleGVGQualifiedServer serverInBlock or notInEnable remove {}", gameServerId);
            } else if (!createValidated(activityListMeta, gameServerId)) {
                logger.info("[GVG] handleGVGQualifiedServer createValidated {} ", gameServerId);
                gvgQualifiedServerDao.delete(gvgQualifiedServer);
            } else {
                logger.info("[GVG] handleGVGQualifiedServer oldServerQualified {}", gameServerId);
            }
        }
    }

    /**
     * 报名阶段处理
     */
    private void handleGVGActivityStatusRegister() {
        logger.info("[GVG] handleGVGActivityStatusRegister begin");
        // 通知所有参赛服选择势力值达标的联盟
        Map<Integer, Set<AllianceBaseDataVo>> res = gvgControlBroadcastToGameService.broadcastNoticeSelectGVGAlliance();
        logger.info("[GVG] handleGVGActivityStatusRegister broadcastNoticeSelectGVGAlliance: {}", res);

        Map<Long, Integer> testMatchAllianceIndexInfo = configService.getConfig(GvgSettingConfig.class).getTestMatchAllianceIndexInfo();
        for (Entry<Integer, Set<AllianceBaseDataVo>> entry : res.entrySet()) {
            int serverId = entry.getKey();
            for (AllianceBaseDataVo allianceBaseDataVo : entry.getValue()) {
                Long allianceId = allianceBaseDataVo.getAllianceId();
                if (JavaUtils.bool(testMatchAllianceIndexInfo) && !testMatchAllianceIndexInfo.containsKey(allianceId)) {
                    ErrorLogUtil.errorLog("[GVG] handleGVGActivityStatusRegister allianceNotInTestList", "allianceId",allianceId);
                    continue;
                }

                if (gvgRegisterInfoDao.findById(allianceId) == null) {
                    logger.info("[GVG] handleGVGActivityStatusRegister automatic register, serverId{}, allianceId{}", serverId, allianceId);
                    gvgRegisterInfoDao.create(serverId, allianceId);
                }
                logger.info("[GVG] handleGVGActivityStatusRegister updateAllianceBaseData {}", allianceId);
                gvgControlService.updateAllianceBaseData(allianceBaseDataVo);
            }
        }
        logger.info("[GVG] handleGVGActivityStatusRegister end");
    }

    /**
     * 修正匹配积分
     * @param signUpInfo
     */
    private void fixAllianceMatchScore(GVGAllianceSignUpInfo signUpInfo) {
        long gvgAllianceMatchBaseScore = signUpInfo.getBaseMatchScore();
        var allianceId = signUpInfo.getAllianceId();
        var matchType = signUpInfo.getMatchType();
        logger.info("[GVG] fixAllianceMatchScore allianceId: {}, matchType: {}, baseMatchScore: {}", allianceId, matchType, gvgAllianceMatchBaseScore);

        final Result result = calcFixedAllianceMatchScore(allianceId, matchType, gvgAllianceMatchBaseScore);

        signUpInfo.setFixedMatchScore(result.fixMatchScore());
        gvgAllianceSignUpInfoDao.save(signUpInfo);

        logger.info("[GVG] fixAllianceMatchScore allianceId: {}, matchType: {}, baseMatchScore: {}, fixMatchScore: {}, rate: {}",
                signUpInfo.getAllianceId(), signUpInfo.getMatchType(), gvgAllianceMatchBaseScore, result.fixMatchScore(), result.rate());
        // 打点
        try {
            biLogUtil.gvgMatchScore(signUpInfo.getAllianceId(), gvgAllianceMatchBaseScore, result.fixMatchScore(), signUpInfo.getMatchType().getId(), (int)(result.rate * 100));
        } catch (ExpectedException ignored){

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("[GVG] fixAllianceMatchScore biLogException", e,"allianceId", signUpInfo.getAllianceId(), "matchType", signUpInfo.getMatchType());
        }
    }

    public Result calcFixedAllianceMatchScore(Long allianceId, GvgMatchType matchType, long oriScore) {
        // 修正系数
        double rate = 0;
        // 取最近5场战斗的胜负差
        GvgAllianceRecord gvgAllianceRecord = gvgAllianceRecordDao.findById(allianceId);
        List<Integer> winLoseRecord = Collections.emptyList();
        if (gvgAllianceRecord != null) {
            winLoseRecord = gvgAllianceRecord.getWinLoseRecordOfType(matchType);
            int sum = winLoseRecord.stream().mapToInt(Integer::intValue).sum();

            if (sum != 0) {
                try {
                    // 分数修正
                    GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
                    double[] gvgActivityMatchRevises = gvgSettingConfig.getGvgActivityMatchRevise();
                    if (gvgActivityMatchRevises == null || gvgActivityMatchRevises.length != 10) {
                        throw new AlertException("[GVG] gvgActivityMatchRevises参数错误","gcgActivityMatchRevises",Arrays.toString(gvgActivityMatchRevises));
                    }
                    if (sum < 0) {
                        sum = Math.max(sum, -5);
                        rate = gvgActivityMatchRevises[sum + 5] / 100.0;
                    } else {
                        sum = Math.min(sum, 5);
                        rate = gvgActivityMatchRevises[sum + 4] / 100.0;
                    }
                } catch (ExpectedException ignored){

                } catch (Exception e) {
                    ErrorLogUtil.exceptionLog("[GVG] GVG分数修正报错", e);
                }
            }
        }

        long fixMatchScore = (long) (oriScore * (1 + rate));
        return new Result(rate, fixMatchScore, winLoseRecord);
    }

    public record Result(double rate, long fixMatchScore, List<Integer> winLoseRecord) {
    }


    public List<TwoTuple<GVGAllianceSignUpInfo, GVGAllianceSignUpInfo>> matchNewFriendly(List<GVGAllianceSignUpInfo> orList) {
        logger.info("[GVG] matchNewFriendly, {}", orList.size());

        List<TwoTuple<GVGAllianceSignUpInfo, GVGAllianceSignUpInfo>> ret = new ArrayList<>();
        // 匹配时根据先根据所有报名同盟的匹配分进行内部排名，然后从高到低依次匹配战力差在10%（可配）以内的随机一个同盟，如果没有则每次扩大5%（可配）范围

        // 1:战力排序
        List<GVGAllianceSignUpInfo> list = orList.stream().sorted(Comparator.comparing(GVGAllianceSignUpInfo::getFixedMatchScore).reversed()).collect(Collectors.toList());


        double matchScoreGap = configService.getConfig(GvgSettingConfig.class).getGvgMatchScoreGap();
        double matchScoreGapFixAdd = configService.getConfig(GvgSettingConfig.class).getGvgMatchScoreGapFixAdd();
        //
        while (!list.isEmpty()) {
            GVGAllianceSignUpInfo allianceFirst = list.removeFirst();
            long scoreFirst = allianceFirst.getFixedMatchScore();

            List<GVGAllianceSignUpInfo> selectedAllianceInfos;
            if (!list.isEmpty()) {
                long scoreBase = getScoreBase(scoreFirst, matchScoreGap, 0L);
                selectedAllianceInfos = selectScoreMatchedAllianceList(list, scoreBase, allianceFirst.getAllianceId());

                // 没有匹配，直接扩大到下一个联盟满足时的积分范围
                if (selectedAllianceInfos.isEmpty()) {
                    GVGAllianceSignUpInfo allianceSecond = list.getFirst();
                    long scoreSecond = allianceSecond.getFixedMatchScore();
                    //方法 范围
                    scoreBase = getScoreMin(scoreFirst, scoreSecond, matchScoreGap, matchScoreGapFixAdd);
                    selectedAllianceInfos = selectScoreMatchedAllianceList(list, scoreBase, allianceFirst.getAllianceId());
                }
                // 还没有，直接把下一个给他（但不能是本联盟）
                if (selectedAllianceInfos.isEmpty()) {
                    for(GVGAllianceSignUpInfo gvgAllianceSignUpInfo : list){
                        if(gvgAllianceSignUpInfo.getAllianceId().equals(allianceFirst.getAllianceId())){
                            continue;
                        }

                        selectedAllianceInfos.add(gvgAllianceSignUpInfo);
                        break;
                    }
                }

                GVGAllianceSignUpInfo random = RandomUtils.random(selectedAllianceInfos);
                ret.add(Tuple.tuple(allianceFirst, random));
                list.remove(random);
            } else {
                ret.add(Tuple.tuple(allianceFirst, null));
            }
        }
        return ret;
    }

    private long getScoreMin(long baseValue, long compareValue, double matchScoreGap , double matchScoreGapFixAdd) {

        // 使用double参与计算
        double scoreGap = baseValue * matchScoreGapFixAdd / 100.0;
        double scoreBase = baseValue * (100.0 - matchScoreGap ) / 100.0;

        if (scoreBase <= compareValue) {
            return (long) scoreBase; // 转换为long类型返回
        }

        // 使用double计算times，并确保结果符合逻辑
        long times = (long) ((scoreBase - compareValue) / scoreGap + 1);
        return (long) (scoreBase - scoreGap * times);
    }

    private static long getScoreBase(long scoreFirst, double matchScoreGap, long gapTimes) {
        // 使用double参与计算
        return (long) (scoreFirst * (100.0 - matchScoreGap * (gapTimes + 1)) / 100.0); // 转换为long类型返回
    }

    /**
     * 选取积分匹配条件的联盟
     * @param list
     * @param scoreBase
     */
    private List<GVGAllianceSignUpInfo>  selectScoreMatchedAllianceList(List<GVGAllianceSignUpInfo> list, long scoreBase, Long exceptAllianceId) {
        List<GVGAllianceSignUpInfo> selectedAllianceInfos = new ArrayList<>();
        for (GVGAllianceSignUpInfo gvgAllianceSignUpInfo : list) {
            if(gvgAllianceSignUpInfo.getAllianceId().equals(exceptAllianceId)){
                continue;   // 排除掉自己联盟
            }

            long scoreSelected = gvgAllianceSignUpInfo.getFixedMatchScore();
            if (scoreSelected < scoreBase) {
                break;
            }
            selectedAllianceInfos.add(gvgAllianceSignUpInfo);
        }
        return selectedAllianceInfos;
    }

    class GVGSignUpAllianceComparator implements Comparator<GVGAllianceSignUpInfo> {
        @Override
        public int compare(GVGAllianceSignUpInfo o1, GVGAllianceSignUpInfo o2) {
            if (o1.getFixedMatchScore() > o2.getFixedMatchScore()) {
                return -1;
            }
            return 1;
        }
    }

    private List<TwoTuple<GVGAllianceSignUpInfo, GVGAllianceSignUpInfo>> match(List<GVGAllianceSignUpInfo> list) {
        List<TwoTuple<GVGAllianceSignUpInfo, GVGAllianceSignUpInfo>> ret = new ArrayList<>();

        // 1:战力排序
        list.sort(new GVGSignUpAllianceComparator());

        // 2：战力分组
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        int matchGroupSize = gvgSettingConfig.getMatchGroupSize();
        List<GVGAllianceSignUpInfo> group = new ArrayList<>();
        while (!list.isEmpty()) {
            // 3-1：组内随机产生对手
            if (group.size() >= matchGroupSize) {
                while (!group.isEmpty()) {
                    GVGAllianceSignUpInfo gvgAllianceSignUpInfo = group.remove(0);
                    GVGAllianceSignUpInfo random = RandomUtils.random(group);
                    ret.add(Tuple.tuple(gvgAllianceSignUpInfo, random));
                    group.remove(random);
                }

                group.clear();
            } else {
                group.add(list.remove(0));
            }
        }

        // 3-2：组内随机产生对手
        while (!group.isEmpty()) {
            if (group.size() == 1) {
                ret.add(Tuple.tuple(group.remove(0), null));
                break;
            }

            GVGAllianceSignUpInfo gvgAllianceSignUpInfo = group.remove(0);
            GVGAllianceSignUpInfo random = RandomUtils.random(group);
            ret.add(Tuple.tuple(gvgAllianceSignUpInfo, random));
            group.remove(random);
        }

        return ret;
    }

    private void matchBILog1(GVGAllianceSignUpInfo gvgAllianceSignUpInfo1, long gvgBattleServerId, int round, int turn, long battleStartTime) {
        try {
            long leaderId = 0;
            AllianceBaseData allianceBaseData1 = allianceBaseDataDao.findById(gvgAllianceSignUpInfo1.getAllianceId());
            if (allianceBaseData1 != null) {
                leaderId = allianceBaseData1.getLeaderRoleId() == null ? 0 : allianceBaseData1.getLeaderRoleId();
            } else {
                ErrorLogUtil.errorLog("GVG打点,匹配直接获胜,联盟基础信息为空", "allianceId",gvgAllianceSignUpInfo1.getAllianceId());
            }

            GVGAllianceLineUpInfo gvgAllianceLineUpInfo  = gvgAllianceSignUpInfo1.getGvgAllianceLineUpInfo();

            biLogUtil.gvgMatchResult(leaderId, gvgBattleServerId, round, turn, battleStartTime, gvgAllianceSignUpInfo1.getServerId(),
                    gvgAllianceSignUpInfo1.getAllianceId(), leaderId,
                    gvgAllianceSignUpInfo1.getBaseMatchScore(), gvgAllianceLineUpInfo.getFormalMemberIds().size(),
                    gvgAllianceLineUpInfo.getTempMemberIds().size(),
                    0, 0, 0, 0, 0, 0, gvgAllianceSignUpInfo1.getMatchType());

        } catch (ExpectedException ignored){

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("GVG打点,联盟直接获胜---匹配结果异常", e,"allianceId",gvgAllianceSignUpInfo1.getAllianceId());
        }
    }

    private void matchBILog2(GVGAllianceSignUpInfo gvgAllianceSignUpInfo1, AllianceBaseData allianceBaseData1, GVGAllianceSignUpInfo gvgAllianceSignUpInfo2,
                             AllianceBaseData allianceBaseData2, long gvgBattleServerId, int round, int turn, long battleStartTime) {
        try {
            biLogUtil.gvgMatchResult(allianceBaseData1.getLeaderRoleId(), gvgBattleServerId, round, turn, battleStartTime,
                    gvgAllianceSignUpInfo1.getServerId(), gvgAllianceSignUpInfo1.getAllianceId(), allianceBaseData1.getLeaderRoleId(), gvgAllianceSignUpInfo1.getBaseMatchScore(),
                    gvgAllianceSignUpInfo1.getGvgAllianceLineUpInfo().getFormalMemberIds().size(), gvgAllianceSignUpInfo1.getGvgAllianceLineUpInfo().getTempMemberIds().size(), gvgAllianceSignUpInfo2.getServerId(),
                    gvgAllianceSignUpInfo2.getAllianceId(), allianceBaseData2.getLeaderRoleId(), gvgAllianceSignUpInfo2.getBaseMatchScore(),
                    gvgAllianceSignUpInfo2.getGvgAllianceLineUpInfo().getFormalMemberIds().size(), gvgAllianceSignUpInfo2.getGvgAllianceLineUpInfo().getTempMemberIds().size(), gvgAllianceSignUpInfo1.getMatchType());
        } catch (ExpectedException ignored){

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("GVG打点,联盟匹配结果异常", e,
                    "allianceId1",gvgAllianceSignUpInfo1.getAllianceId(),
                    "allianceId2",gvgAllianceSignUpInfo2.getAllianceId());
        }
    }

    /**
     * 新版匹配
     */
    private void handleNewMatch(Activity activity, List<GVGAllianceSignUpInfo> list, String groupId, GvgMatchType gvgMatchType) {
        logger.info("[GVG] handleNewMatch group: {} teamCount: {}", groupId, list.size());

        // 预分配的战斗服id
        List<Integer> canUseBattleServerIds = gvgControlService.getCanUseBattleServerIds(list.size() / 2);
        List<Integer> tempBattleServerIds = new ArrayList<>(canUseBattleServerIds);
        logger.info("[GVG] handleNewMatch group: {} canUseBattleServerIds: {}", groupId, canUseBattleServerIds);

        GvgSettingConfig config = configService.getConfig(GvgSettingConfig.class);
        Map<Long, Integer> testMatchAllianceIndexInfo = config.getTestMatchAllianceIndexInfo();

        List<TwoTuple<GVGAllianceSignUpInfo, GVGAllianceSignUpInfo>> ret = JavaUtils.bool(testMatchAllianceIndexInfo) ?
                testMatchNewFriendly(list, testMatchAllianceIndexInfo) : matchNewFriendly(list);

        logger.info("[GVG] handleNewMatch group: {} matchResult: {}", groupId, ret);

        // 获取本周所有场的活动信息
        GVGActivityContext gvgActivityContext = activity.getActivityContext();
        Long battleStartTime = gvgActivityContext.getBattleStartTimes().getOrDefault(gvgMatchType, 0L);
        if (battleStartTime <= 0) {
            logger.info("[GVG] 官渡匹配 获取战斗开始时间异常 group: {} type {}", groupId, gvgMatchType);
            return;
        }

        long time = 0;
        List<Integer> battleServerIds = new ArrayList<>();
        for (TwoTuple<GVGAllianceSignUpInfo, GVGAllianceSignUpInfo> matchAlliance : ret) {
            GVGAllianceSignUpInfo signUp1 = matchAlliance.getFirst();
            GVGAllianceSignUpInfo signUp2 = matchAlliance.getSecond();

            if (signUp2 == null) {
                logger.info("[GVG] handleNewMatch noRival round: {}, group: {} allianceId1: {}, matchType1: {}, serverId1: {}", gvgActivityContext.getRound(), groupId, signUp1.getAllianceId(), signUp1.getMatchType(), signUp1.getServerId());
                // 只有一个，轮空
                // 直接获胜
                GVGBattleRecord gvgBattleRecord = gvgBattleRecordDao.create(0L, 0, 0,
                        signUp1.getAllianceId(), 0, signUp1.getServerId(),
                        activity.getPersistKey(), gvgActivityContext.getRound(), signUp1.getGameRound(), signUp1.getMatchType(), gvgActivityContext.getRound(),
                        signUp1.getWarZoneId());
                gvgBattleRecord.setBye(true);
                gvgBattleRecordDao.save(gvgBattleRecord);
                GVGBattleRecordVo gvgBattleRecordVo = new GVGBattleRecordVo(gvgBattleRecord);
                gvgControlService.uploadGVGBattleRecord(gvgBattleRecordVo);
                // 轮空
                signUp1.setBye(true);

                gvgAllianceSignUpInfoDao.save(signUp1);

                matchBILog1(signUp1, 0, gvgActivityContext.getRound(), 0, 0);

                // 匹配完以后，通知玩家对应场次
                gvgControlBroadcastToGameService.broadcastAllianceSignUpInfo(signUp1.getServerId(), signUp1);
            } else {
                // 设置比较靠后的时间， 防止record被 ticker 清掉
                // 战斗服id
                Integer battleServerId = tempBattleServerIds.removeFirst();
                battleServerIds.add(battleServerId);

                long delayDestroyTime = battleStartTime + getDurationOfGVGBattle();

                logger.info("[GVG] handleNewMatch match group: {} battleServerId: {}, round: {}" +
                                "allianceId1: {}, matchType1: {}, serverId1: {}, allianceId2: {}, matchType2: {}, serverId2: {}, battleStartTime: {}, delayDestroyTime: {}",
                        groupId, battleServerId, gvgActivityContext.getRound(),
                        signUp1.getAllianceId(), signUp1.getMatchType(), signUp1.getServerId(), signUp2.getAllianceId(), signUp2.getMatchType(), signUp2.getServerId(), battleStartTime, delayDestroyTime);
                gvgAllianceSignUpInfoDao.save(signUp1);
                gvgAllianceSignUpInfoDao.save(signUp2);


                GvgBattleServerDispatchRecord dispatchRecord = gvgBattleServerDispatchRecordDao.findById(Long.valueOf(battleServerId));
                if (dispatchRecord != null) {
                    ErrorLogUtil.errorLog("[GVG] handleNewMatch battleServerAlreadyDispatched",new RuntimeException(),"battleServerId", battleServerId);
                    gvgBattleServerDispatchRecordDao.delete(Long.valueOf(battleServerId));
                }

                dispatchRecord = gvgBattleServerDispatchRecordDao.create(Long.valueOf(battleServerId), signUp1.getAllianceId(),
                        signUp1.getServerId(), signUp2.getAllianceId(), signUp2.getServerId(), battleStartTime, delayDestroyTime,
                        gvgActivityContext.getRound(), signUp1.getGameRound(), signUp1.getMatchType(), gvgActivityContext.getRound(), 0, "");

                // 广播给对方服务器，自己的联盟信息
                int alliance1ServerId = dispatchRecord.getAlliance1ServerId();
                int alliance2ServerId = dispatchRecord.getAlliance2ServerId();

                Long allianceId1 = dispatchRecord.getAllianceId1();
                AllianceBaseData allianceBaseData1 = allianceBaseDataDao.findById(allianceId1);
                gvgControlBroadcastToGameService.broadcastAllianceBaseData(alliance2ServerId, allianceBaseData1);

                Long allianceId2 = dispatchRecord.getAllianceId2();
                AllianceBaseData allianceBaseData2 = allianceBaseDataDao.findById(allianceId2);
                gvgControlBroadcastToGameService.broadcastAllianceBaseData(alliance1ServerId, allianceBaseData2);

                gvgControlBroadcastToGameService.broadcastGvgBattleServerDispatchRecord(dispatchRecord);

                List<GVGAllianceSignUpInfo> gvgBattleAllianceSignUpInfos = new ArrayList<>();
                gvgBattleAllianceSignUpInfos.add(signUp1);
                gvgBattleAllianceSignUpInfos.add(signUp2);
                gvgControlBroadcastToGVGBattleService.broadcastGVGAllianceSignUpInfo(battleServerId, gvgBattleAllianceSignUpInfos);

                matchBILog2(signUp1, allianceBaseData1, signUp2, allianceBaseData2, battleServerId, gvgActivityContext.getRound(),
                        dispatchRecord.getBattleTurn(), dispatchRecord.getBattleStartTime());

                // 匹配完以后，通知玩家对应场次
                gvgControlBroadcastToGameService.broadcastAllianceSignUpInfo(alliance1ServerId, signUp1);
                gvgControlBroadcastToGameService.broadcastAllianceSignUpInfo(alliance2ServerId, signUp2);
            }
        }


        GVGActivityTurn gvgActivityTurn;
        int week = getGVGTurnByYearAndWeek();
        gvgActivityTurn = gvgActivityTurnDao.findByWeek(week);
        if (gvgActivityTurn == null) {
            gvgActivityTurn = gvgActivityTurnDao.create(week);
        }

        if (gvgActivityTurn != null) {
            gvgActivityTurn.setTime(time);
            gvgActivityTurn.setBattleServerIds(battleServerIds);
            gvgActivityTurnDao.save(gvgActivityTurn);
            logger.info("[GVG] handleNewMatch group{} turn：{}，serverCount：{}，serverList：{}", groupId, gvgActivityTurn.getTurn(), battleServerIds.size(), battleServerIds);
        }
    }

    /**
     * 根据匹配类型获取战斗开始时间
     * @param gvgMatchType
     * @return
     */
    private long getBattleServerStartTime(GvgMatchType gvgMatchType) {
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        List<GvgActivityTime> gvgActivityTimes = gvgSettingConfig.getGvgActivityTime();

        boolean isAdvanced = gvgSettingConfig.isGvgLegion2MatchStartEarly();
        int timeIndex = 0;
        switch (gvgMatchType) {
            case GvgMatchType.FRIENDLY:
                timeIndex = 3;
                break;
            case GvgMatchType.FRIENDLY_TEAM2:
                timeIndex = isAdvanced ? 2 : 3;
                break;
            default:
                throw new IllegalArgumentException("unknown gvgMatchType: " + gvgMatchType);
        }
        GvgActivityTime gvgActivityTime = gvgActivityTimes.get(timeIndex);
        Date nextDate = GvgSettingConfig.getGVGActivityNextDate(gvgActivityTime.getWeek(), gvgActivityTime.getLocalTime());
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(nextDate);
        return calendar.getTimeInMillis();
    }

    private List<TwoTuple<GVGAllianceSignUpInfo, GVGAllianceSignUpInfo>> testMatchNewFriendly(List<GVGAllianceSignUpInfo> list, Map<Long, Integer> testMatchAllianceIndexInfo) {
        logger.info("[GVG] testMatchNewFriendly");
        Map<Integer, List<GVGAllianceSignUpInfo>> map = new HashMap<>();
        List<TwoTuple<GVGAllianceSignUpInfo, GVGAllianceSignUpInfo>> ret = new ArrayList<>();
        for (GVGAllianceSignUpInfo gvgAllianceSignUpInfo : list) {
            Long allianceId = gvgAllianceSignUpInfo.getAllianceId();
            if (!testMatchAllianceIndexInfo.containsKey(allianceId)) {
                logger.info("[GVG] testMatchNewFriendly allianceNotInTestList allianceId: {}, matchType: {}", allianceId, gvgAllianceSignUpInfo.getMatchType());
                continue;
            }

            int index = testMatchAllianceIndexInfo.get(allianceId);
            if (index < 0) {
                logger.info("[GVG] testMatchNewFriendly index < 0 allianceId: {}, matchType: {}", allianceId, gvgAllianceSignUpInfo.getMatchType());
                continue;
            }

            if (!map.containsKey(index)) {
                map.put(index, new ArrayList<>());
            }
            map.get(index).add(gvgAllianceSignUpInfo);
        }
        if (JavaUtils.bool(map)) {
            for (Entry<Integer, List<GVGAllianceSignUpInfo>> entry : map.entrySet()) {
                List<GVGAllianceSignUpInfo> value = entry.getValue();
                if (value.size() != 2) {
                    ErrorLogUtil.errorLog("[GVG] testMatchNewFriendly error, match count error", "key",entry.getKey(), "count",value.size());
                    continue;
                }

                ret.add(Tuple.tuple(value.get(0), value.get(1)));
            }
        }
        return ret;
    }

    // todo 战场持续时间。暂时写死七天
    private long getDurationOfGVGBattle() {
        return TimeUtil.DAY_MILLIS;
    }

    /**
     * 入场阶段处理
     */
    private void handleGvgMatchNew(Activity activity, GvgMatchType gvgMatchType) {
        logger.info("[GVG] handleGvgMatchNew gvgMatchType: {}", gvgMatchType);
        // 一共有多少队伍参战
        Collection<GVGAllianceSignUpInfo> gvgAllianceSignUpInfos = gvgAllianceSignUpInfoDao.findAll();
        if (!JavaUtils.bool(gvgAllianceSignUpInfos)) {
            logger.info("[GVG] handleGvgMatchNew noAllianceSignUpInfo gvgMatchType: {}", gvgMatchType);
            return;
        }

        Map<Long, Integer> testMatchAllianceIndexInfo = configService.getConfig(GvgSettingConfig.class).getTestMatchAllianceIndexInfo();

        // 按时间分组 key:时间索引
        Map<String, List<GVGAllianceSignUpInfo>> signupInfosGroupByMatchGroup = new HashMap<>();
        for (GVGAllianceSignUpInfo gvgAllianceSignUpInfo : gvgAllianceSignUpInfos) {
            // 跳过杯赛报名数据
            if (gvgAllianceSignUpInfo.getMatchType() != gvgMatchType) {
                continue;
            }

            if (JavaUtils.bool(testMatchAllianceIndexInfo) && !testMatchAllianceIndexInfo.containsKey(gvgAllianceSignUpInfo.getAllianceId())) {
                ErrorLogUtil.errorLog("[GVG] handleGvgMatchNew allianceNotInTestList", "serverId", gvgAllianceSignUpInfo.getServerId(), "allianceId", gvgAllianceSignUpInfo.getAllianceId());
                continue;
            }

            String group = findGvgMatchGroupByServerId(gvgAllianceSignUpInfo.getServerId());
            if (!JavaUtils.bool(group)) {
                ErrorLogUtil.errorLog("[GVG] handleGvgMatchNew can'tFindGroup", "serverId", gvgAllianceSignUpInfo.getServerId(), "allianceId", gvgAllianceSignUpInfo.getAllianceId());
                continue;
            }

            // 修正匹配分（基于连胜场次）
            fixAllianceMatchScore(gvgAllianceSignUpInfo);
            signupInfosGroupByMatchGroup.compute(group, (k, v) -> v == null ? new ArrayList<>() : v).add(gvgAllianceSignUpInfo);
        }

        logger.info("[GVG] handleGvgMatchNew groupInfo: {}", signupInfosGroupByMatchGroup);

        // 2022年2月18日 @文云豪，修改匹配规则： 用手配的服务器分组，分别执行原匹配逻辑。
        signupInfosGroupByMatchGroup.forEach((key, value) -> handleNewMatch(activity, value, key, gvgMatchType));

        // 更新首次参加的标记
        for (GVGQualifiedServer gvgQualifiedServer : gvgQualifiedServerDao.findAll()) {
            if (gvgQualifiedServer.isFirstJoin()) {
                gvgQualifiedServer.setFirstJoin(false);
                gvgQualifiedServerDao.save(gvgQualifiedServer);
            }
        }

    }

    @Override
    public PsActivityInfo getRoleActivityInfo(Role role, ActivityListMeta activityMeta) {
        ActivityVo gvgActivityVo = gvgDataVoManager.findGvgActivityVo();
        GvgSettingConfig config = configService.getConfig(GvgSettingConfig.class);
        Map<Long, Integer> map = config.getTestMatchAllianceIndexInfo();
        if (JavaUtils.bool(map)) {
            if (role == null || !JavaUtils.bool(role.getAllianceId()) || !map.containsKey(role.getAllianceId())) {
                return null;
            }
        }


        if (role != null && role.getLevel() < activityMeta.getLevelLimit()) {
            return null;
        }

        if (gvgActivityVo != null && TimeUtil.getNow() < gvgActivityVo.getEndTime()) {
            PsActivityInfo info = new PsActivityInfo();
            info.setId(String.valueOf(gvgActivityVo.getId()));
            info.setStartTime(gvgActivityVo.getStartTime());
            info.setEndTime(gvgActivityVo.getEndTime());
            info.setMetaId(activityMeta.getId());
            info.setType(getType().getPsType());
            info.setStatus(gvgActivityVo.getStatus().getPsStatus());
            info.setHasReward(false);
            return info;
        }
        return null;
    }

    @Override
    public boolean createValidated(ActivityListMeta activityMeta,long activityEndTime) {
        // GVG永远不会被阻挡
        return true;
    }

    /**
     * 2位年+2位周数
     *
     * @return
     */
    public static int getGVGTurnByYearAndWeek() {
        Calendar calendar = Calendar.getInstance();
        // 现在是2022年， 在78年之内（2100年之前）， 都能保证是2位年。 78年之后如果有需要再优化吧。 哈哈哈
        int year = calendar.get(Calendar.YEAR) % 100;
        int weekOfYear = calendar.get(Calendar.WEEK_OF_YEAR);
        int res = Integer.parseInt(year + (weekOfYear < 10 ? "0" : "") + weekOfYear);
        return res;
    }

    /**
     * 分组信息。按照赛季分组。灰度则 * 10000。同时 过滤海外
     * @param serverId
     * @return
     */
    public String findGvgMatchGroupByServerId(int serverId) {
        ServerType serverType = ServerConfigManager.getInstance().getServerTypeConfig().getServerType(serverId);
        int season = 1;
        int days = serverInfoService.getOpenServerDay(serverId);
        KvkSeasonServerGroupConfig seasonServerGroupConfig;
        if (serverType == ServerType.GAME) {
            seasonServerGroupConfig = configCenter.getKvkSeasonServerGroupConfigByOServerId(serverId, TimeUtil.getNow());
            if (seasonServerGroupConfig == null) {
                if (serverId < 100) {
                    return null;
                }

                // 没进赛季的游戏服
                // 开服时间超过多久有效
                int serverOpenTimeLimit = configService.getConfig(GvgSettingConfig.class).getGvgActivityAutoOpenCondition();
                if (days < serverOpenTimeLimit) {
                    return null;
                }

                // 首次开放官渡的
                GVGQualifiedServer gvgQualifiedServer = gvgQualifiedServerDao.findById((long) serverId);
                if (gvgQualifiedServer == null) {
                    return parseGroupKey(0, 0);
                }

            } else {
                season = seasonServerGroupConfig.getSeason();
            }
        } else if (serverType == ServerType.KVK_SEASON) {
            seasonServerGroupConfig = configCenter.getKvkSeasonServerGroupConfigByKServerId(serverId, TimeUtil.getNow());
            if (seasonServerGroupConfig == null) {
                return null;
            } else {
                season = seasonServerGroupConfig.getSeason();
            }
        }
        return parseGroupKey(season, getMatchGroupByDay(days));
    }

    /**
     * 根据开服时间计算分组
     * @param day
     * @return
     */
    private int getMatchGroupByDay(int day) {
        List<Integer> gvgMatchGroupSeverOpenDaysList = configService.getConfig(GvgSettingConfig.class).getGvgMatchGroupSeverOpenDaysList();
        for (int index = 0; index < gvgMatchGroupSeverOpenDaysList.size(); index++) {
            if (day <= gvgMatchGroupSeverOpenDaysList.get(index)) {
                return index;
            }
        }
        return gvgMatchGroupSeverOpenDaysList.size();
    }

    private String parseGroupKey(int season, int index) {
        return season + "_" + index;
    }


    /**
     * gvg活动进入约战
     */
    public void handleGvgActivityStatusSignup(Activity activity) {
        logger.info("[GVG] handleGvgActivityStatusSignup begin");
        //锁定匹配分
        handleGvGAllianceMatchInfo(activity);

        int total = gvgAllianceSignUpInfoDao.findAll().size();
        logger.info("Gvg 共有{} 个联盟报名", total);

        handlerBattleServerStartTime(activity);

        logger.info("执行匹配");
        handleGvgMatchNew(activity, GvgMatchType.FRIENDLY);
        handleGvgMatchNew(activity, GvgMatchType.FRIENDLY_TEAM2);
        logger.info("[GVG] handleGvgActivityStatusSignup end");
    }

    private void handlerBattleServerStartTime(Activity activity) {
        List<GvgMatchType> typeList = new ArrayList<>();
        typeList.add(GvgMatchType.FRIENDLY);
        typeList.add(GvgMatchType.FRIENDLY_TEAM2);

        GVGActivityContext activityContext = (GVGActivityContext)activity.getActivityContext();
        for (GvgMatchType gvgMatchType : typeList) {

            try {
                long battleStartTime = getBattleServerStartTime(gvgMatchType);
                activityContext.getBattleStartTimes().put(gvgMatchType, battleStartTime);
                logger.info("[GVG] 匹配 计算战斗开始时间, type {} startTime {}", gvgMatchType, battleStartTime);
            } catch (Exception e) {
                ErrorLogUtil.errorLog("[GVG] 匹配 计算战斗开始时间异常", e, "type", gvgMatchType);
            }
        }
    }

    /**
     * 处理联盟的匹配信息
     */
    private void handleGvGAllianceMatchInfo(Activity activity) {
        logger.info("[GVG] handleGvGAllianceMatchInfo");

        Map<Integer, Map<Long, GVGAllianceMatchInfoVo>> gameMatchInfo = gvgControlBroadcastToGameService.broadcastGvgAllianceMatchInfo();
        logger.info("[GVG] handleGvGAllianceMatchInfo gameMatchInfo: {}", gameMatchInfo);

        Map<Long, Integer> testMatchAllianceIndexInfo = configService.getConfig(GvgSettingConfig.class).getTestMatchAllianceIndexInfo();

        long backupCreateTime = TimeUtil.getNow();
        // 从各个服拿到符合条件的联盟，然后创建报名信息：gvgAllianceSignUpInfoDao
        for (Entry<Integer, Map<Long, GVGAllianceMatchInfoVo>> gameMatchInfoEntry : gameMatchInfo.entrySet()) {
            for (Entry<Long, GVGAllianceMatchInfoVo> allianceMatchInfoEntry : gameMatchInfoEntry.getValue().entrySet()) {
                long allianceId = allianceMatchInfoEntry.getKey();
                GVGAllianceMatchInfoVo allianceMatchInfo = allianceMatchInfoEntry.getValue();
                GvgRegisterInfo entity = gvgRegisterInfoDao.findById(allianceId);
                if (entity == null) {
                    ErrorLogUtil.errorLog("[GVG] handleGvGAllianceMatchInfo can'tFindRegisterAlliance", "allianceId", allianceId);
                    continue;
                }

                if(allianceMatchInfo.getGvgAllianceLineUpInfoMap() == null || allianceMatchInfo.getGvgAllianceLineUpInfoMap().isEmpty()){
                    ErrorLogUtil.errorLog("[GVG] handleGvGAllianceMatchInfo allianceMatchInfo nullOrEmpty", "allianceId", allianceId);
                    continue;
                }

                if (JavaUtils.bool(testMatchAllianceIndexInfo) && !testMatchAllianceIndexInfo.containsKey(allianceId)) {
                    ErrorLogUtil.errorLog("[GVG] handleGvGAllianceMatchInfo allianceIdNotInTestList", "allianceId", allianceId);
                    continue;
                }

                // 创建联盟报名信息
                createGvGAllianceSignInfo(activity, allianceMatchInfo, backupCreateTime);
            }
        }
    }

    private void createGvGAllianceSignInfo(Activity activity, GVGAllianceMatchInfoVo allianceMatchInfoVo, long backupCreateTime) {
        GVGActivityContext activityContext = activity.getActivityContext();

        // 创建报名信息
        for(var lineUpInfo: allianceMatchInfoVo.getGvgAllianceLineUpInfoMap().values()){
            var matchType = lineUpInfo.getGvgMatchType();
            GVGAllianceSignUpInfo info = gvgAllianceSignUpInfoDao.create(allianceMatchInfoVo.getAllianceId(), allianceMatchInfoVo.getServerId(), matchType, activityContext.getRound(), activityContext.getRound(), 0);
            // 设置阵容
            info.setGvgAllianceLineUpInfo(lineUpInfo);
            gvgAllianceSignUpInfoDao.save(info);
            logger.info("[GVG] createGvGAllianceSignInfo allianceId: {}, matchType: {}, formalMember: {}, tempMember: {}", allianceMatchInfoVo.getAllianceId(), matchType, lineUpInfo.getFormalMemberIds(), lineUpInfo.getTempMemberIds());

            // 创建匹配的备份数据，目前暂时用于模拟下一轮匹配
            var memberList = new ArrayList<Long>();
            memberList.addAll(lineUpInfo.getFormalMemberIds());
            memberList.addAll(lineUpInfo.getTempMemberIds());
            gvgMatchResultBackupDao.create(lineUpInfo.getAllianceId(), allianceMatchInfoVo.getServerId(), memberList, lineUpInfo.getGvgMatchType(), activityContext.getRound(), backupCreateTime);
        }
    }

    private void handleGvgInAdmittance(Activity activity) {
        boolean isAdvanced = configService.getConfig(GvgSettingConfig.class).isGvgLegion2MatchStartEarly();
        handleGvgInAdmittance(activity, isAdvanced ? GvgMatchType.FRIENDLY : null);
    }

    /**
     * gvg进入入场阶段 gvg优化六期 的版本
     *
     * @param activity
     */
    private void handleGvgInAdmittance(Activity activity, GvgMatchType gvgMatchType) {
        GVGActivityContext gvgActivityContext = activity.getActivityContext();

        // 1.遍历报名信息
        Collection<GvgBattleServerDispatchRecord> dispatchList = gvgBattleServerDispatchRecordDao.findAll();
        if (dispatchList == null || dispatchList.isEmpty()) {
            logger.info("[GVG] handleGvgInAdmittance noDispatch");
            return;
        }

        // 过滤掉已经结束的。
        Collection<GvgBattleServerDispatchRecord> validDispatches = dispatchList.stream().filter(entity -> entity.getStatus() != GvgBattleServerDispatchRecord.STATUS_END).toList();
        logger.info("[GVG] handleGvgInAdmittance turn: {}, dispatchCount: {}, validDispatch {}", gvgActivityContext.getRound(), dispatchList.size(), validDispatches.size());

        boolean isAll = gvgMatchType == null;
        Collection<GvgBattleServerDispatchRecord> all = isAll ? new ArrayList<>(validDispatches) : validDispatches.stream().filter(entity -> entity.getMatchType() == gvgMatchType).toList();

        // 这里仅仅只是交验数据
        for (GvgBattleServerDispatchRecord record : all) {
            long allianceId1 = record.getAllianceId1();
            long allianceId2 = record.getAllianceId2();
            Map<GvgMatchType, GVGAllianceSignUpInfo> infoMap1 = gvgAllianceSignUpInfoDao.getDataByAllianceIdAndMatchType().get(allianceId1);
            Map<GvgMatchType, GVGAllianceSignUpInfo> infoMap2 = gvgAllianceSignUpInfoDao.getDataByAllianceIdAndMatchType().get(allianceId2);
            GVGAllianceSignUpInfo info1 = infoMap1.get(record.getMatchType());
            GVGAllianceSignUpInfo info2 = infoMap2.get(record.getMatchType());
            if (info1 == null || info2 == null) {
                ErrorLogUtil.errorLog("[GVG] handleGvgInAdmittance can'tFindAllianceSignUpInfo",
                        "allianceId1",allianceId1, "allianceId2",allianceId2,
                        "recordMatchType", record.getMatchType(), "info1", info1, "info2", info2);
            }
        }

        gvgControlBroadcastToGameService.broadcastGVGAllianceSignUpInfos(all);
    }

    public void setGvgStatusByGm(boolean restart, int status) {
        Activity activity = activityDao.findActivityByActivityType(ActivityType.GVG);
        if (activity == null) {
            return;
        }

        setActivityContextTest(activity, restart, status);
    }

    public void setActivityContextTest(Activity activity, boolean restart, int status) {
        GVGActivityContext activityContext = activity.getActivityContext();
        skipTicker = true;
        logger.info("GVG 战场 修改协议开始 参数为 {}, {}", restart, status);

        try {
            if (restart) {
                // GM命令开启新的一轮，否则需要等一个星期才会开，这样可能会导致数据错乱，比如GVGBattleRecord会存所有的数据
                ActivityTurn activityTurn = activityTurnDao.findByActivityType(ActivityType.GVG);
                activityTurn.setTurn(activityTurn.getTurn() + 1);
                activityTurnDao.save(activityTurn);

                activityContext.setGvgActivityStatus(GVGActivityStatus.READY);
                activityContext.setNextGvgActivityStatusTime(TimeUtil.getNow());
                onActivityStatusReady(activityContext);
            }

            if (status == activityContext.getGvgActivityStatus().getId()) {
                return;
            }


            if (activityContext.getGvgActivityStatus() == GVGActivityStatus.READY) {
                activityContext.setGvgActivityStatus(GVGActivityStatus.REGISTER);
                activityContext.setNextGvgActivityStatusTime(TimeUtil.getNow() + 7 * TimeUtil.DAY_MILLIS);
                handleGVGActivityStatusRegister();
            }
            if (status == activityContext.getGvgActivityStatus().getId()) {
                return;
            }


            if (activityContext.getGvgActivityStatus() == GVGActivityStatus.REGISTER) {
                activityContext.setGvgActivityStatus(GVGActivityStatus.SIGNUP);
                activityContext.setNextGvgActivityStatusTime(TimeUtil.getNow() + 7 * TimeUtil.DAY_MILLIS);
                handleGvgActivityStatusSignup(activity);
            }
            if (status == activityContext.getGvgActivityStatus().getId()) {
                return;
            }

            if (activityContext.getGvgActivityStatus() == GVGActivityStatus.SIGNUP) {
                activityContext.setGvgActivityStatus(GVGActivityStatus.ADMITTANCE);
                activityContext.setNextGvgActivityStatusTime(TimeUtil.getNow() + 7 * TimeUtil.DAY_MILLIS);
                Collection<GvgBattleServerDispatchRecord> all = gvgBattleServerDispatchRecordDao.findAll();
                for (GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord : all) {
                    gvgBattleServerDispatchRecord.setBattleStartTime(TimeUtil.getNow());
                    gvgBattleServerDispatchRecord.setBattleDestroyTime(TimeUtil.getNow() + getDurationOfGVGBattle());
                    gvgBattleServerDispatchRecordDao.save(gvgBattleServerDispatchRecord);
                }

                handleGvgInAdmittance(activity);
            }

        } catch (ExpectedException ignored){

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("setActivityContextTest exception", e);
        } finally {
            afterGvgActivityStatusContinue(activity);
            activityContext.setNextGvgActivityStatusTime(TimeUtil.getNow() + 7 * TimeUtil.DAY_MILLIS);
            logger.info("GVG 战场返回 当前阶段为{}", activityContext.getGvgActivityStatus().getId());
            activityDao.save(activity);
            skipTicker = false;
        }
    }
}
