package com.lc.billion.icefire.gvgcontrol.biz.model;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import org.jongo.marshall.jackson.oid.MongoId;

/**
 * RZE 有资格参赛的服务器
 *
 */
public class RZEQualifiedServer extends AbstractEntity {

	private static final long serialVersionUID = -203092323194112864L;

	@MongoId
	private Long serverId;

	public int getServerId() {
		return Long.valueOf(serverId).intValue();
	}

	@Override
	public void setPersistKey(Long id) {
		serverId = id;
	}

	@Override
	public Long getPersistKey() {
		return serverId;
	}

	@Override
	public Long getGroupingId() {
		return serverId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

}
