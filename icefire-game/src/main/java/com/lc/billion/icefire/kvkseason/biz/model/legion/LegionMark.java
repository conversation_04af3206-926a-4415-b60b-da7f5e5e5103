package com.lc.billion.icefire.kvkseason.biz.model.legion;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.annotation.MongoIndex;
import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.protocol.structure.PsLegionMark;

/**
 * <AUTHOR>
 *
 */
public class LegionMark extends AbstractEntity {

	private static final long serialVersionUID = 5661407745918953109L;

	@MongoId
	private Long id;
	@MongoIndex
	private Long legionId;
	private String metaId;
	private int serverId;
	private int x;
	private int y;
	private String content;
	private String createrName;

	public PsLegionMark toPsLegionMark() {
		PsLegionMark psLegionMark = new PsLegionMark();
		psLegionMark.setContent(content);
		psLegionMark.setMetaId(metaId);
		psLegionMark.setX(x);
		psLegionMark.setY(y);
//		psLegionMark.setCreaterName(createrName);
		return psLegionMark;
	}

	@Override
	public void setPersistKey(Long id) {
		this.id = id;
	}

	@Override
	public Long getPersistKey() {
		return id;
	}

	@Override
	public Long getGroupingId() {
		return legionId;
	}

	public Long getLegionId() {
		return legionId;
	}

	public void setLegionId(Long legionId) {
		this.legionId = legionId;
	}

	public String getMetaId() {
		return metaId;
	}

	public void setMetaId(String metaId) {
		this.metaId = metaId;
	}

	public int getServerId() {
		return serverId;
	}

	public void setServerId(int serverId) {
		this.serverId = serverId;
	}

	public int getX() {
		return x;
	}

	public void setX(int x) {
		this.x = x;
	}

	public int getY() {
		return y;
	}

	public void setY(int y) {
		this.y = y;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getCreaterName() {
		return createrName;
	}

	public void setCreaterName(String createrName) {
		this.createrName = createrName;
	}

	@Override
	public int hashCodeImpl() {
		return hashCode(legionId, metaId, serverId);
	}

	@Override
	public boolean equalsImpl(Object obj) {
		LegionMark legionMark = (LegionMark) obj;
		return equals(legionId, legionMark.getLegionId()) && equals(metaId, legionMark.getMetaId()) && serverId == legionMark.getServerId();
	}

}
