package com.lc.billion.icefire.kvkseason.biz.manager.legion;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lc.billion.icefire.game.biz.manager.AllianceManager;
import com.lc.billion.icefire.game.biz.manager.IRoleAllianceManager;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionLogDao;
import com.lc.billion.icefire.kvkseason.biz.model.legion.Legion;
import com.lc.billion.icefire.kvkseason.biz.model.legion.log.LegionLog;
import com.lc.billion.icefire.protocol.GcLegionLogList;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 *
 */
@Component
public class LegionLogManager implements IRoleAllianceManager {

	@Autowired
	private LegionLogDao legionLogDao;
	@Autowired
	private AllianceManager allianceManager;

	public GcLegionLogList toGcLegionLogList(Long legionId) {
		GcLegionLogList gcLegionLogList = new GcLegionLogList();
		List<LegionLog> legionLogs = legionLogDao.findByLegionId(legionId);
		if (JavaUtils.bool(legionLogs)) {
			for (LegionLog legionLog : legionLogs) {
				gcLegionLogList.addToLogs(legionLog.toPsLegionLog());
			}
		}
		return gcLegionLogList;
	}

	public void onLegionDismiss(Legion legion) {
		List<LegionLog> legionLogs = legionLogDao.findByLegionId(legion.getId());
		if (JavaUtils.bool(legionLogs)) {
			legionLogDao.delete(legionLogs);
		}
	}

	@Override
	public void onAllianceJoin(Long roleId, Long allianceId) {

	}

	@Override
	public void onAllianceLeave(Long roleId, Long allianceId) {

	}

	@Override
	public void onLegionJoin(Long allianceId, Long legionId) {
		allianceManager.broadcast(allianceId, toGcLegionLogList(legionId));
	}

	@Override
	public void onLegionLeave(Long allianceId, Long legionId) {

	}

}
