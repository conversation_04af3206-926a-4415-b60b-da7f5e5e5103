package com.lc.billion.icefire.csacontrol.biz.service.impl.activity.handler;

import com.alibaba.fastjson.JSON;
import com.lc.billion.icefire.core.common.RandomUtils;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.csabattle.biz.config.CrossSeverAttackSetting;
import com.lc.billion.icefire.csabattle.biz.model.battle.RoleCsaBattle;
import com.lc.billion.icefire.csabattle.biz.service.CsaBattleService;
import com.lc.billion.icefire.csacontrol.biz.config.CrossSeverAttackMatchConfig;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivityContext;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivityGroupContext;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivityStatus;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivitySubType;
import com.lc.billion.icefire.csacontrol.biz.service.common.CSAControlBroadcastToGameService;
import com.lc.billion.icefire.csacontrol.biz.service.rpc.CSAControlRPCToGameProxyService;
import com.lc.billion.icefire.csacontrol.biz.util.CSAUtils;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.async.csa.control.CSAGetGameServerCombatRpcThreadOperation;
import com.lc.billion.icefire.game.biz.async.csa.control.CSAServerCompensateRpcThreadOperation;
import com.lc.billion.icefire.game.biz.config.ActivityListConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.root.CSAActivityHistoryDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.CSAServerBattleInfoDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.CSAServerTrophyDao;
import com.lc.billion.icefire.game.biz.manager.RoleRecordManager;
import com.lc.billion.icefire.game.biz.manager.csa.CSAGameDataVoManager;
import com.lc.billion.icefire.game.biz.manager.csa.CSAServerBattleInfoManager;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.game.biz.model.csa.CSAActivityHistory;
import com.lc.billion.icefire.game.biz.model.csa.CSAServerBattleInfo;
import com.lc.billion.icefire.game.biz.model.csa.CSAServerTrophy;
import com.lc.billion.icefire.game.biz.model.record.RoleRecord;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.activity.AbstractActivityHandler;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.rank.impl.CsaRoleBattlePointRank;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.protocol.GcCSAActivityInfoV2;
import com.lc.billion.icefire.protocol.GcCSAStageInfo;
import com.lc.billion.icefire.protocol.constant.PsCSAActivityStatus;
import com.lc.billion.icefire.protocol.structure.PsActivityInfo;
import com.lc.billion.icefire.protocol.structure.PsCSABattleInfoVO;
import com.lc.billion.icefire.protocol.structure.PsRankMember;
import com.lc.billion.icefire.rpc.vo.csa.CSAActivityVo;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.config.ServerTypeConfig;
import com.longtech.ls.zookeeper.GameServerConfig;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import com.longtech.ls.zookeeper.KvkSeasonsConfig;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.thrift.TBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class CSAActivityHandler extends AbstractActivityHandler<Activity> {
    @Autowired
    private CSAControlBroadcastToGameService csaControlBroadcastToGameService;
    @Autowired
    private CSAGameDataVoManager csaGameDataVoManager;
    @Autowired
    private CsaBattleService csaBattleService;
    @Autowired
    private RoleRecordManager roleRecordManager;
    @Autowired
    private CsaRoleBattlePointRank csaRoleBattlePointRankHandler;
    @Autowired
    private CSAControlRPCToGameProxyService csaControlRPCToGameProxyService;
    @Autowired
    private CSAActivityHistoryDao csaActivityHistoryDao;
    @Autowired
    private CSAServerBattleInfoDao csaServerBattleInfoDao;
    @Autowired
    private CSAServerBattleInfoManager csaServerBattleInfoManager;
    @Autowired
    private CSAServerTrophyDao serverTrophyDao;

    @Override
    protected void onStartImpl(Activity activity) {
        // 检测是否为CSA中控
        if (Application.getServerType() != ServerType.CSA_CONTROL) {
            throw new AlertException("not csa control but execute csa control operation");
        }

        // CSA友谊赛补偿（只会运行一次）
        CSAServerBattleInfo csaControlServerInfo = csaServerBattleInfoManager.getCSAControlServerBattleInfo();
        if (csaControlServerInfo != null && csaControlServerInfo.getServerCompensate() == 0) {
            csaControlServerInfo.setServerCompensate(1);
            csaServerBattleInfoDao.save(csaControlServerInfo);

            for (CSAServerBattleInfo csaServerBattleInfo : csaServerBattleInfoDao.findAll()) {

                if (csaServerBattleInfo.getServerId() == 92230) {
                    continue;
                }

                // 全服补偿
                CSAServerCompensateRpcThreadOperation csaServerCompensateRpcThreadOperation =
                        new CSAServerCompensateRpcThreadOperation(csaServerBattleInfo.getServerId(), csaServerBattleInfo.getTotalPlayCount(), csaControlRPCToGameProxyService, csaServerBattleInfoDao);
                asyncOperationService.execute(csaServerCompensateRpcThreadOperation);
            }
        }


        // 初始化CSA中控信息
        ServerTypeConfig serverTypeConfig = ServerConfigManager.getInstance().getServerTypeConfig();
        int csaControlServer = serverTypeConfig.getCsaControlServer();
        // 更新中控服信息
        increaseServerPlayCount(csaControlServer, 0, CSAActivitySubType.NORMAL, 0);

        // 活动预热
        CSAActivityContext activityContext = activity.getActivityContext();
        logger.info("csa activity preheat start. id is {}.", activity.getMetaId());
        activityContext.setActivityId(activity.getMetaId());

        long now = TimeUtil.getNow();
        Map<Integer, GameServerConfig> gameConfigs = configCenter.getLsConfig().getGameServers();
        KvkSeasonsConfig kvkSeasons = configCenter.getLsConfig().getKvkSeasons();

        // 初始化可预览的服务器信息
        initCSAServerInfo(activity, activityContext, now, gameConfigs);
        // 广播活动消息
        csaControlBroadcastToGameService.broadcastCSAActivity(activity, 0, false, false);

        // 服务器对战匹配
        serverMatch(activity, activityContext, now, gameConfigs, kvkSeasons);
        activityDao.save(activity);


        CSAServerBattleInfo csaControlServerBattleInfo = csaServerBattleInfoManager.getCSAControlServerBattleInfo();
        if (csaControlServerBattleInfo == null) {
            ErrorLogUtil.errorLog("activity start,but csa control info is null");
            return;
        }

        // 活动开始
        logger.info("csa activity start. id is {}.", activity.getMetaId());

        // 更新活动中的服务器信息
        for (Map.Entry<Integer, CSAActivityGroupContext> entry : activityContext.getServerInfoMap().entrySet()) {

            int serverId = entry.getKey();
            CSAActivityGroupContext csaActivityGroupContext = entry.getValue();

            // 轮空
            if (csaActivityGroupContext.isBye()) {
                continue;
            }

            increaseServerPlayCount(serverId, csaActivityGroupContext.getSeason(), csaActivityGroupContext.getSubType(), csaActivityGroupContext.getAgainstServerId());

            csaActivityGroupContext.setCsaActivityStatus(CSAActivityStatus.WARMUP);
            csaActivityGroupContext.setNextCsaActivityStatusTime(activity.getStartTime() + CSAActivityStatus.WARMUP.currStageContinueTime());
        }
        activityDao.save(activity);

        // 持久化活动部分内容
        CSAActivityHistory csaActivityHistory = csaActivityHistoryDao.create(serverTypeConfig.getCsaControlServer(), activity.getMetaId(), csaControlServerBattleInfo.getTotalPlayCount());
        csaActivityHistory.setActivityStartTime(activity.getStartTime());
        csaActivityHistory.setActivityEndTime(activity.getEndTime());
        csaActivityHistoryDao.save(csaActivityHistory);

        // 计算服务器战力
        for (Integer serverId : activityContext.getServerInfoMap().keySet()) {
            CSAActivityGroupContext csaActivityGroupContext = activityContext.getServerInfoMap().get(serverId);

            // 广播活动消息
            csaActivityGroupContext.setCurrStageEventTriggerTime(csaActivityGroupContext.getCurrStageEventTriggerTime() + 1);
            csaControlBroadcastToGameService.broadcastCSAActivity(activity, serverId, true, false);

            CSAGetGameServerCombatRpcThreadOperation getGameServerCombatRpcThreadOperation = new CSAGetGameServerCombatRpcThreadOperation(serverId, activity, activityDao, csaControlRPCToGameProxyService, csaControlBroadcastToGameService);
            asyncOperationService.execute(getGameServerCombatRpcThreadOperation);
        }
    }

    @Override
    protected void onStopImpl(Activity activity) {
        logger.info("csa activity stop. id is {}.", activity.getMetaId());

        // 广播活动消息
        csaControlBroadcastToGameService.broadcastCSAActivity(activity, 0, false, false);
    }

    @Override
    public ActivityType getType() {
        return ActivityType.CROSS_SERVER_ATTACK;
    }

    @Override
    public void tick(Activity activity, long now) {
        // CSA活动阶段处理
        CSAActivityContext csaActivityContext = activity.getActivityContext();
        if (csaActivityContext == null) {
            return;
        }

        for (var entry : csaActivityContext.getServerInfoMap().entrySet()) {
            Integer serverId = entry.getKey();
            CSAActivityGroupContext groupContext = entry.getValue();
            long nextCsaActivityStatusTime = groupContext.getNextCsaActivityStatusTime();
            if (now >= nextCsaActivityStatusTime) {
                csaActivityStatusContinue(activity, serverId);
            }
        }
    }

    @Override
    public TBase<?, ?> getActivityInfo(Role role, String activityTypeMetaId) {
        if (role == null) {
            ErrorLogUtil.errorLog("get csa activity info but role is null", "serverId",Application.getServerId());
            return null;
        }

        CSAActivityVo csaActivityVo = csaGameDataVoManager.getCsaActivity();
        if (csaActivityVo != null) {
            int serverId = csaBattleService.getRoleOriginGameServer(role);

            CSAActivityContext activityContext = csaActivityVo.getActivityContext();
            CSAActivityGroupContext csaActivityGroupContext = activityContext.getServerInfoMap().get(serverId);

            if (csaActivityGroupContext == null) {
                return null;
            }

            CSAActivityStatus csaActivityStatus = csaActivityGroupContext.getCsaActivityStatus();

            GcCSAStageInfo msg = new GcCSAStageInfo();
            RoleRecord roleRecord = roleRecordManager.getRoleRecordAndCheckCSA(role.getRoleId(), csaActivityVo.getStartTime());
            if (roleRecord != null && roleRecord.getCsaEnterNum() > 0) {
                msg.setCsaEnterNum(roleRecord.getCsaEnterNum());
            }

            msg.setStatus(csaActivityStatus.getCsaActivityStatus());
            msg.setNextStatusTime(csaActivityGroupContext.getNextCsaActivityStatusTime());
            if (csaActivityStatus == CSAActivityStatus.BATTLE_FIRST_SETTLEMENT) {
                msg.setNextStatusTime(csaActivityGroupContext.getNextCsaActivityStatusTime() + CSAActivityStatus.BATTLE_REST.currStageContinueTime());
            } else if (csaActivityStatus == CSAActivityStatus.BATTLE_SECOND_SETTLEMENT) {
                msg.setNextStatusTime(csaActivityVo.getEndTime());
            }

            Map<Integer/* serverId */, PsRankMember> mvpPsRankMember = csaRoleBattlePointRankHandler.buildMvpPsRankMember(serverId);

            PsCSABattleInfoVO selfBattleInfo = new PsCSABattleInfoVO();
            // 判断当前服是否为进攻状态
            boolean isAttack = csaActivityStatus.getCsaClientShowStatus() != 0 && csaActivityGroupContext.isFirstHalfAttack() == (csaActivityStatus.getCsaClientShowStatus() == 1);
            selfBattleInfo.setIsAttacker(isAttack);
            selfBattleInfo.setServerId(serverId);
            selfBattleInfo.setIsWin(csaActivityGroupContext.isActivityWin());
            selfBattleInfo.setServerCombat(Double.valueOf(csaActivityGroupContext.getServerCombat()).longValue());
            selfBattleInfo.setServerScore(getCurrStageShowScore(csaActivityStatus, csaActivityGroupContext));
            selfBattleInfo.setMvpPlayerInfo(mvpPsRankMember.get(serverId));
            CSAServerBattleInfo csaServerBattleInfo = csaGameDataVoManager.getCsaServerBattleInfoMap().get(csaActivityGroupContext.getSelfServerId());
            int currWinTime = csaServerBattleInfo == null ? 0 : csaServerBattleInfo.getCurrLeagueWinTime();
            selfBattleInfo.setWinTimes(currWinTime);
            // 奖杯信息
            Map<String, Integer> cupMap = new HashMap<>();
            Collection<CSAServerTrophy> csaServerTrophies = serverTrophyDao.findByCurrentServerId(csaActivityGroupContext.getSelfServerId());
            if (JavaUtils.bool(csaServerTrophies)) {
                for (CSAServerTrophy csaServerTrophy : csaServerTrophies) {
                    cupMap.compute(csaServerTrophy.getMetaId(), (k, v) -> v == null || v == 0 ? 1 : v + 1);
                }
            }
            selfBattleInfo.setCupMap(cupMap);
            msg.setSelfServerBattleInfoVo(selfBattleInfo);

            CSAActivityGroupContext enemyCsaActivityGroupContext = activityContext.getServerInfoMap().get(csaActivityGroupContext.getAgainstServerId());
            if (enemyCsaActivityGroupContext != null) {
                PsCSABattleInfoVO enemyBattleInfo = new PsCSABattleInfoVO();
                // 判断对手服是否为进攻状态
                boolean againstIsAttack = csaActivityStatus.getCsaClientShowStatus() != 0 && enemyCsaActivityGroupContext.isFirstHalfAttack() == (csaActivityStatus.getCsaClientShowStatus() == 1);
                enemyBattleInfo.setIsAttacker(againstIsAttack);
                enemyBattleInfo.setServerId(enemyCsaActivityGroupContext.getSelfServerId());
                enemyBattleInfo.setIsWin(enemyCsaActivityGroupContext.isActivityWin());
                enemyBattleInfo.setServerCombat(Double.valueOf(enemyCsaActivityGroupContext.getServerCombat()).longValue());
                enemyBattleInfo.setServerScore(getCurrStageShowScore(csaActivityStatus, enemyCsaActivityGroupContext));
                enemyBattleInfo.setMvpPlayerInfo(mvpPsRankMember.get(csaActivityGroupContext.getAgainstServerId()));
                CSAServerBattleInfo againstCsaBattleInfo = csaGameDataVoManager.getCsaServerBattleInfoMap().get(csaActivityGroupContext.getAgainstServerId());
                int againstCurrWinTime = againstCsaBattleInfo == null ? 0 : againstCsaBattleInfo.getCurrLeagueWinTime();
                enemyBattleInfo.setWinTimes(againstCurrWinTime);
                // 奖杯信息
                Map<String, Integer> againstCupMap = new HashMap<>();
                Collection<CSAServerTrophy> againstCsaServerTrophies = serverTrophyDao.findByCurrentServerId(csaActivityGroupContext.getAgainstServerId());
                if (JavaUtils.bool(againstCsaServerTrophies)) {
                    for (CSAServerTrophy csaServerTrophy : againstCsaServerTrophies) {
                        againstCupMap.compute(csaServerTrophy.getMetaId(), (k, v) -> v == null || v == 0 ? 1 : v + 1);
                    }
                }
                enemyBattleInfo.setCupMap(againstCupMap);
                msg.setEnemyServerBattleInfoVo(enemyBattleInfo);
            }

            msg.setAcivityListId(String.valueOf(csaActivityVo.getId()));
            msg.setMatchConfigId(csaActivityGroupContext.getCsaMatchMetaId());
            // 返回给客户端服务器选择时间
            msg.setSelectTimeList(new ArrayList<>(csaActivityGroupContext.getSelectedBattleTimeList()));
            // 通知客户端个人得分
            RoleCsaBattle roleCsaBattle = csaBattleService.getRoleCsaBattle(role);
            if (roleCsaBattle != null) {
                msg.setRoleBattlePoint(roleCsaBattle.getBattlePoint());
            }

            CrossSeverAttackSetting crossSeverAttackSetting = configService.getConfig(CrossSeverAttackSetting.class);
            msg.setTotalGameCount(crossSeverAttackSetting.getCsaSeriesMatchNum());

            int season = csaActivityGroupContext.getSeason();
            CSAServerBattleInfo gameCsaServerBattleInfo = csaGameDataVoManager.getGameCsaServerBattleInfo(csaActivityGroupContext.getSelfServerId());

            GcCSAActivityInfoV2 csaActivityInfoV2 = new GcCSAActivityInfoV2();

            if (csaActivityGroupContext.getSubType() == CSAActivitySubType.LEAGUE && gameCsaServerBattleInfo != null && gameCsaServerBattleInfo.getLeagueRecord().containsKey(season)) {

                msg.setCurrPlayGameCount(gameCsaServerBattleInfo.getLeagueRecord().get(season));
                csaActivityInfoV2.setIsLeague(true);
            } else {

                msg.setCurrPlayGameCount(0);
                csaActivityInfoV2.setIsLeague(false);
            }

            role.send(csaActivityInfoV2);
            return msg;
        }

        return null;
    }

    /**
     * 获取当前阶段展示分数
     */
    private int getCurrStageShowScore(CSAActivityStatus csaActivityStatus, CSAActivityGroupContext csaActivityGroupContext) {
        return csaBattleService.getBattleScoreByStatus(csaActivityStatus, csaActivityGroupContext);
    }

    /**
     * 活动状态流转
     * <p>
     * 代码逻辑中存在同步控制，目前在且只能在tick中调用
     *
     * @param activity
     * @param serverId
     */
    public void csaActivityStatusContinue(Activity activity, int serverId) {
        CSAActivityContext activityContext = activity.getActivityContext();
        CSAActivityGroupContext csaActivityGroupContext = activityContext.getServerInfoMap().get(serverId);
        if (csaActivityGroupContext == null) {
            return;
        }

        // 获取对手服信息
        CSAActivityGroupContext csaAgainstActivityGroupContext = CSAUtils.getCsaActivityGroupContext(activity, csaActivityGroupContext.getAgainstServerId());
        if (csaAgainstActivityGroupContext == null) {
            return;
        }

        // 当前阶段
        CSAActivityStatus currentCsaActivityStatus = csaActivityGroupContext.getCsaActivityStatus();
        // 下一阶段开始时间
        long nextStageStartTime = csaActivityGroupContext.getNextCsaActivityStatusTime();

        // 推送失败需要再推送一下
        if (csaActivityGroupContext.isPushGameFailed()) {
            csaControlBroadcastToGameService.broadcastCSAActivity(activity, serverId, false, true);
        }

        // 事件触发
        if (csaActivityGroupContext.getCurrStageEventTriggerTime() == csaActivityGroupContext.getCurrStageEventCompleteTime()
                && csaAgainstActivityGroupContext.getCurrStageEventTriggerTime() == csaAgainstActivityGroupContext.getCurrStageEventCompleteTime()
                && csaActivityGroupContext.getCurrStageEventCompleteTime() == csaAgainstActivityGroupContext.getCurrStageEventCompleteTime()) {

            // 事件全部完成，触发下一阶段
            if (csaActivityGroupContext.getCurrStageEventCompleteTime() == currentCsaActivityStatus.getEventList(csaActivityGroupContext).size()
                    && csaAgainstActivityGroupContext.getCurrStageEventCompleteTime() == currentCsaActivityStatus.getEventList(csaAgainstActivityGroupContext).size()) {

                // 事件全部完成，且没有下一阶段
                PsCSAActivityStatus psNextCSAActivityStatus = currentCsaActivityStatus.getNextCsaActivityStatus();
                if (psNextCSAActivityStatus == null) {
                    return;
                }
                CSAActivityStatus nextCsaActivityStatus = CSAActivityStatus.findById(psNextCSAActivityStatus.getValue());
                if (nextCsaActivityStatus == null) {
                    return;
                }

                // 构建本服阶段信息
                csaActivityGroupContext.setCsaActivityStatus(nextCsaActivityStatus);
                // 设置阶段结束时间，部分阶段特殊处理
                if (nextCsaActivityStatus == CSAActivityStatus.OVER) {
                    csaActivityGroupContext.setNextCsaActivityStatusTime(activity.getEndTime());
                } else if (nextCsaActivityStatus == CSAActivityStatus.BATTLE_NOTICE) {
                    csaActivityGroupContext.setNextCsaActivityStatusTime(csaActivityGroupContext.getBattleStartTime());
                } else {
                    csaActivityGroupContext.setNextCsaActivityStatusTime(nextStageStartTime + nextCsaActivityStatus.currStageContinueTime());
                }
                csaActivityGroupContext.setCurrStageEventTriggerTime(0);
                csaActivityGroupContext.setCurrStageEventCompleteTime(0);

                // 构建对手服阶段信息
                csaAgainstActivityGroupContext.setCsaActivityStatus(nextCsaActivityStatus);
                // 设置阶段结束时间，部分阶段特殊处理
                if (nextCsaActivityStatus == CSAActivityStatus.OVER) {
                    csaAgainstActivityGroupContext.setNextCsaActivityStatusTime(activity.getEndTime());
                } else if (nextCsaActivityStatus == CSAActivityStatus.BATTLE_NOTICE) {
                    csaAgainstActivityGroupContext.setNextCsaActivityStatusTime(csaAgainstActivityGroupContext.getBattleStartTime());
                } else {
                    csaAgainstActivityGroupContext.setNextCsaActivityStatusTime(nextStageStartTime + nextCsaActivityStatus.currStageContinueTime());
                }
                csaAgainstActivityGroupContext.setCurrStageEventTriggerTime(0);
                csaAgainstActivityGroupContext.setCurrStageEventCompleteTime(0);
            }

            csaActivityGroupContext.setCurrStageEventTriggerTime(csaActivityGroupContext.getCurrStageEventTriggerTime() + 1);
            csaAgainstActivityGroupContext.setCurrStageEventTriggerTime(csaAgainstActivityGroupContext.getCurrStageEventTriggerTime() + 1);

            activityDao.save(activity);
            // 广播活动消息
            csaControlBroadcastToGameService.broadcastCSAActivity(activity, serverId, true, true);
        }
    }

    @Override
    public PsActivityInfo getRoleActivityInfo(Role role, ActivityListConfig.ActivityListMeta activityMeta) {
        CSAActivityVo csaActivityVo = csaGameDataVoManager.getCsaActivity();
        if (csaActivityVo != null) {
            PsActivityInfo info = new PsActivityInfo();
            info.setId(String.valueOf(csaActivityVo.getId()));
            info.setStartTime(csaActivityVo.getStartTime());
            info.setEndTime(csaActivityVo.getEndTime());
            info.setMetaId(activityMeta.getId());
            info.setType(getType().getPsType());
            info.setStatus(csaActivityVo.getStatus().getPsStatus());
            info.setHasReward(false);
            return info;
        }
        return null;
    }


    @Override
    public boolean createValidated(ActivityListConfig.ActivityListMeta activityMeta, long activityEndTime) {
        // 跨服夺城根据服务器本身来确认某个服务器是否开启，不在活动开启时阻拦整个活动
        return true;
    }

    /**
     * 服务器场参与次增1
     *
     * @return 当前赛季联赛胜场数
     */
    private void increaseServerPlayCount(int serverId, int season, CSAActivitySubType subType, int againstServerId) {
        CrossSeverAttackSetting crossSeverAttackSetting = configService.getConfig(CrossSeverAttackSetting.class);
        CSAServerBattleInfo csaServerBattleInfo = csaServerBattleInfoDao.findById((long) serverId);
        ServerTypeConfig serverTypeConfig = ServerConfigManager.getInstance().getServerTypeConfig();
        if (csaServerBattleInfo == null) {
            csaServerBattleInfo = csaServerBattleInfoDao.create(serverTypeConfig.getCsaControlServer(), serverId);
        }
        csaServerBattleInfo.setCurrSeason(season);

        if (subType == CSAActivitySubType.LEAGUE && season > 1) {
            Integer seasonPlayRecord = csaServerBattleInfo.getLeagueRecord().get(season);

            // 赛季开启新一轮联赛
            if (seasonPlayRecord == null) {
                csaServerBattleInfo.getLeagueRecord().put(season, 1);
                csaServerBattleInfo.setCurrLeaguePlayCount(1);
                csaServerBattleInfo.setCurrLeagueWinTime(0);
            } else {
                csaServerBattleInfo.getLeagueRecord().put(season, seasonPlayRecord + 1);
                csaServerBattleInfo.setCurrLeaguePlayCount(seasonPlayRecord + 1);
            }
        }

        csaServerBattleInfo.getRecentMatchedServerList().add(againstServerId);
        if (csaServerBattleInfo.getRecentMatchedServerList().size() > crossSeverAttackSetting.getCsaPracticeMatchCondition()) {
            int sparePlayCount = csaServerBattleInfo.getRecentMatchedServerList().size() - crossSeverAttackSetting.getCsaPracticeMatchCondition();

            for (int i = 0; i < sparePlayCount; i++) {
                if (csaServerBattleInfo.getRecentMatchedServerList().isEmpty()) {
                    break;
                }

                csaServerBattleInfo.getRecentMatchedServerList().remove(0);
            }
        }

        csaServerBattleInfo.setTotalPlayCount(csaServerBattleInfo.getTotalPlayCount() + 1);
        csaServerBattleInfoDao.save(csaServerBattleInfo);
    }

    /**
     * 服务器对战匹配
     *
     * @param activity
     * @param activityContext
     * @param now
     * @param gameConfigs
     * @param kvkSeasons
     */
    private void serverMatch(Activity activity, CSAActivityContext activityContext, long now, Map<Integer, GameServerConfig> gameConfigs, KvkSeasonsConfig kvkSeasons) {
        CrossSeverAttackSetting crossSeverAttackSetting = configService.getConfig(CrossSeverAttackSetting.class);
        CrossSeverAttackMatchConfig crossSeverAttackMatchConfig = configService.getConfig(CrossSeverAttackMatchConfig.class);

        // 可对战服务器
        List<Integer> canMatchServerList = new ArrayList<>();
        // 服务器匹配结果（用来做校验）attack -> defense
        Map<Integer, Integer> matchResult = new HashMap<>();

        // 筛选可对战服务器
        for (Integer serverId : activityContext.getServerInfoMap().keySet()) {
            GameServerConfig gameServerConfig = gameConfigs.get(serverId);

            // 开服时长大于设置天数才可以对战
            if (gameServerConfig.getOpenTimeMs() + crossSeverAttackSetting.getCsaMatchLimit() * TimeUtil.DAY_MILLIS > now) {
                continue;
            }

            canMatchServerList.add(serverId);
        }

        // 打乱排序
        Collections.shuffle(canMatchServerList);


        List<String> metaList = crossSeverAttackMatchConfig.getGroupByActivity().get(activity.getMetaId());
        List<String> defaultConfigIdList = crossSeverAttackMatchConfig.getDefaultConfigMap().containsKey(activity.getMetaId()) ?
                crossSeverAttackMatchConfig.getDefaultConfigMap().get(activity.getMetaId()) : crossSeverAttackMatchConfig.getDefaultConfigIdList();

        // 正常匹配

        // 优先进行数值设置的匹配
        List<CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta> matchMetaList = new ArrayList<>();
        if (JavaUtils.bool(metaList)) {
            for (String matchCfgId : metaList) {
                CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta crossSeverAttackMatchMeta = crossSeverAttackMatchConfig.getMeta(matchCfgId);
                matchMetaList.add(crossSeverAttackMatchMeta);
            }
        }

        // 数值匹配
        metaMatch(activity, activityContext, now, gameConfigs, kvkSeasons, canMatchServerList, matchResult, matchMetaList);

        // 打印看下匹配结果
        logger.info("meta match is {}.", JSON.toJSONString(matchResult));


        // 处理自由匹配池
        // 根据赛季匹配 key1: subType -> < key2: season   value: List<ServerId> >
        Map<Integer, Map<Integer, List<Integer>>> freeTypeSeasonServerMap = getMetaTypeSeasonServerMap(now, activityContext, gameConfigs, kvkSeasons, canMatchServerList);

        // 联赛分组
        Map<Integer, List<Integer>> freeLeagueSeasonServerMap = freeTypeSeasonServerMap.get(CSAActivitySubType.LEAGUE.getId());
        if (freeLeagueSeasonServerMap != null && !freeLeagueSeasonServerMap.isEmpty()) {
            // 获得自由匹配结果
            for (Map.Entry<Integer, List<Integer>> entry : freeLeagueSeasonServerMap.entrySet()) {
                Integer season = entry.getKey();
                List<Integer> sameSeasonServerList = entry.getValue();

                if (sameSeasonServerList.size() % 2 != 0) {
                    // 服务器号最大的轮空
                    Integer bysServer = 0;
                    for (Integer server : sameSeasonServerList) {
                        bysServer = server > bysServer ? server : bysServer;
                    }
                    sameSeasonServerList.remove(bysServer);
                    logger.info("free match bye. season is {}. server is {}.", season, bysServer);
                }

                // 打乱排序
                Collections.shuffle(sameSeasonServerList);
                // 进行最终匹配
                matchBattleByWinTime(activity, activityContext, canMatchServerList, matchResult, getDefaultMatchConfigList(activity.getMetaId()).get(0), sameSeasonServerList);
            }
        }

        // 友谊赛分组
        Map<Integer, List<Integer>> freeNormalSeasonServerMap = freeTypeSeasonServerMap.get(CSAActivitySubType.NORMAL.getId());
        if (freeNormalSeasonServerMap != null && !freeNormalSeasonServerMap.isEmpty()) {
            // 获得自由匹配结果
            for (Map.Entry<Integer, List<Integer>> entry : freeNormalSeasonServerMap.entrySet()) {
                Integer season = entry.getKey();
                List<Integer> sameSeasonServerList = entry.getValue();

                if (sameSeasonServerList.size() % 2 != 0) {
                    // 服务器号最大的轮空
                    Integer bysServer = 0;
                    for (Integer server : sameSeasonServerList) {
                        bysServer = server > bysServer ? server : bysServer;
                    }
                    sameSeasonServerList.remove(bysServer);
                    logger.info("free match bye. season is {}. server is {}.", season, bysServer);
                }

                // 打乱排序
                Collections.shuffle(sameSeasonServerList);
                // 进行最终匹配
                matchBattleByRecentServer(activity, activityContext, canMatchServerList, matchResult, getDefaultMatchConfigList(activity.getMetaId()).get(0), sameSeasonServerList);
            }
        }

        // 打印看下轮空结果 （这里应该为空）
        logger.info("free bye result is {}.", JSON.toJSONString(freeTypeSeasonServerMap));

        // 打印看下匹配结果
        logger.info("free match is {}.", JSON.toJSONString(matchResult));


        // 在跨服夺城友谊赛功能后 此部分弃用

			/*
			if (crossSeverAttackSetting.getCsaKvkForceMatchSwitch() != 0) {
				// 已匹配的k组
				List<Integer> matchedKServer = new ArrayList<>();
				// 生成的匹配配置
				// List<CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta> matchMetaList = new ArrayList<>();

				// K服强制分时段匹配
				if (JavaUtils.bool(metaList)) {
					List<CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta> forceMatchMetaListByMeta = new ArrayList<>();
					for (String matchCfgId : metaList) {
						CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta originMatchConfig = crossSeverAttackMatchConfig.getMeta(matchCfgId);
						List<Integer> battleServerList = originMatchConfig.getBattleServerList();

						// 生成匹配配置
						forceMatchMetaListByMeta.addAll(buildKvkForceMatchMeta(now, kvkSeasons, crossSeverAttackMatchConfig, defaultConfigIdList, matchedKServer, originMatchConfig, battleServerList));
					}

					if (JavaUtils.bool(forceMatchMetaListByMeta)) {
						metaMatch(activity, activityContext, now, gameConfigs, kvkSeasons, canMatchServerList, matchResult, forceMatchMetaListByMeta);
					}
				}

				// 未配置的服务器生成匹配数值
				List<CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta> forceMatchMetaList = buildKvkForceMatchMeta(now, kvkSeasons, crossSeverAttackMatchConfig, defaultConfigIdList, matchedKServer, null, canMatchServerList);
				if (JavaUtils.bool(forceMatchMetaList)) {
					metaMatch(activity, activityContext, now, gameConfigs, kvkSeasons, canMatchServerList, matchResult, forceMatchMetaList);
				}
			}
			*/
    }

    private List<CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta> buildKvkForceMatchMeta(long now, KvkSeasonsConfig kvkSeasons, CrossSeverAttackMatchConfig crossSeverAttackMatchConfig, List<String> defaultConfigIdList, List<Integer> matchedKServer, CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta originMatchConfig, List<Integer> battleServerList) {
        List<CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta> matchMetaList = new ArrayList<>();
        // 参与匹配的非赛季服
        List<Integer> originGameServerList = new ArrayList<>();
        // 参与匹配的K服
        Map<Integer, List<Integer>> seasonKMap = new HashMap<>();

        for (int serverId : battleServerList) {
            KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = kvkSeasons.getServerGroupByOServerIdAndTime(serverId, now);
            if (kvkSeasonServerGroupConfig != null && kvkSeasonServerGroupConfig.getSeason() >= 2) {
                // 检验K组是否已分配
                int kServerId = kvkSeasonServerGroupConfig.getKServerId();
                if (matchedKServer.contains(kServerId)) {
                    continue;
                }

                seasonKMap.compute(kvkSeasonServerGroupConfig.getSeason(), (k, v) -> v == null ? new ArrayList<>() : v).add(kServerId);
                // 记录已分配K组
                matchedKServer.add(kServerId);
            } else {
                originGameServerList.add(serverId);
            }
        }

        // 生成非赛季服匹配配置
        if (JavaUtils.bool(originGameServerList)) {
            CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta defaultMeta = crossSeverAttackMatchConfig.getMeta(defaultConfigIdList.get(0));
            CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta originGameServerMatch = originMatchConfig == null ?
                    defaultMeta.copy(defaultMeta.getId(), originGameServerList, defaultMeta.getCanChooseTime()) : originMatchConfig.copy(defaultMeta.getId(), originGameServerList, defaultMeta.getCanChooseTime());
            matchMetaList.add(originGameServerMatch);
        }

        // 生成赛季服匹配配置
        if (JavaUtils.bool(seasonKMap)) {
            for (Map.Entry<Integer, List<Integer>> entry : seasonKMap.entrySet()) {
                List<Integer> kServerList = entry.getValue();
                if (kServerList.isEmpty()) {
                    continue;
                }

                // 如果为奇数，取一个K组进行组内匹配
                if (kServerList.size() % 2 != 0) {
                    int kServerId = kServerList.remove(kServerList.size() - 1);
                    KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getKvkSeasonServerGroupConfigByKServerId(kServerId, now);
                    if (kvkSeasonServerGroupConfig != null) {
                        List<Integer> oServerIds = new ArrayList<>(kvkSeasonServerGroupConfig.getOServerIds());
                        for (int i = 0; i < oServerIds.size() / 2; i++) {
                            List<Integer> matchServerList = new ArrayList<>();
                            matchServerList.add(oServerIds.get(2 * i));
                            matchServerList.add(oServerIds.get(2 * i + 1));

                            CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta defaultMeta = crossSeverAttackMatchConfig.getMeta(defaultConfigIdList.get(i % defaultConfigIdList.size()));
                            CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta oneKServerMatch = originMatchConfig == null ?
                                    defaultMeta.copy(defaultMeta.getId(), matchServerList, defaultMeta.getCanChooseTime()) : originMatchConfig.copy(defaultMeta.getId(), matchServerList, defaultMeta.getCanChooseTime());
                            matchMetaList.add(oneKServerMatch);
                        }
                    }
                }

                if (kServerList.isEmpty()) {
                    continue;
                }

                Map<String, List<Integer>> defaultConfigServerMap = new HashMap<>();
                for (int kServerId : kServerList) {
                    KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getKvkSeasonServerGroupConfigByKServerId(kServerId, now);
                    if (kvkSeasonServerGroupConfig != null) {
                        List<Integer> oServerIds = new ArrayList<>(kvkSeasonServerGroupConfig.getOServerIds());
                        for (int i = 0; i < oServerIds.size(); i++) {
                            CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta defaultMeta = crossSeverAttackMatchConfig.getMeta(defaultConfigIdList.get(i % defaultConfigIdList.size()));
                            defaultConfigServerMap.compute(defaultMeta.getId(), (k, v) -> v == null ? new ArrayList<>() : v).add(oServerIds.get(i));
                        }
                    }
                }

                for (Map.Entry<String, List<Integer>> defaultConfigServer : defaultConfigServerMap.entrySet()) {
                    CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta defaultMeta = crossSeverAttackMatchConfig.getMeta(defaultConfigServer.getKey());
                    CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta moreKeyServerMatch = originMatchConfig == null ?
                            defaultMeta.copy(defaultMeta.getId(), defaultConfigServer.getValue(), defaultMeta.getCanChooseTime()) : originMatchConfig.copy(defaultMeta.getId(), defaultConfigServer.getValue(), defaultMeta.getCanChooseTime());
                    matchMetaList.add(moreKeyServerMatch);
                }
            }
        }

        return matchMetaList;
    }

    private void metaMatch(Activity activity, CSAActivityContext activityContext, long now, Map<Integer, GameServerConfig> gameConfigs, KvkSeasonsConfig kvkSeasons, List<Integer> canMatchServerList, Map<Integer, Integer> matchResult, List<CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta> matchMetaList) {
        if (JavaUtils.bool(matchMetaList)) {
            for (CrossSeverAttackMatchConfig.CrossSeverAttackMatchMeta crossSeverAttackMatchMeta : matchMetaList) {
                if (crossSeverAttackMatchMeta == null) {
                    continue;
                }

                String metaId = crossSeverAttackMatchMeta.getId();
                // 未分组配置不处理
                if (crossSeverAttackMatchMeta.getGroupType() == 0) {
                    continue;
                }

                List<Integer> battleServerList = crossSeverAttackMatchMeta.getBattleServerList();
                List<Integer> battleServerListCopy = new ArrayList<>(battleServerList);
                // 打乱排序
                Collections.shuffle(battleServerListCopy);
                logger.info("after shuffle. meta is {}. server list is {}.", metaId, JSON.toJSONString(battleServerListCopy));

                // 筛选可用服务器
                if (JavaUtils.bool(battleServerList)) {
                    for (Integer serverId : battleServerList) {
                        // 不可对战但是有数值配置
                        if (!canMatchServerList.contains(serverId)) {
                            ErrorLogUtil.errorLog("server can not attend csa", "serverId",serverId, "metaId",metaId);
                            battleServerListCopy.remove(serverId);
                        }
                    }
                }

                // 根据 比赛类型 赛季 分组   key1: subType -> < key2: season   value: List<ServerId> >
                Map<Integer, Map<Integer, List<Integer>>> metaTypeSeasonServerMap = getMetaTypeSeasonServerMap(now, activityContext, gameConfigs, kvkSeasons, battleServerListCopy);

                // 联赛分组
                Map<Integer, List<Integer>> metaLeagueSeasonServerMap = metaTypeSeasonServerMap.get(CSAActivitySubType.LEAGUE.getId());
                if (metaLeagueSeasonServerMap != null && !metaLeagueSeasonServerMap.isEmpty()) {
                    // 获得数值匹配结果
                    for (Map.Entry<Integer, List<Integer>> entry : metaLeagueSeasonServerMap.entrySet()) {
                        List<Integer> sameSeasonServerList = entry.getValue();
                        // 打乱排序
                        Collections.shuffle(sameSeasonServerList);
                        // 进行最终匹配
                        matchBattleByWinTime(activity, activityContext, canMatchServerList, matchResult, metaId, sameSeasonServerList);
                    }
                }

                // 友谊赛分组
                Map<Integer, List<Integer>> metaNormalSeasonServerMap = metaTypeSeasonServerMap.get(CSAActivitySubType.NORMAL.getId());
                if (metaNormalSeasonServerMap != null && !metaNormalSeasonServerMap.isEmpty()) {
                    // 获得数值匹配结果
                    for (Map.Entry<Integer, List<Integer>> entry : metaNormalSeasonServerMap.entrySet()) {
                        List<Integer> sameSeasonServerList = entry.getValue();
                        // 打乱排序
                        Collections.shuffle(sameSeasonServerList);
                        // 进行最终匹配
                        matchBattleByRecentServer(activity, activityContext, canMatchServerList, matchResult, metaId, sameSeasonServerList);
                    }
                }

                // 打印看下轮空结果
                logger.info("bye result is {}.", JSON.toJSONString(metaTypeSeasonServerMap));
            }
        }
    }

    /**
     * 获取无分组配置
     */
    private List<String> getDefaultMatchConfigList(String activityId) {
        CrossSeverAttackMatchConfig crossSeverAttackMatchConfig = configService.getConfig(CrossSeverAttackMatchConfig.class);
        return crossSeverAttackMatchConfig.getDefaultConfigMap().containsKey(activityId) ?
                crossSeverAttackMatchConfig.getDefaultConfigMap().get(activityId) : crossSeverAttackMatchConfig.getDefaultConfigIdList();
    }

    /**
     * 根据胜场匹配
     *
     * @param activity
     * @param activityContext
     * @param canMatchServerList
     * @param matchResult
     * @param metaId
     * @param sameSeasonServerList
     */
    private void matchBattleByWinTime(Activity activity, CSAActivityContext activityContext, List<Integer> canMatchServerList, Map<Integer, Integer> matchResult, String metaId, List<Integer> sameSeasonServerList) {
        CSAServerBattleInfo csaControlServerBattleInfo = csaServerBattleInfoManager.getCSAControlServerBattleInfo();
        if (csaControlServerBattleInfo == null) {
            ErrorLogUtil.errorLog("match start,but csa control info is null");
            return;
        }

        // server -> 胜场 Map
        Map<Integer, Integer> serverWinTimes = csaServerBattleInfoManager.getServerWinTimes(sameSeasonServerList);
        // 胜场 -> server Map
        Map<Integer, List<Integer>> winTimesServerListMap = csaServerBattleInfoManager.getWinTimesServerListMap(sameSeasonServerList);

        while (sameSeasonServerList.size() > 1) {
            Integer attackServer = RandomUtils.random(sameSeasonServerList);
            sameSeasonServerList.remove(attackServer);
            canMatchServerList.remove(attackServer);

            // 胜场 -> server Map 数据同步移除
            int winTimes = serverWinTimes.get(attackServer) == null ? 0 : serverWinTimes.get(attackServer);
            if (winTimesServerListMap.get(winTimes) != null) {
                winTimesServerListMap.get(winTimes).remove(attackServer);
            } else {
                ErrorLogUtil.errorLog("map is error", "winTimes",winTimes,
                        "serverWinTimes",JSON.toJSONString(serverWinTimes),
                        "winTimesServerListMap",JSON.toJSONString(winTimesServerListMap));
            }

            // 获取胜场数接近的服务器
            int dValue = 0;
            List<Integer> canUseServerList = winTimesServerListMap.get(winTimes);
            while (!JavaUtils.bool(canUseServerList)) {
                dValue = dValue + 1;
                if (dValue > winTimesServerListMap.size() - 1) {
                    ErrorLogUtil.errorLog("越界了但都没找到可匹配的", "winTimesServerListMap",JSON.toJSONString(winTimesServerListMap));
                    canUseServerList = sameSeasonServerList;
                    break;
                } else {
                    if (JavaUtils.bool(winTimesServerListMap.get(winTimes + dValue))) {
                        canUseServerList = winTimesServerListMap.get(winTimes + dValue);
                        break;
                    } else if (JavaUtils.bool(winTimesServerListMap.get(winTimes - dValue))) {
                        canUseServerList = winTimesServerListMap.get(winTimes - dValue);
                        break;
                    }
                }
            }

            Integer defenseServer = RandomUtils.random(canUseServerList);

            int defenseWinTimes = serverWinTimes.get(defenseServer) == null ? 0 : serverWinTimes.get(defenseServer);
            if (winTimesServerListMap.get(defenseWinTimes) != null) {
                winTimesServerListMap.get(defenseWinTimes).remove(defenseServer);
            } else {
                ErrorLogUtil.errorLog("map is error", "winTimes",defenseWinTimes,
                        "serverWinTimes",JSON.toJSONString(serverWinTimes),
                        "winTimesServerListMap",JSON.toJSONString(winTimesServerListMap));
            }

            sameSeasonServerList.remove(defenseServer);
            canMatchServerList.remove(defenseServer);

            // 添加对战信息
            matchResult.put(attackServer, defenseServer);

            String rankId = "PlayCount" + csaControlServerBattleInfo.getTotalPlayCount() + activity.getMetaId() + "server" + attackServer + "server" + defenseServer;

            // 构建先攻服信息
            CSAActivityGroupContext firstAttackGroupContext = activityContext.getServerInfoMap().get(attackServer);

            firstAttackGroupContext.setSelfServerId(attackServer);
            firstAttackGroupContext.setAgainstServerId(defenseServer);
            firstAttackGroupContext.setFirstHalfAttack(true);
            firstAttackGroupContext.setCsaMatchMetaId(metaId);
            firstAttackGroupContext.setRankId(rankId);
            firstAttackGroupContext.setBye(false);

            activityContext.getServerInfoMap().put(attackServer, firstAttackGroupContext);


            // 构建后攻服基础信息
            CSAActivityGroupContext secondAttackGroupContext = activityContext.getServerInfoMap().get(defenseServer);

            secondAttackGroupContext.setSelfServerId(defenseServer);
            secondAttackGroupContext.setAgainstServerId(attackServer);
            secondAttackGroupContext.setFirstHalfAttack(false);
            secondAttackGroupContext.setCsaMatchMetaId(metaId);
            secondAttackGroupContext.setRankId(rankId);
            secondAttackGroupContext.setBye(false);

            activityContext.getServerInfoMap().put(defenseServer, secondAttackGroupContext);
        }
    }

    /**
     * 根据最近匹配过的服务器匹配
     * @param activity
     * @param activityContext
     * @param canMatchServerList
     * @param matchResult
     * @param metaId
     * @param sameSeasonServerList
     */
    private void matchBattleByRecentServer(Activity activity, CSAActivityContext activityContext, List<Integer> canMatchServerList, Map<Integer, Integer> matchResult, String metaId, List<Integer> sameSeasonServerList) {
        CSAServerBattleInfo csaControlServerBattleInfo = csaServerBattleInfoManager.getCSAControlServerBattleInfo();
        if (csaControlServerBattleInfo == null) {
            ErrorLogUtil.errorLog("match start,but csa control info is null");
            return;
        }

        while (sameSeasonServerList.size() > 1) {
            Integer attackServer = RandomUtils.random(sameSeasonServerList);
            CSAServerBattleInfo attackBattleInfo = csaServerBattleInfoManager.getCSAServerBattleInfoByServerId(attackServer);

            sameSeasonServerList.remove(attackServer);
            canMatchServerList.remove(attackServer);

            Integer defenseServer = null;
            for (Integer server : sameSeasonServerList) {
                CSAServerBattleInfo defenseBattleInfo = csaServerBattleInfoManager.getCSAServerBattleInfoByServerId(server);
                if (attackBattleInfo != null && defenseBattleInfo != null
                        && (attackBattleInfo.getRecentMatchedServerList().contains(server) || defenseBattleInfo.getRecentMatchedServerList().contains(attackServer))) {
                    continue;
                }

                defenseServer = server;
                break;
            }

            if (defenseServer == null) {
                // 理论上不存在这种情况
                if(attackBattleInfo.getRecentMatchedServerList().isEmpty()) {
                    logger.info("server match failed. server is {}. match list is {}.", attackServer, JSON.toJSONString(sameSeasonServerList));
                    continue;
                }

                defenseServer = attackBattleInfo.getRecentMatchedServerList().get(0);
            }

            sameSeasonServerList.remove(defenseServer);
            canMatchServerList.remove(defenseServer);

            // 添加对战信息
            matchResult.put(attackServer, defenseServer);

            String rankId = "PlayCount" + csaControlServerBattleInfo.getTotalPlayCount() + activity.getMetaId() + "server" + attackServer + "server" + defenseServer;

            // 构建先攻服信息
            CSAActivityGroupContext firstAttackGroupContext = activityContext.getServerInfoMap().get(attackServer);

            firstAttackGroupContext.setSelfServerId(attackServer);
            firstAttackGroupContext.setAgainstServerId(defenseServer);
            firstAttackGroupContext.setFirstHalfAttack(true);
            firstAttackGroupContext.setCsaMatchMetaId(metaId);
            firstAttackGroupContext.setRankId(rankId);
            firstAttackGroupContext.setBye(false);

            activityContext.getServerInfoMap().put(attackServer, firstAttackGroupContext);


            // 构建后攻服基础信息
            CSAActivityGroupContext secondAttackGroupContext = activityContext.getServerInfoMap().get(defenseServer);

            secondAttackGroupContext.setSelfServerId(defenseServer);
            secondAttackGroupContext.setAgainstServerId(attackServer);
            secondAttackGroupContext.setFirstHalfAttack(false);
            secondAttackGroupContext.setCsaMatchMetaId(metaId);
            secondAttackGroupContext.setRankId(rankId);
            secondAttackGroupContext.setBye(false);

            activityContext.getServerInfoMap().put(defenseServer, secondAttackGroupContext);
        }
    }

    /**
     * 根据 比赛类型 赛季 分组   key1: subType -> < key2: season   value: List<ServerId> >
     */
    private Map<Integer, Map<Integer, List<Integer>>> getMetaTypeSeasonServerMap(long now, CSAActivityContext activityContext, Map<Integer, GameServerConfig> gameConfigs, KvkSeasonsConfig kvkSeasons, List<Integer> matchServerList) {
        Map<Integer, Map<Integer, List<Integer>>> result = new HashMap<>();
        for (Integer serverId : matchServerList) {
            int season = 0;
            GameServerConfig gameServerConfig = gameConfigs.get(serverId);
            KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = kvkSeasons.getServerGroupByOServerIdAndTime(gameServerConfig.getGameServerId(), now);
            if (kvkSeasonServerGroupConfig != null) {
                season = kvkSeasonServerGroupConfig.getSeason();
            }

            CSAActivityGroupContext csaActivityGroupContext = activityContext.getServerInfoMap().get(serverId);
            if (csaActivityGroupContext == null) {
                continue;
            }

            Map<Integer, List<Integer>> seasonServerMap = result.get(csaActivityGroupContext.getSubType().getId());
            if (seasonServerMap == null) {
                result.put(csaActivityGroupContext.getSubType().getId(), new HashMap<>());
                seasonServerMap = result.get(csaActivityGroupContext.getSubType().getId());
            }

            seasonServerMap.compute(season, (k, v) -> v == null ? new ArrayList<>() : v).add(serverId);
        }

        return result;
    }

    /**
     * 初始化可预览的服务器信息
     */
    private void initCSAServerInfo(Activity activity, CSAActivityContext activityContext, long now, Map<Integer, GameServerConfig> gameConfigs) {
        CrossSeverAttackSetting crossSeverAttackSetting = configService.getConfig(CrossSeverAttackSetting.class);
        KvkSeasonsConfig kvkSeasons = configCenter.getLsConfig().getKvkSeasons();
        for (Map.Entry<Integer, GameServerConfig> gameServerConfigEntry : gameConfigs.entrySet()) {
            int serverId = gameServerConfigEntry.getKey();
            GameServerConfig gameServerConfig = gameServerConfigEntry.getValue();
            CSAServerBattleInfo csaServerBattleInfo = csaServerBattleInfoDao.findById((long) serverId);

            if (!gameServerConfig.serverTypeIsGAME()) {
                return;
            }

            // 开服时长大于设置天数才有预览信息 1：预览天数配置 2：匹配天数配置
            if (gameServerConfig.getOpenTimeMs() + crossSeverAttackSetting.getCsaFirstAppearTime() * TimeUtil.DAY_MILLIS > now) {
                continue;
            }

            // 如果目标服在赛季，则要判断K服
            CSAActivitySubType subType = CSAActivitySubType.NORMAL;
            int season = 0;
            KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = kvkSeasons.getServerGroupByOServerIdAndTime(gameServerConfig.getGameServerId(), now);
            if (kvkSeasonServerGroupConfig != null) {
                season = kvkSeasonServerGroupConfig.getSeason();
                int kServerId = kvkSeasonServerGroupConfig.getKServerId();
                GameServerConfig kvkGameServerConfig = configCenter.getLsConfig().getGameServers().get(kServerId);

                // 不能在结算期开启对战
                if (crossSeverAttackSetting.getCsaSeasonEndSwitch() == 0) {
                    // 服务器处于赛季结算期
                    if (now >= kvkSeasonServerGroupConfig.getSettleTime()) {
                        logger.warn("CSA匹配中，服务器处于赛季结算期. game is {}. kvk is {}. now is {}. season is {}. settle time is {}.",
                                gameServerConfig.getGameServerId(), kvkGameServerConfig.getGameServerId(), now, season, kvkSeasonServerGroupConfig.getSettleTime());
                        continue;
                    }

                    // 活动时间不能处于结算期
                    if (activity.getEndTime() >= kvkSeasonServerGroupConfig.getSettleTime()) {
                        logger.warn("CSA匹配中，活动结束时间处于赛季结算期. game is {}. kvk is {}. activity end is {}. season is {}. settle time is {}.",
                                gameServerConfig.getGameServerId(), kvkGameServerConfig.getGameServerId(), activity.getEndTime(), season, kvkSeasonServerGroupConfig.getSettleTime());
                        continue;
                    }
                } else {
                    // 可以在结算期开启
                    // K服的下个赛季信息
                    KvkSeasonServerGroupConfig nextKvkSeasonServerGroupConfig = Application.getConfigCenter().getLsConfig().getKvkSeasons().getNextSeasonGroupByKServerIdAndTime(kServerId, now);
                    if (nextKvkSeasonServerGroupConfig != null) {
                        if (activity.getEndTime() >= nextKvkSeasonServerGroupConfig.getStartTime() - TimeUtil.HOUR_MILLIS) {
                            logger.warn("CSA匹配中，活动可以在赛季结算期开启但活动结束时间大于下赛季开启时间. game is {}. kSever is {}. activity end is {}. season is {}. next season ready time is {}.",
                                    gameServerConfig.getGameServerId(), kServerId, activity.getEndTime(), season, nextKvkSeasonServerGroupConfig.getStartTime());
                            continue;
                        }
                    }
                }

                logger.info("now is {}. battle time is {}. settle time is {}.", now, kvkSeasonServerGroupConfig.getStartTime(), kvkSeasonServerGroupConfig.getSettleTime());
                if (csaServerBattleInfo == null || !csaServerBattleInfo.getLeagueRecord().containsKey(season)) {
                    if (season > 1 && now >= kvkSeasonServerGroupConfig.getStartTime()
                            && now + crossSeverAttackSetting.getCsaLeagueTimelimit() * TimeUtil.DAY_MILLIS < kvkSeasonServerGroupConfig.getSettleTime()) {
                        subType = CSAActivitySubType.LEAGUE;
                    }
                } else {
                    int record = csaServerBattleInfo.getLeagueRecord().get(season);
                    if (season > 1 && record < crossSeverAttackSetting.getCsaSeriesMatchNum()) {
                        subType = CSAActivitySubType.LEAGUE;
                    }

                }
            } else {
                season = 1;
                // 不能在结算期开启对战
                if (crossSeverAttackSetting.getCsaSeasonEndSwitch() == 0) {
//                    long rewardTime = KvkSeasonTicker.getRewardTime(serverId);
//                    if (now >= rewardTime) {
//                        logger.warn("CSA匹配中，服务器处于第一赛季结算期. game is {}. now is {}. reward time is {}.",
//                                gameServerConfig.getGameServerId(), now, rewardTime);
//                        continue;
//                    }

//                    if (activity.getEndTime() >= rewardTime) {
//                        logger.warn("CSA匹配中，服务器处于第一赛季结算期. game is {}. end time is {}. reward time is {}.",
//                                gameServerConfig.getGameServerId(), now, rewardTime);
//                        continue;
//                    }
                } else {
                    // 可以在结算期开启
                    // K服的下个赛季信息
                    KvkSeasonServerGroupConfig nextKvkSeasonServerGroupConfig = Application.getConfigCenter().getLsConfig().getKvkSeasons().getNextSeasonGroupByOServerIdAndTime(gameServerConfig.getGameServerId(), TimeUtil.getBeginOfDay(TimeUtil.getNow()));
                    if (nextKvkSeasonServerGroupConfig != null) {
                        if (activity.getEndTime() >= nextKvkSeasonServerGroupConfig.getStartTime() - TimeUtil.HOUR_MILLIS) {
                            logger.warn("CSA匹配中，服务器处于第一赛季, 活动可以在赛季结算期开启但活动结束时间大于下赛季开启时间. game is {}. activity end is {}. season is {}. next season ready time is {}.",
                                    gameServerConfig.getGameServerId(), activity.getEndTime(), season, nextKvkSeasonServerGroupConfig.getStartTime());
                            continue;
                        }
                    }
                }
            }

            CSAActivityGroupContext csaActivityGroupContext = new CSAActivityGroupContext();

            csaActivityGroupContext.setCsaActivityStatus(CSAActivityStatus.PREHEAT);
            csaActivityGroupContext.setNextCsaActivityStatusTime(activity.getEndTime());
            csaActivityGroupContext.setSelfServerId(serverId);

            // 确定subType
            csaActivityGroupContext.setSubType(subType);
            csaActivityGroupContext.setSeason(season);

            activityContext.getServerInfoMap().put(serverId, csaActivityGroupContext);

            // 检查并创建空缺的CsaBattleServer
            ServerTypeConfig serverTypeConfig = ServerConfigManager.getInstance().getServerTypeConfig();
            if (csaServerBattleInfoDao.findById((long) serverId) == null) {
                csaServerBattleInfoDao.create(serverTypeConfig.getCsaControlServer(), serverId);
            }
        }
    }
}
