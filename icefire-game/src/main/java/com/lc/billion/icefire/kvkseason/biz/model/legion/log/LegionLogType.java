package com.lc.billion.icefire.kvkseason.biz.model.legion.log;

import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;

/**
 * <AUTHOR>
 *
 */
public enum LegionLogType implements IntEnum {

	/**
	 * 1.新联盟加入，格式：联盟[{0}]{1}加入了军团
	 */
	JOIN(1),
	/**
	 * 2.联盟退出(退出成功时记录)，格式：联盟[{0}]{1}退出了军团
	 */
	EXIT(2),
	/**
	 * 3.被任命新官职，格式：玩家{0}被{1}任命为{2}
	 */
	OFFICE_NEW(3),
	/**
	 * 4.官职提升，格式：玩家{0}被{1}任命为{2}
	 */
	OFFICE_UP(4),
	/**
	 * 5.官职下降，格式：玩家{0}被{1}任命为{2}
	 */
	OFFICE_DOWN(5),
	/**
	 * 6.被解除官职，格式：玩家{0}被{1}解除了官职
	 */
	OFFICE_NO(6),
	/**
	 * 7.主联盟转让，格式：联盟[{0}]{1}被军团长转让为主联盟
	 */
	TRANSLATE(7),
	/**
	 * 8.联盟被移出，格式：联盟[{0}]{1}被军团长移出了军团
	 */
	KICK(8),

	;

	private int id;

	private LegionLogType(int id) {
		this.id = id;
	}

	@Override
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	private static final LegionLogType[] INDEXES = EnumUtils.toArray(values());

	public static LegionLogType findById(int id) {
		if (id < 0 || id >= INDEXES.length) {
			return null;
		}
		return INDEXES[id];
	}

}
