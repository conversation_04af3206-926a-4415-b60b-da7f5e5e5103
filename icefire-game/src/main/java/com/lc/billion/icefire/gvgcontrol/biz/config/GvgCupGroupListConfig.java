package com.lc.billion.icefire.gvgcontrol.biz.config;

import java.util.*;

import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.simfun.sgf.common.tuple.TwoTuple;

/**
 * GVG杯赛小组赛分组数据
 *
 * <AUTHOR>
 * @created 2021-08-25
 */
@Config(name = "GvgCupGroupList", metaClass = GvgCupGroupListConfig.GvgCupGroupListMeta.class)
public class GvgCupGroupListConfig {

    @MetaMap
    private Map<String, GvgCupGroupListMeta> idMap;
    //<status,<serverId,allianceId>>
    private Map<Integer, List<TwoTuple<Integer, Long>>> all;
    //<warZone,<group,<sn,TwoTuple<serverId,allianceId>>>>
    private Map<Integer, Map<Integer, Map<Integer, TwoTuple<Integer, Long>>>> groupSnMapper;

    public Map<Integer, List<TwoTuple<Integer, Long>>> getAll() {
        return all;
    }


    /**
     * 根据组别和序号查找联盟
     */
    public TwoTuple<Integer, Long> findAllianceByGroupAndSn(int warZoneId, int group, int sn) {
        if (groupSnMapper.containsKey(warZoneId)) {
            if (groupSnMapper.get(warZoneId).containsKey(group))
                return groupSnMapper.get(warZoneId).get(group).get(sn);
        }
        return null;
    }

    public Set<Integer> getAllWarZoneIds() {
        return groupSnMapper.keySet();
    }

    public Set<Integer> getAllGroupsByWarZone(int warZoneId) {
        if (groupSnMapper.containsKey(warZoneId)) {
            return groupSnMapper.get(warZoneId).keySet();
        }
        return null;
    }

    public Set<Integer> getAllAllianceByWarzoneAndGroup(int warZoneId, int group) {
        if (groupSnMapper.containsKey(warZoneId)) {
            if (groupSnMapper.get(warZoneId).containsKey(group)) {
                return groupSnMapper.get(warZoneId).get(group).keySet();
            }
        }
        return null;
    }

    public void init(List<GvgCupGroupListMeta> list) {
        all = new HashMap<>();
        groupSnMapper = new HashMap<>();
        for (GvgCupGroupListMeta meta : list) {
            // -1 表示表中无数据
            if (meta.getGroup() == -1) {
                break;
            }
            int participation = meta.getParticipation();
            if (!all.containsKey(participation)) {
                all.put(participation, new ArrayList<>());
            }
            all.get(participation).add(new TwoTuple<>(meta.getServerId(), meta.getAllianceId()));

            groupSnMapper.compute(meta.getWarZoneId(), (k, v) -> v == null ? new HashMap<>() : v).compute(meta.getGroup(), (k, v) -> v == null ? new HashMap<>() : v).put(meta.getSn(), new TwoTuple<>(meta.getServerId(), meta.getAllianceId()));
        }
    }

    public static class GvgCupGroupListMeta extends AbstractMeta {

        // 小组赛 组别
        private int group;
        private int serverId;
        private Long allianceId;
        // gvg杯赛 参赛状态 0-默认值 1-小组赛已入围 2-小组赛未入围 3-已淘汰*/
        private int participation;
        // 战区
        private int warZoneId;
        // 序号
        private int sn;

        public int getSn() {
            return sn;
        }

        public void setSn(int sn) {
            this.sn = sn;
        }

        public int getGroup() {
            return group;
        }

        public void setGroup(int group) {
            this.group = group;
        }

        public Long getAllianceId() {
            return allianceId;
        }

        public void setAllianceId(Long allianceId) {
            this.allianceId = allianceId;
        }

        public int getServerId() {
            return serverId;
        }

        public void setServerId(int serverId) {
            this.serverId = serverId;
        }

        public int getParticipation() {
            return participation;
        }

        public void setParticipation(int participation) {
            this.participation = participation;
        }

        public int getWarZoneId() {
            return warZoneId;
        }

        public void setWarZoneId(int warZoneId) {
            this.warZoneId = warZoneId;
        }
    }
}
