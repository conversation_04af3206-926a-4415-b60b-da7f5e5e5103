package com.lc.billion.icefire.kvkseason.biz.service.impl.kingdombuff;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.config.BuffConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.manager.RoleCalcPropManager;
import com.lc.billion.icefire.game.biz.manager.prop.base.ICalcProp;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.buff.BuffServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.player.PlayerServiceImpl;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.KingdomBuffDao;
import com.lc.billion.icefire.kvkseason.biz.model.kindombuff.KingdomBuff;
import com.lc.billion.icefire.protocol.GcBuffRemove;
import com.lc.billion.icefire.protocol.GcBuffUpdate;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/13
 */
@Service
public class KingdomBuffServiceImpl {
	private static final Logger logger = LoggerFactory.getLogger(KingdomBuffServiceImpl.class);

	@Autowired
	private BuffServiceImpl buffService;
	@Autowired
	private ConfigServiceImpl configService;
	@Autowired
	private KingdomBuffDao kingdomBuffDao;
	@Autowired
	private RoleDao roleDao;
	@Autowired
	private RoleCalcPropManager roleCalcPropManager;
	@Autowired
	private PlayerServiceImpl playerService;

	public void onEnterWorld(Role role) {
		// GcBuffList 协议客户端全覆盖的，先直接在buffservice里面处理吧
		//
		//
		// List<KingdomBuff> kingdomBuffs =
		// kingdomBuffDao.findBuff(role.getoServerId());
		// if (JavaUtils.bool(kingdomBuffs)) {
		// GcBuffList gcMsg = new GcBuffList();
		// for (KingdomBuff kingdomBuff : kingdomBuffs) {
		// gcMsg.addToBuffs(kingdomBuff.toInfo());
		// }
		// role.send(gcMsg);
		// }
	}

	/**
	 * 添加一个王国buff
	 * 
	 * @param buffId
	 *            buff表Id
	 * @param belongServerId
	 *            buff是给哪个王国的
	 * @param effectServerIdList
	 *            这个buff的生效服IdList
	 * @param buffType
	 *            buff 类型 //TODO 暂时没用 KVK系统添加的都是攻防血， 只是更新prop即可。
	 * 
	 *            //TODO 如果有采集类等，需要buff添加后，对生效服的正在采集队列更新，类似处理需要根绝type做结构
	 * 
	 */
	public void addKingdomBuff(String buffId, int belongServerId, List<Integer> effectServerIdList, int buffType) {
		BuffConfig.BuffMeta buffMeta = configService.getConfig(BuffConfig.class).getBuffMeta(buffId);
		if (buffMeta == null) {
			ErrorLogUtil.errorLog("config missing, has no buff meta", new RuntimeException(),"buffId",buffId);
			return;
		}
		if (!JavaUtils.bool(effectServerIdList))
			return;
		long now = TimeUtil.getNow();
		KingdomBuff buff = kingdomBuffDao.findBuff(belongServerId, buffId);
		if (buff != null) {
			buff.setEndTime(now + buffMeta.getEffectParaTime());
			kingdomBuffDao.save(buff);
		} else {
			buff = kingdomBuffDao.create(belongServerId, effectServerIdList, buffId, now, now + buffMeta.getEffectParaTime());
		}

		// 推送客户端
		GcBuffUpdate gcMsg = new GcBuffUpdate();
		gcMsg.setBuff(buff.toInfo());
		playerService.broadcast(belongServerId, 0, gcMsg);
		//
		// 更新属性： 所属王国匹配 && 当前在生效服的玩家
		List<Role> belongServerRoles = roleDao.findByOServerId(belongServerId);
		for (Role role : belongServerRoles)
			if (effectServerIdList.contains(role.getCurrentServerId()))
				roleCalcPropManager.addChangeType(role, ICalcProp.RoleType.KINGDOM_BUFF);

	}

	public void remove(KingdomBuff kingdomBuff) {
		// 删除
		kingdomBuffDao.delete(kingdomBuff);

		// 更新属性： 所属王国匹配 && 当前在生效服的玩家
		List<Role> belongServerRoles = roleDao.findByOServerId(kingdomBuff.getBelongServerId());
		for (Role role : belongServerRoles) {
			if (kingdomBuff.getEffectServerIds().contains(role.getCurrentServerId())) {
				roleCalcPropManager.addChangeType(role, ICalcProp.RoleType.KINGDOM_BUFF);
				// 通知前端
				if (role.isOnline()) {
					GcBuffRemove msg = new GcBuffRemove(kingdomBuff.getBuffId());
					role.send(msg);
				}
			}
		}
	}
}