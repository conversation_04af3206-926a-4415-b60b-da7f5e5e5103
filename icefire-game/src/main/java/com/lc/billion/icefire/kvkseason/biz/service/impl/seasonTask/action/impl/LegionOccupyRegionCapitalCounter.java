package com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.impl;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.kvk.SeasonTaskConfig;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.node.RegionCapitalNode;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.LegionSeasonTaskProgress;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.RoleSeasonTaskRecord;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.AbstractSeasonTaskAction;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.SeasonTaskActionType;
import com.longtech.ls.config.ServerType;
import com.simfun.sgf.utils.JavaUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2022/3/16
 */
@Service
public class LegionOccupyRegionCapitalCounter extends AbstractSeasonTaskAction {
	@Override
	public SeasonTaskActionType getType() {
		return SeasonTaskActionType.LEGION_OCCUPY_REGION_CAPITAL_COUNTER;
	}

	@Override
	public long getProgress(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		Long legionId = legionService.getLegionId(role);
		if (legionId == null) {
			return 0L;
		}
		LegionSeasonTaskProgress legionRecord = legionSeasonTaskProgressDao.getByLegionId(legionId);
		int i = meta.getConditions().indexOf(getType().getId());
		int param2 = meta.getParam2s().get(i);
		return legionRecord.getOccupyTimes(param2);
	}

	@Override
	protected void updateProgress(Role role, RoleSeasonTaskRecord taskRecord, Object... params) {
		RegionCapitalNode node = (RegionCapitalNode) params[0];
		if (Application.getConfigCenter().getServerType(node.getCurrentServerId()) != ServerType.KVK_SEASON) {
			return;
		}
		Long legionId = node.getBelongAllianceId();
		if (!JavaUtils.bool(legionId)) {
			return;
		}
		LegionSeasonTaskProgress legionSeasonTaskProgress = legionSeasonTaskProgressDao.getByLegionId(legionId);
		legionSeasonTaskProgress.addOccupyTimes(node.getLevel());
		legionSeasonTaskProgressDao.save(legionSeasonTaskProgress);
	}

	@Override
	public boolean actionFinishCheck(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		int i = meta.getConditions().indexOf(getType().getId());
		int param1 = meta.getParam1s().get(i);
		return getProgress(role, meta) >= param1;
	}
}
