package com.lc.billion.icefire.kvkseason.biz.dao.mongo.root;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.kvkseason.biz.model.legion.Legion;
import com.lc.billion.icefire.kvkseason.biz.model.legion.log.LegionLog;

/**
 * <AUTHOR>
 *
 */
@Repository
public class LegionLogDao extends RootDao<LegionLog> {

	private Map<Long, List<LegionLog>> dataByLegionId = new MyConcurrentMap<>();
	private Map<Long, Map<String, List<LegionLog>>> dataByLegionIdAndMetaId = new MyConcurrentMap<>();

	public LegionLogDao() {
		super(LegionLog.class, true);
	}

	@Override
	protected MongoCursor<LegionLog> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(LegionLog entity) {
		dataByLegionId.compute(entity.getLegionId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
		dataByLegionIdAndMetaId.compute(entity.getLegionId(), (k, v) -> v == null ? new HashMap<>() : v).compute(entity.getMetaId(), (k, v) -> v == null ? new ArrayList<>() : v)
				.add(entity);
	}

	@Override
	protected void removeMemoryIndexes(LegionLog entity) {
		List<LegionLog> list = dataByLegionId.get(entity.getLegionId());
		if (list != null) {
			list.remove(entity);
		}
		Map<String, List<LegionLog>> map = dataByLegionIdAndMetaId.get(entity.getLegionId());
		if (map != null) {
			List<LegionLog> list2 = map.get(entity.getMetaId());
			if (list2 != null) {
				list2.remove(entity);
			}
		}
	}

	public LegionLog create(Legion legion, String metaId, List<String> params) {
		LegionLog legionLog = newEntityInstance();
		legionLog.setLegionId(legion.getId());
		legionLog.setMetaId(metaId);
		legionLog.setParams(params);
		return createEntity(legion.getDB(), legionLog);
	}

	public List<LegionLog> findByLegionId(Long legionId) {
		return dataByLegionId.get(legionId);
	}

	public List<LegionLog> findByLegionIdAndMetaId(Long legionId, String metaId) {
		Map<String, List<LegionLog>> map = dataByLegionIdAndMetaId.get(legionId);
		if (map == null) {
			return Collections.emptyList();
		}
		return map.get(metaId);

	}
}
