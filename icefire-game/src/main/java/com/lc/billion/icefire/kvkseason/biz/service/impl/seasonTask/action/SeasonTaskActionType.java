package com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action;

/**
 * <AUTHOR>
 * @date 2021/11/15
 */
public enum SeasonTaskActionType {

	/**
	 *     - 名次解释——狂野之地：KVK新地图的名字
	 *     - 1=玩家个人在狂野之地击败X个Y级及以上的丧尸
	 *       - 必须击败出生在狂野之地的丧尸，才会累计进度
	 *     - 3=玩家个人累计参与建造远征要塞或远征哨塔X分钟
	 *       - 参与建造远征要塞或远征哨塔，才会累计进度
	 *       - 玩家每次部队返回时，记录下该次建造的持续时间，并累计进度，无需实时刷新
	 *     - 4=玩家个人在狂野之地重伤/击杀X个Y级及以上的其他服务器玩家的战斗单位
	 *       - 必须在狂野之地重伤/击杀其他服务器玩家的战斗部队，才会累计进度
	 *     - 5=玩家个人在狂野之地总共采集X资源
	 *       - 必须针对在狂野之地的资源点，通过部队收集或英雄驻扎获得的资源量，才会累计进度
	 *       - 采集资源的部队返回时或驻扎英雄回城时，进行累计进度，无需实时刷新
	 *     - 6=玩家个人参与攻占X次在狂野之地中的城市
	 *       - 城市有PVE阶段和PVP阶段，每种阶段下的每个轮次，最多记1次参与
	 *         - PVE阶段：玩家部队与某个城市中的守军发生至少一次交战，且该轮次（1小时倒计时）联盟完成对城市的占领，则记为该玩家参与攻占该城市一次
	 *         - PVP阶段：玩家部队在该轮次时间内，有部队攻击过某个城市，且最终倒计时结束后归属联盟发生变化，则记为该玩家参与攻占该城市一次
	 *     - 7=个人荣誉值达到X
	 *     - 8=所在联盟占领X次Y级城
	 *       - 联盟自身需要记录下对Y级城市的占领次数
	 *         - PVE阶段：联盟完成对Y级城市的占领，则记为该联盟攻占Y级城市1次
	 *         - PVP阶段：最终倒计时结束后	，联盟完成对Y级城市的占领，则记为该联盟攻占Y级城市1次
	 *     - 9=所在联盟赛季势力值达到X
	 *     - 10=所在联盟在赛季结算时占领着X个Y级城
	 *     - 11=所在联盟累计摧毁X个其他服联盟的远征哨塔
	 *       - 联盟自身需要记录下摧毁其他服联盟远征哨塔的次数
	 *         - 所在联盟内的任意玩家，对某个其他服联盟的远征哨塔耐久进行最后一击时，记1次
	 *     - 12=联盟荣誉值达到X
	 *     - 13=所在服务器在赛季结算时占领着X个Y级城
	 *       - 服务器下的联盟占领着某个城市，即为该服也占领着该城市
	 *     - 14=所在服务器占领X次Y级城
	 *       - 服务器下的联盟对Y级城市的占领次数加1时，也需要令服务器对Y级城市的占领次数加1
	 *     - 15=服务器荣誉值达到X
	 *
	 *		军团新增
	 *		- 18=所在军团占领X次Y级城
	 *      - 19=所在军团赛季势力值达到X
	 *      - 20=所在军团在赛季结算时占领着X个Y级城
	 *      - 21=所在军团累计摧毁X个其他服联盟的远征哨塔
	 *      - 22=军团荣誉值达到X
	 */
	PVE_NPC(1, SeasonTaskType.PERSONAL), // 玩家个人在狂野之地击败X个Y级及以上的丧尸

	KILL_ENEMY(4, SeasonTaskType.PERSONAL), // 玩家个人在狂野之地重伤/击杀X个Y级及以上的其他服务器玩家的战斗单位

	GATHER_AMOUNT(5, SeasonTaskType.PERSONAL), // 玩家个人在狂野之地总共采集X资源

	OCCUPY_REGION_CAPITAL_COUNTER(6, SeasonTaskType.PERSONAL),

	PERSONAL_SEASON_HONOR(7, SeasonTaskType.PERSONAL), // 个人荣誉值达到X

	ALLIANCE_OCCUPY_REGION_CAPITAL_COUNTER(8, SeasonTaskType.ALLIANCE), // 所在联盟占领X次Y级城

	ALLIANCE_PROSPERITY(9, SeasonTaskType.ALLIANCE), // 所在联盟赛季势力值达到X

	ALLIANCE_OCCUPY_REGION_CAPITAL_NUM_FINAL(10, SeasonTaskType.ALLIANCE), // 所在联盟在赛季结算时占领着X个Y级城

	ALLIANCE_HONOR(12, SeasonTaskType.ALLIANCE), // 联盟荣誉值达到X

	SERVER_OCCUPY_REGION_CAPITAL_NUM_FINAL(13, SeasonTaskType.SERVER), // 所在服务器在赛季结算时占领着X个Y级城

	SERVER_OCCUPY_REGION_CAPITAL_COUNTER(14, SeasonTaskType.SERVER), // 所在服务器占领X次Y级城

	SERVER_HONOR(15, SeasonTaskType.SERVER), // 服务器荣誉值达到X

	LEGION_OCCUPY_REGION_CAPITAL_COUNTER(18, SeasonTaskType.LEGION), // 所在军团占领X次Y级城

	LEGION_PROSPERITY(19, SeasonTaskType.LEGION), // 所在军团赛季势力值达到X

	LEGION_OCCUPY_REGION_CAPITAL_NUM_FINAL(20, SeasonTaskType.LEGION), // 所在军团在赛季结算时占领着X个Y级城

	LEGION_HONOR(22, SeasonTaskType.LEGION), // 军团荣誉值达到X

	;

	private int id;
	private SeasonTaskType taskType;

	SeasonTaskActionType(int id, SeasonTaskType taskType) {
		this.id = id;
		this.taskType = taskType;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public SeasonTaskType getTaskType() {
		return taskType;
	}

	public void setTaskType(SeasonTaskType taskType) {
		this.taskType = taskType;
	}

	public static SeasonTaskActionType findById(int value) {
		SeasonTaskActionType[] values = values();
		if (values == null)
			return null;
		for (SeasonTaskActionType type : values)
			if (type.getId() == value)
				return type;
		return null;
	}
}
