package com.lc.billion.icefire.kvkseason.biz.service.impl.honor;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.BizTime;
import com.lc.billion.icefire.game.biz.config.SeasonPersonalRewardConfig;
import com.lc.billion.icefire.game.biz.config.SeasonScoreConfig;
import com.lc.billion.icefire.game.biz.config.SeasonWeekRewardConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ServerInfoDao;
import com.lc.billion.icefire.game.biz.manager.RoleCityManager;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionType;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankType;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.roles.RoleSeasonInfoDao;
import com.lc.billion.icefire.kvkseason.biz.manager.honor.KvkHonorManager;
import com.lc.billion.icefire.kvkseason.biz.model.kvk.RoleSeasonInfo;
import com.lc.billion.icefire.protocol.*;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Service
public class KvkHonorService {
    private static final Logger logger = LoggerFactory.getLogger(KvkHonorService.class);

    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private KvkHonorManager kvkHonorManager;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private RoleSeasonInfoDao roleSeasonInfoDao;
    @Autowired
    private ServiceDependency srvdp;
    @Autowired
    private ServerInfoDao serverInfoDao;
    @Autowired
    protected RoleCityManager roleCityManager;
    //由于赛季荣誉变更很频繁，一场战斗可能要加好几次 保存变更定时通知玩家
    // roleId -> roleId
    private final Map<Long, Long> honorChangedSet = new ConcurrentHashMap<>();

    private boolean isSendSeasonHonorReward;
    public static final int RESULT_SUCCESS = 0;
    public static final int RESULT_FAIL = 1;

    public void startService() {
        var serverInfo = serverInfoDao.findById((long) Application.getServerId());
        int season = Application.getSeason();
        if (serverInfo != null && serverInfo.getSeasonRewardTimeMap().containsKey(season)) {
            isSendSeasonHonorReward = true;
        }
    }

    public long trigger(Role role, HonorType scoreType, int para1, int para2, long num) {
        long score = 0;
        if (role == null) {
            return score;
        }
        if (role.getCurrentServerId() != Application.getServerId()) {
            // 赛季服中的原服不得积分
            return score;
        }
        try {
            score = trigger0(role, scoreType, para1, para2, num);
        } catch (ExpectedException ignored){

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("KvkHonorService addHonor ERROR" ,e,"roleId",role.getId(),
                    "type",scoreType,"para1",para1,"para2",para2 ,"num",num);
        }
        return score;
    }

    private long trigger0(Role role, HonorType scoreType, int para1, int para2, long num) {
        long score = 0;
        int season = configCenter.getSeason();
        var config = configService.getConfig(SeasonScoreConfig.class);
        var typeList = config.getMetaListBySeasonAndType(season, scoreType);
        if (!JavaUtils.bool(typeList)) {
            return score;
        }
        for (var meta : typeList) {
            if (meta.getPara1() == para1 && meta.getPara2() == para2) {
                score += meta.getScore() * num;
            }
        }
        //TODO 暂时先放在这
        srvdp.getMissionService().onMissionFinish(role, MissionType.OBTAIN_MERIT_COUNT,(int)score);
        logger.info("KvkHonorService addHonor role={} season={} type={} para1={} para2={} num={} score={}", role.getId(), season, scoreType, para1, para2, num, score);
        // 赛季积分结束了就不加了,但是,其他功能还需要加
        if (isSendSeasonHonorReward) {
            return score;
        }
        addHonor("type:" + scoreType, role, score);
        return score;
    }

    public boolean isSendSeasonHonorReward() {
        return isSendSeasonHonorReward;
    }

    public boolean isPersonalRewardOpen() {
        return srvdp.getFunctionSwitchService().isOpen(FunctionType.SEASON_PERSONAL_REWARD);
    }

    public void pushHonorRewardRecord(Role role) {
        if (!isPersonalRewardOpen()) {
            return;
        }
        var info = roleSeasonInfoDao.findById(role.getId());
        if (info == null) {
            return;
        }
        int season = Application.getSeason();
        var rewardSet = info.getPersonalRewardSet(season);
        GcSeasonPersonalRewardedList gcSeasonPersonalRewardedList = new GcSeasonPersonalRewardedList();
        gcSeasonPersonalRewardedList.setMetaIdList(new ArrayList<>(rewardSet));
        gcSeasonPersonalRewardedList.setHonorValue(info.getHonor(season));
        role.send(gcSeasonPersonalRewardedList);

        // 增加推送周功勋领奖记录
        int weekNum = srvdp.getServerInfoService().getServerWeek();
        GcSeasonWeekTakenPush weekTakenPush = new GcSeasonWeekTakenPush();
        weekTakenPush.setMetaIdList(new ArrayList<>(info.getWeekRewardSet(season, weekNum)));
        weekTakenPush.setHonorWeekValue(info.getWeekHonor(season, weekNum));
        weekTakenPush.setCurWeek(weekNum);
        role.send(weekTakenPush);
    }

    public void getSeasonWeekReward(Role role, String metaId, boolean receiveAll) {
        // 功能未开、传参有误
        int season = Application.getSeason();
        if (!isPersonalRewardOpen()) {
            logger.warn("getSeasonWeekReward fail 功能未开启 role={} metaId={} receiveAll={} season={}", role.getId(), metaId, receiveAll, season);
            return;
        }
        if (!JavaUtils.bool(metaId) && !receiveAll) {
            return;
        }

        var config = configService.getConfig(SeasonWeekRewardConfig.class);
        RoleSeasonInfo roleSeasonInfo = roleSeasonInfoDao.findById(role.getId());
        Map<String, String> rewardMap = new HashMap<>();
        int weekNum = srvdp.getServerInfoService().getServerWeek();
        if(weekNum <= 1) {
            logger.warn("getSeasonWeekReward fail 首周不许配奖励，找策划去 role={} metaId={} receiveAll={} season={} weekNum={}", role.getId(), metaId, receiveAll, season, weekNum);
            return;
        }
        var rewardedSet = roleSeasonInfo.getWeekRewardSet(season, weekNum);
        var currencyNum = roleSeasonInfo.getWeekHonor(season, weekNum);
        do {
            var metaList = config.getMetaList(season, weekNum);
            if (!JavaUtils.bool(metaList)) {
                logger.warn("getSeasonWeekReward, fail 本赛季周功勋未配置奖励 role={} metaId={} receiveAll={} season={} weekNum={}", role.getId(), metaId, receiveAll, season, weekNum);
                break;
            }
            for (var meta : metaList) {
                if (!receiveAll && !meta.getId().equals(metaId)) {
                    continue;
                }
                if (meta.getRank() <= currencyNum && !rewardedSet.contains(meta.getId())) {
                    rewardedSet.add(meta.getId());
                    rewardMap.put(meta.getId(), meta.getReward());
                }
            }

        } while (false);

        if (!JavaUtils.bool(rewardMap)) {
            logger.warn("getSeasonWeekReward fail 没有可领取的奖励 role={} metaId={} receiveAll={} season={} weekNum={}", role.getId(), metaId, receiveAll, season, weekNum);
        } else {
            for (var entry : rewardMap.entrySet()) {
                var itemList = srvdp.getDropService().drop(entry.getValue());
                if (JavaUtils.bool(itemList)) {
                    srvdp.getItemService().give(role, itemList, LogReasons.ItemLogReason.SEASON_WEEK_TASK_REWARD);
                    logger.info("getSeasonWeekReward success role={} metaId={} dropId={} season={} weekNum={}", role.getId(), entry.getKey(), entry.getValue(), season, weekNum);
                }
                srvdp.getBiLogUtil().getSeasonWeekReward(role, entry.getKey(), season, weekNum, entry.getValue(), receiveAll, currencyNum);
            }
            roleSeasonInfoDao.save(roleSeasonInfo);
        }
        GcGetSeasonWeekReward gcGetSeasonWeekReward = new GcGetSeasonWeekReward();
        gcGetSeasonWeekReward.setResult(rewardMap.isEmpty() ? RESULT_FAIL : RESULT_SUCCESS);
        gcGetSeasonWeekReward.setRewardMap(rewardMap);
        role.send(gcGetSeasonWeekReward);
        pushHonorRewardRecord(role);
    }

    /**
     * 领取赛季制个人功勋值奖励
     *
     * @param role
     * @param metaId
     * @param receiveAll
     */
    public void getSeasonPersonalReward(Role role, String metaId, boolean receiveAll) {
        int season = Application.getSeason();
        if (!isPersonalRewardOpen()) {
            logger.warn("getSeasonPersonalReward fail 功能未开启 role={} metaId={} receiveAll={} season={}", role.getId(), metaId, receiveAll, season);
            return;
        }
        if (!JavaUtils.bool(metaId) && !receiveAll) {
            return;
        }
        var config = configService.getConfig(SeasonPersonalRewardConfig.class);
        RoleSeasonInfo roleSeasonInfo = roleSeasonInfoDao.findById(role.getId());
        Map<String, String> rewardMap = new HashMap<>();
        var rewardedSet = roleSeasonInfo.getPersonalRewardSet(season);
        var currencyNum = roleSeasonInfo.getHonor(season);
        do {
            var metaList = config.getMetaListBySeason(season);
            if (!JavaUtils.bool(metaList)) {
                logger.warn("getSeasonPersonalReward, fail 本赛季未配置奖励 role={} metaId={} receiveAll={} season={}", role.getId(), metaId, receiveAll, season);
                break;
            }
            for (var meta : metaList) {
                if (!receiveAll && !meta.getId().equals(metaId)) {
                    continue;
                }
                if (meta.getRank() <= currencyNum && !rewardedSet.contains(meta.getId())) {
                    rewardedSet.add(meta.getId());
                    rewardMap.put(meta.getId(), meta.getReward());
                }
            }

        } while (false);
        if (!JavaUtils.bool(rewardMap)) {
            logger.warn("getSeasonPersonalReward fail 没有可领取的奖励 role={} metaId={} receiveAll={} season={}", role.getId(), metaId, receiveAll, season);
        } else {
            for (var entry : rewardMap.entrySet()) {
                var itemList = srvdp.getDropService().drop(entry.getValue());
                if (JavaUtils.bool(itemList)) {
                    srvdp.getItemService().give(role, itemList, LogReasons.ItemLogReason.SEASON_TASK_REWARD);
                    logger.info("getSeasonPersonalReward success role={} metaId={} dropId={} season={}", role.getId(), entry.getKey(), entry.getValue(), season);
                }
                srvdp.getBiLogUtil().getSeasonPersonalReward(role, entry.getKey(), season, entry.getValue(), receiveAll, currencyNum);
            }
            roleSeasonInfoDao.save(roleSeasonInfo);
        }
        GcGetSeasonPersonalReward gcGetSeasonPersonalReward = new GcGetSeasonPersonalReward();
        gcGetSeasonPersonalReward.setResult(!rewardMap.isEmpty() ? RESULT_SUCCESS : RESULT_FAIL);
        gcGetSeasonPersonalReward.setRewardMap(rewardMap);
        role.send(gcGetSeasonPersonalReward);
        pushHonorRewardRecord(role);
    }

    public void pushHonorUpdate(Role role, long value) {
        GcHonorValueUpdate gcHonorValueUpdate = new GcHonorValueUpdate();
        gcHonorValueUpdate.setHonorValue(value);
        role.send(gcHonorValueUpdate);
    }

    public void pushWeekHonorUpdtae(Role role) {
        int season = Application.getSeason();
        RoleSeasonInfo roleSeasonInfo = roleSeasonInfoDao.findById(role.getId());
        if(roleSeasonInfo == null) return;
        int weekNum = srvdp.getServerInfoService().getServerWeek();
        GcHonorWeekValueUpdate weekHonorValueUpdate = new GcHonorWeekValueUpdate();
        weekHonorValueUpdate.setHonorWeekValue(roleSeasonInfo.getWeekHonor(season, weekNum));
        weekHonorValueUpdate.setCurWeek(weekNum);
        role.send(weekHonorValueUpdate);
    }

    public void pushHonorUpdate(Role role) {
        if (role == null) {
            return;
        }
        pushHonorUpdate(role, kvkHonorManager.getRoleHorner(role.getId()));
        pushWeekHonorUpdtae(role);
    }

    public void addHonor(String sourceId, Role role, long addValue) {
        addHonor(sourceId, role, addValue, false);
    }

    public void addHonor(String sourceId, Role role, long addValue, boolean notify) {
        long total = kvkHonorManager.addHonorRole(role.getId(), addValue);
        if (notify) {
            pushHonorUpdate(role, total);
        } else {
            role.setNeedNotifyKonor(true);
        }
        if (addValue > 0) {
            srvdp.getRankService().updateRankScore(RankType.SEASON_HONOR_PERSNAL, total, BizTime.now(), role);
            // 玩家此时在盟里，写一份联盟内的赛季功勋榜、联盟内的周功勋榜
            Long allianceId = role.getAllianceId();
            if(allianceId != null && allianceId > 0) { // 正常来说一定有值，州府战在盟里才能玩
                srvdp.getRankService().updateRankScore(RankType.SEASON_HONOR_ALLIANCE, total, BizTime.now(), role, allianceId.toString());
                // 注意周功勋，是 incr 增量更新，然后每周一，在 AllianceServiceImpl.onZero 清理
                srvdp.getRankService().increaseRankScore(RankType.WEEK_HONOR_ALLIANCE, addValue, role, allianceId.toString());
            }
        }
//        srvdp.getRankService().increaseRankScore(RankType.SEASON_HONOR_PERSNAL, addValue, role);
        srvdp.getBiLogUtil().kvkHonorForRole(role, sourceId, addValue, total);
//        addChange(role.getRoleId());
    }

    public void setSendSeasonHonorReward(boolean sendSeasonHonorReward) {
        isSendSeasonHonorReward = sendSeasonHonorReward;
    }

    private void sendHonorRewardMail(Role role, int season) {
        var roleSeasonInfo = roleSeasonInfoDao.findById(role.getId());
        if (roleSeasonInfo == null) {
            return;
        }
        long honor = roleSeasonInfo.getHonor(season);
        var config = configService.getConfig(SeasonPersonalRewardConfig.class);
        List<SimpleItem> itemList = new ArrayList<>();
        var rewardedSet = roleSeasonInfo.getPersonalRewardSet(season);
        for (var meta : config.getMetaListBySeason(season)) {
            if (meta.getRank() > honor) {
                break;
            }
            if (roleSeasonInfo.isGotPersonalReward(season, meta.getId())) {
                continue;
            }
            rewardedSet.add(meta.getId());
            logger.info("补发赛季个人荣誉奖励 season={} role={} rank={} reward={} metaId={}", season, role.getId(), meta.getRank(), meta.getReward(), meta.getId());
            srvdp.getBiLogUtil().seasonHonrorSettle(role, honor, meta.getRank(), meta.getId(), meta.getReward());
            var rewardList = srvdp.getDropService().drop(meta.getReward());
            if (rewardList != null) {
                itemList.addAll(rewardList);
            }
        }
        roleSeasonInfoDao.save(roleSeasonInfo);
        if (JavaUtils.bool(itemList)) {
            var email = srvdp.getMailCreator().createSystemMail(role.getId(), role.getCurrentServerId(), EmailConstants.SEASON_ALLIANCE_PERSONAL_REWARD, itemList,
                    List.of(), LogReasons.ItemLogReason.SEASON_PERSONAL_HONOR_REWARD);
            srvdp.getMailSender().sendOneMail(email);
        }
    }

    public void sendHonorRewardMail(int season) {
        for (var role : srvdp.getRoleDao().findAll()) {
            sendHonorRewardMail(role, season);
        }
    }

    // 补发周功勋漏领取奖励
    public void reissueSeasonWeekReward(int season, int lastWeekNum) {
        logger.info("reissueSeasonWeekReward start season={} lastWeekNum={}", season, lastWeekNum);
        for (var role : srvdp.getRoleDao().findAll()) {
            doReissueWeekReward(role, season, lastWeekNum);
        }
        logger.info("reissueSeasonWeekReward finish season={} lastWeekNum={}", season, lastWeekNum);
    }

    public void doReissueWeekReward(Role role, int season, int lastWeekNum) {
        RoleSeasonInfo roleSeasonInfo = roleSeasonInfoDao.findById(role.getId());
        if (roleSeasonInfo == null) {
            return;
        }
        long weekHonor = roleSeasonInfo.getWeekHonor(season, lastWeekNum);
        var weekRewardedSet = roleSeasonInfo.getWeekRewardSet(season, lastWeekNum);

        List<SimpleItem> itemList = new ArrayList<>();
        var weekConfig = configService.getConfig(SeasonWeekRewardConfig.class);
        for (var meta : weekConfig.getMetaList(season, lastWeekNum)) {
            if (meta.getRank() > weekHonor) {
                break;
            }
            if (roleSeasonInfo.isGotWeekReward(season, lastWeekNum, meta.getId())) {
                continue;
            }
            weekRewardedSet.add(meta.getId());
            logger.info("reissueSeasonWeekReward season={} lastWeekNum={} role={} rank={} reward={} metaId={}", season, lastWeekNum, role.getId(), meta.getRank(), meta.getReward(), meta.getId());
            srvdp.getBiLogUtil().seasonHonrorWeekSettle(role, season, lastWeekNum, weekHonor, meta.getRank(), meta.getId(), meta.getReward());
            var rewardList = srvdp.getDropService().drop(meta.getReward());
            if (rewardList != null) {
                itemList.addAll(rewardList);
            }
        }
        roleSeasonInfoDao.save(roleSeasonInfo); // 保存 weekRewardedSet
        if (JavaUtils.bool(itemList)) {
            var email = srvdp.getMailCreator().createSystemMail(role.getId(), role.getCurrentServerId(), EmailConstants.SEASON_WEEK_PERSONAL_REWARD, itemList,
                    List.of(), LogReasons.ItemLogReason.SEASON_WEEK_HONOR_REWARD); // 邮件号蹭赛季功勋的，日志单独常量
            srvdp.getMailSender().sendOneMail(email);
        }
    }
}
