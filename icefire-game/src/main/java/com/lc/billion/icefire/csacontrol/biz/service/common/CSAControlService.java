package com.lc.billion.icefire.csacontrol.biz.service.common;

import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGAllianceSignUpInfo;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGBattleRecord;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgBattleServerDispatchRecord;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.context.GVGActivityContext;
import com.lc.billion.icefire.rpc.vo.csa.CSAActivityVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGBattleRecordVo;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.config.WorldMapConfig;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Service
public class CSAControlService {

	private static final Logger logger = LoggerFactory.getLogger(CSAControlService.class);

	@Autowired
	ActivityDao activityDao;
	@Autowired
	private CSAControlBroadcastToGameService csaControlBroadcastToGameService;

	public void startService() {
		WorldMapConfig worldMapConfig = ServerConfigManager.getInstance().getWorldMapConfig();
		ServerType serverType = worldMapConfig.getServerType();

		// 同步CSA活动信息给GAME服
		if (serverType == ServerType.CSA_CONTROL) {
			Activity csaActivity = activityDao.findActivityByActivityType(ActivityType.CROSS_SERVER_ATTACK);
			if (csaActivity == null) {
				return;
			}

			csaControlBroadcastToGameService.broadcastCSAActivity(csaActivity, 0, false, false);
			logger.info("csa control start. send activity to game. activity is {}.", csaActivity);
		}
	}
}
