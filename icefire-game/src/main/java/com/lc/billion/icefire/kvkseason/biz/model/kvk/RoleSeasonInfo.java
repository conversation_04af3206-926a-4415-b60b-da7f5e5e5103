package com.lc.billion.icefire.kvkseason.biz.model.kvk;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.IRolesEntity;
import lombok.Getter;
import org.jongo.marshall.jackson.oid.MongoId;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 *
 */
public class RoleSeasonInfo extends AbstractEntity implements IRolesEntity {
	private static final long serialVersionUID = -401832573303954337L;

	@MongoId
	private Long roleId;
	private int season;
	/** KVK迁服后首次被攻击 */
	private boolean firstBeAttack;
	/** KVK首次进入给罩子 */
	private boolean firstProtected;

	private final Map<Integer, HashSet<String>> seasonPersonalReward = new HashMap<>();

	// 补发跑在异步线程里，会多源修改领奖标记
	private final Map<String, HashSet<String>> seasonWeekReward = new ConcurrentHashMap<>();

	private final Map<Integer, Long> honorMap = new HashMap<>();

	// season --> weekNum --> honorValue
	private final Map<Integer, Map<Integer, Long>> honorWeekMap = new HashMap<>();
	/**
	 * 存储赛季结算后联盟给分配奖励的信息
	 * key 是season value是给他分配奖励的联盟ID
	 */
	private final Map<Integer, Long> seasonAllianceRewardMap = new HashMap<>();

	private final Map<Integer, Long> likeMap = new HashMap<>();

	// 死兵信息
	@Getter
	private final Map<Integer, Map<String, Integer>> soldierDeads = new HashMap<>();
	// 是否已经领取
	@Getter
	private final Map<Integer, Boolean> soldierReward = new HashMap<>();

	@Getter
	private final Map<Integer, Integer> kvkEnterTime = new HashMap<>();

	@Override
	public void setPersistKey(Long id) {
		roleId = id;
	}

	@Override
	public Long getPersistKey() {
		return roleId;
	}

	@Override
	public Long getRoleId() {
		return roleId;
	}

	@Override
	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	@Override
	public Long getGroupingId() {
		return roleId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public int getSeason() {
		return season;
	}

	public void setSeason(int season) {
		this.season = season;
	}

	public boolean isFirstBeAttack() {
		return firstBeAttack;
	}

	public void setFirstBeAttack(boolean firstBeAttack) {
		this.firstBeAttack = firstBeAttack;
	}

	public boolean isFirstProtected() {
		return firstProtected;
	}

	public void setFirstProtected(boolean firstProtected) {
		this.firstProtected = firstProtected;
	}

	public Map<Integer, Long> getLikeMap() {
		return likeMap;
	}

	/**
	 * 取玩家某个赛季的个人功勋值领奖记录
	 * @param season
	 * @return
	 */
	public Set<String> getPersonalRewardSet(int season) {
        return seasonPersonalReward.computeIfAbsent(season, k -> new HashSet<>());
	}

	public Set<String> getWeekRewardSet(int season, int weekNum) {
		String key = String.format("%s_%s", season, weekNum);
		return seasonWeekReward.computeIfAbsent(key, k -> new HashSet<>());
	}

	public long getHonor(int season) {
		return honorMap.getOrDefault(season, 0L);
	}
	public long addHonor(int season, long addValue) {
		long total = getHonor(season) + addValue;
		honorMap.put(season, total);
		return total;
	}

	public long getWeekHonor(int season,int weekNum) {
		if(!honorWeekMap.containsKey(season)) return 0L;
		return honorWeekMap.get(season).getOrDefault(weekNum, 0L);
	}

	public void addWeekHonor(int season, int weekNum, long delta) {
		long old = getWeekHonor(season, weekNum);
		if(!honorWeekMap.containsKey(season)) {
			honorWeekMap.put(season, new HashMap());
		}
		honorWeekMap.get(season).put(weekNum, old + delta);
	}

	public void clearWeekHonor() {
		honorWeekMap.clear();
	}

	public Long getRewardAllianceId(int season) {
		return seasonAllianceRewardMap.get(season);
	}
	public void setRewardAllianceId(int season, Long allianceId) {
		seasonAllianceRewardMap.put(season, allianceId);
	}
	public boolean isGotPersonalReward(int season, String metaId) {
		if (seasonPersonalReward.get(season) == null) {
			return false;
		}
		return seasonPersonalReward.get(season).contains(metaId);
	}

	public boolean isGotWeekReward(int season, int weekNum, String metaId) {
		String key = String.format("%s_%s", season, weekNum);
		if (seasonWeekReward.get(key) == null) {
			return false;
		}
		return seasonWeekReward.get(key).contains(metaId);
	}

	public void resetKvk(int season) {
		likeMap.remove(season);
		seasonWeekReward.remove(season);
		seasonPersonalReward.remove(season);
		seasonAllianceRewardMap.remove(season);
		honorMap.remove(season);
		soldierDeads.remove(season);
	}

	public Map<Integer, Integer> getKvkEnterTime() {
		return kvkEnterTime;
	}
}
