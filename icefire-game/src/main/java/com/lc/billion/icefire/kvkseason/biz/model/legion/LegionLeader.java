package com.lc.billion.icefire.kvkseason.biz.model.legion;

import com.lc.billion.icefire.game.biz.model.role.AbstractRole;
import com.lc.billion.icefire.game.biz.model.role.info.RoleInfo;

/**
 * <AUTHOR>
 *
 */
public class LegionLeader {

	private Long roleId;

	private Long allianceId;

	private String name;

	private String head;
    private RoleInfo roleInfo;

	public void copyProperty(AbstractRole role) {
		setRoleId(role.getId());
		setAllianceId(role.getAllianceId());
		setName(role.getName());
		setHead(role.getHead());
        RoleInfo roleInfo1 = new RoleInfo();
        roleInfo1.setHeadFrame(role.getHeadFrame());
        setRoleInfo(roleInfo1);
	}

	public Long getRoleId() {
		return roleId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	public Long getAllianceId() {
		return allianceId;
	}

	public void setAllianceId(Long allianceId) {
		this.allianceId = allianceId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getHead() {
		return head;
	}

	public void setHead(String head) {
		this.head = head;
	}

    public RoleInfo getRoleInfo() {
        return roleInfo;
    }

    public void setRoleInfo(RoleInfo roleInfo) {
        this.roleInfo = roleInfo;
    }
}
