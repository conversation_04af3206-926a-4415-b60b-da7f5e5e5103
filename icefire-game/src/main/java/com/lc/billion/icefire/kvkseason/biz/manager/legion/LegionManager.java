package com.lc.billion.icefire.kvkseason.biz.manager.legion;

import com.lc.billion.icefire.core.config.model.IntKeyIntValue;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.config.legion.LegionSettingConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.root.RegionCapitalNodeDao;
import com.lc.billion.icefire.game.biz.manager.AllianceMemberManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.manager.kvk.IKvkSeasonSwitchManager;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceMember;
import com.lc.billion.icefire.game.biz.model.email.AbstractEmail;
import com.lc.billion.icefire.game.biz.model.email.MailUtils;
import com.lc.billion.icefire.game.biz.model.role.AbstractRole;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.node.RegionCapitalNode;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.*;
import com.lc.billion.icefire.kvkseason.biz.model.honor.KvkHonorAlliance;
import com.lc.billion.icefire.kvkseason.biz.model.legion.*;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.thrift.TBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 *
 */
@Component
public class LegionManager implements IKvkSeasonSwitchManager {
	// private static final Logger log = LoggerFactory.getLogger(LegionManager.class);

	@Autowired
	private LegionMemberDao legionMemberDao;
	@Autowired
	private RegionCapitalNodeDao regionCapitalNodeDao;
	@Autowired
	private LegionDao legionDao;
	@Autowired
	private ConfigServiceImpl configService;
	@Autowired
	private KvkHonorAllianceDao kvkHonorAllianceDao;
	@Autowired
	private AllianceMemberManager allianceMemberManager;
	@Autowired
	private RoleManager roleManager;
	@Autowired
	private WorldServiceImpl worldService;
	@Autowired
	private LegionOfficialManager legionOfficialManager;
	@Autowired
	private LegionOfficialDao legionOfficialDao;
	@Autowired
	private LegionRequestDao legionRequestDao;
	@Autowired
	private ServiceDependency sdp;

	/**
	 * 更新军团长，住军团转让或盟主让位的时候
	 * 
	 * @param alliance
	 */
	public void updateLegionLeader(Alliance alliance, boolean isTransfer) {
		Long allianceId = alliance.getId();
		Legion legion = findLegionByAllianceId(allianceId);
		if (legion == null) {
			// 军团可能已经解散了或没有军团
			return;
		}
		if (!isTransfer && legion.getLeaderAllianceId().longValue() != allianceId.longValue()) {
			// 不是转让且联盟不是主军团
			return;
		}
		//
		Long oldLeaderAllianceId = legion.getLeaderAllianceId();
		//
		AbstractRole abstractRole = worldService.getWorld().getAbstractRole(alliance.getLeaderId());
		LegionLeader legionLeader = new LegionLeader();
		legionLeader.copyProperty(abstractRole);
		legion.setLeader(legionLeader);
		legionDao.save(legion);
		// 更新官职
		legionOfficialManager.updateLegionOfficialForLeader(legion.getId(), alliance.getLeaderId());
		//
		// 主盟变更，军团邮件
		Alliance oldAlliance = sdp.getAllianceService().getAllianceById(oldLeaderAllianceId);
		if (oldLeaderAllianceId.longValue() != legion.getLeaderAllianceId().longValue() && oldAlliance != null) {
			List<AbstractEmail> sendMailList = new ArrayList<>();
			Collection<LegionMember> legionMembers = legionMemberDao.findByLegionId(legion.getPersistKey());
			for (LegionMember lm : legionMembers) {
				List<AllianceMember> allianceMembers = allianceMemberManager.getMembers(lm.getPersistKey());
				if (JavaUtils.bool(allianceMembers)) {
					for (AllianceMember member : allianceMembers) {
						sendMailList.add(sdp.getMailCreator().createLegionLeadAllianceChangeMail(legion, oldAlliance, alliance, member.getPersistKey(), legion.getServerId(), MailUtils.buildEmptyTitle()));
					}
				}
			}
			if (JavaUtils.bool(sendMailList)) {
				sdp.getMailSender().sendBatchMail(sendMailList);
			}
		}
	}

	public void sendMessage(Long legionId, TBase<?, ?> message) {
		List<AllianceMember> allianceMembers = findLegionRoleMemberByLegionId(legionId);
		if (JavaUtils.bool(allianceMembers)) {
			for (AllianceMember allianceMember : allianceMembers) {
				Role role = roleManager.getRole(allianceMember.getPersistKey());
				if (role != null) {
					role.send(message);
				}
			}
		}
	}

	/**
	 * 根据legionId获得所有玩家成员
	 * 
	 * @param legionId
	 * @return
	 */
	public List<AllianceMember> findLegionRoleMemberByLegionId(Long legionId) {
		if (!JavaUtils.bool(legionId)) {
			return Collections.emptyList();
		}
		Collection<LegionMember> legionMembers = legionMemberDao.findByLegionId(legionId);
		if (!JavaUtils.bool(legionMembers)) {
			return Collections.emptyList();
		}
		List<AllianceMember> allianceMembers = new ArrayList<>();
		for (LegionMember legionMember : legionMembers) {
			List<AllianceMember> members = allianceMemberManager.getMembers(legionMember.getPersistKey());
			allianceMembers.addAll(members);
		}
		return allianceMembers;
	}

	public List<AllianceMember> findLegionRoleMemberByAllianceId(Long allianceId) {
		if (!JavaUtils.bool(allianceId)) {
			return Collections.emptyList();
		}
		Legion legion = findLegionByAllianceId(allianceId);
		if (legion == null) {
			return Collections.emptyList();
		}
		return findLegionRoleMemberByLegionId(legion.getId());
	}

	public Legion findLegionByRole(AbstractRole role) {
		Long allianceId = role.getAllianceId();
		return findLegionByAllianceId(allianceId);
	}

	public Legion findLegionByAllianceId(Long allianceId) {
		if (!JavaUtils.bool(allianceId)) {
			return null;
		}
		LegionMember legionMember = legionMemberDao.findById(allianceId);
		if (legionMember == null) {
			return null;
		}
		return findLegionByLegionId(legionMember.getLegionId());
	}

	public Legion findLegionByLegionId(Long legionId) {
		if (!JavaUtils.bool(legionId)) {
			return null;
		}
		Legion legion = legionDao.findById(legionId);
		return legion;
	}

	/**
	 * 获得军团所有城市
	 *
	 * @param legionId
	 * @return
	 */
	public Collection<RegionCapitalNode> findRegionCapitalNodeByLegionId(Long legionId) {
		List<RegionCapitalNode> ret = new ArrayList<>();
		Collection<LegionMember> legionMembers = legionMemberDao.findByLegionId(legionId);
		if (JavaUtils.bool(legionMembers)) {
			for (LegionMember legionMember : legionMembers) {
				Collection<RegionCapitalNode> nodes = regionCapitalNodeDao.findByAllianceId(legionMember.getPersistKey());
				if (JavaUtils.bool(nodes)) {
					ret.addAll(nodes);
				}
			}
		}
		return ret;
	}

	/**
	 * 军团人数上限
	 * 
	 * @param legionId
	 * @return
	 */
	public int getLegionMemberMax(Long legionId) {
		int max = 0;
		// 初始值
		LegionSettingConfig legionSettingConfig = configService.getConfig(LegionSettingConfig.class);
		int initialAlliancesNum = legionSettingConfig.getInitialAlliancesNum();
		max += initialAlliancesNum;

		Legion legion = legionDao.findById(legionId);
		Long leaderAllianceId = legion.getLeaderAllianceId();
		// 势力值
		List<IntKeyIntValue> prosperityIncreaseMembers = legionSettingConfig.getProsperityIncreaseMembers();
		if (JavaUtils.bool(prosperityIncreaseMembers)) {
			long legionProsperity = legion.getProsperity();
			for (IntKeyIntValue intKeyIntValue : prosperityIncreaseMembers) {
				int prosperity = intKeyIntValue.getId();
				if (legionProsperity >= prosperity) {
					int count = intKeyIntValue.getCount();
					max += count;
				}
			}
		}

		// 荣誉值
		List<IntKeyIntValue> honorIncreaseMembers = legionSettingConfig.getHonorIncreaseMembers();
		if (JavaUtils.bool(honorIncreaseMembers)) {
			KvkHonorAlliance kvkHonorAlliance = kvkHonorAllianceDao.findById(leaderAllianceId);
			if (kvkHonorAlliance != null) {
				for (IntKeyIntValue intKeyIntValue : honorIncreaseMembers) {
					int honor = intKeyIntValue.getId();
					if (kvkHonorAlliance.getHonorValue() >= honor) {
						int count = intKeyIntValue.getCount();
						max += count;
					}
				}
			}
		}
		return max;
	}

	/**
	 * 军团人数
	 * 
	 * @param legionId
	 * @return
	 */
	public int getLegionMemberCount(Long legionId) {
		Collection<LegionMember> legionMembers = legionMemberDao.findByLegionId(legionId);
		if (legionMembers == null) {
			return 0;
		}
		return legionMembers.size();
	}

	public boolean isLegionMemberFull(Long legionId) {
		int legionMemberCount = getLegionMemberCount(legionId);
		int legionMemberMax = getLegionMemberMax(legionId);
		return legionMemberCount >= legionMemberMax;
	}

	public boolean isAllianceInLegion(Long allianceId, Long legionId) {
		if (allianceId == null || legionId == null) {
			return false;
		}
		LegionMember legionMember = legionMemberDao.findById(allianceId);
		if (legionMember == null) {
			return false;
		}
		if (legionMember.getLegionId().longValue() != legionId.longValue()) {
			return false;
		}
		return true;
	}

	public Set<Long> findAllianceIdByLegionId(Long legionId) {
		Set<Long> ret = new HashSet<>();
		Collection<LegionMember> legionMembers = legionMemberDao.findByLegionId(legionId);
		if (JavaUtils.bool(legionMembers)) {
			for (LegionMember legionMember : legionMembers) {
				ret.add(legionMember.getPersistKey());
			}
		}
		return ret;
	}

	@Override
	public void kvkSeasonSwitch(int newSeason) {
		Collection<Legion> legions = legionDao.findAll();
		if (JavaUtils.bool(legions)) {
			legionDao.delete(legions);
		}
		Collection<LegionMember> legionMembers = legionMemberDao.findAll();
		if (JavaUtils.bool(legionMembers)) {
			legionMemberDao.delete(legionMembers);
		}
		Collection<LegionOfficial> legionOfficials = legionOfficialDao.findAll();
		if (JavaUtils.bool(legionOfficials)) {
			legionOfficialDao.delete(legionOfficials);
		}
		Collection<LegionRequest> legionRequests = legionRequestDao.findAll();
		if (JavaUtils.bool(legionRequests)) {
			legionRequestDao.delete(legionRequests);
		}
	}
}
