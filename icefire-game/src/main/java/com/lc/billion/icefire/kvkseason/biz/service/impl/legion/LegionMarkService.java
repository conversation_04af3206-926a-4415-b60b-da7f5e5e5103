package com.lc.billion.icefire.kvkseason.biz.service.impl.legion;

import java.util.List;

import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.biz.BizException;
import com.lc.billion.icefire.game.biz.config.legion.LegionMarkConfig;
import com.lc.billion.icefire.game.biz.config.legion.LegionMarkConfig.LegionMarkMeta;
import com.lc.billion.icefire.game.biz.dao.mongo.alliances.AllianceMarkDao;
import com.lc.billion.icefire.game.biz.model.alliance.alliancemark.AllianceMark;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.CheckStringServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.worldarea.WorldAreaService;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionMarkDao;
import com.lc.billion.icefire.kvkseason.biz.manager.legion.LegionManager;
import com.lc.billion.icefire.kvkseason.biz.manager.legion.LegionOfficialManager;
import com.lc.billion.icefire.kvkseason.biz.model.legion.Legion;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionMark;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionRankAuthEnum;
import com.lc.billion.icefire.kvkseason.biz.service.impl.legion.async.LegionMarkCheckContentOperation;
import com.lc.billion.icefire.protocol.GcLegionMarkDel;
import com.lc.billion.icefire.protocol.GcLegionMarkDelNotify;
import com.lc.billion.icefire.protocol.GcLegionMarkList;
import com.lc.billion.icefire.protocol.GcLegionMarkSet;
import com.lc.billion.icefire.protocol.GcLegionMarkSetNotify;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 *
 */
@Service
public class LegionMarkService {

	private static final Logger logger = LoggerFactory.getLogger(LegionMarkService.class);

	@Autowired
	private CheckStringServiceImpl checkStringService;
	@Autowired
	private WorldAreaService worldAreaService;
	@Autowired
	private AsyncOperationServiceImpl asyncOperationService;
	@Autowired
	private LegionManager legionManager;
	@Autowired
	private LegionMarkDao legionMarkDao;
	@Autowired
	private LegionOfficialManager legionOfficialManager;
	@Autowired
	private ConfigServiceImpl configService;
	@Autowired
	private AllianceMarkDao allianceMarkDao;

	public void onEnterWorld(Role role) {
		Long allianceId = role.getAllianceId();
		if (!JavaUtils.bool(allianceId)) {
			return;
		}
		Legion legion = legionManager.findLegionByAllianceId(allianceId);
		if (legion == null) {
			return;
		}
		List<LegionMark> legionMarks = legionMarkDao.findByLegionIdAndServerId(legion.getId(), role.getCurrentServerId());
		if (!JavaUtils.bool(legionMarks)) {
			return;
		}
		GcLegionMarkList gcLegionMarkList = new GcLegionMarkList();
		for (LegionMark mark : legionMarks) {
			gcLegionMarkList.addToMarks(mark.toPsLegionMark());
		}
		role.send(gcLegionMarkList);
	}

	/**
	 * 设置军团标记
	 *
	 * @param role
	 * @param metaId
	 */
	public void cgLegionMarkSet(Role role, String metaId, int x, int y, String content) {
		Long allianceId = role.getAllianceId();
		if (!JavaUtils.bool(allianceId)) {
			return;
		}
		Legion legion = legionManager.findLegionByAllianceId(allianceId);
		if (legion == null) {
			return;
		}

		LegionMarkConfig legionMarkConfig = configService.getConfig(LegionMarkConfig.class);
		LegionMarkMeta legionMarkMeta = legionMarkConfig.getMeta(metaId);
		if (legionMarkMeta == null) {
			ErrorLogUtil.errorLog("军团标记配置不存在", "metaId",metaId);
			return;
		}

		boolean checkLegionOfficialAuth = legionOfficialManager.checkLegionOfficialAuth(LegionRankAuthEnum.AUTH_LEGION_MARK, role);
		if (!checkLegionOfficialAuth) {
			ErrorLogUtil.errorLog("玩家设置军团标记,无权限");
			sendGcLegionMarkSet(role, 1, metaId);
			return;
		}

		int currentServerId = role.getCurrentServerId();

		// 区域判断
		if (!worldAreaService.canEnterArea(role.getCurrentServerId(), x, y)) {
			ErrorLogUtil.errorLog("军团标记不能放入指定区域", "roleId",role.getId(), "serverId",currentServerId, "posX",x, "posY",y);
			sendGcLegionMarkSet(role, 3, metaId);
			return;
		}

		// 检查敏感词
		asyncOperationService.execute(new LegionMarkCheckContentOperation(this, checkStringService, role, metaId, x, y, content));
	}

	public void sendGcLegionMarkSet(Role role, int errorCode, String metaId) {
		GcLegionMarkSet gcLegionMarkSet = new GcLegionMarkSet();
		gcLegionMarkSet.setErrorCode(errorCode);
		gcLegionMarkSet.setMetaId(metaId);
		role.send(gcLegionMarkSet);
	}

	public void legionMarkSet2Step(Role role, String metaId, int x, int y, String content) {
		Long allianceId = role.getAllianceId();
		int serverId = role.getCurrentServerId();
		Legion legion = legionManager.findLegionByAllianceId(allianceId);
		if (legion == null) {
			ErrorLogUtil.errorLog("军团为空", new BizException(),"roleId",role.getId(),"allianceId",allianceId);
			return;
		}
		// 检查坐标是否占用
		List<LegionMark> legionMarks = legionMarkDao.findByPosition(legion.getId(), serverId, x, y);
		if (JavaUtils.bool(legionMarks)) {
			legionMarkDao.delete(legionMarks);
		}
		List<AllianceMark> allianceMarks = allianceMarkDao.findByPosition(allianceId, serverId, x, y);
		if (JavaUtils.bool(allianceMarks)) {
			allianceMarkDao.delete(allianceMarks);
		}
		LegionMark legionMark = legionMarkDao.findByLegionIdAndServerIdAndMetaId(legion.getId(), serverId, metaId);
		if (legionMark != null) {
			// 替换/重置
			legionMark.setContent(content);
			legionMark.setX(x);
			legionMark.setY(y);
			legionMark.setCreaterName(role.getName());
			legionMarkDao.save(legionMark);
			logger.info("军团标记覆盖{}", legionMark);
		} else {
			// 新建
			legionMark = legionMarkDao.create(legion, serverId, Point.getInstance(x, y), metaId, content, role.getName());
		}

		GcLegionMarkSetNotify gcLegionMarkSetNotify = new GcLegionMarkSetNotify();
		gcLegionMarkSetNotify.addToMarks(legionMark.toPsLegionMark());
		legionManager.sendMessage(legion.getId(), gcLegionMarkSetNotify);

		sendGcLegionMarkSet(role, 0, metaId);
	}

	/**
	 * 删除军团标记
	 *
	 * @param role
	 * @param metaId
	 */
	public void cgLegionMarkDel(Role role, String metaId) {
		Long allianceId = role.getAllianceId();
		if (!JavaUtils.bool(allianceId)) {
			return;
		}
		Legion legion = legionManager.findLegionByAllianceId(allianceId);
		if (legion == null) {
			return;
		}
		GcLegionMarkDel gcLegionMarkDel = new GcLegionMarkDel();
		gcLegionMarkDel.setMetaId(metaId);

		boolean checkLegionOfficialAuth = legionOfficialManager.checkLegionOfficialAuth(LegionRankAuthEnum.AUTH_LEGION_MARK, role);
		if (!checkLegionOfficialAuth) {
			gcLegionMarkDel.setErrorCode(1);
			role.send(gcLegionMarkDel);
			return;
		}

		LegionMark legionMark = legionMarkDao.findByLegionIdAndServerIdAndMetaId(legion.getId(), role.getCurrentServerId(), metaId);
		if (legionMark != null) {
			legionMarkDao.delete(legionMark);
			GcLegionMarkDelNotify gcLegionMarkDelNotify = new GcLegionMarkDelNotify();
			gcLegionMarkDelNotify.setMetaId(metaId);
			gcLegionMarkDelNotify.setX(legionMark.getX());
			gcLegionMarkDelNotify.setY(legionMark.getY());
			legionManager.sendMessage(legion.getId(), gcLegionMarkDelNotify);
		}

		gcLegionMarkDel.setErrorCode(0);
		role.send(gcLegionMarkDel);

	}
}
