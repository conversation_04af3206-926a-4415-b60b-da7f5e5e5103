package com.lc.billion.icefire.gvgcontrol.biz.service;

import java.util.*;
import java.util.Map.Entry;

import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.rpc.vo.gvg.*;
import com.lc.billion.icefire.rpc.vo.gvg.test.GVGTestMatchAllianceVo;
import com.longtech.cod.rpc.client.RpcClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.AllianceBaseDataDao;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GVGAllianceSignUpInfoDao;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GVGQualifiedServerDao;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GvgBattleServerDispatchRecordDao;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GvgCupApplyInfoDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.AllianceBaseData;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGAllianceSignUpInfo;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGQualifiedServer;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgAllianceRecord;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgBattleServerDispatchRecord;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgCupApplyInfo;
import com.lc.billion.icefire.gvgcontrol.biz.service.rpc.GVGControlRPCToGameProxyService;
import com.lc.billion.icefire.rpc.service.gvg.IGVGControlRemoteGameService;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 */
@Service
public class GVGControlBroadcastToGameService {

    private static final Logger logger = LoggerFactory.getLogger(GVGControlBroadcastToGameService.class);

    @Autowired
    private GVGControlRPCToGameProxyService gvgControlRPCToGameProxyService;
    @Autowired
    private GVGQualifiedServerDao gvgQualifiedServerDao;
    @Autowired
    private GVGAllianceSignUpInfoDao gvgAllianceSignUpInfoDao;
    @Autowired
    private GvgBattleServerDispatchRecordDao gvgBattleServerDispatchRecordDao;
    @Autowired
    private AllianceBaseDataDao allianceBaseDataDao;
    @Autowired
    private GvgCupApplyInfoDao gvgCupApplyInfoDao;
    @Autowired
    private GVGControlService gvgControlService;

    /**
     * 根据serverId 判断两个给定serverId的Rpc远端是否为同一进程
     *
     * @param serverId1
     * @param serverId2
     * @return
     */
    private boolean isSameRpcProcess(int serverId1, int serverId2) {
        if (serverId1 == serverId2) {
            return true;
        }
        RpcClient rpcClient1 = gvgControlRPCToGameProxyService.getGvgControlRemoteGameRpcClient(serverId1);
        if (rpcClient1 == null) {
            return false;
        }
        RpcClient rpcClient2 = gvgControlRPCToGameProxyService.getGvgControlRemoteGameRpcClient(serverId2);
        return rpcClient1.equals(rpcClient2);
    }

    /**
     * 匹配结束广播分配记录
     */
    public void broadcastGvgBattleServerDispatchRecord(GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord) {
        if (gvgBattleServerDispatchRecord == null) {
            return;
        }
        GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo = new GvgBattleServerDispatchRecordVo(gvgBattleServerDispatchRecord);
        int alliance1ServerId = gvgBattleServerDispatchRecord.getAlliance1ServerId();
        IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(alliance1ServerId);
        if (gvgControlRemoteGameService != null) {
            gvgControlRemoteGameService.broadcastGvgBattleServerDispatchRecord(gvgBattleServerDispatchRecordVo);
        } else {
            ErrorLogUtil.errorLog("中控服广播对战信息,但游戏服没起来", "gvgBattleServerDispatchRecordVo",gvgBattleServerDispatchRecordVo);
        }
        int alliance2ServerId = gvgBattleServerDispatchRecord.getAlliance2ServerId();
        if (!isSameRpcProcess(alliance1ServerId, alliance2ServerId)) {
            // 红蓝方不同rpc目标
            gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(alliance2ServerId);
            if (gvgControlRemoteGameService != null) {
                gvgControlRemoteGameService.broadcastGvgBattleServerDispatchRecord(gvgBattleServerDispatchRecordVo);
            } else {
                ErrorLogUtil.errorLog("中控服广播对战信息,但游戏服没起来", "gvgBattleServerDispatchRecordVo",gvgBattleServerDispatchRecordVo);
            }
        }

    }

    /**
     * 指定类型清除，
     *
     * @param types
     */
    public void broadcastGvgBattleServerDispatchRecordClear(GvgMatchType[] types) {
        Map<Integer, IGVGControlRemoteGameService> gvgControlRemoteGameServices = gvgControlRPCToGameProxyService.getGVGControlRemoteGameServices();
        if (JavaUtils.bool(gvgControlRemoteGameServices)) {
            for (Entry<Integer, IGVGControlRemoteGameService> entry : gvgControlRemoteGameServices.entrySet()) {
                Integer serverId = entry.getKey();
                IGVGControlRemoteGameService gvgControlRemoteGameService = entry.getValue();
                gvgControlRemoteGameService.broadcastGvgBattleServerDispatchRecordClear(serverId, types);
            }
        }
    }

    /**
     * 中控广播给所有game，报名信息，中控服启服调用
     */
    public void broadcastGVGAllianceSignUpInfos(Collection<GvgBattleServerDispatchRecord> all) {
        if (!JavaUtils.bool(all)) {
            ErrorLogUtil.errorLog("同步阵容，匹配信息为空null");
            return;
        }

        Map<Integer, List<GVGAllianceSignUpInfo>> serverSignInfoMap = new HashMap<>();
        for (GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord : all) {
            Long allianceId1 = gvgBattleServerDispatchRecord.getAllianceId1();
            Long allianceId2 = gvgBattleServerDispatchRecord.getAllianceId2();
            GvgMatchType matchType = gvgBattleServerDispatchRecord.getMatchType();
            GVGAllianceSignUpInfo allianceSignUpInfo1 = gvgAllianceSignUpInfoDao.findByAllianceIdAndType(allianceId1, matchType);
            GVGAllianceSignUpInfo allianceSignUpInfo2 = gvgAllianceSignUpInfoDao.findByAllianceIdAndType(allianceId2, matchType);
            if (allianceSignUpInfo1 != null) {
                serverSignInfoMap.compute(allianceSignUpInfo1.getServerId(), (k,v) -> v == null ? new ArrayList<>() : v).add(allianceSignUpInfo1);
            }
            if (allianceSignUpInfo2 != null) {
                serverSignInfoMap.compute(allianceSignUpInfo2.getServerId(), (k,v) -> v == null ? new ArrayList<>() : v).add(allianceSignUpInfo2);
            }

        }

        // 按服务器id分组发送
        for (Entry<Integer, List<GVGAllianceSignUpInfo>> entry : serverSignInfoMap.entrySet()) {
            Integer serverId = entry.getKey();
            List<GVGAllianceSignUpInfo> gvgAllianceSignUpInfos = entry.getValue();
            IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(serverId);
            List<GVGAllianceSignUpInfoVo> ret = new ArrayList<>();
            for (GVGAllianceSignUpInfo gvgAllianceSignUpInfo : gvgAllianceSignUpInfos) {
                ret.add(new GVGAllianceSignUpInfoVo(gvgAllianceSignUpInfo));
            }
            if (gvgControlRemoteGameService != null) {
                gvgControlRemoteGameService.broadcastGVGAllianceSignUpInfos(ret);
            }
        }
    }

    public void broadcastGVGAllianceSignUpInfos() {
        Map<Integer, List<GVGAllianceSignUpInfo>> gvgAllianceSignUpInfoMap = gvgAllianceSignUpInfoDao.findByServerId();
        if (!JavaUtils.bool(gvgAllianceSignUpInfoMap)) {
            ErrorLogUtil.errorLog("报名信息null");
            return;
        }
        // 按服务器id分组发送
        for (Entry<Integer, List<GVGAllianceSignUpInfo>> entry : gvgAllianceSignUpInfoMap.entrySet()) {
            Integer serverId = entry.getKey();
            List<GVGAllianceSignUpInfo> gvgAllianceSignUpInfos = entry.getValue();
            IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(serverId);
            List<GVGAllianceSignUpInfoVo> ret = new ArrayList<>();
            for (GVGAllianceSignUpInfo gvgAllianceSignUpInfo : gvgAllianceSignUpInfos) {
                ret.add(new GVGAllianceSignUpInfoVo(gvgAllianceSignUpInfo));
            }
            if (gvgControlRemoteGameService != null) {
                gvgControlRemoteGameService.broadcastGVGAllianceSignUpInfos(ret);
            }
        }
    }

    /**
     * 中控服广播战斗记录给game服，方便领奖
     */
    public void broadcastGVGBattleRecord(GVGBattleRecordVo gvgBattleRecordVo) {
        if (gvgBattleRecordVo == null) {
            return;
        }
        int victoryAllianceServerId = gvgBattleRecordVo.getVictoryAllianceServerId();
        IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(victoryAllianceServerId);
        if (gvgControlRemoteGameService == null) {
            ErrorLogUtil.errorLog("[GVG] broadcastGVGBattleRecord can'tFindGameServer","victoryAllianceServerId",victoryAllianceServerId);
        } else {
            gvgControlRemoteGameService.broadcastGVGBattleRecord(gvgBattleRecordVo);
        }

        int failAllianceServerId = gvgBattleRecordVo.getFailAllianceServerId();
        if (failAllianceServerId != 0 && !isSameRpcProcess(victoryAllianceServerId, failAllianceServerId)) {
            IGVGControlRemoteGameService gvgControlRemoteGameService2 = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(failAllianceServerId);
            if (gvgControlRemoteGameService2 == null) {
                ErrorLogUtil.errorLog("[GVG] broadcastGVGBattleRecord can'tFindGameServer2","failAllianceServerId",failAllianceServerId);
            } else if (gvgControlRemoteGameService == gvgControlRemoteGameService2) {
                logger.info("[GVG] broadcastGVGBattleRecord sameServerOfTwoSide，{} - {}", victoryAllianceServerId, failAllianceServerId);
            } else {
                gvgControlRemoteGameService2.broadcastGVGBattleRecord(gvgBattleRecordVo);
            }
        }
    }


    public void broadcastGVGActivity(Activity activity, Set<Integer> serverIds) {
        if (activity == null || !JavaUtils.bool(serverIds)) {
            return;
        }

        Collection<GVGQualifiedServer> gvgQualifiedServers = gvgQualifiedServerDao.findAll();
        if (JavaUtils.bool(gvgQualifiedServers)) {
            ActivityVo activityVo = new ActivityVo(activity);
            Set<RpcClient> alreadySentRpcClients = new HashSet<>();
            for (GVGQualifiedServer gvgQualifiedServer : gvgQualifiedServers) {
                int serverId = gvgQualifiedServer.getServerId();
                if (!serverIds.contains(serverId)) {
                    continue;
                }
                IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(serverId);
                if (gvgControlRemoteGameService != null) {
                    RpcClient rpcClient = gvgControlRPCToGameProxyService.getGvgControlRemoteGameRpcClient(gvgQualifiedServer.getServerId());
                    if (rpcClient != null && alreadySentRpcClients.add(rpcClient)) {
                        gvgControlRemoteGameService.broadcastGVGActivity(activityVo);
                    } else {
                        ErrorLogUtil.errorLog("中控服找不到game服的rpc client 或者 已经广播过了,不广播", "gvgQualifiedServerId",gvgQualifiedServer.getServerId());
                    }
                } else {
                    ErrorLogUtil.errorLog("中控服找不到game服的rpc连接不广播", "gvgQualifiedServerId",gvgQualifiedServer.getServerId());
                }
            }
        }
    }
    /**
     * 广播gvg活动信息给有资格的game服
     */
    public void broadcastGVGActivity(Activity activity) {
        if (activity == null) {
            return;
        }
        Collection<GVGQualifiedServer> gvgQualifiedServers = gvgQualifiedServerDao.findAll();
        if (JavaUtils.bool(gvgQualifiedServers)) {
            ActivityVo activityVo = new ActivityVo(activity);
            Set<RpcClient> alreadySentRpcClients = new HashSet<>();
            gvgQualifiedServers.forEach(gvgQualifiedServer -> {
                IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(gvgQualifiedServer.getServerId());
                if (gvgControlRemoteGameService != null) {
                    RpcClient rpcClient = gvgControlRPCToGameProxyService.getGvgControlRemoteGameRpcClient(gvgQualifiedServer.getServerId());
                    if (rpcClient != null && alreadySentRpcClients.add(rpcClient)) {
                        gvgControlRemoteGameService.broadcastGVGActivity(activityVo);
                    } else {
                        ErrorLogUtil.errorLog("中控服找不到game服的rpc client 或者 已经广播过了,不广播", "gvgQualifiedServerId",gvgQualifiedServer.getServerId());
                    }
                } else {
                    ErrorLogUtil.errorLog("中控服找不到game服的rpc连接不广播", "gvgQualifiedServerId",gvgQualifiedServer.getServerId());
                }
            });
        }
    }

    public Map<Integer, Set<AllianceBaseDataVo>> broadcastNoticeSelectGVGAlliance() {
        Map<Integer, Set<AllianceBaseDataVo>> qualifiedAllianceIdsByServerId = new HashMap<>();
        // 通知game服删除老的资格联盟并选出新的资格联盟
        Map<Integer, IGVGControlRemoteGameService> gvgControlRemoteGameServices = gvgControlRPCToGameProxyService.getGVGControlRemoteGameServices();
        if (JavaUtils.bool(gvgControlRemoteGameServices)) {
            for (Entry<Integer, IGVGControlRemoteGameService> gvgControlRemoteGameService : gvgControlRemoteGameServices.entrySet()) {
                if (gvgControlRemoteGameService != null) {
                    Integer serverId = gvgControlRemoteGameService.getKey();
                    // 优化检测
                    if (serverId == null || serverId == 0 || !gvgControlService.findGVGServerQualified(serverId)) {
                        continue;
                    }
                    IGVGControlRemoteGameService service = gvgControlRemoteGameService.getValue();
                    Set<AllianceBaseDataVo> res = service.broadcastNoticeSelectGVGAlliance(serverId);
                    if (res != null) {
                        logger.info("[GVG] broadcastNoticeSelectGVGAlliance serverId {} qualified alliance count is {}", serverId, res.size());
                        qualifiedAllianceIdsByServerId.compute(serverId, (k, v) -> v == null ? new HashSet<>() : v).addAll(res);
                    } else {
                        logger.info("[GVG]] broadcastNoticeSelectGVGAlliance serverId {} qualified alliance set is null", serverId);
                    }
                }
            }
        }
        return qualifiedAllianceIdsByServerId;
    }

    /**
     * 批量获取联盟战斗力
     *
     * @param serverId
     * @param allianceIds
     * @return 战力排名与战力映射<Ranking, Long [ ] { AllianceId, FightPower, serverId }>
     */
    public Map<Long, Long[]> getAllianceFightPower(int serverId, Set<Long> allianceIds) {
        IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(serverId);
        return gvgControlRemoteGameService.getAllianceFightPowerRankMapper(serverId, allianceIds);
    }

    public void broadcastAllianceBaseData(int serverId, AllianceBaseData allianceBaseData) {
        if (allianceBaseData == null) {
            return;
        }
        IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(serverId);
        if (gvgControlRemoteGameService != null) {
            gvgControlRemoteGameService.broadcastAllianceBaseData(new AllianceBaseDataVo(allianceBaseData));
        }
    }

    public void broadcastAllianceSignUpInfo(int serverId, GVGAllianceSignUpInfo allianceSignUpInfo) {
        IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(serverId);
        if (gvgControlRemoteGameService != null) {
            gvgControlRemoteGameService.broadcastAllianceSignUp(new GVGAllianceSignUpInfoVo(allianceSignUpInfo));
        }
    }

    public void broadcastAllianceBaseData() {
        Map<Integer, List<GvgBattleServerDispatchRecord>> gvgBattleServerDispatchRecordsByServerId = gvgBattleServerDispatchRecordDao.findByServerId();
        if (JavaUtils.bool(gvgBattleServerDispatchRecordsByServerId)) {
            for (Entry<Integer, List<GvgBattleServerDispatchRecord>> entry : gvgBattleServerDispatchRecordsByServerId.entrySet()) {
                Integer serverId = entry.getKey();
                List<GvgBattleServerDispatchRecord> gvgBattleServerDispatchRecords = entry.getValue();
                if (JavaUtils.bool(gvgBattleServerDispatchRecords)) {
                    IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(serverId);
                    if (gvgControlRemoteGameService != null) {
                        Set<Long> allianceIds = new HashSet<>();
                        gvgBattleServerDispatchRecords.forEach(gvgBattleServerDispatchRecord -> {
                            allianceIds.add(gvgBattleServerDispatchRecord.getAllianceId1());
                            allianceIds.add(gvgBattleServerDispatchRecord.getAllianceId2());
                        });
                        if (JavaUtils.bool(allianceIds)) {
                            List<AllianceBaseDataVo> ret = new ArrayList<>();
                            allianceIds.forEach(allianceId -> {
                                AllianceBaseData allianceBaseData = allianceBaseDataDao.findById(allianceId);
                                ret.add(new AllianceBaseDataVo(allianceBaseData));
                            });
                            gvgControlRemoteGameService.broadcastAllianceBaseDatas(ret);
                        }
                    }
                }
            }
        }
    }

    public void broadcastGvgAllianceRecord(GvgAllianceRecord gvgAllianceRecord) {
        if (gvgAllianceRecord == null || gvgAllianceRecord.getServerId() == 0) {
            return;
        }
        IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(gvgAllianceRecord.getServerId());
        if (gvgControlRemoteGameService == null) {
            ErrorLogUtil.errorLog("中控服广播game服GvgAllianceRecord找不到服务",new RuntimeException(),"gvgServerId",gvgAllianceRecord.getServerId());
            return;
        }
        gvgControlRemoteGameService.broadcastGvgAllianceRecord(new GvgAllianceRecordVo(gvgAllianceRecord));
        logger.info("中控服广播GvgAllianceRecord到server,{},{}", gvgAllianceRecord.getServerId(), gvgAllianceRecord.getAllianceId());
    }

    // public void noticeGameServerQualifieds() {
    // Collection<GVGQualifiedServer> gvgQualifiedServers =
    // gvgQualifiedServerDao.findAll();
    // if (JavaUtils.bool(gvgQualifiedServers)) {
    // gvgQualifiedServers.forEach(gvgQualifiedServer -> {
    // IGVGControlRemoteGameService gvgControlRemoteGameService =
    // gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(gvgQualifiedServer.getServerId());
    // if (gvgControlRemoteGameService != null) {
    // gvgControlRemoteGameService.noticeGameServerQualified();
    // }
    // });
    // }
    // }

    /**
     * 广播小组赛入围联盟名单
     *
     * @param map
     */
    public void broadcastGvgCupApplyInfo(Map<Integer, List<GvgCupApplyInfoVO>> map) {
        if (map == null || map.size() < 1) {
            return;
        }
        for (int serverId : map.keySet()) {
            IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(serverId);
            if (gvgControlRemoteGameService == null) {
                ErrorLogUtil.errorLog("中控服广播game服GvgCupGroupList找不到服务","serverId",serverId);
                continue;
            }
            List<GvgCupApplyInfoVO> vos = map.get(serverId);
            if (vos != null && vos.size() > 0) {
                gvgControlRemoteGameService.broadcastGvgCupApplyInfo(vos);
            }
        }
    }

    public void broadcastGvgCupApplyInfo() {
        Map<Integer, List<GvgCupApplyInfo>> all = gvgCupApplyInfoDao.getDataByServerId();
        if (all != null) {
            Map<Integer, List<GvgCupApplyInfoVO>> res = new HashMap<>();
            for (Integer serverId : all.keySet()) {
                List<GvgCupApplyInfoVO> vos = new ArrayList<>();
                for (GvgCupApplyInfo info : all.get(serverId)) {
                    vos.add(new GvgCupApplyInfoVO(info));
                }
                res.put(serverId, vos);
            }

            broadcastGvgCupApplyInfo(res);
        }

    }

    /**
     * 通知各game服 更新gvg观战数据列表
     */
    public void noticeGameUpdateGvgObMatchList(Map<Integer, List<GvgObMatchInfoVo>> vos) {

        Map<Integer, IGVGControlRemoteGameService> gvgControlRemoteGameServices = gvgControlRPCToGameProxyService.getGVGControlRemoteGameServices();
        if (JavaUtils.bool(gvgControlRemoteGameServices)) {
            for (Entry<Integer, IGVGControlRemoteGameService> entry : gvgControlRemoteGameServices.entrySet()) {
                Integer serverId = entry.getKey();
                IGVGControlRemoteGameService gvgControlRemoteGameService = entry.getValue();
                logger.info("GvgCup:通知game #{} 更新GVG观战数据列表", serverId);
                gvgControlRemoteGameService.broadcastGvgObMatchList(vos);
            }
        }
    }

    /**
     * 通知各game服 清除观赛数据
     */
    public void noticeGameDeletegvgObMatchList() {
        Map<Integer, IGVGControlRemoteGameService> gvgControlRemoteGameServices = gvgControlRPCToGameProxyService.getGVGControlRemoteGameServices();
        if (JavaUtils.bool(gvgControlRemoteGameServices)) {
            for (Entry<Integer, IGVGControlRemoteGameService> entry : gvgControlRemoteGameServices.entrySet()) {
                Integer serverId = entry.getKey();
                IGVGControlRemoteGameService gvgControlRemoteGameService = entry.getValue();
                gvgControlRemoteGameService.broadcastGvgObserveListClear(serverId);
            }
        }
    }

    /**
     * 通知各game 清除gvg入场推送
     */
    public void broadcastGvgAdmissionPushClear() {
        Map<Integer, IGVGControlRemoteGameService> gvgControlRemoteGameServices = gvgControlRPCToGameProxyService.getGVGControlRemoteGameServices();
        Set<RpcClient> alreadySentRpcClients = new HashSet<>();
        if (JavaUtils.bool(gvgControlRemoteGameServices)) {
            for (Entry<Integer, IGVGControlRemoteGameService> entry : gvgControlRemoteGameServices.entrySet()) {
                Integer serverId = entry.getKey();
                IGVGControlRemoteGameService gvgControlRemoteGameService = entry.getValue();
                RpcClient rpcClient = gvgControlRPCToGameProxyService.getGvgControlRemoteGameRpcClient(serverId);
                if (gvgControlRemoteGameService != null && rpcClient != null && alreadySentRpcClients.add(rpcClient)) {
                    gvgControlRemoteGameService.broadcastGvgAdmissionPushClear();
                }
            }
        }
    }

    public void broadcastGvgadmissionPushCreate() {
        Map<Integer, IGVGControlRemoteGameService> gvgControlRemoteGameServices = gvgControlRPCToGameProxyService.getGVGControlRemoteGameServices();
        Set<RpcClient> alreadySentRpcClients = new HashSet<>();
        if (JavaUtils.bool(gvgControlRemoteGameServices)) {
            for (Entry<Integer, IGVGControlRemoteGameService> entry : gvgControlRemoteGameServices.entrySet()) {
                Integer serverId = entry.getKey();
                IGVGControlRemoteGameService gvgControlRemoteGameService = entry.getValue();
                RpcClient rpcClient = gvgControlRPCToGameProxyService.getGvgControlRemoteGameRpcClient(serverId);
                if (gvgControlRemoteGameService != null && rpcClient != null && alreadySentRpcClients.add(rpcClient)) {
                    gvgControlRemoteGameService.broadcastGvgAdmissionPushCreate();
                }
            }
        }
    }

    /**
     * 广播gvg活动信息给有资格的game服
     */
    public void broadcastRZEActivity(Activity activity) {
        if (activity == null) {
            return;
        }
        Collection<GVGQualifiedServer> gvgQualifiedServers = gvgQualifiedServerDao.findAll();
        if (JavaUtils.bool(gvgQualifiedServers)) {
            ActivityVo activityVo = new ActivityVo(activity);
            Set<IGVGControlRemoteGameService> alreadySentRPCProxies = new HashSet<>();
            gvgQualifiedServers.forEach(gvgQualifiedServer -> {
                IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(gvgQualifiedServer.getServerId());
                if (gvgControlRemoteGameService != null) {
                    if (alreadySentRPCProxies.add(gvgControlRemoteGameService)) {
                        gvgControlRemoteGameService.broadcastRZEActivity(activityVo);
                    }
                } else {
                    ErrorLogUtil.errorLog("RZE 中控服找不到game服的rpc连接不广播", "gvgQualifiedServerId",gvgQualifiedServer.getServerId());
                }
            });
        }
    }

    /**
     * 广播各game 选出RZE 有资格联盟
     */
    public void broadcastNoticeSelectRZEAlliance() {
        Map<Integer, IGVGControlRemoteGameService> gvgControlRemoteGameServices = gvgControlRPCToGameProxyService.getGVGControlRemoteGameServices();
        if (JavaUtils.bool(gvgControlRemoteGameServices)) {
            for (Entry<Integer, IGVGControlRemoteGameService> gvgControlRemoteGameService : gvgControlRemoteGameServices.entrySet()) {
                if (gvgControlRemoteGameService != null) {
                    Integer serverId = gvgControlRemoteGameService.getKey();
                    IGVGControlRemoteGameService service = gvgControlRemoteGameService.getValue();
                    service.broadcastNoticeSelectRZEAlliance(serverId);
                }
            }
        }
    }

    public void broadcastRoomInfoToServer(RZERoomInfoVo rzeRoomInfoVo, Long broadcastAllianceId, int broadcastServerId) {
        if (rzeRoomInfoVo == null) {
            return;
        }

        IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(broadcastServerId);
        if (gvgControlRemoteGameService != null) {
            gvgControlRemoteGameService.broadcastRZERoom(rzeRoomInfoVo, broadcastAllianceId);
        } else {
            ErrorLogUtil.errorLog("RZE 中控服找不到game服的rpc连接不广播", "broadcastServerId",broadcastServerId);
        }
    }

    public void broadcastBeInviteInfoToServer(Long roomId, Long broadcastAllianceId, int broadcastServerId) {
        IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(broadcastServerId);
        if (gvgControlRemoteGameService != null) {
            gvgControlRemoteGameService.broadcastBeInviteRoom(roomId, broadcastAllianceId);
        } else {
            ErrorLogUtil.errorLog("RZE 中控服找不到game服的rpc连接不广播", "broadcastServerId",broadcastServerId);
        }
    }

    /**
     * 广播删除申请信息到对应服
     */
    public void broadcastRemoveApplyInfoToServer(Long roomId, List<Integer> broadcastServerIds) {
        for (Integer serverId : broadcastServerIds) {
            IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(serverId);
            if (gvgControlRemoteGameService != null) {
                gvgControlRemoteGameService.broadcastRemoveApply(roomId);
            } else {
                ErrorLogUtil.errorLog("RZE 中控服找不到game服的rpc连接不广播", "serverId",serverId);
            }
        }
    }

    /**
     * 广播删除被邀请信息到对应服
     */
    public void broadcastRemoveBeInviteInfoToServer(Long roomId, List<Integer> broadcastServerIds) {
        for (Integer serverId : broadcastServerIds) {
            IGVGControlRemoteGameService gvgControlRemoteGameService = gvgControlRPCToGameProxyService.getGVGControlRemoteGameService(serverId);
            if (gvgControlRemoteGameService != null) {
                gvgControlRemoteGameService.broadcastRemoveBeInvite(roomId);
            } else {
                ErrorLogUtil.errorLog("RZE 中控服找不到game服的rpc连接不广播", "serverId",serverId);
            }
        }
    }

    public void broadcastRZEAllianceInfoVo(Collection<RZEAllianceInfoVo> infos) {
        Map<Integer, IGVGControlRemoteGameService> gvgControlRemoteGameServices = gvgControlRPCToGameProxyService.getGVGControlRemoteGameServices();
        if (JavaUtils.bool(gvgControlRemoteGameServices)) {
            for (int serverId : gvgControlRemoteGameServices.keySet()) {
                broadcastRZEAllianceInfoVo(serverId, infos);
            }
        }
    }

    public void broadcastRZEAllianceInfoVo(int serverId, Collection<RZEAllianceInfoVo> infos) {
        Map<Integer, IGVGControlRemoteGameService> gvgControlRemoteGameServices = gvgControlRPCToGameProxyService.getGVGControlRemoteGameServices();
        if (JavaUtils.bool(gvgControlRemoteGameServices)) {
            Set<IGVGControlRemoteGameService> alreadySentRPCProxies = new HashSet<>();
            IGVGControlRemoteGameService service = gvgControlRemoteGameServices.get(serverId);
            if (service != null && alreadySentRPCProxies.add(service)) {
                service.broadcastRZEAllianceInfoVo(serverId, infos);
            }
        }
    }

    public Map<Integer, Map<Long, GVGAllianceMatchInfoVo>> broadcastGvgAllianceMatchInfo() {
        Map<Integer, IGVGControlRemoteGameService> gvgControlRemoteGameServices = gvgControlRPCToGameProxyService.getGVGControlRemoteGameServices();
        Map<Integer, Map<Long, GVGAllianceMatchInfoVo>> res = new HashMap<>();
        if (JavaUtils.bool(gvgControlRemoteGameServices)) {
            for (Entry<Integer, IGVGControlRemoteGameService> gvgControlRemoteGameService : gvgControlRemoteGameServices.entrySet()) {
                if (gvgControlRemoteGameService != null) {
                    Integer serverId = gvgControlRemoteGameService.getKey();
                    // 优化检测
                    if (serverId == null || serverId == 0 || !gvgControlService.findGVGServerQualified(serverId)) {
                        continue;
                    }

                    IGVGControlRemoteGameService service = gvgControlRemoteGameService.getValue();
                    Map<Long, GVGAllianceMatchInfoVo> gvgAllianceMatchInfoVoMap = service.broadcastAllianceMatchInfo(serverId);
                    if (gvgAllianceMatchInfoVoMap != null) {
                        res.put(serverId, gvgAllianceMatchInfoVoMap);
                    }
                }
            }
        }
        return res;
    }

    public List<GVGTestMatchAllianceVo> collectAllianceMatchData(HashMap<Integer, Map<Long, List<Long>>> serverAllianceMap, GvgMatchType matchType) {
        Map<Integer, IGVGControlRemoteGameService> gvgControlRemoteGameServices = gvgControlRPCToGameProxyService.getGVGControlRemoteGameServices();
        List<GVGTestMatchAllianceVo> ret = new ArrayList<>();
        if (!JavaUtils.bool(gvgControlRemoteGameServices)) {
            return ret;
        }

        for (Entry<Integer, IGVGControlRemoteGameService> entry : gvgControlRemoteGameServices.entrySet()) {
            if (entry == null) {
                continue;
            }

            Integer serverId = entry.getKey();
            // 优化检测
            if (serverId == null || serverId == 0 || !gvgControlService.findGVGServerQualified(serverId)) {
                continue;
            }

            var reqData = serverAllianceMap.get(serverId);
            if(reqData == null){
                continue;
            }

            IGVGControlRemoteGameService service = entry.getValue();
            List<GVGTestMatchAllianceVo> rpcRet = service.collectAllianceMatchData(serverId, reqData, matchType);
            ret.addAll(rpcRet);
        }

        return ret;
    }
}
