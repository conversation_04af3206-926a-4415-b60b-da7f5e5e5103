package com.lc.billion.icefire.kvkseason.biz.model.seasonTask;

import java.util.HashMap;
import java.util.Map;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;

/**
 * 
 * - 18=所在军团占领X次Y级城 -
 * 
 * - 19=所在军团赛季势力值达到X
 * 
 * - 20=所在军团在赛季结算时占领着X个Y级城
 * 
 * - 21=所在军团累计摧毁X个其他服联盟的远征哨塔
 * 
 * - 22=军团荣誉值达到X
 *
 * <AUTHOR>
 * @Date 2022/3/16
 */
public class LegionSeasonTaskProgress extends AbstractEntity {

	@MongoId
	private Long legionId;// 军团id

	private Map<Integer, Integer> occupyCityTimes = new HashMap<>();// 占领王城次数
	private Map<Integer, Integer> occupyCity = new HashMap<>();// 当前占领王城状态,限时任务数据，结算后不再更新

	@Override
	public void setPersistKey(Long id) {
		this.legionId = id;
	}

	@Override
	public Long getPersistKey() {
		return legionId;
	}

	@Override
	public Long getGroupingId() {
		return legionId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	/**
	 * 占领几次 x级城
	 * 
	 * @param level
	 * @return
	 */
	public int getOccupyTimes(int level) {
		return occupyCityTimes.containsKey(level) ? occupyCityTimes.get(level) : 0;
	}

	public void addOccupyTimes(int level) {
		occupyCityTimes.compute(level, (k, v) -> v == null ? 1 : v + 1);
	}

	/**
	 * 当前占领 x级城 y个
	 * 
	 * @param level
	 * @return
	 */
	public int getOccupyCount(int level) {
		return occupyCity.containsKey(level) ? occupyCity.get(level) : 0;
	}

	public void setOccupyCity(Map<Integer, Integer> occupyCity) {
		this.occupyCity = occupyCity;
	}
}
