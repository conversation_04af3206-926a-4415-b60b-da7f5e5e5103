package com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.impl;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.kvk.SeasonTaskConfig;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.node.RegionCapitalNode;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.RoleSeasonTaskRecord;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.ServerSeasonTaskProgress;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.AbstractSeasonTaskAction;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.SeasonTaskActionType;
import com.longtech.ls.config.ServerType;
import org.springframework.stereotype.Service;

/**
 * 
 * 所在服务器在赛季结算时占领着X个Y级城
 * 
 * <AUTHOR>
 * @date 2021/11/16
 */
@Service
public class ServerOccupyRegionCapitalNumFinal extends AbstractSeasonTaskAction {
	@Override
	public SeasonTaskActionType getType() {
		return SeasonTaskActionType.SERVER_OCCUPY_REGION_CAPITAL_NUM_FINAL;
	}

	@Override
	public long getProgress(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		ServerSeasonTaskProgress serverRecord = serverSeasonTaskProgressDao.getByServerId(role.getoServerId());
		int i = meta.getConditions().indexOf(getType().getId());
		int param2 = meta.getParam2s().get(i);
		return serverRecord.getOccupyCityNum(param2);
	}

	@Override
	protected void updateProgress(Role role, RoleSeasonTaskRecord taskRecord, Object... params) {
		RegionCapitalNode node = (RegionCapitalNode) params[0];
		Long oldBelongOwnerId = null;// 联盟Id或者军团id
		if (params[1] != null) {
			oldBelongOwnerId = (Long) params[1];
		}
		if (Application.getConfigCenter().getServerType(node.getCurrentServerId()) != ServerType.KVK_SEASON) {
			// 不是赛季服K服的王城，跳过
			return;
		}

		// 计算 新旧所属的服务器Id
		int newBelongServerId = 0;
		int oldBelongServerId = 0;
		Alliance newBelongAlliance = allianceService.getAllianceById(node.getBelongAllianceId());
		Alliance oldBelongAlliance = allianceService.getAllianceById(oldBelongOwnerId);
		if (newBelongAlliance != null) {
			newBelongServerId = newBelongAlliance.getoServerId();
		}
		if (oldBelongAlliance != null) {
			oldBelongServerId = oldBelongAlliance.getoServerId();
		}

		//
		// 新旧所属服务器不为0 && 新旧所属服务器相同，不需要处理
		if (newBelongServerId != 0 && oldBelongServerId != 0 && newBelongServerId == oldBelongServerId) {
			return;
		}
		//
		if (newBelongServerId != 0) {
			ServerSeasonTaskProgress serverSeasonTaskProgress = serverSeasonTaskProgressDao.getByServerId(newBelongServerId);
			serverSeasonTaskProgress.addOccupyCity(node.getLevel());
			serverSeasonTaskProgressDao.save(serverSeasonTaskProgress);
		}
		if (oldBelongServerId != 0) {
			ServerSeasonTaskProgress serverSeasonTaskProgress = serverSeasonTaskProgressDao.getByServerId(oldBelongServerId);
			serverSeasonTaskProgress.delOccupyCity(node.getLevel());
			serverSeasonTaskProgressDao.save(serverSeasonTaskProgress);
		}
	}

	@Override
	public boolean actionFinishCheck(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		int i = meta.getConditions().indexOf(getType().getId());
		int param1 = meta.getParam1s().get(i);
		return getProgress(role, meta) >= param1;
	}
}
