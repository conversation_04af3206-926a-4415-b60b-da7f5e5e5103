package com.lc.billion.icefire.kvkseason.biz.async;

import java.util.List;
import java.util.Map;

import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.biz.config.kvk.SeasonTaskConfig;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.SeasonTaskService;
import com.simfun.sgf.utils.JavaUtils;

/**
 * 
 * 赛季任务结算 发送所有未领 奖励邮件
 * 
 *
 * <AUTHOR>
 * @date 2021/12/6
 */
public class SeasonTaskSettlementOperation implements AsyncOperation {

	private SeasonTaskService seasonTaskService;
	private Map<Long, List<SeasonTaskConfig.SeasonTaskMeta>> autoRewardMap;

	public SeasonTaskSettlementOperation(SeasonTaskService seasonTaskService, Map<Long, List<SeasonTaskConfig.SeasonTaskMeta>> autoRewardMap) {
		this.seasonTaskService = seasonTaskService;
		this.autoRewardMap = autoRewardMap;
	}

	@Override
	public boolean init() {
		return true;
	}

	@Override
	public boolean run() {
		if (JavaUtils.bool(autoRewardMap)) {
			seasonTaskService.sendSeasonTaskRewardMail(autoRewardMap);
		}
		return false;
	}

	@Override
	public void finish() {
		// doNothing
	}
}
