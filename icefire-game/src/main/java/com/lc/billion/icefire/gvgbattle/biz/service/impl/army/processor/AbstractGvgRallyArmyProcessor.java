package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.processor;

import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import com.lc.billion.icefire.game.biz.service.impl.army.processor.RallyArmyProcessor;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021/2/22
 */
public abstract class AbstractGvgRallyArmyProcessor extends RallyArmyProcessor {

	@Autowired
	protected GVGStrongHoldService gvgStrongHoldService;

	@Override
	public ArmyInfo initArmy(Role attacker, ArmySetoutParam param) {
		SceneNode targetSceneNode = sdp.getSceneService().getSceneNode(attacker.getCurrentServerId(), param.getTargetPoint());
		if (targetSceneNode == null) {
			return null;
		}
		ArmyInfo army = armyBuildDirector.creatArmy(attacker, getArmyType(), 0, targetSceneNode, param);
		if (army == null) {
			return null;
		}
		army.setTargetNode(targetSceneNode);
		army.setHideHeaderHero(param.isHideHeaderHero());
		return army;
	}
}
