package com.lc.billion.icefire.kvkseason.biz.model.honor;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;

/**
 * <AUTHOR>
 *
 */
public class KvkHonorAlliance extends AbstractEntity {

	private static final long serialVersionUID = 4800432926998346307L;

	@MongoId
	private Long allianceId;
	/** 荣誉值 */
	private long honorValue;

	@Override
	public void setPersistKey(Long id) {
		allianceId = id;
	}

	@Override
	public Long getPersistKey() {
		return allianceId;
	}

	@Override
	public Long getGroupingId() {
		return allianceId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public long getHonorValue() {
		return honorValue;
	}

	public void addHonorValue(long honorValue) {
		this.honorValue += honorValue;
	}

}
