package com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgCupApplyInfo;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgRegisterInfo;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Repository
public class GvgRegisterInfoDao extends RootDao<GvgRegisterInfo> {

    private Map<Integer, List<GvgRegisterInfo>> dataByServerId = new MyConcurrentMap<>();

    protected GvgRegisterInfoDao() {
        super(GvgRegisterInfo.class, false);
    }

    @Override
    protected MongoCursor<GvgRegisterInfo> doFindAll(int db) {
        return dbFindAllForWorldEntity(db);
    }

    @Override
    protected void putMemoryIndexes(GvgRegisterInfo entity) {
        dataByServerId.compute(entity.getServerId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);

    }

    @Override
    protected void removeMemoryIndexes(GvgRegisterInfo entity) {
        List<GvgRegisterInfo> set = dataByServerId.get(entity.getServerId());
        if (set != null) {
            set.remove(entity);
        }
    }

    public GvgRegisterInfo create(int serverId, Long allianceId) {
        int db = Application.getServerId();
        GvgRegisterInfo entity = newEntityInstance();
        entity.setPersistKey(allianceId);
        entity.setServerId(serverId);
        return createEntity(db, entity);
    }


    public List<GvgRegisterInfo> findByServerId(int gameServerId) {
        return dataByServerId.get(gameServerId);
    }


    public Map<Integer, List<GvgRegisterInfo>> getAllDatas() {
        return dataByServerId;
    }

    public void deleteAll() {
        delete(findAll());
    }
}
