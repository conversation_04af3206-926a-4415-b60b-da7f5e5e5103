package com.lc.billion.icefire.gvgbattle.biz.service.impl;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.battle.result.FightLostInfo;
import com.lc.billion.icefire.game.biz.battle.result.FightLostType;
import com.lc.billion.icefire.game.biz.battle.result.FightResult;
import com.lc.billion.icefire.game.biz.config.SoldierConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.login.LoginServiceImpl;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.AllianceBattlePointDao;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.RoleGVGBattleDao;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.AddPointType;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.AllianceBattlePoint;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.RoleGVGBattle;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 战场内 玩家数据统计类
 *  积分
 *  杀敌数
 *  采集
 *        等等
 * <AUTHOR>
 */
@Service
public class GVGBattleScoreService {

    private static final Logger logger = LoggerFactory.getLogger(GVGBattleScoreService.class);

    @Autowired
    private GVGStrongHoldService GVGStrongHoldService;
    @Autowired
    private AllianceBattlePointDao allianceBattlePointDao;
    @Autowired
    private AllianceServiceImpl allianceService;
    @Autowired
    private GVGBattleService gvgBattleService;
    @Autowired
    private LoginServiceImpl loginService;
    @Autowired
    private RoleGVGBattleDao roleGVGBattleDao;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private RoleDao roleDao;
    @Autowired
    private RoleManager roleManager;

    /**
     * 加联盟点数:个人和联盟都加
     */
    public void addPoint(Role role, int point, AddPointType type) {
        addPoint(role, point, type, true);
    }

    public void addPoint(Role role, int point, AddPointType type, boolean isPush) {
        if (!loginService.isLoginSwitch()) {// 战斗服已经执行battleStop。这个标记是false
            return;
        }

        Alliance alliance = allianceService.getAllianceByRoleId(role.getId());
        if (alliance == null) {
            ErrorLogUtil.errorLog("addPoint alliance is null", "roleId",role.getId());
            return;
        }
        // 1:计算个人点数
        addRolePoint(role, point, type, isPush);
        // 2.计算联盟点数
        addAlliancePoint(alliance.getId(), point, type, isPush);
    }

    public void addRolePointBatch(List<Long> roleIdList, int point, AddPointType type, boolean isPush){
        for(var roleId: roleIdList) {
            var role = roleManager.getRole(roleId);
            if(role == null){
                ErrorLogUtil.errorLog("[GVG]addRolePoint role is null", "roleId", roleId, "roleIdList", roleIdList);
                continue;
            }

            addRolePoint(role, point, type, isPush);
        }
    }

    public void addRolePoint(Role role, int point, AddPointType type, boolean isPush) {
        if (role == null) {
            return;
        }
        if (point <= 0) {
            return;
        }
        RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(role.getId());
        if (roleGVGBattle == null) {
            ErrorLogUtil.errorLog("[GVG]addRolePoint roleGVGBattle is null", "roleId",role.getId());
            return;
        }

        logger.info("[GVG] 触发个人积分 {} ， 积分 {} 变化 {} 类型 {} push {}", role.getId(), roleGVGBattle.getBattlePoint(), point, type, isPush);

        // 单独积分
        if(type == AddPointType.GATHER){
            roleGVGBattle.setGatherScore(roleGVGBattle.getGatherScore() + point);
        } else if(type == AddPointType.KILL_NPC){
            roleGVGBattle.setKillNpcScore(roleGVGBattle.getKillNpcScore() + point);
        } else if(type == AddPointType.KILL_ENEMY){
            roleGVGBattle.setKillEnemyScore(roleGVGBattle.getKillEnemyScore() + point);
        } else if(type == AddPointType.WUCHAO_COMPLETE){
            roleGVGBattle.setWuchaoScore(roleGVGBattle.getWuchaoScore() + point);
        } else if(type == AddPointType.OCCUPYING){
            roleGVGBattle.setOccupyScore(roleGVGBattle.getOccupyScore() + point);
        } else if(type == AddPointType.FIRST_OCCUPY){
            roleGVGBattle.setFirstOccupyScore(roleGVGBattle.getFirstOccupyScore() + point);
        }

        roleGVGBattle.addBattlePoint(point);

        roleGVGBattleDao.save(roleGVGBattle);

        if (isPush) {
            GVGStrongHoldService.sendGVGBattleInfo(role);
        }
    }

    /**
     * 增加联盟积分
     * @param alliance
     * @param point
     * @param type
     */
    public void addAlliancePoint(Alliance alliance, int point, AddPointType type) {
        addAlliancePoint(alliance.getId(), point, type, true);
    }

    public void addAlliancePoint(Long allianceId, int point, AddPointType type, boolean isPush) {
        if (type == AddPointType.KILL_ENEMY) {
            return;
        }

        AllianceBattlePoint allianceBattlePoint = gvgBattleService.getOneAllianceBattlePoint(allianceId);

        logger.info("[GVG]addAlliancePoint, allianceId: {}，old Point:{}, addition: {} type: {}, push: {}", allianceId, allianceBattlePoint.getValue(), point, type, isPush);

        allianceBattlePoint.setValue(allianceBattlePoint.getValue() + point);

        if(type == AddPointType.GATHER){
            // 采集
            allianceBattlePoint.setGatherScore(allianceBattlePoint.getGatherScore() + point);
        } else if(type == AddPointType.KILL_NPC){
            // 杀敌
            allianceBattlePoint.setKillNpcScore(allianceBattlePoint.getKillNpcScore() + point);
        } else if(type == AddPointType.WUCHAO_COMPLETE){
            // 乌巢
            allianceBattlePoint.addWuchaoScore(point);
        } else if(type == AddPointType.OCCUPYING || type == AddPointType.FIRST_OCCUPY){
            // 建筑
            allianceBattlePoint.addBuildingScore(point);
        }

        allianceBattlePointDao.save(allianceBattlePoint);

        updateBattleEndTime(allianceBattlePoint);

        if (isPush) {
            // 同步给客户端战斗服信息
            GVGStrongHoldService.pushInformationBothSides(point, allianceId);
        }
    }


    private AllianceBattlePoint getOneAllianceBattlePoint(Long allianceId) {
        if (allianceId == 0) {
            return null;
        }
        AllianceBattlePoint allianceBattlePoint = allianceBattlePointDao.findById(allianceId);
        if (allianceBattlePoint == null) {
            allianceBattlePoint = allianceBattlePointDao.createById(allianceId);
        }
        return allianceBattlePoint;
    }


    /**
     * 战场建筑 添加杀兵积分
     */
    public void onGVGFightEnd(ArmyInfo army) {
        // 不经过战斗
        if (army.getFightContext() == null) {
            ErrorLogUtil.errorLog("[GVG]onGVGFightEnd, fightContext null:", "armyId",army.getPersistKey());
            return;
        }
        // 只给进攻者加积分
        logger.info("[GVG]onGVGFightEnd, addFightPoint: {}", army.getPersistKey());
        var fightResult = army.getFightContext().getFightResult();

        var attackedCity = army.getTargetNodeType() == SceneNodeType.CITY;
        addFightPoint(fightResult, true, attackedCity);
        // 也给防守方加积分
        addFightPoint(fightResult, false, attackedCity);
    }

    private void addFightPoint(FightResult fightResult, boolean isAttacker, boolean attackedCity) {
        SoldierConfig soldierConfig = configService.getConfig(SoldierConfig.class);
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);

        // 参与者加积分
        List<Long> sendRoleIds = new ArrayList<>();
        var totalKillScore = 0;
        var totalKillCount = 0;
        Long allianceId = 0L;// 取第一个部队的联盟ID

        var fighterIds = fightResult.getFighters(isAttacker);

        for(var fighterId: fighterIds) {
            var killAndBadlyMap = fightResult.getKillCount(isAttacker, false, fighterId);
            if (killAndBadlyMap == null) {
                continue;
            }
            var killCount = 0;
            var killPower = 0L;
            for(var entry: killAndBadlyMap.entrySet()) {
                var soldierMeta = soldierConfig.get(entry.getKey());
                killCount += entry.getValue();
                killPower += (long) soldierMeta.getPower() * entry.getValue();
            }
            var scorePerPower = attackedCity ? gvgSettingConfig.getGvgBaseKillPoint(): gvgSettingConfig.getGvgKillPoint();
            int killAddScore = (int) (killPower / scorePerPower);

            totalKillScore += killAddScore;
            totalKillCount += killCount;


            // fightId转换为int型的roleId
            var fightRoleId = -1L;
            try {
                fightRoleId = Long.parseLong(fighterId);
            } catch(NumberFormatException e) {
                ErrorLogUtil.errorLog("[GVG]addFightPoint, fightRole parse error", "fightRoleId",fighterId,
                        "killPower",killPower, "killScore",killAddScore, "killNum",killCount);
                continue;
            }

            var fightRole = roleManager.getRole(fightRoleId);
            if(fightRole == null) {
                ErrorLogUtil.errorLog("[GVG]addFightPoint, fightRole null",  "fightRoleId",fighterId,
                        "killPower",killPower, "killScore",killAddScore, "killNum",killCount);
                continue;
            }

            // 个人点数计算
            RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(fightRoleId);
            if (roleGVGBattle == null) {
                ErrorLogUtil.errorLog("[GVG]addFightPoint, roleGVGBattle null", "fightRoleId",fightRoleId,
                        "killPower",killPower, "killScore",killAddScore, "killNum",killCount);
                continue;
            }

            if(allianceId == null || allianceId <= 0L) {
                allianceId = fightRole.getAllianceId();
            } else {
                if(!allianceId.equals(fightRole.getAllianceId())){
                    ErrorLogUtil.errorLog("[GVG]addFightPoint, another allianceId", "fightRoleAllianceId",fightRole.getAllianceId(),
                            "allianceId",allianceId);
                }
            }

            // 增加个人杀兵数量
            roleGVGBattle.addKillEnemyNum(killCount);
            roleGVGBattleDao.save(roleGVGBattle);
            // 杀兵只加个人点数，不加联盟点数
            var rolePointBefore = roleGVGBattle.getBattlePoint();
            addRolePoint(fightRole, killAddScore, AddPointType.KILL_ENEMY, false);
            var rolePointAfter = roleGVGBattle.getBattlePoint();
            sendRoleIds.add(fightRoleId);

            logger.info("[GVG]addFightPoint, addRolePoint, fightRoleId: {}, killPower: {}, killScore: {}, killNum: {}, rolePointChange: {} -> {}",
                    fightRoleId, killPower, killAddScore, killCount, rolePointBefore, rolePointAfter);
        }

        // 联盟点数计算
        AllianceBattlePoint alliancePoint = gvgBattleService.getOneAllianceBattlePoint(allianceId);
        if (alliancePoint == null) {
            ErrorLogUtil.errorLog("[GVG]addFightPoint, addAlliancePoint error", "allianceId",allianceId);
            return;
        }
        // 杀兵积分（注意：杀敌不加联盟积分）
        var allianceKillPointOld = alliancePoint.getKillEnemyScore();
        var allianceKillPointNew = allianceKillPointOld + totalKillScore;
        alliancePoint.setKillEnemyScore(alliancePoint.getKillEnemyScore() + totalKillScore);
        alliancePoint.addKillEnemyCount(totalKillCount);
        allianceBattlePointDao.save(alliancePoint);

        logger.info("[GVG]addFightPoint, addAlliancePoint, allianceId: {}, totalKillScore: {}, totalKillNum: {}, allianceKillPointChange: {} -> {}",
                allianceId, totalKillScore, totalKillCount, allianceKillPointOld, allianceKillPointNew);

        for (Long roleId : sendRoleIds) {
            Role memberRole = roleManager.getRole(roleId);
            if (memberRole != null) {
                GVGStrongHoldService.sendGVGBattleInfo(memberRole);
            }
        }
    }

    private void updateBattleEndTime(AllianceBattlePoint allianceBattlePoint) {
        // 判断联盟积分是否达到上限
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        int max = gvgSettingConfig.getGvgWinPoint();
        if (allianceBattlePoint.getValue() >= max) {
            // 超出上限结束了，设置为最大上限
            allianceBattlePoint.setValue(max);
            // 速度设成0，防止tick继续走
            allianceBattlePoint.setPointSpeed(0);
            allianceBattlePoint.setEndTime(TimeUtil.getNow());  // 立刻结束
            allianceBattlePointDao.save(allianceBattlePoint);

            logger.info("[GVG]updateBattleEndTime allianceId: {}, max: {}, endTime: {}", allianceBattlePoint.getPersistKey(), max, allianceBattlePoint.getEndTime());
        } else {
            // 没结束，重算结束时间
            // 剩余值
            var additionPoint = allianceBattlePoint.additionPoint(TimeUtil.getNow());
            long remainValue = max - (allianceBattlePoint.getValue() + (int)additionPoint);
            // 当前速度
            double pointSpeed = allianceBattlePoint.getPointSpeed();
            double remainTime = remainValue * TimeUtil.SECONDS_MILLIS / pointSpeed;
            // 多加一秒防止数值不对重算
            allianceBattlePoint.setEndTime(TimeUtil.getNow() + (long) remainTime + 1);
            allianceBattlePointDao.save(allianceBattlePoint);

            logger.info("[GVG]updateBattleEndTime allianceId: {}, oldValue: {}, additionPoint: {}, remainValue: {}, pointSpeed: {}, remainTime: {}, endTime: {}",
                    allianceBattlePoint.getPersistKey(), allianceBattlePoint.getValue(), additionPoint, remainValue, pointSpeed, remainTime, allianceBattlePoint.getEndTime());
        }
    }


    /**
     * 攻击/集结玩家城市--添加杀敌数
     *  资源点争夺
     */
    public void addAttackPlayerKillEnemyCount(ArmyInfo armyInfo) {
        var fightResult = armyInfo.getFightContext().getFightResult();
        if (fightResult.getAttackerLostInfo() != null) {
            addAttackPlayerKillEnemyCount(fightResult.getAttackerLostInfo());
        }

        if (fightResult.getDefenderLostInfo() != null) {
            addAttackPlayerKillEnemyCount(fightResult.getDefenderLostInfo());
        }
    }

    private void addAttackPlayerKillEnemyCount(FightLostInfo lostInfo) {
        var lostIds = lostInfo.getLostMap().rowKeySet();
        if (!JavaUtils.bool(lostIds)) {
            return;
        }

        for (var lostId : lostIds) {
            int killCount = 0;
            Map<String, Integer> kv = lostInfo.getValueDetail(lostId, FightLostType.KILL);
            for (Map.Entry<String, Integer> entry : kv.entrySet()) {
                killCount += entry.getValue();
            }

            if (killCount <= 0) {
                continue;
            }

            var fightRoleId = Long.parseLong(lostId);
            Role fightRole = roleDao.findById(fightRoleId);
            if (fightRole == null) {
                continue;
            }

            // 联盟点数
            AllianceBattlePoint allianceBattlePoint = gvgBattleService.getOneAllianceBattlePoint(fightRole.getAllianceId());
            // 杀兵数量
            allianceBattlePoint.addKillEnemyCount(killCount);
            allianceBattlePointDao.save(allianceBattlePoint);
        }
    }

    /**
     * 占领官渡的时间
     * @param oldAllianceId
     * @param addOccupiedTime
     */
    public void addAllianceOccupyGuanduTime(long oldAllianceId, long addOccupiedTime) {
        if (oldAllianceId <= 0) {
            return;
        }
        if (addOccupiedTime <= 0) {
            return;
        }
        
        // 联盟点数
        AllianceBattlePoint allianceBattlePoint = getOneAllianceBattlePoint(oldAllianceId);
        // 杀兵数量
        allianceBattlePoint.addGuanduOccupyTime(addOccupiedTime);
        allianceBattlePointDao.save(allianceBattlePoint);
    }
}
