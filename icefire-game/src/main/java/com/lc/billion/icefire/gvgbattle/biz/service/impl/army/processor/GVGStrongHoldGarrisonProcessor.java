package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.processor;

import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import com.lc.billion.icefire.game.biz.service.impl.notice.NoticeConstants;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgBuildingConfig;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.StrongHoldNode;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import com.lc.billion.icefire.protocol.structure.PsArmyProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 
 * 驻防GVG战场建筑
 * 
 * <AUTHOR>
 * @date 2020/12/24
 */
@Service
public class GVGStrongHoldGarrisonProcessor extends AbstractGvgArmyProcessor {
	@Autowired
	private GVGStrongHoldService gvgStrongHoldService;

	@Override
	public ArmyType getArmyType() {
		return ArmyType.GARRISON_STRONGHOLD;
	}

	@Override
	protected boolean check(Role role, SceneNode targetNode, ArmySetoutParam param) {
		logger.info("[GVG]check, role:{}, targetNode:{}, armyType:{}", role.getId(), targetNode.getPersistKey(), param.getArmyType());
		return gvgStrongHoldService.setOutToStrongHoldCheck(role, targetNode, param);
	}

	@Override
	protected void start(ArmyInfo army) {
		logger.info("[GVG]start, army:{}", army.getPersistKey());
		gvgStrongHoldService.setOutToStrongHoldStart(army);
	}

    @Override
    protected void onRecallArmyDone(ArmyInfo army, ArmyWorkType oldWorkType){
        logger.info("[GVG]onRecallArmyDone, army: {}", army.getPersistKey());
        gvgStrongHoldService.onArmyLeftStrongHold(army, oldWorkType);
    }

    public void onDeletedArmy(ArmyInfo army) {
        logger.info("[GVG]onDeletedArmy, army: {}", army.getPersistKey());
        if(army.getWorkType() == ArmyWorkType.DEFENDING) {
            gvgStrongHoldService.onArmyLeftStrongHold(army, army.getWorkType());
        }
    }

	@Override
	protected PsArmyProgress toArmyProgressInfo(ArmyInfo army, PsArmyProgress info) {
		if (army.getWorkType() == ArmyWorkType.DEFENDING) {
			info.setStartX(army.getStartX());
			info.setStartY(army.getStartY());
			info.setEndX(army.getEndX());
			info.setEndY(army.getEndY());
			SceneNode armyTargetNode = armyManager.getArmyTargetNode(army);
			if (armyTargetNode != null && armyTargetNode.getNodeType() == SceneNodeType.STRONGHOLD) {
				var buildingMetaByType = configService.getConfig(GvgBuildingConfig.class).getBuildingMetaByType(((StrongHoldNode) armyTargetNode).getBuildingType().getId());
				if (buildingMetaByType != null && buildingMetaByType.getBuildName() != null)
					info.setTargetName(NoticeConstants.getKey(buildingMetaByType.getBuildName()));
			}
		}
		return info;
	}

	@Override
	public boolean recallFreeCheck(ArmyInfo army) {
		return army.getWorkType() == ArmyWorkType.DEFENDING;
	}
}
