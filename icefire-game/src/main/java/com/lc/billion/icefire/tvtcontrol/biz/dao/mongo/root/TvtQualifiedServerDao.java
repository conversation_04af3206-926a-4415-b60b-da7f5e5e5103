package com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtQualifiedServer;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

/**
 * @author: maoqq
 * @Date: 2022/04/11 2:49 PM
 */
@Repository
public class TvtQualifiedServerDao extends RootDao<TvtQualifiedServer> {
    protected TvtQualifiedServerDao() {
        super(TvtQualifiedServer.class, false);
    }

    @Override
    protected void putMemoryIndexes(TvtQualifiedServer entity) {

    }

    @Override
    protected void removeMemoryIndexes(TvtQualifiedServer entity) {

    }

    @Override
    protected MongoCursor<TvtQualifiedServer> doFindAll(int db) {
        return dbFindAllForWorldEntity(db);
    }

    public TvtQualifiedServer create(int serverId) {
        int db = Application.getServerId();
        TvtQualifiedServer entity = newEntityInstance();
        entity.setPersistKey(Long.valueOf(serverId));
        return createEntity(db, entity);
    }
}
