package com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtPlayerSimpleInfo;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtQualifiedServer;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

/**
 * @author: maoqq
 * @Date: 2022/04/11 2:49 PM
 */
@Repository
public class TvtPlayerSimpleInfoDao extends RootDao<TvtPlayerSimpleInfo> {
    protected TvtPlayerSimpleInfoDao() {
        super(TvtPlayerSimpleInfo.class, false);
    }

    @Override
    protected void putMemoryIndexes(TvtPlayerSimpleInfo entity) {

    }

    @Override
    protected void removeMemoryIndexes(TvtPlayerSimpleInfo entity) {

    }

    @Override
    protected MongoCursor<TvtPlayerSimpleInfo> doFindAll(int db) {
        return dbFindAllForWorldEntity(db);
    }

    public TvtPlayerSimpleInfo create(Long roleId, String name, String avatar, int serverId, Long fightPower, int townHallLevel, int gender) {
        int db = Application.getServerId();
        TvtPlayerSimpleInfo entity = newEntityInstance();
        entity.setAvatar(avatar);
        entity.setFightPower(fightPower);
        entity.setRoleId(roleId);
        entity.setName(name);
        entity.setServerId(serverId);
        entity.setTownHallLevel(townHallLevel);
        entity.setGender(gender);
        return createEntity(db, entity);
    }

    public void deleteAll() {
        delete(findAll());
    }
}
