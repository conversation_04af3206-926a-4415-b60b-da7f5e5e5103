package com.lc.billion.icefire.tvtcontrol.biz.model;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.rpc.vo.gvg.AllianceBaseDataVo;
import com.lc.billion.icefire.rpc.vo.tvt.TvtPlayerSimpleInfoVo;
import org.jongo.marshall.jackson.oid.MongoId;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2022/04/14
 */
public class TvtPlayerSimpleInfo extends AbstractEntity {

	private static final long serialVersionUID = 4763433721200170561L;

	@MongoId
	private Long roleId;
	private String name;
	private String avatar;
	private int serverId;
	private Long fightPower;
	private int townHallLevel;
	private int gender;

	public void copyAllianceBaseData(TvtPlayerSimpleInfoVo vo) {
		BeanUtils.copyProperties(vo, this);
	}

	@Override
	public void setPersistKey(Long id) {
		roleId = id;
	}

	@Override
	public Long getPersistKey() {
		return roleId;
	}

	@Override
	public Long getGroupingId() {
		return roleId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public Long getRoleId() {
		return roleId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAvatar() {
		return avatar;
	}

	public void setAvatar(String avatar) {
		this.avatar = avatar;
	}

	public int getServerId() {
		return serverId;
	}

	public void setServerId(int serverId) {
		this.serverId = serverId;
	}

	public Long getFightPower() {
		return fightPower;
	}

	public void setFightPower(Long fightPower) {
		this.fightPower = fightPower;
	}

	public int getTownHallLevel() {
		return townHallLevel;
	}

	public void setTownHallLevel(int townHallLevel) {
		this.townHallLevel = townHallLevel;
	}

	public int getGender() {
		return gender;
	}

	public void setGender(int gender) {
		this.gender = gender;
	}

	@Override
	public String toString() {
		return "TvtPlayerSimpleInfo{" +
				"roleId=" + roleId +
				", name='" + name + '\'' +
				", avatar='" + avatar + '\'' +
				", serverId=" + serverId +
				", fightPower=" + fightPower +
				", townHallLevel=" + townHallLevel +
				", gender=" + gender +
				'}';
	}
}
