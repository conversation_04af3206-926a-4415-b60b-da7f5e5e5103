package com.lc.billion.icefire.kvkseason.biz.model.legion;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;

/**
 * <AUTHOR>
 *
 */
public class LegionMember extends AbstractEntity {

	private static final long serialVersionUID = -8185882398386883803L;

	@MongoId
	private Long allianceId;
	/** 军团id */
	private Long legionId;
	/** 退出倒计时 */
	private long exitCDTime;
	/** 转让倒计时 */
	private long transferCDTime;
	private long kickCDTime;

	@Override
	public void setPersistKey(Long id) {
		allianceId = id;
	}

	@Override
	public Long getPersistKey() {
		return allianceId;
	}

	@Override
	public Long getGroupingId() {
		return allianceId;
	}

	public Long getLegionId() {
		return legionId;
	}

	public void setLegionId(Long legionId) {
		this.legionId = legionId;
	}

	public long getExitCDTime() {
		return exitCDTime;
	}

	public void setExitCDTime(long exitCDTime) {
		this.exitCDTime = exitCDTime;
	}

	public long getTransferCDTime() {
		return transferCDTime;
	}

	public void setTransferCDTime(long transferCDTime) {
		this.transferCDTime = transferCDTime;
	}

	public long getKickCDTime() {
		return kickCDTime;
	}

	public void setKickCDTime(long kickCDTime) {
		this.kickCDTime = kickCDTime;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

}
