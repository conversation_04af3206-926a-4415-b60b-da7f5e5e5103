package com.lc.billion.icefire.kvkseason.biz.dao.mongo.root;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionRequest;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 *
 */
@Repository
public class LegionRequestDao extends RootDao<LegionRequest> {

	/**
	 * 联盟请求-军团id
	 */
	private Map<Long, Map<Integer, List<LegionRequest>>> dataByLegionId = new MyConcurrentMap<>();
	/**
	 * 联盟请求-联盟id
	 */
	private Map<Long, Map<Integer, List<LegionRequest>>> dataByAllianceId = new MyConcurrentMap<>();

	public LegionRequestDao() {
		super(LegionRequest.class, true);
	}

	@Override
	protected MongoCursor<LegionRequest> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(LegionRequest entity) {
		dataByLegionId.compute(entity.getLegionId(), (k, v) -> v == null ? new MyConcurrentMap<>() : v)
				.compute(entity.getType(), (k, v) -> v == null ? new CopyOnWriteArrayList<>() : v).add(entity);
		dataByAllianceId.compute(entity.getAllianceId(), (k, v) -> v == null ? new MyConcurrentMap<>() : v)
				.compute(entity.getType(), (k, v) -> v == null ? new CopyOnWriteArrayList<>() : v).add(entity);
	}

	@Override
	protected void removeMemoryIndexes(LegionRequest entity) {
		Map<Integer, List<LegionRequest>> map = dataByLegionId.get(entity.getLegionId());
		if (map != null) {
			List<LegionRequest> list = map.get(entity.getType());
			if (list != null) {
				list.remove(entity);
			}
		}
		map = dataByAllianceId.get(entity.getAllianceId());
		if (map != null) {
			List<LegionRequest> list = map.get(entity.getType());
			if (list != null) {
				list.remove(entity);
			}
		}
	}

	public Map<Integer, List<LegionRequest>> findByLegionId(Long legionId) {
		return dataByLegionId.get(legionId);
	}

	public Map<Integer, List<LegionRequest>> findByAllianceId(Long allianceId) {
		if (!JavaUtils.bool(allianceId)) {
			return Collections.emptyMap();
		}
		return dataByAllianceId.get(allianceId);
	}

	public LegionRequest create(int db, Long legionId, Long allianceId, int type) {
		LegionRequest legionRequest = newEntityInstance();
		legionRequest.setAllianceId(allianceId);
		legionRequest.setLegionId(legionId);
		legionRequest.setType(type);
		return createEntity(db, legionRequest);
	}

	public LegionRequest findByLegionIdAndAllianceIdAndType(Long legionId, Long allianceId, int type) {
		Map<Integer, List<LegionRequest>> map = findByAllianceId(allianceId);
		if (map != null) {
			List<LegionRequest> list = map.get(type);
			if (list != null) {
				for (LegionRequest legionRequest : list) {
					if (legionRequest.getLegionId().longValue() == legionId.longValue()) {
						return legionRequest;
					}
				}
			}
		}
		return null;
	}

}
