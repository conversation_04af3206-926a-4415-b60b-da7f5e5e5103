package com.lc.billion.icefire.kvkseason.biz.dao.mongo.root;

import java.util.Collection;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.LegionSeasonTaskProgress;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 * @Date 2022/3/16
 */
@Repository
public class LegionSeasonTaskProgressDao extends RootDao<LegionSeasonTaskProgress> {

	protected LegionSeasonTaskProgressDao() {
		super(LegionSeasonTaskProgress.class, false);
	}

	@Override
	protected void putMemoryIndexes(LegionSeasonTaskProgress entity) {

	}

	@Override
	protected void removeMemoryIndexes(LegionSeasonTaskProgress entity) {

	}

	@Override
	protected MongoCursor<LegionSeasonTaskProgress> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	public LegionSeasonTaskProgress create(Long legionId) {
		LegionSeasonTaskProgress progress = newEntityInstance();
		progress.setPersistKey(legionId);
		return createEntity(Application.getServerId(), progress);
	}

	public LegionSeasonTaskProgress getByLegionId(Long legionId) {
		LegionSeasonTaskProgress progress = findById(legionId);
		if (progress == null) {
			return create(legionId);
		}
		return progress;
	}

	public void clearPreSeasonData() {
		Collection<LegionSeasonTaskProgress> all = findAll();
		if (JavaUtils.bool(all)) {
			for (LegionSeasonTaskProgress record : all) {
				delete(record);
			}
		}
	}
}
