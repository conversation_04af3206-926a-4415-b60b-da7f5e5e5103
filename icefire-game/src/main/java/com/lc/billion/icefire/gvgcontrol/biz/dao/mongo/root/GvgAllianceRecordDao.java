package com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgAllianceRecord;

/**
 * GVG联盟记录Dao
 * 
 * <AUTHOR>
 * @date 2020/12/08
 */
@Repository
public class GvgAllianceRecordDao extends RootDao<GvgAllianceRecord> {

	private Map<Integer, List<GvgAllianceRecord>> dataByServerId = new MyConcurrentMap<>();

	protected GvgAllianceRecordDao() {
		super(GvgAllianceRecord.class, false);
	}

	@Override
	protected MongoCursor<GvgAllianceRecord> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(GvgAllianceRecord entity) {
		dataByServerId.compute(entity.getServerId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
	}

	@Override
	protected void removeMemoryIndexes(GvgAllianceRecord entity) {
		List<GvgAllianceRecord> list = dataByServerId.get(entity.getServerId());
		if (list != null) {
			list.remove(entity);
		}
	}

	public GvgAllianceRecord create(Long allianceId, int serverId) {
		int db = Application.getServerId();
		GvgAllianceRecord record = new GvgAllianceRecord();
		record.setPersistKey(allianceId);
		record.setServerId(serverId);
		return createEntity(db, record);
	}

	public List<GvgAllianceRecord> findByServerId(int serverId) {
		return dataByServerId.get(serverId);
	}
}
