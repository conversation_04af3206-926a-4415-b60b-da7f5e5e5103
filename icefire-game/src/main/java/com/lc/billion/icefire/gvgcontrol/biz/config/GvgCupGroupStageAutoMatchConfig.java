package com.lc.billion.icefire.gvgcontrol.biz.config;

import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;

import java.util.*;

/**
 * GvgCup小组赛自动生成对阵信息
 *
 * <AUTHOR>
 * @created 2021-11-09
 */
@Config(name = "GvgCupGroupStageAutoMatch", metaClass = GvgCupGroupStageAutoMatchConfig.GvgCupGroupStageAutoMatchMeta.class)
public class GvgCupGroupStageAutoMatchConfig {

    @MetaMap
    private Map<String, GvgCupGroupStageAutoMatchMeta> idMap;

    private Map<Integer, Set<GvgCupGroupStageAutoMatchMeta>> warZoneMapper;

    public Map<Integer, Set<GvgCupGroupStageAutoMatchMeta>> getWarZoneMapper() {
        return Collections.unmodifiableMap(warZoneMapper);
    }

    public void init(List<GvgCupGroupStageAutoMatchMeta> list) {
        warZoneMapper = new HashMap<>();
        list.forEach(m -> warZoneMapper.compute(m.getWarZoneId(), (k, v) -> v == null ? new HashSet<>() : v).add(m));
    }

    public static class GvgCupGroupStageAutoMatchMeta extends AbstractMeta {
        private int warZoneId;
        private int red;
        private int blue;
        private int gameRound;
        private int round;
        private GvgMatchType week;

        public int getWarZoneId() {
            return warZoneId;
        }

        public void setWarZoneId(int warZoneId) {
            this.warZoneId = warZoneId;
        }

        public int getRed() {
            return red;
        }

        public void setRed(int red) {
            this.red = red;
        }

        public int getBlue() {
            return blue;
        }

        public void setBlue(int blue) {
            this.blue = blue;
        }

        public int getGameRound() {
            return gameRound;
        }

        public void setGameRound(int gameRound) {
            this.gameRound = gameRound;
        }

        public int getRound() {
            return round;
        }

        public void setRound(int round) {
            this.round = round;
        }

        public GvgMatchType getWeek() {
            return week;
        }

        public void setWeek(GvgMatchType week) {
            this.week = week;
        }
    }
}
