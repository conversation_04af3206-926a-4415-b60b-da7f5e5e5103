package com.lc.billion.icefire.tvtcontrol.biz.model;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import org.jongo.marshall.jackson.oid.MongoId;

/**
 * @author: maoqq
 * @Date: 2022/04/11 2:46 PM
 */
public class TvtQualifiedServer extends AbstractEntity {
    private static final long serialVersionUID = 5178875592017664926L;
    @MongoId
    private Long serverId;

    @Override
    public void setPersistKey(Long id) {
        serverId = id;
    }

    public int getServerId() {
        return Long.valueOf(serverId).intValue();
    }

    @Override
    public Long getPersistKey() {
        return serverId;
    }

    @Override
    public Long getGroupingId() {
        return serverId;
    }

    @Override
    public int hashCodeImpl() {
        return hashCodeForPersistKey();
    }

    @Override
    public boolean equalsImpl(Object obj) {
        return equalsForPersistKey(obj);
    }
}
