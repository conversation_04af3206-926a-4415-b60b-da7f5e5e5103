package com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtBattleServerDispatchRecord;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtPlayerSimpleInfo;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * @author: maoqq
 * @Date: 2022/04/06 5:32 PM
 */
@Repository
public class TvtBattleServerDispatchRecordDao extends RootDao<TvtBattleServerDispatchRecord> {
    private Map<Integer, Set<TvtBattleServerDispatchRecord>> datasByServerId = new MyConcurrentMap<>();

    private Map<Long, List<TvtBattleServerDispatchRecord>> datasByTime = new MyConcurrentMap<>();

    private Map<Long, TvtBattleServerDispatchRecord> datasByBattleServerId = new MyConcurrentMap<>();

    protected TvtBattleServerDispatchRecordDao() {
        super(TvtBattleServerDispatchRecord.class, false);
    }

    @Override
    protected MongoCursor<TvtBattleServerDispatchRecord> doFindAll(int db) {
        return dbFindAllForWorldEntity(db);
    }

    @Override
    protected void putMemoryIndexes(TvtBattleServerDispatchRecord entity) {
        datasByTime.compute(entity.getBattleStartTime(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
        datasByBattleServerId.put(entity.getBattleServerId(), entity);

        List<TvtPlayerSimpleInfo> redLineup = entity.getRedLineup();
        if (redLineup != null) {
            redLineup.forEach(p -> datasByServerId.compute(p.getServerId(), (k, v) -> v == null ? new HashSet<>() : v).add(entity));
        }
        List<TvtPlayerSimpleInfo> blueLineup = entity.getBlueLineup();
        if (blueLineup != null) {
            blueLineup.forEach(p -> datasByServerId.compute(p.getServerId(), (k, v) -> v == null ? new HashSet<>() : v).add(entity));
        }
    }

    @Override
    protected void removeMemoryIndexes(TvtBattleServerDispatchRecord entity) {
        List<TvtBattleServerDispatchRecord> list = datasByTime.get(entity.getBattleStartTime());
        if (list != null) {
            list.remove(entity);
        }
        datasByBattleServerId.remove(entity.getBattleServerId());
        List<TvtPlayerSimpleInfo> redLineup = entity.getRedLineup();
        if (redLineup != null) {
            redLineup.forEach(p -> {
                if (datasByServerId.containsKey(p.getServerId())) {
                    datasByServerId.get(p.getServerId()).remove(entity);
                }
            });
        }

        List<TvtPlayerSimpleInfo> blueLineup = entity.getBlueLineup();
        if (blueLineup != null) {
            blueLineup.forEach(p -> {
                if (datasByServerId.containsKey(p.getServerId())) {
                    datasByServerId.get(p.getServerId()).remove(entity);
                }
            });
        }

    }

    public TvtBattleServerDispatchRecord create(Long battleServerId, long battleStartTime, long battleDestroyTime, int status, List<TvtPlayerSimpleInfo> redLineup, List<TvtPlayerSimpleInfo> blueLineup) {
        TvtBattleServerDispatchRecord record = newEntityInstance();
        record.setBattleServerId(battleServerId);
        record.setBattleStartTime(battleStartTime);
        record.setBattleTurn(0);
        record.setStatus(status);
        record.setBattleDestroyTime(battleDestroyTime);
        record.setRedLineup(redLineup);
        record.setBlueLineup(blueLineup);
        return createEntity(Application.getServerId(), record);
    }

    public Set<TvtBattleServerDispatchRecord> findByServerId(int gameServerId) {
        return datasByServerId.get(gameServerId);
    }

    public Map<Integer, Set<TvtBattleServerDispatchRecord>> findByServerId() {
        return datasByServerId;
    }

    public List<TvtBattleServerDispatchRecord> findByTime(long time) {
        return datasByTime.get(time);
    }

    public Set<Long> findBattleTimes() {
        return datasByTime.keySet();
    }

    public TvtBattleServerDispatchRecord findByBattleServerId(String battleServerId) {
        return datasByBattleServerId.get(Long.valueOf(battleServerId));
    }
}
