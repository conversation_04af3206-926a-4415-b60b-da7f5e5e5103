package com.lc.billion.icefire.tvtcontrol.biz.model;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import org.jongo.marshall.jackson.oid.MongoId;

import java.util.List;
import java.util.Objects;

/**
 * @author: maoqq
 * @Date: 2022/04/06 5:30 PM
 */
public class TvtBattleServerDispatchRecord extends AbstractEntity {
    private static final long serialVersionUID = -3718895375269959563L;

    /**
     * 分配
     */
    public static final int STATUS_MATCH = 0;
    /**
     * 通知
     */
    public static final int STATUS_NOTICE = 1;
    /**
     * 启动
     */
    public static final int STATUS_START = 2;
    /**
     * 结束
     */
    public static final int STATUS_END = 3;
    /**
     * 查询状态
     */
    public static final int STATUS_RENOTICE = 4;

    @MongoId
    private Long battleServerId;// 战斗服id
    private long battleStartTime;// 战场开启时间
    // 0-分配，1-通知，2-启动，3-结束，4-再次通知
    private int status;
    private int battleTurn;
    // 战场销毁时间，战场结束后半小时
    private long battleDestroyTime;
    // 阵容
    private List<TvtPlayerSimpleInfo> redLineup;
    private List<TvtPlayerSimpleInfo> blueLineup;


    @Override
    public String toString() {
        return "TvtBattleServerDispatchRecord{" +
                "battleServerId=" + battleServerId +
                ", battleStartTime=" + battleStartTime +
                ", status=" + status +
                ", battleTurn=" + battleTurn +
                ", battleDestroyTime=" + battleDestroyTime +
                ", redLineup=" + redLineup +
                ", blueLineup=" + blueLineup +
                '}';
    }

    @Override
    public void setPersistKey(Long id) {
        this.battleServerId = id;
    }

    @Override
    public Long getPersistKey() {
        return this.battleServerId;
    }

    @Override
    public Long getGroupingId() {
        return this.battleServerId;
    }

    @Override
    public int hashCodeImpl() {
        return hashCodeForPersistKey();
    }

    @Override
    public boolean equalsImpl(Object obj) {
        return equalsForPersistKey(obj);
    }

    public Long getBattleServerId() {
        return battleServerId;
    }

    public void setBattleServerId(Long battleServerId) {
        this.battleServerId = battleServerId;
    }

    public long getBattleStartTime() {
        return battleStartTime;
    }

    public void setBattleStartTime(long battleStartTime) {
        this.battleStartTime = battleStartTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getBattleTurn() {
        return battleTurn;
    }

    public void setBattleTurn(int battleTurn) {
        this.battleTurn = battleTurn;
    }

    public long getBattleDestroyTime() {
        return battleDestroyTime;
    }

    public void setBattleDestroyTime(long battleDestroyTime) {
        this.battleDestroyTime = battleDestroyTime;
    }

    public List<TvtPlayerSimpleInfo> getRedLineup() {
        return redLineup;
    }

    public void setRedLineup(List<TvtPlayerSimpleInfo> redLineup) {
        this.redLineup = redLineup;
    }

    public List<TvtPlayerSimpleInfo> getBlueLineup() {
        return blueLineup;
    }

    public void setBlueLineup(List<TvtPlayerSimpleInfo> blueLineup) {
        this.blueLineup = blueLineup;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        TvtBattleServerDispatchRecord that = (TvtBattleServerDispatchRecord) o;
        return battleStartTime == that.battleStartTime && status == that.status && battleTurn == that.battleTurn && battleDestroyTime == that.battleDestroyTime && battleServerId.equals(that.battleServerId) && Objects.equals(redLineup, that.redLineup) && Objects.equals(blueLineup, that.blueLineup);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), battleServerId, battleStartTime, status, battleTurn, battleDestroyTime, redLineup, blueLineup);
    }
}
