package com.lc.billion.icefire.kvkseason.biz.tick;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.async.email.SendSystemEmailOperation;
import com.lc.billion.icefire.game.biz.async.kvkHonor.ReissueWeekRewardOperation;
import com.lc.billion.icefire.game.biz.common.AbstractTicker;
import com.lc.billion.icefire.game.biz.config.MailConfig;
import com.lc.billion.icefire.game.biz.config.kvk.KVKSettingConfig;
import com.lc.billion.icefire.game.biz.config.kvk.KvkGroupConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ServerInfoDao;
import com.lc.billion.icefire.game.biz.model.ServerInfo;
import com.lc.billion.icefire.game.biz.model.email.AbstractEmail;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.email.MailCreator;
import com.lc.billion.icefire.game.biz.service.impl.email.MailSender;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankType;
import com.lc.billion.icefire.kvkseason.biz.async.KvkAllianceSeasonRewardOperation;
import com.lc.billion.icefire.kvkseason.biz.async.KvkServerSeasonRewardOperation;
import com.lc.billion.icefire.kvkseason.biz.service.impl.honor.KvkHonorService;
import com.lc.billion.icefire.kvkseason.biz.service.impl.kvk.KvkSeasonServiceImpl;
import com.lc.billion.icefire.kvkseason.biz.service.impl.kvk.StageTime;
import com.simfun.sgf.utils.TimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 赛季 tick
 */
@Service
public class KvkSeasonTicker extends AbstractTicker<ServerInfo> {
    @Autowired
    private ServerInfoDao serverInfoDao;
    @Autowired
    private KvkSeasonServiceImpl kvkSeasonService;
    @Autowired
    private AsyncOperationServiceImpl asyncOperationService;
    @Autowired
    private KvkHonorService kvkHonorService;
    @Autowired
    private ServiceDependency srvdp;
    @Autowired
    private ConfigServiceImpl configService;

    public KvkSeasonTicker() {
        super(10 * TimeUtils.SECONDS_MILLIS);
    }

    public void reissueWeekReward(ServerInfo serverInfo, long now) {
        int season = Application.getSeason();
        int thisWeekNum = srvdp.getServerInfoService().getServerWeek();
        if(thisWeekNum <= 2) { // 第一周不配奖励，用于各服对齐赛季，奖励从第2周开始配，补发从第三周开始
            return;
        }
        int lastWeekNum = thisWeekNum - 1;
        if(serverInfo.hasReissuedWeek(season, lastWeekNum)) { // 已经补发过了
            return;
        }
        // 执行补发，先标记上，省的多份
        serverInfo.markReissuedWeek(season, lastWeekNum, now);
        serverInfoDao.save(serverInfo);
        // 再异步执行补发，不阻塞tick
        asyncOperationService.execute(new ReissueWeekRewardOperation(season, lastWeekNum));
    }

    @Override
    protected void tick(ServerInfo serverInfo, long now) {
        int season = Application.getSeason();
        StageTime stageTime = kvkSeasonService.getGameKvkStageTime(Application.getServerId());
        if (stageTime == null) {
            return;
        }
        var nowStage = stageTime.getStage(now);
        if (serverInfo.getCurStage() != nowStage) {
            logger.info("GAME stage season={} change== serverId={} oldStage={} nowStage={}", serverInfo.getCurrentServerId(), serverInfo.getSeason(), serverInfo.getCurStage(), nowStage);
            serverInfo.setCurStage(nowStage);
            serverInfoDao.save(serverInfo);
            kvkSeasonService.broadcastKvkStageChange();
        }
        reissueWeekReward(serverInfo, now);
        switch (nowStage) {
            case SETTLEMENT:
                if (serverInfo.getSeasonRewardTimeMap().containsKey(season)) {
                    return;
                }
                kvkHonorService.setSendSeasonHonorReward(true);
                logger.info("赛季结算 | ticker判断已经可以结算了 serverId={} season={}", serverInfo.getId(), season);
                serverInfo.getSeasonRewardTimeMap().put(season, now);
                for (var id : Application.getAllServerIds()) {
                    // 把所有服的结算信息都更新一下
                    var data = serverInfoDao.findById(Long.valueOf(id));
                    if (data != null && !data.getSeasonRewardTimeMap().containsKey(season)) {
                        data.getSeasonRewardTimeMap().put(season, now);
                        serverInfoDao.save(data);
                        logger.info("赛季结算 服务器={} 更新赛季结算时间戳season={}", data.getId(), season);
                    }
                }
                serverInfoDao.save(serverInfo);
                if (season == 1) {
                    asyncOperationService.execute(new KvkAllianceSeasonRewardOperation(kvkSeasonService, RankType.ALLIANCE_PROSPERITY));
                } else {
                    asyncOperationService.execute(new KvkAllianceSeasonRewardOperation(kvkSeasonService, RankType.ALLIANCE_PROSPERITY_KVK));
                    asyncOperationService.execute(new KvkServerSeasonRewardOperation());
                }
                break;
            case MATCHING:
                if (serverInfo.getHonorRewardTimeMap().containsKey(season)) {
                    return;
                }

                logger.info("开始补发个人荣誉奖励邮件....");
                serverInfo.getHonorRewardTimeMap().put(season, now);
                for (var id : Application.getAllServerIds()) {
                    // 把所有服的结算信息都更新一下
                    var data = serverInfoDao.findById(Long.valueOf(id));
                    if (data != null && !data.getSeasonRewardTimeMap().containsKey(season)) {
                        data.getHonorRewardTimeMap().put(season, now);
                        serverInfoDao.save(data);
                        logger.info("赛季结算 服务器={} 开始补发个人荣誉奖励时间戳season={}", data.getId(), season);
                    }
                }
                serverInfo.getHonorRewardTimeMap().put(season, now);
                serverInfoDao.save(serverInfo);
                kvkHonorService.sendHonorRewardMail(season);
                logger.info("开始发送赛季预告邮件");
                for (var serverId : Application.getAllServerIds()) {
                    sendNextSeasonNotifyMail(serverId);
                    logger.info("开始发送赛季预告邮件{} 赛季={}", serverId, season);
                }
                logger.info("发送赛季预告邮件OK");
                break;
            case MATCHED:
                break;
        }
    }

    @Override
    protected Collection<ServerInfo> findAll() {
        ServerInfo serverInfo = serverInfoDao.findById((long) Application.getServerId());
        return List.of(serverInfo);
    }

    public void sendNextSeasonNotifyMail(int serverId) {
        int nextSeason = Application.getSeason() + 1;
        KvkGroupConfig kvkGroupConfig = configService.getConfig(KvkGroupConfig.class);
        var kvkSetting = configService.getConfig(KVKSettingConfig.class);
        var group = kvkGroupConfig.getKvkGroupBySeasonAndOServerId(nextSeason, serverId);
        if (group == null) {
            logger.warn("赛季分组不存在不发送 season={} serverId={}", nextSeason, serverId);
            return;
        }
        String mailId = kvkSetting.getNotifyMail(nextSeason);
        var meta = configService.getConfig(MailConfig.class).getById(mailId);
        if (meta == null) {
            logger.warn("sendNextSeasonNotifyMail 赛季:{} 的邮件找不到meta, metaId={}", nextSeason, mailId);
            return;
        }
        List<String> params = List.of(String.valueOf(nextSeason), group.getOpenTime());
        asyncOperationService.execute(new SendSystemEmailOperation(srvdp.getRoleDao(), srvdp.getMailService(), mailId, null,
                params, 0, false, null, null, false, serverId));
    }
}
