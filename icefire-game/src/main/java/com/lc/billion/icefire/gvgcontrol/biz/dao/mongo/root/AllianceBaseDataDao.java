package com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.AllianceBaseData;
import com.lc.billion.icefire.rpc.vo.gvg.AllianceBaseDataVo;

/**
 * <AUTHOR>
 *
 */
@Repository
public class AllianceBaseDataDao extends RootDao<AllianceBaseData> {

	// private Map<Integer, List<AllianceBaseData>> dataByServerId = new
	// MyConcurrentMap<>();

	public AllianceBaseDataDao() {
		super(AllianceBaseData.class, false);
	}

	@Override
	protected MongoCursor<AllianceBaseData> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(AllianceBaseData entity) {
		// dataByServerId.compute(entity.getServerId(), (k, v) -> v == null ? new
		// ArrayList<>() : v).add(entity);
	}

	@Override
	protected void removeMemoryIndexes(AllianceBaseData entity) {
		// List<AllianceBaseData> list = dataByServerId.get(entity.getServerId());
		// if (list != null) {
		// list.remove(entity);
		// }
	}

	public AllianceBaseData create(AllianceBaseDataVo allianceBaseDataVo) {
		int db = Application.getServerId();
		AllianceBaseData allianceBaseData = newEntityInstance();
		allianceBaseData.copyAllianceBaseData(allianceBaseDataVo);
		return createEntity(db, allianceBaseData);
	}

	// public List<AllianceBaseData> findByServerId(int serverId) {
	// return dataByServerId.get(serverId);
	// }
	//
	// public Map<Integer, List<AllianceBaseData>> findAllByServerId() {
	// return dataByServerId;
	// }

}
