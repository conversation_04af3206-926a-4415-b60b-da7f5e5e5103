package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.processor;

import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/1/13
 */
@Service
public class GVGStrongHoldAttackProcessor extends AbstractGvgArmyProcessor {
	@Autowired
	private GVGStrongHoldService gvgStrongHoldService;

	@Override
	public ArmyType getArmyType() {
		return ArmyType.PVP_STRONGHOLD;
	}

	@Override
	protected boolean check(Role role, SceneNode targetNode, ArmySetoutParam param) {
		logger.info("[GVG]check, role:{}, targetNode:{}, armyType:{}", role.getId(), targetNode.getPersistKey(), param.getArmyType());
		return gvgStrongHoldService.setOutToStrongHoldCheck(role, targetNode, param);
	}

	@Override
	protected void start(ArmyInfo army) {
		logger.info("[GVG]start, army:{}", army.getPersistKey());
		gvgStrongHoldService.setOutToStrongHoldStart(army);
	}

	@Override
	protected void onRecallArmyDone(ArmyInfo army, ArmyWorkType oldWorkType){
		logger.info("[GVG]onRecallArmyDone, army: {}", army.getPersistKey());
		gvgStrongHoldService.onArmyLeftStrongHold(army, oldWorkType);
	}

	public void onDeletedArmy(ArmyInfo army) {
		logger.info("[GVG]onDeletedArmy, army: {}", army.getPersistKey());
		if(army.getWorkType() == ArmyWorkType.DEFENDING) {
			gvgStrongHoldService.onArmyLeftStrongHold(army, army.getWorkType());
		}
	}
}
