package com.lc.billion.icefire.kvkseason.biz.async;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.biz.dao.impl.RankDaoImpl;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankType;
import com.lc.billion.icefire.kvkseason.biz.service.impl.kvk.KvkSeasonServiceImpl;
import redis.clients.jedis.resps.Tuple;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/8/26
 */
public class KvkAllianceSeasonRewardOperation implements AsyncOperation {
    private static int RECORD_NUM = 300;

    private final List<Long> rankList = new ArrayList<>();
    private final Map<Long, Double> scoreMap = new HashMap<>();
    private final KvkSeasonServiceImpl kvkSeasonService;
    private final RankType rankType;

    public KvkAllianceSeasonRewardOperation(KvkSeasonServiceImpl service, RankType rankType) {
        this.kvkSeasonService = service;
        this.rankType = rankType;
    }

    @Override
    public boolean run() {
        RankDaoImpl rankDao = Application.getBean(RankDaoImpl.class);
        String key = RankServiceImpl.getKey(rankType);

        var data = rankDao.getRank(key, 0, RECORD_NUM + 50, rankType.getRedisType());// 多取50个。redis可能有脏数据，排名顺延
        logger.info("KVK赛季结算开始1 | {} | data={}", rankType, data);
        StringBuilder log = new StringBuilder();
        if (data != null) {
            int count = 0;
            for (Tuple t : data) {
                int rank = (++count);
                if (rank <= RECORD_NUM) {
                    Long memberId = Long.parseLong(t.getElement());
                    Double score = t.getScore();
                    // 数据合法性检测
                    if (!redisDataValidate(memberId)) {
                        rank--;
                    }
                    rankList.add(memberId);
                    scoreMap.put(memberId, score);
                    log.append(getOServerId(memberId)).append("服:").append(t.getElement()).append(",排名:").append(rank).append(", 积分:")
                            .append(Double.valueOf(t.getScore()).longValue()).append("\n");
                }
            }
        }
        logger.info("KVK赛季结算开始2 | {} | data={}", rankType, data);
        return true;
    }

    /**
     * redis数据合法性检测。 有可能有脏数据
     *
     * @param memberId
     * @return
     */
    private boolean redisDataValidate(Long memberId) {
        AllianceDao allianceDao = Application.getBean(AllianceDao.class);
        if (rankType == RankType.ALLIANCE_PROSPERITY_KVK || rankType == RankType.ALLIANCE_PROSPERITY) {
            Alliance alliance = allianceDao.findById(memberId);
            if (alliance == null) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取舒服所属服Id
     *
     * @param memberId
     * @return
     */
    private int getOServerId(Long memberId) {
        AllianceDao allianceDao = Application.getBean(AllianceDao.class);
        if (rankType == RankType.ALLIANCE_PROSPERITY_KVK || rankType == RankType.ALLIANCE_PROSPERITY) {
            Alliance alliance = allianceDao.findById(memberId);
            if (alliance != null) {
                return alliance.getDB();
            }
        }

        return 0;
    }

    @Override
    public void finish() {
        kvkSeasonService.sendReward(rankType, rankList, scoreMap);
        kvkSeasonService.broadcastKvkStageChange();
    }
}
