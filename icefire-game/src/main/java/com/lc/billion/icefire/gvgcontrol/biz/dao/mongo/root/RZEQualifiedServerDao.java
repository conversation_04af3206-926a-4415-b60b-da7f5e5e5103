package com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGQualifiedServer;
import com.lc.billion.icefire.gvgcontrol.biz.model.RZEQualifiedServer;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

/**
 *
 */
@Repository
public class RZEQualifiedServerDao extends RootDao<RZEQualifiedServer> {

	public RZEQualifiedServerDao() {
		super(RZEQualifiedServer.class, false);
	}

	@Override
	protected MongoCursor<RZEQualifiedServer> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(RZEQualifiedServer entity) {

	}

	@Override
	protected void removeMemoryIndexes(RZEQualifiedServer entity) {

	}

	public RZEQualifiedServer create(int serverId) {
		int db = Application.getServerId();
		RZEQualifiedServer rzeQualifiedServer = newEntityInstance();
		rzeQualifiedServer.setPersistKey(Long.valueOf(serverId));
		return createEntity(db, rzeQualifiedServer);
	}

}
