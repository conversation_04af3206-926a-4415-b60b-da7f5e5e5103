package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.processor;

import java.util.ArrayList;

import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import com.lc.billion.icefire.protocol.structure.PsArmyProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;

/**
 * <AUTHOR>
 * @date 2021/1/19
 */
@Service
public class GVGStrongHoldRallyProcessor extends AbstractGvgRallyArmyProcessor {
	@Autowired
	private GVGStrongHoldService gvgStrongHoldService;
	@Override
	public ArmyType getArmyType() {
		return ArmyType.RALLY_STRONGHOLD;
	}

	@Override
	protected boolean check(Role role, SceneNode targetNode, ArmySetoutParam param) {
		logger.info("[GVG]check, role:{}, targetNode:{}, armyType:{}", role.getId(), targetNode.getPersistKey(), param.getArmyType());
		return gvgStrongHoldService.setOutToStrongHoldCheck(role, targetNode, param);
	}

	@Override
	protected void start(ArmyInfo army) {
		logger.info("[GVG]start, army: {}", army.getPersistKey());
		gvgStrongHoldService.setOutToStrongHoldStart(army);
	}

	private void recallJoinArmy(ArmyInfo army, boolean immediately){
		var rallyContext = army.getRallyContext();
		if(rallyContext == null) {
			ErrorLogUtil.errorLog("[GVG]recallJoinArmy, rallyContext null", "armyId",army.getPersistKey(),
					"roleId",army.getRoleId(), "immediately",immediately);
			return;
		}

		var joinArmyList = new ArrayList<ArmyInfo>();
		for(var joinArmyId: rallyContext.getJoinArmyIdList()){
			ArmyInfo joinArmy = armyManager.findById(joinArmyId);
			if(joinArmy == null) {
				// 当发生集结，然后GVG服务器重启，会出现这种情况
				ErrorLogUtil.errorLog("[GVG]recallJoinArmy, joinArmy null",  "armyId",army.getPersistKey(),
						"roleId",army.getRoleId(), "immediately",immediately, "joinArmyId",joinArmyId);
				continue;
			}

			joinArmyList.add(joinArmy);
		}

		for(var joinArmy: joinArmyList) {
			if(immediately){
				armyManager.returnArmyImmediately(joinArmy);
			} else {
				armyManager.returnArmy(joinArmy);
			}
		}
	}

	@Override
	protected void doRecall(ArmyInfo army) {
		logger.info("[GVG]doRecall, army: {}", army.getPersistKey());
		recallJoinArmy(army, false);
	}

	@Override
	public void recallArmyImmediately(ArmyInfo army) {
		logger.info("[GVG]recallArmyImmediately, army: {}", army.getPersistKey());
		recallJoinArmy(army, false);
		armyManager.takeBackArmy(army);
	}

	@Override
	protected void onRecallArmyDone(ArmyInfo army, ArmyWorkType oldWorkType){
		logger.info("[GVG]onRecallArmyDone, army: {}", army.getPersistKey());
		warService.warDel(army);
		gvgStrongHoldService.onArmyLeftStrongHold(army, oldWorkType);
	}

	public void onDeletedArmy(ArmyInfo army) {
		logger.info("[GVG]onDeletedArmy, army: {}", army.getPersistKey());
		warService.warDel(army);
		if(army.getWorkType() == ArmyWorkType.DEFENDING) {
			gvgStrongHoldService.onArmyLeftStrongHold(army, army.getWorkType());
		}
	}
}
