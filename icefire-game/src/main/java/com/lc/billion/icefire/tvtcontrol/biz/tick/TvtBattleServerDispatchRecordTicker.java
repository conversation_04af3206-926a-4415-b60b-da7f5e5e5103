package com.lc.billion.icefire.tvtcontrol.biz.tick;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.async.asyncIO.AsyncIOThreadOperation;
import com.lc.billion.icefire.game.biz.common.AbstractTicker;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.tvtcontrol.biz.TvtBattleServerCreateConfig;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtBattleServerDispatchRecordDao;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtBattleServerDispatchRecord;
import com.lc.billion.icefire.tvtcontrol.biz.service.TvtControlService;
import com.simfun.sgf.utils.JavaUtils;
import com.simfun.sgf.utils.TimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * @author: maoqq
 * @Date: 2022/04/18 10:08 PM
 */
@Service
public class TvtBattleServerDispatchRecordTicker extends AbstractTicker<Long> {

    @Autowired
    private TvtBattleServerDispatchRecordDao tvtBattleServerDispatchRecordDao;
    @Autowired
    private TvtControlService tvtControlService;
    @Autowired
    private AsyncOperationServiceImpl asyncOperationService;


    public TvtBattleServerDispatchRecordTicker() {
        super(10 * TimeUtils.SECONDS_MILLIS);
    }

    @Override
    protected void tick(Long battleTime, long now) {
        List<TvtBattleServerDispatchRecord> tvtBattleServerDispatchRecords = tvtBattleServerDispatchRecordDao.findByTime(battleTime);
        if (!JavaUtils.bool(tvtBattleServerDispatchRecords)) {
            return;
        }
        TvtBattleServerCreateConfig tvtBattleServerCreateConfig = ServerConfigManager.getInstance().getTvtBattleServerCreateConfig();
        List<Integer> createBattleServerIds = new ArrayList<>();
        List<Integer> statusBattleServerIds = new ArrayList<>();
        List<Integer> destroyBattleServerIds = new ArrayList<>();
        int battleTurn = 0;
        for (TvtBattleServerDispatchRecord tvtBattleServerDispatchRecord : tvtBattleServerDispatchRecords) {
            battleTurn = tvtBattleServerDispatchRecord.getBattleTurn();
            switch (tvtBattleServerDispatchRecord.getStatus()) {
                case TvtBattleServerDispatchRecord.STATUS_MATCH:
                    // 60分钟内立即执行
                    int checkTime = tvtBattleServerCreateConfig.getCheckTime();
                    if (checkTime == 0) {
                        checkTime = 60;
                    }
                    if (now > battleTime - checkTime * TimeUtil.MINUTE_MILLIS) {
                        tvtBattleServerDispatchRecord.setStatus(TvtBattleServerDispatchRecord.STATUS_NOTICE);
                        tvtBattleServerDispatchRecordDao.save(tvtBattleServerDispatchRecord);
                        createBattleServerIds.add(tvtBattleServerDispatchRecord.getBattleServerId().intValue());
                    }
                    break;
                case TvtBattleServerDispatchRecord.STATUS_NOTICE:
                    // 30分支查看状态，报警
                    int statusTime = tvtBattleServerCreateConfig.getStatusTime();
                    if (now > battleTime - statusTime * TimeUtil.MINUTE_MILLIS) {
                        tvtBattleServerDispatchRecord.setStatus(TvtBattleServerDispatchRecord.STATUS_RENOTICE);
                        tvtBattleServerDispatchRecordDao.save(tvtBattleServerDispatchRecord);
                        statusBattleServerIds.add(tvtBattleServerDispatchRecord.getBattleServerId().intValue());
                    }
                    break;
                case TvtBattleServerDispatchRecord.STATUS_START:
                    // 啥都不干
                    break;
                case TvtBattleServerDispatchRecord.STATUS_RENOTICE:
                    // 啥都不干
                    break;
                case TvtBattleServerDispatchRecord.STATUS_END:
                    // 销毁
                    if (tvtBattleServerDispatchRecord.getBattleDestroyTime() < now) {

                        destroyBattleServerIds.add(tvtBattleServerDispatchRecord.getBattleServerId().intValue());
                    }
                    break;
                default:
                    break;
            }
        }

        int _battleTurn = battleTurn;
        //创建、查询、销毁战斗服的操作由同步改为异步。 2021/10/18
        asyncOperationService.execute(new AsyncIOThreadOperation() {
            @Override
            public boolean run() {
                tvtControlService.noticeCreateBattleServer(createBattleServerIds, _battleTurn);
                tvtControlService.reNoticeCreateBattleServer(statusBattleServerIds, _battleTurn);
                tvtControlService.noticeDestroy(destroyBattleServerIds, _battleTurn);
                return false;
            }

            @Override
            public void finish() {
            }
        });
    }

    @Override
    protected Collection<Long> findAll() {
        return tvtBattleServerDispatchRecordDao.findBattleTimes();
    }
}
