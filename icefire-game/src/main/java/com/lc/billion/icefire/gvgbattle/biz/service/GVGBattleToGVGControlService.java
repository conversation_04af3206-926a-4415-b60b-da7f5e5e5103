package com.lc.billion.icefire.gvgbattle.biz.service;

import java.util.List;

import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.gvgbattle.biz.service.rpc.TVTBattleRPCToTVTControlProxyService;
import com.lc.billion.icefire.rpc.service.tvt.ITvtBattleRemoteTvtControlService;
import com.longtech.ls.config.ServerType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.gvgbattle.biz.manager.gvg.GVGBattleDataVoManager;
import com.lc.billion.icefire.gvgbattle.biz.service.rpc.GVGBattleRPCToGVGControlProxyService;
import com.lc.billion.icefire.rpc.service.gvg.IGVGBattleRemoteGVGControlService;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGAllianceSignUpInfoVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGBattleRecordVo;
import com.lc.billion.icefire.rpc.vo.gvg.GvgBattleServerDispatchRecordVo;

/**
 * <AUTHOR>
 * 说明：GVG/TVT战斗服调用中控服逻辑
 *
 */
@Service
public class GVGBattleToGVGControlService {

	private static final Logger logger = LoggerFactory.getLogger(GVGBattleToGVGControlService.class);

	@Autowired
	private GVGBattleRPCToGVGControlProxyService gvgBattleRPCToGVGControlProxyService;
	@Autowired
	private TVTBattleRPCToTVTControlProxyService tvtBattleRPCToTVTControlProxyService;
	@Autowired
	private GVGBattleDataVoManager gvgDataVoManager;

	/**
	 * 战斗服启服去中控服注册，注意不要造成死锁
	 */
	public GvgBattleServerDispatchRecordVo registerGVGBattleServerToGVGControlServer() {
		int serverId = Application.getServerId();

		if(Application.getServerType() == ServerType.GVG_BATTLE){
			IGVGBattleRemoteGVGControlService gvgBattleRemoteGVGControlService = gvgBattleRPCToGVGControlProxyService.getGVGBattleRemoteGVGControlService();
			if (gvgBattleRemoteGVGControlService != null) {
				return gvgBattleRemoteGVGControlService.registerGVGBattleServerToGVGControlServer(serverId);
			}
		}else if(Application.getServerType() == ServerType.TVT_BATTLE){
			ITvtBattleRemoteTvtControlService tvtBattleRemoteTvtControlService = tvtBattleRPCToTVTControlProxyService.getTVTBattleRemoteTVTControlService();
			if (tvtBattleRemoteTvtControlService != null) {
				return tvtBattleRemoteTvtControlService.registerTVTBattleServerToTVTControlServer(serverId);
			}
		}
		return null;
	}

	/**
	 * 战斗服启服从中控服取对阵信息，主动拉取，注意不要造成死锁
	 */
	public void findGvgBattleServerDispatchRecordVo() {
		if(Application.getServerType() == ServerType.TVT_BATTLE){
			return;
		}

		int battleServerId = Application.getServerId();
		IGVGBattleRemoteGVGControlService gvgBattleRemoteGVGControlService = gvgBattleRPCToGVGControlProxyService.getGVGBattleRemoteGVGControlService();
		if (gvgBattleRemoteGVGControlService != null) {
			logger.info("战斗服主动拉取对阵信息start");
			GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo = gvgBattleRemoteGVGControlService.findGvgBattleServerDispatchRecord(battleServerId);
			gvgDataVoManager.updateGvgBattleServerDispatchRecordVo(gvgBattleServerDispatchRecordVo);
			logger.info("战斗服主动拉取对阵信息end");
		} else {
			ErrorLogUtil.errorLog("IGVGBattleRemoteGVGControlService还未初始化");
		}
	}

	/**
	 * 更新战斗信息
	 */
	public void uploadGVGBattleRecord(GVGBattleRecordVo gvgBattleRecordVo) {
		if(Application.getServerType() == ServerType.GVG_BATTLE){
			IGVGBattleRemoteGVGControlService gvgBattleRemoteGVGControlService = gvgBattleRPCToGVGControlProxyService.getGVGBattleRemoteGVGControlService();
			if (gvgBattleRemoteGVGControlService != null) {
				gvgBattleRemoteGVGControlService.uploadGVGBattleRecord(gvgBattleRecordVo);
			}
		}else if(Application.getServerType() == ServerType.TVT_BATTLE){
			ITvtBattleRemoteTvtControlService tvtBattleRemoteTvtControlService = tvtBattleRPCToTVTControlProxyService.getTVTBattleRemoteTVTControlService();
			if (tvtBattleRemoteTvtControlService != null) {
				tvtBattleRemoteTvtControlService.uploadTVTBattleRecord(gvgBattleRecordVo);
			}
		}
	}

	public List<GVGAllianceSignUpInfoVo> findGVGAllianceSignUpInfoVo(int battleServerId) {
		if (Application.getServerType() == ServerType.GVG_BATTLE) {
			IGVGBattleRemoteGVGControlService gvgBattleRemoteGVGControlService = gvgBattleRPCToGVGControlProxyService.getGVGBattleRemoteGVGControlService();
			if (gvgBattleRemoteGVGControlService != null) {
				return gvgBattleRemoteGVGControlService.findGVGAllianceSignUpInfoVo(battleServerId);
			} else {
				ErrorLogUtil.errorLog("战斗服从中控服拉取GVGAllianceSignUpInfoVo，中控服服务找不到");
				return null;
			}
		} else if (Application.getServerType() == ServerType.TVT_BATTLE) {
			ITvtBattleRemoteTvtControlService tvtBattleRemoteTvtControlService = tvtBattleRPCToTVTControlProxyService.getTVTBattleRemoteTVTControlService();
			if (tvtBattleRemoteTvtControlService != null) {
				return tvtBattleRemoteTvtControlService.findTVTAllianceSignUpInfoVo(battleServerId);
			} else {
				ErrorLogUtil.errorLog("战斗服从中控服拉取GVGAllianceSignUpInfoVo，中控服服务找不到");
				return null;
			}
		}
		return null;
	}

	public ActivityVo findActivity() {
		if(Application.getServerType() == ServerType.GVG_BATTLE){
			IGVGBattleRemoteGVGControlService gvgBattleRemoteGVGControlService = gvgBattleRPCToGVGControlProxyService.getGVGBattleRemoteGVGControlService();
			if (gvgBattleRemoteGVGControlService != null) {
				return gvgBattleRemoteGVGControlService.findActivityVo();
			} else {
				ErrorLogUtil.errorLog("战斗服从中控服拉取ActivityVo，中控服服务找不到");
				return null;
			}
		}else if(Application.getServerType() == ServerType.TVT_BATTLE){
			ITvtBattleRemoteTvtControlService tvtBattleRemoteTvtControlService = tvtBattleRPCToTVTControlProxyService.getTVTBattleRemoteTVTControlService();
			if (tvtBattleRemoteTvtControlService != null) {
				return tvtBattleRemoteTvtControlService.findActivityVo();
			} else {
				ErrorLogUtil.errorLog("战斗服从中控服拉取ActivityVo，中控服服务找不到");
				return null;
			}
		}
		return null;
	}

	public void noticeControlServerDestroyBattleServer(int battleServerId) {
		if(Application.getServerType() == ServerType.GVG_BATTLE){
			IGVGBattleRemoteGVGControlService gvgBattleRemoteGVGControlService = gvgBattleRPCToGVGControlProxyService.getGVGBattleRemoteGVGControlService();
			if (gvgBattleRemoteGVGControlService != null) {
				gvgBattleRemoteGVGControlService.noticeDestroyBattleServer(battleServerId);
			} else {
				ErrorLogUtil.errorLog("战斗服找不到中控服rpc服务器");
			}
		}else if(Application.getServerType() == ServerType.TVT_BATTLE){
			ITvtBattleRemoteTvtControlService tvtBattleRemoteTvtControlService = tvtBattleRPCToTVTControlProxyService.getTVTBattleRemoteTVTControlService();
			if (tvtBattleRemoteTvtControlService != null) {
				tvtBattleRemoteTvtControlService.noticeDestroyBattleServer(battleServerId);
			} else {
				ErrorLogUtil.errorLog("战斗服找不到中控服rpc服务器");
			}
		}
	}
}
