package com.lc.billion.icefire.kvkseason.biz.service.impl.honor;

import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;

public enum HonorType implements IntEnum {
    /**
     * 击杀州府战内的怪物
     * para1 怪物士兵的等级 score
     */
    KILL_CAPITAL_PVE(1),
    /**
     * 击杀州府战内的士兵
     * para1 为士兵等级
     */
    KILL_CAPITAL_PVP(2),
    /**
     * 决战王城占领获得功勋
     * para1 等级 para2 代表每秒多少功勋
     */
    OCCUPY_CAPITAL_TIME(3),
    /**
     * 州府战中自己的重伤或者死亡
     * para1 士兵等级
     */
    BE_KILLED_IN_CAPITAL(4),

    /**
     * 州府战中自己的重伤或者死亡,被炮台打死
     * para1 士兵等级
     */
    BE_KILLED_IN_ARTILLERY(5),
    ;
    private static final HonorType[] INDEXES = EnumUtils.toArray(values());
    private int id;

    HonorType(int id) {
        this.id = id;
    }

    @Override
    public int getId() {
        return id;
    }

    public static HonorType findById(int id) {
        if (id < 0 || id >= INDEXES.length) {
            return null;
        }
        return INDEXES[id];
    }
}
