package com.lc.billion.icefire.kvkseason.biz.manager.honor;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.manager.AbstractRoleManager;
import com.lc.billion.icefire.game.biz.manager.kvk.IMigrateForKVKManager;
import com.lc.billion.icefire.game.biz.model.player.Player;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.roles.RoleSeasonInfoDao;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.KvkHonorAllianceDao;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.KvkHonorRoleDao;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.KvkHonorServerDao;
import com.lc.billion.icefire.kvkseason.biz.model.honor.KvkHonorAlliance;
import com.lc.billion.icefire.kvkseason.biz.model.honor.KvkHonorRole;
import com.lc.billion.icefire.kvkseason.biz.model.honor.KvkHonorServer;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.simfun.sgf.utils.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *
 */
@Component
public class KvkHonorManager extends AbstractRoleManager implements IMigrateForKVKManager {

	@Autowired
	private ConfigCenter configCenter;
	@Autowired
	private KvkHonorAllianceDao kvkHonorAllianceDao;
	@Autowired
	private KvkHonorRoleDao kvkHonorRoleDao;
	@Autowired
	private KvkHonorServerDao kvkHonorServerDao;
	@Autowired
	private RoleDao roleDao;
	@Autowired
	private RoleSeasonInfoDao roleSeasonInfoDao;
	@Autowired
	private ServiceDependency srvdp;

	/**
	 * 给玩家加荣誉
	 * 
	 * @param roleId
	 * @param value
	 */
	public long addHonorRole(Long roleId, long value) {
		int season = Application.getSeason();
		var roleSeasonInfo = roleSeasonInfoDao.findById(roleId);
		if (roleSeasonInfo == null) {
			return 0L;
		}
		long total = roleSeasonInfo.addHonor(season, value);
		int weekNum = srvdp.getServerInfoService().getServerWeek();
		roleSeasonInfo.addWeekHonor(season, weekNum, value);
		roleSeasonInfoDao.save(roleSeasonInfo);
		return total;
	}

	public void initKvkHonor(Long roleId) {
		ServerType currentGameServerType = configCenter.getCurrentGameServerType();
		if (currentGameServerType != ServerType.KVK_SEASON) {
			return;
		}
		int db = Application.getServerId();
		// 正常初始化honor信息
		KvkHonorRole kvkHonorRole = kvkHonorRoleDao.findById(roleId);
		if (kvkHonorRole == null) {
			ErrorLogUtil.errorLog("初始化KvkHonorRole", "roleId",roleId);
			kvkHonorRoleDao.create(db, roleId);
		}
		Role role = roleDao.findById(roleId);
		Long allianceId = role.getAllianceId();
		if (JavaUtils.bool(allianceId)) {
			KvkHonorAlliance kvkHonorAlliance = kvkHonorAllianceDao.findById(allianceId);
			if (kvkHonorAlliance == null) {
				ErrorLogUtil.errorLog("初始化KvkHonorAlliance", "allianceId",allianceId);
				kvkHonorAllianceDao.create(db, allianceId);
			}
		}
		int oServerId = role.getoServerId();
		KvkHonorServer kvkHonorServer = kvkHonorServerDao.findByServerId(oServerId);
		if (kvkHonorServer == null) {
			ErrorLogUtil.errorLog("初始化KvkHonorServer", "oServerId",oServerId);
			kvkHonorServerDao.create(db, oServerId);
		}
	}

	@Override
	public void kvkSeasonSwitchFirstLogin(Role role) {
	}

	@Override
	public void onCreateRole(Role role, Player player) {

	}

	@Override
	protected void onCreateRoleFailed(Role role) {

	}

	@Override
	public void beforeLogin(Role role) {
		initKvkHonor(role.getId());
	}

	@Override
	public void afterLogin(Role role) {

	}
	public long getRoleHorner(Long roleId) {
		int season = Application.getSeason();
		var roleSeasonInfo = roleSeasonInfoDao.findById(roleId);
		if (roleSeasonInfo == null) {
			return 0L;
		}
		long total = roleSeasonInfo.getHonor(season);
		return total;
	}

	public long getRoleCurWeekHonor(Long roleId) {
		var roleSeasonInfo = roleSeasonInfoDao.findById(roleId);
		if (roleSeasonInfo == null) {
			return 0L;
		}

		int season = Application.getSeason();
		int weekNum = srvdp.getServerInfoService().getServerWeek();
		return roleSeasonInfo.getWeekHonor(season, weekNum);
	}
}
