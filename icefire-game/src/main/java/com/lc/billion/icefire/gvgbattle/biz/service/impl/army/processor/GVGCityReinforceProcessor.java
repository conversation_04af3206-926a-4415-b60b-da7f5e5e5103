package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.processor;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.alliance.reinforce.AllianceReinforceService;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import com.lc.billion.icefire.game.biz.service.impl.login.LoginServiceImpl;
import com.lc.billion.icefire.protocol.GcAllianceReinforce;
import com.lc.billion.icefire.protocol.constant.PsAllianceWarInfoUpdateReason;
import com.lc.billion.icefire.protocol.structure.PsArmyProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/2/26
 */
@Service
public class GVGCityReinforceProcessor extends AbstractGvgArmyProcessor {
	@Autowired
	private AllianceReinforceService reinforceService;
	@Autowired
	private AllianceServiceImpl allianceService;

	@Override
	public ArmyType getArmyType() {
		return ArmyType.GVG_REINFORCE;
	}

	@Override
	protected void doRecall(ArmyInfo army) {
		armyDao.removeReinforceIndex(army.getTargetNodeId(), army);
		updateWar(army);
		reinforceService.pushAllianceReinforceChange(army.getTargetNodeId());
	}

	@Override
	public void recallArmyImmediately(ArmyInfo army) {
		armyDao.removeReinforceIndex(army.getTargetNodeId(), army);
		armyManager.takeBackArmy(army);
		updateWar(army);
		reinforceService.pushAllianceReinforceChange(army.getTargetNodeId());
	}

	private void updateWar(ArmyInfo army) {
		var targetNode = sceneService.getSceneNode(Application.getServerId(), army.getEndPoint());
		if(targetNode != null) {
			warService.broadcastUpdate(targetNode, PsAllianceWarInfoUpdateReason.REINFORCE_CHANGE);
		}
	}

	@Override
	protected boolean check(Role role, SceneNode targetNode, ArmySetoutParam param) {
		if (!Application.getBean(LoginServiceImpl.class).isLoginSwitch()) {// gvg战斗服停了，战斗结束了
			return false;
		}
		if (!gvgStrongHoldService.isMoveGvgActivityStage(TimeUtil.getNow()))// 入场阶段，不允许行军
			return false;
		// 保护罩检测
		if (targetNode.getNodeType() != SceneNodeType.CITY) {
			return false;
		}
		// 是同一个联盟
		if (!allianceService.isSameAlliance(role.getPersistKey(), targetNode.getPersistKey())) {
			return false;
		}
		//
		// GcAllianceReinforce 0 : 成功 1 : 已经援助 2 : 容量不足
		//
		if (reinforceService.existReinforcement(role, targetNode)) {
			GcAllianceReinforce gcAllianceReinforce = new GcAllianceReinforce();
			gcAllianceReinforce.setErrorCode(1);
			role.send(gcAllianceReinforce);
			return false;
		}
		long capacity = reinforceService.getLeftReinforceCapacity(targetNode);
		if (capacity <= 0 || capacity < param.getSoldierCount()) {
			GcAllianceReinforce gcAllianceReinforce = new GcAllianceReinforce();
			gcAllianceReinforce.setErrorCode(2);
			role.send(gcAllianceReinforce);
			return false;
		}
		return true;
	}

	@Override
	protected void start(ArmyInfo army) {
//		armyDao.addReinforceIndex(army.getTargetNodeId(), army);
		//
		sdp.getArmyServiceImpl().addArmyToAoi(army, addToAoi());
		//
		GcAllianceReinforce gcAllianceReinforce = new GcAllianceReinforce();
		gcAllianceReinforce.setErrorCode(0);
		Role role = roleManager.getRole(army.getRoleId());
		if (role.isOnline()) {
			role.send(gcAllianceReinforce);
		}
	}

	@Override
	protected PsArmyProgress toArmyProgressInfo(ArmyInfo army, PsArmyProgress info) {
		if (army.getWorkType() == ArmyWorkType.REINFORCING) {
			// info.setStartServerPos(army.getStartServerPos());
			info.setStartX(army.getStartX());
			info.setStartY(army.getStartY());
			// info.setEndServerPos(army.getEndServerPos());
			info.setEndX(army.getEndX());
			info.setEndY(army.getEndY());
		}
		return info;
	}

	@Override
	public boolean recallFreeCheck(ArmyInfo army) {
		if (army.getWorkType() == ArmyWorkType.REINFORCING) {
			return true;
		}
		return false;
	}
}
