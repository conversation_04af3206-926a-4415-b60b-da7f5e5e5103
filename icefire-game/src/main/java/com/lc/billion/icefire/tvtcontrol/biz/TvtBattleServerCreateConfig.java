package com.lc.billion.icefire.tvtcontrol.biz;

import com.simfun.sgf.utils.JavaUtils;

/**
 * 1.接口说明 创建tvt接口，5分钟内只允许执行一次，提交成功后，非必要不要重复执行。 回收tvt接口，同样的sid，只能执行一次。
 * 获取状态接口，包括了正在创建和回收的状态。 返回状态码 status包含ok 或者 error。 ok提交成功，error提交失败。
 * 
 * 2.请求说明 请求方法：post 请求参数： { "project_name": "LS-DEV", # 必选参数。 "sid": "90001",
 * # 逗号分割，必须参数。 "db_index": 1 #创建接口必选，其他接口可选。 }
 * 创建gvg服请求接口：http://172.16.0.170:8000/api/gvg/create {"status": "ok",
 * "sids": ["90001"], "message": "Commit create / gvg success"}
 * 
 * 获取gvg服状态接口：http://172.16.0.170:8000/api/gvg/status {"status": "ok",
 * "running": ["90001"], "pending": [], "destroy": []}
 * 
 * 回收gvg服请求接口：http://172.16.0.170:8000/api/gvg/destroy {"status": "ok",
 * "message": ["90001"]}
 *
 *
 */
public class TvtBattleServerCreateConfig {

	private static final String API_COMMON_PREFIX = "/api/tvt/";

	private static final String CREATE_SUFFIX = "create";

	private static final String STATUS_SUFFIX = "status";

	private static final String DESTROY_SUFFIX = "destroy";

	public static final String PROJECT_NAME_KEY = "project_name";

	public static final String SID_KEY = "sid";

	public static final String DB_INDEX_KEY = "db_index";

	// 动态创建回收的url
	private String url;
	// 通知参数/2-DEV、/2-TESTFLIGHT3
	private String projectName;
	// 检测时间，因为要提前创建，所以从开始分配到战斗服开启的时间要大于检测时间，否则可能来不及创建
	private int checkTime;
	private int statusTime;

	/**
	 * {"status": "ok", "sids": ["70000"], "message": "Commit create /gvg
	 * success"}
	 */
	public String getCreateUrl() {
		return concatUrl(CREATE_SUFFIX);
	}

	/**
	 * {"status": "ok", "running": ["70000"], "pending": [], "destroy": []}
	 */
	public String getStatusUrl() {
		return concatUrl(STATUS_SUFFIX);
	}

	/**
	 * {"status": "ok", "message": ["70000"]}
	 */
	public String getDestroyUrl() {
		return concatUrl(DESTROY_SUFFIX);
	}

	private String concatUrl(String suffix) {
		if (!JavaUtils.bool(url)) {
			return null;
		}
		return url + API_COMMON_PREFIX + suffix;
	}

	public String getUrl() {
		return url;
	}

	public int getCheckTime() {
		return checkTime;
	}

	public String getProjectName() {
		return projectName;
	}

	public int getStatusTime() {
		return statusTime;
	}
}
