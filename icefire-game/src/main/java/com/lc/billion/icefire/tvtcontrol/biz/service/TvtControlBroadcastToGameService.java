package com.lc.billion.icefire.tvtcontrol.biz.service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.rpc.service.tvt.ITvtControlRemoteGameService;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGBattleRecordVo;
import com.lc.billion.icefire.rpc.vo.gvg.RoleGVGBattleVo;
import com.lc.billion.icefire.rpc.vo.tvt.TvtBattleServerDispatchRecordVo;
import com.lc.billion.icefire.rpc.vo.tvt.TvtPlayerSignupInfoVo;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtBattleServerDispatchRecordDao;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtPlayerSignupInfoDao;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtQualifiedServerDao;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtBattleServerDispatchRecord;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtPlayerSignupInfo;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtPlayerSimpleInfo;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtQualifiedServer;
import com.lc.billion.icefire.tvtcontrol.biz.service.rpc.TvtControlRpcToGameProxyService;
import com.longtech.cod.rpc.client.RpcClient;
import com.google.api.client.util.Maps;
import com.simfun.sgf.utils.JavaUtils;


/**
 * <AUTHOR>
 * @date 2022/04/06
 */
@Service
public class TvtControlBroadcastToGameService {
    private static final Logger logger = LoggerFactory.getLogger(TvtControlBroadcastToGameService.class);

    @Autowired
    private TvtControlRpcToGameProxyService tvtControlRpcToGameProxyService;
    @Autowired
    private TvtQualifiedServerDao tvtQualifiedServerDao;
    @Autowired
    private TvtPlayerSignupInfoDao tvtPlayerSignupInfoDao;
    @Autowired
    private TvtBattleServerDispatchRecordDao tvtBattleServerDispatchRecordDao;

    public void broadcastActivityDailyReset(Activity activity) {
        if (activity == null) {
            ErrorLogUtil.errorLog("tvt broadcastTvtActivity error activity is null");
            return;
        }
        Collection<TvtQualifiedServer> tvtQualifiedServers = tvtQualifiedServerDao.findAll();
        if (JavaUtils.bool(tvtQualifiedServers)) {
            Set<RpcClient> alreadySentRpcClients = new HashSet<>();
            ActivityVo activityVo = new ActivityVo(activity);
            tvtQualifiedServers.forEach(tvtQualifiedServer -> {
                ITvtControlRemoteGameService tvtControlRemoteGameService = tvtControlRpcToGameProxyService.getTvtControlRemoteGameService(tvtQualifiedServer.getServerId());
                if (tvtControlRemoteGameService != null) {
                    RpcClient rpcClient = tvtControlRpcToGameProxyService.getTvtControlRemoteGameRpcClient(tvtQualifiedServer.getServerId());
                    if (rpcClient != null && alreadySentRpcClients.add(rpcClient)) {
                        tvtControlRemoteGameService.broadcastTvtActivityDailyReset(activityVo);
                    } else {
                        logger.info("tvt广播每日重置，{} 重复发送", tvtQualifiedServer.getServerId());
                    }
                } else {
                    ErrorLogUtil.errorLog("中控服找不到game服的rpc连接不广播", "serverId",tvtQualifiedServer.getServerId());
                }
            });
        }
    }

    public void broadcastTvtActivity(Activity activity) {
        if (activity == null) {
            ErrorLogUtil.errorLog("tvt broadcastTvtActivity error activity is null");
            return;
        }
        Collection<TvtQualifiedServer> tvtQualifiedServers = tvtQualifiedServerDao.findAll();
        if (JavaUtils.bool(tvtQualifiedServers)) {
            Set<RpcClient> alreadySentRpcClients = new HashSet<>();
            ActivityVo activityVo = new ActivityVo(activity);
            tvtQualifiedServers.forEach(tvtQualifiedServer -> {
                ITvtControlRemoteGameService tvtControlRemoteGameService = tvtControlRpcToGameProxyService.getTvtControlRemoteGameService(tvtQualifiedServer.getServerId());
                if (tvtControlRemoteGameService != null) {
                    RpcClient rpcClient = tvtControlRpcToGameProxyService.getTvtControlRemoteGameRpcClient(tvtQualifiedServer.getServerId());
                    if (rpcClient != null && alreadySentRpcClients.add(rpcClient)) {
                        tvtControlRemoteGameService.broadcastTvtActivity(activityVo);
                    } else {
                        logger.info("tvt广播活动信息，{} 重复发送", tvtQualifiedServer.getServerId());
                    }
                } else {
                    ErrorLogUtil.errorLog("中控服找不到game服的rpc连接不广播", "serverId",tvtQualifiedServer.getServerId());
                }
            });
        }
    }

    /**
     * 匹配结束广播分配记录
     */
    public void broadcastTvtBattleServerDispatchRecord(int serverId, Collection<TvtBattleServerDispatchRecord> tvtBattleServerDispatchRecord) {
        if (tvtBattleServerDispatchRecord == null) {
            return;
        }
        Collection<TvtBattleServerDispatchRecordVo> data = new ArrayList<>();
        tvtBattleServerDispatchRecord.forEach(record -> data.add(new TvtBattleServerDispatchRecordVo(record)));
        ITvtControlRemoteGameService tvtControlRemoteGameService = tvtControlRpcToGameProxyService.getTvtControlRemoteGameService(serverId);
        if (tvtControlRemoteGameService != null) {
            tvtControlRemoteGameService.broadcastTvtBattleServerDispatchRecord(data);
        } else {
            ErrorLogUtil.errorLog("中控服广播对战信息,但游戏服没起来", "serverId",serverId);
        }
    }


    /**
     * 每组k服 只广播一次
     */
    public void broadcastTvtBattleServerDispatchRecordClear() {
        Map<Integer, ITvtControlRemoteGameService> tvtControlRemoteGameServices = tvtControlRpcToGameProxyService.getTvtControlRemoteGameServices();
        if (JavaUtils.bool(tvtControlRemoteGameServices)) {
            Set<RpcClient> alreadySentRpcClients = new HashSet<>();
            for (Map.Entry<Integer, ITvtControlRemoteGameService> entry : tvtControlRemoteGameServices.entrySet()) {
                Integer serverId = entry.getKey();
                ITvtControlRemoteGameService tvtControlRemoteGameService = entry.getValue();
                if (tvtControlRemoteGameService != null) {
                    RpcClient rpcClient = tvtControlRpcToGameProxyService.getTvtControlRemoteGameRpcClient(serverId);
                    if (rpcClient != null && alreadySentRpcClients.add(rpcClient)) {
                        tvtControlRemoteGameService.broadcastTvtBattleServerDispatchRecordClear();
                    }
                }
            }
        }
    }

    /**
     * 每组k服 只广播一次
     */
    public void broadcastTvtPlayerSignupInfoClear() {
        Map<Integer, ITvtControlRemoteGameService> tvtControlRemoteGameServices = tvtControlRpcToGameProxyService.getTvtControlRemoteGameServices();
        if (JavaUtils.bool(tvtControlRemoteGameServices)) {
            Set<RpcClient> alreadySentRpcClients = new HashSet<>();
            for (Map.Entry<Integer, ITvtControlRemoteGameService> entry : tvtControlRemoteGameServices.entrySet()) {
                Integer serverId = entry.getKey();
                ITvtControlRemoteGameService tvtControlRemoteGameService = entry.getValue();
                if (tvtControlRemoteGameService != null) {
                    RpcClient rpcClient = tvtControlRpcToGameProxyService.getTvtControlRemoteGameRpcClient(serverId);
                    if (rpcClient != null && alreadySentRpcClients.add(rpcClient)) {
                        tvtControlRemoteGameService.broadcastTvtPlayerSignupInfoClear();
                    }
                }
            }
        }
    }

    /**
     * 广播tvt报名信息
     *
     * @param info
     */
    public void broadcastTvtPlayerSignupInfoVo(TvtPlayerSignupInfo info) {
        if (info == null) {
            return;
        }
        ITvtControlRemoteGameService tvtControlRemoteGameService = tvtControlRpcToGameProxyService.getTvtControlRemoteGameService(info.getServerId());
        if (tvtControlRemoteGameService == null) {
            ErrorLogUtil.errorLog("tvt 广播报名信息找不到rpc服务",new RuntimeException(),"serverId",info.getServerId());
            return;
        }
        tvtControlRemoteGameService.broadcastTvtPlayerSignupInfo(new TvtPlayerSignupInfoVo(info));

    }


    /**
     * 中控服广播战斗记录给game服，方便领奖
     */
    public void broadcastTvtBattleRecord(GVGBattleRecordVo gvgBattleRecordVo) {

        logger.info("TVT中控，战斗结束，中控给Game广播战斗结果，开始");

        if (gvgBattleRecordVo == null) {
            logger.info("TVT中控，中控给Game广播战斗结果，错误，gvgBattleRecordVo 为Null");
            return;
        }
        Map<Integer, String> serverId2RoleIds = Maps.newHashMap();
        for (RoleGVGBattleVo roleGVGBattleVo : gvgBattleRecordVo.getVictoryRoleGVGBattleVos()) {
            TvtPlayerSignupInfo signupInfo = tvtPlayerSignupInfoDao.findByRoleId(roleGVGBattleVo.getRoleId());
            String roleIdStr = "";
            if (serverId2RoleIds.containsKey(signupInfo.getServerId())) {
                roleIdStr = roleIdStr + "_" + roleGVGBattleVo.getRoleId();
            } else {
                roleIdStr = roleGVGBattleVo.getRoleId() + "";
            }
            serverId2RoleIds.put(signupInfo.getServerId(), roleIdStr);
        }
        for (RoleGVGBattleVo roleGVGBattleVo : gvgBattleRecordVo.getFailRoleGVGBattleVos()) {
            TvtPlayerSignupInfo signupInfo = tvtPlayerSignupInfoDao.findByRoleId(roleGVGBattleVo.getRoleId());
            String roleIdStr = "";
            if (serverId2RoleIds.containsKey(signupInfo.getServerId())) {
                roleIdStr = roleIdStr + "_" + roleGVGBattleVo.getRoleId();
            } else {
                roleIdStr = roleGVGBattleVo.getRoleId() + "";
            }
            serverId2RoleIds.put(signupInfo.getServerId(), roleIdStr);
        }

        for (int serverId : serverId2RoleIds.keySet()) {
            ITvtControlRemoteGameService tvtControlRemoteGameService = tvtControlRpcToGameProxyService.getTvtControlRemoteGameService(serverId);
            if (tvtControlRemoteGameService == null) {
                ErrorLogUtil.errorLog("TVT中控,中控给Game广播战斗结果错误,中控服没找到玩家game服的的rpc服务",
                        "serverId",serverId, "roleId",serverId2RoleIds.get(serverId));
            } else {
                tvtControlRemoteGameService.broadcastGVGBattleRecord(gvgBattleRecordVo);
                logger.info("TVT中控，中控给Game广播战斗结果，serverId:{},roleId:{}", serverId, serverId2RoleIds.get(serverId));
            }
        }

        logger.info("TVT中控，战斗结束，中控给Game广播战斗结果，结束");

    }


    public void broadcastTvtPlayerSignupInfos() {
        Map<Integer, List<TvtPlayerSignupInfo>> tvtPlayerSignupInfoMap = tvtPlayerSignupInfoDao.getDataByServerId();
        if (!JavaUtils.bool(tvtPlayerSignupInfoMap)) {
            ErrorLogUtil.errorLog("tvt 报名信息为null");
            return;
        }
        // 按服务器id分组发送
        for (Map.Entry<Integer, List<TvtPlayerSignupInfo>> entry : tvtPlayerSignupInfoMap.entrySet()) {
            Integer serverId = entry.getKey();
            List<TvtPlayerSignupInfo> tvtPlayerSignUpInfos = entry.getValue();
            ITvtControlRemoteGameService tvtControlRemoteGameService = tvtControlRpcToGameProxyService.getTvtControlRemoteGameService(serverId);
            List<TvtPlayerSignupInfoVo> res = new ArrayList<>();
            for (TvtPlayerSignupInfo info : tvtPlayerSignUpInfos) {
                res.add(new TvtPlayerSignupInfoVo(info));
            }
            if (tvtControlRemoteGameService != null) {
                tvtControlRemoteGameService.broadcastTvtPlayerSignupInfos(res);
            }
        }
    }

    public void broadcastTvtBattleServerDispatchRecords() {
        Map<Integer, Set<TvtBattleServerDispatchRecord>> tvtBattleServerDispatchRecordMap = tvtBattleServerDispatchRecordDao.findByServerId();
        if (!JavaUtils.bool(tvtBattleServerDispatchRecordMap)) {
            ErrorLogUtil.errorLog("tvt battleServerDispatchRecord 信息为null");
            return;
        }
        // 按服务器id分组发送
        for (Map.Entry<Integer, Set<TvtBattleServerDispatchRecord>> entry : tvtBattleServerDispatchRecordMap.entrySet()) {
            Integer serverId = entry.getKey();
            Set<TvtBattleServerDispatchRecord> tvtBattleServerDispatchRecords = entry.getValue();
            ITvtControlRemoteGameService tvtControlRemoteGameService = tvtControlRpcToGameProxyService.getTvtControlRemoteGameService(serverId);
            List<TvtBattleServerDispatchRecordVo> res = new ArrayList<>();
            for (TvtBattleServerDispatchRecord record : tvtBattleServerDispatchRecords) {
                if (record.getStatus() != TvtBattleServerDispatchRecord.STATUS_END) {
                    res.add(new TvtBattleServerDispatchRecordVo(record));
                }
            }
            if (tvtControlRemoteGameService != null) {
                tvtControlRemoteGameService.broadcastTvtBattleServerDispatchRecord(res);
            }
        }
    }


    /**
     * tvt 常驻服务器的情况下 会出现 中控收到战斗服销毁请求， 而不存在battleServerDispatchRecord的情况。 此时中控会create一个数据不完整的record。目的是为了让ticker走到destroy流程。
     * 但如果这时候重新部署，战斗服重启后又会向中控来注册。 引发的后续流程中因为record的数据不完整会报错。
     *
     *
     * fix这个问题。
     * @param record
     */
    public void broadcastTvtBattleServerDispatchRecord(TvtBattleServerDispatchRecord record) {
        if (record.getRedLineup() == null || record.getBlueLineup() == null) {
            ErrorLogUtil.errorLog("tvt broadcastTvtBattleServerDispatchRecord error,分配记录数据不完整,应该是本地常驻服务器的情况导致");
            return;
        }
        Set<Integer> serverIds = new HashSet<>();
        for (TvtPlayerSimpleInfo tvtPlayerSimpleInfo : record.getRedLineup()) {
            int serverId = tvtPlayerSimpleInfo.getServerId();
            serverIds.add(serverId);
        }
        for (TvtPlayerSimpleInfo tvtPlayerSimpleInfo : record.getBlueLineup()) {
            int serverId = tvtPlayerSimpleInfo.getServerId();
            serverIds.add(serverId);
        }
        Collection<TvtBattleServerDispatchRecord> list = new ArrayList();
        list.add(record);
        Set<RpcClient> distinctRpcClients = new HashSet<>();
        for (int serverId : serverIds) {
            RpcClient rpcClient = tvtControlRpcToGameProxyService.getTvtControlRemoteGameRpcClient(serverId);
            //过滤掉重复发送的serverId
            if (rpcClient != null && distinctRpcClients.add(rpcClient)) {
                this.broadcastTvtBattleServerDispatchRecord(serverId, list);
            }
        }

    }
}
