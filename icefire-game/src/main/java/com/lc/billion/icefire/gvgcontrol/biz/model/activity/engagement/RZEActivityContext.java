package com.lc.billion.icefire.gvgcontrol.biz.model.activity.engagement;

import com.lc.billion.icefire.game.biz.model.activity.ActivityContext;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.context.GVGActivitySignUpInfo;

import java.util.Map;

public class RZEActivityContext extends ActivityContext {

    /**
     * 当前阶段
     */
    private RZEActivityStatus status;
    /**
     * 下一阶段时间
     */
    private long nextStatusTime;

    /**
     * 各时间段报名情况，（用于限制单个时间段最大场次）
     */
    private Map<Integer, GVGActivitySignUpInfo> signUpInfos;

    public RZEActivityStatus getStatus() {
        return status;
    }

    public void setStatus(RZEActivityStatus status) {
        this.status = status;
    }

    public long getNextStatusTime() {
        return nextStatusTime;
    }

    public void setNextStatusTime(long nextStatusTime) {
        this.nextStatusTime = nextStatusTime;
    }

    public Map<Integer, GVGActivitySignUpInfo> getSignUpInfos() {
        return signUpInfos;
    }

    public void setSignUpInfos(Map<Integer, GVGActivitySignUpInfo> signUpInfos) {
        this.signUpInfos = signUpInfos;
    }
}
