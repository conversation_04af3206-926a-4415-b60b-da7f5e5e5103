package com.lc.billion.icefire.kvkseason.biz.dao.mongo.root;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.kvkseason.biz.model.honor.KvkHonorAlliance;

/**
 * <AUTHOR>
 *
 */
@Repository
public class KvkHonorAllianceDao extends RootDao<KvkHonorAlliance> {

	public KvkHonorAllianceDao() {
		super(KvkHonorAlliance.class, false);
	}

	@Override
	protected MongoCursor<KvkHonorAlliance> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(KvkHonorAlliance entity) {

	}

	@Override
	protected void removeMemoryIndexes(KvkHonorAlliance entity) {

	}

	public KvkHonorAlliance create(int db, Long allianceId) {
		KvkHonorAlliance kvkHonorAlliance = newEntityInstance();
		kvkHonorAlliance.setPersistKey(allianceId);
		return createEntity(db, kvkHonorAlliance);
	}
}
