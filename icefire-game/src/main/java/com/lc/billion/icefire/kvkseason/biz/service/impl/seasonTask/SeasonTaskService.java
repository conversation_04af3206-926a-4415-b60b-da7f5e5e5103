package com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.battle.FightArmy;
import com.lc.billion.icefire.game.biz.battle.FightContext;
import com.lc.billion.icefire.game.biz.battle.result.FightLostInfo;
import com.lc.billion.icefire.game.biz.battle.result.FightLostType;
import com.lc.billion.icefire.game.biz.config.kvk.KvkGroupConfig;
import com.lc.billion.icefire.game.biz.config.kvk.SeasonTaskConfig;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.email.AbstractEmail;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.csattack.impl.CrossServerAttackServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.drop.DropServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.email.MailSender;
import com.lc.billion.icefire.game.biz.service.impl.email.MailService;
import com.lc.billion.icefire.game.biz.service.impl.item.ItemServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.kvkseason.biz.async.SeasonTaskSettlementOperation;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.alliances.AllianceSeasonTaskProgressDao;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.roles.RoleSeasonTaskRecordDao;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionSeasonTaskProgressDao;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.ServerSeasonTaskProgressDao;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.RoleSeasonTaskRecord;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.AbstractSeasonTaskAction;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.SeasonTaskActionType;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.SeasonTaskType;
import com.lc.billion.icefire.protocol.GcGetSeasonTaskReward;
import com.lc.billion.icefire.protocol.GcSeasonTaskList;
import com.lc.billion.icefire.protocol.structure.PsSeasonTaskInfo;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 
 * 赛季任务service
 * 
 * <AUTHOR>
 * @date 2021/11/15
 */
@Service
public class SeasonTaskService {
	private static final Logger logger = LoggerFactory.getLogger(SeasonTaskService.class);

	@Autowired
	private ConfigServiceImpl configService;
	@Autowired
	private RoleSeasonTaskRecordDao roleSeasonTaskRecordDao;
	@Autowired
	private DropServiceImpl dropService;
	@Autowired
	private ItemServiceImpl itemService;
	@Autowired
	private RoleManager roleManager;
	@Autowired
	private AsyncOperationServiceImpl asyncOperationService;
	@Autowired
	private MailService mailService;
	@Autowired
	private CrossServerAttackServiceImpl crossServerAttackService;
	@Autowired
	private BiLogUtil biLogUtil;

	private Map<SeasonTaskActionType, AbstractSeasonTaskAction> actionMap;

	/**
	 * 启动服务器初始化
	 * 
	 * 1：actionMap,各个行为类型的处理类
	 */
	public void startService() {
		Map<SeasonTaskActionType, AbstractSeasonTaskAction> map = new EnumMap<>(SeasonTaskActionType.class);
		Map<String, AbstractSeasonTaskAction> beans = Application.getApplicationContext().getBeansOfType(AbstractSeasonTaskAction.class, false, true);
		for (AbstractSeasonTaskAction actionHandler : beans.values()) {
			AbstractSeasonTaskAction exists = map.put(actionHandler.getType(), actionHandler);
			if (exists != null) {
				throw new AlertException("AbstractSeasonTaskAction duplicate" ,"type",exists.getType());
			}
		}
		this.actionMap = Collections.unmodifiableMap(map);
		logger.info("服务器启动，本赛季服{},赛季任务开关{}", Application.getServerId(), taskOpenCheck());
	}

	/**
	 * 根据类型获取对应的处理类
	 * 
	 * @param actionType
	 * @return
	 */
	private AbstractSeasonTaskAction getActionHandler(SeasonTaskActionType actionType) {
		AbstractSeasonTaskAction action = actionMap.get(actionType);
		if (action == null)
			ErrorLogUtil.errorLog("找不到对应的处理类",new RuntimeException(),"PreheatActivityActionType",actionType != null ? actionType : "");
		return action;
	}

	public void onEnterWorld(Role role) {
		if (!taskOpenCheck()) {
			return;
		}
		// 当前进程 非赛季服 不发
		if (Application.getServerType() != ServerType.KVK_SEASON) {
			return;
		}
		// // 玩家 是跨服过来的 参加csa活动，不发
		if (crossServerAttackService.isCSACrossPlayer(role)) {
			return;
		}
		// 脏数据检测
		roleSeasonTaskRecordDao.dirtyDataValidation(role.getPersistKey());

		if (roleSeasonTaskRecordDao.needInit(role.getPersistKey())) {
			initPlayerTask(role);
		}
		GcSeasonTaskList gcSeasonTaskList = wrapperGcSeasonTaskList(role);
		if (gcSeasonTaskList != null)
			role.send(gcSeasonTaskList);
	}

	private void initPlayerTask(Role role) {
		List<SeasonTaskConfig.SeasonTaskMeta> metaList = configService.getConfig(SeasonTaskConfig.class).getMetaList();
		if (JavaUtils.bool(metaList)) {
			for (SeasonTaskConfig.SeasonTaskMeta meta : metaList) {
				roleSeasonTaskRecordDao.create(role, meta);
			}
		}
	}

	/**
	 * 互动开关
	 * 
	 * @return
	 */
	public boolean taskOpenCheck() {
		int season = Application.getConfigCenter().getSeason();
		if (season <= 1) {
			return false;
		}
		KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = Application.getConfigCenter().getCurrentKvkSeasonServerGroupConfig();
		if (kvkSeasonServerGroupConfig == null) {
			return false;
		}
		KvkGroupConfig.KvkGroupMeta currentKvkGroupMeta = configService.getConfig(KvkGroupConfig.class).getKvkServerMeta(kvkSeasonServerGroupConfig.getKServerId());
		if (null == currentKvkGroupMeta || !currentKvkGroupMeta.isSeasonTaskSwitch()) {
			// 开关关闭了
			return false;
		}
		return true;
	}

	/**
	 * 赛季任务 触发，更新进度
	 * 
	 * @param role
	 * @param taskType
	 * @param params
	 */
	public void trigger(Role role, SeasonTaskActionType taskType, Object... params) {
		if (!taskOpenCheck()) {
			return;
		}
		AbstractSeasonTaskAction actionHandler = getActionHandler(taskType);
		if (actionHandler == null)
			return;
		try {
			actionHandler.update(role, params);
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.errorLog("赛季任务触发异常", e);
		}
	}

	/**
	 * 任务完成检测，需要判断所有行为都完成
	 * 
	 * @param role
	 * @param meta
	 * @return
	 */
	public boolean taskFinishCheck(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		RoleSeasonTaskRecord record = roleSeasonTaskRecordDao.getRecordByRoleAndMetaId(role, meta.getId());
		if (record == null)
			return false;
		if (record.isExpire()) {
			return record.isFinish();
		}
		if (record.isFinish()) {
			return true;
		}
		for (Integer actionType : record.getConditions()) {
			AbstractSeasonTaskAction actionHandler = getActionHandler(SeasonTaskActionType.findById(actionType));
			if (actionHandler == null)
				return false;
			if (!actionHandler.actionFinishCheck(role, meta))
				return false;
		}
		// 每个action都满足 任务完成
		record.setFinish(true);
		roleSeasonTaskRecordDao.save(record);
		return true;
	}

	/**
	 * 构建PsSeasonTaskInfo
	 * 
	 * @param role
	 * @param meta
	 * @return
	 */
	public PsSeasonTaskInfo toPsInfo(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		PsSeasonTaskInfo info = new PsSeasonTaskInfo();
		info.setMetaId(meta.getId());
		Map<Integer, Long> progressMap = new HashMap<>();
		RoleSeasonTaskRecord record = roleSeasonTaskRecordDao.getRecordByRoleAndMetaId(role, meta.getId());
		if (record != null) {
			for (Integer actionType : record.getConditions()) {
				var taskType = SeasonTaskActionType.findById(actionType);
				if (null == taskType) {
					continue;
				}
				if (taskType.getTaskType() == SeasonTaskType.PERSONAL) {
					progressMap.put(actionType, record.getProgress(actionType));
				} else {
					AbstractSeasonTaskAction actionHandler = getActionHandler(SeasonTaskActionType.findById(actionType));
					if (actionHandler == null) {
						continue;
					}
					if (taskType.getTaskType() == SeasonTaskType.ALLIANCE) {
						progressMap.put(actionType, actionHandler.getProgress(role, meta));
					} else if (taskType.getTaskType() == SeasonTaskType.SERVER) {
						progressMap.put(actionType, actionHandler.getProgress(role, meta));
					} else if (taskType.getTaskType() == SeasonTaskType.LEGION) {
						progressMap.put(actionType, actionHandler.getProgress(role, meta));
					}
				}
			}
		}
		info.setProgress(progressMap);
		boolean isFinish = roleSeasonTaskRecordDao.isFinish(role, meta.getId());
		info.setCompleted(isFinish);
		if (isFinish) {
			info.setAward(roleSeasonTaskRecordDao.isGetReward(role, meta.getId()));
		} else {
			info.setAward(false);
		}
		if (meta.getSettlementType() == 1) {
			// TODO 时间点结算的需要下发 结束时间， 有需要么
			// info.setEndTime()
		}
		return info;
	}

	/**
	 * 构建 请求赛季任务列表的 返回消息
	 * 
	 * @param role
	 * @return
	 */
	public GcSeasonTaskList wrapperGcSeasonTaskList(Role role) {
		GcSeasonTaskList gcMsg = new GcSeasonTaskList();
		List<SeasonTaskConfig.SeasonTaskMeta> metaList = configService.getConfig(SeasonTaskConfig.class).getMetaList();
		if (!JavaUtils.bool(metaList)) {
			return null;
		}
		for (SeasonTaskConfig.SeasonTaskMeta seasonTaskMeta : metaList) {
			PsSeasonTaskInfo psSeasonTaskInfo = null;
			try {
				psSeasonTaskInfo = toPsInfo(role, seasonTaskMeta);
			} catch (ExpectedException ignored){

			} catch (Exception e) {
				ErrorLogUtil.exceptionLog("赛季任务出错", e,
						"roleId",role.getPersistKey(), "metaId",seasonTaskMeta.getId());
				continue;
			}
			if (psSeasonTaskInfo != null) {
				gcMsg.addToRecords(psSeasonTaskInfo);
			}
		}
		return gcMsg;
	}

	/**
	 * 领取赛季任务奖励的处理
	 * 
	 * @param role
	 * @param metaId
	 */
	public void seasonTaskReward(Role role, String metaId) {
		GcGetSeasonTaskReward gcMsg = new GcGetSeasonTaskReward();
		SeasonTaskConfig.SeasonTaskMeta seasonTaskMeta = configService.getConfig(SeasonTaskConfig.class).getMetaMap().get(metaId);
		if (seasonTaskMeta == null) {
			ErrorLogUtil.errorLog("请求领奖的赛季任务配置为空", "roleId",role.getPersistKey(), "metaId",metaId);
			gcMsg.setResult(1);
			role.send(gcMsg);
			return;
		}
		RoleSeasonTaskRecord record = roleSeasonTaskRecordDao.getRecordByRoleAndMetaId(role, metaId);
		if (record == null || record.isReward()) {
			gcMsg.setResult(1);
			role.send(gcMsg);
			return;
		}
		if (!record.isFinish() && !taskFinishCheck(role, seasonTaskMeta)) {
			gcMsg.setResult(1);
			role.send(gcMsg);
			return;
		}
		// 发奖
		itemService.give(role, dropService.drop(seasonTaskMeta.getReward()), LogReasons.ItemLogReason.SEASON_TASK_REWARD);
		// bilog
		biLogUtil.kvkSeasonTaskGetReward(role, seasonTaskMeta.getId());
		// 更改领奖标志
		roleSeasonTaskRecordDao.setRewardStatus(role, seasonTaskMeta);
		// 返回消息
		gcMsg.setMetaId(metaId);
		gcMsg.setResult(0);
		role.send(gcMsg);
	}

	public void test(Role role, int level) {
		trigger(role, SeasonTaskActionType.PVE_NPC, level);
	}

	public void onFinishBattle(ArmyInfo army) {
		try {
			if (!ArmyType.isPvpBattle(army.getArmyType())) {
				return;
			}

			Role mainAttackerRole = army.getOwner();
			FightContext context = army.getFightContext();
			FightArmy defenderArmy = context.getDefender();
			Role mainDefendRole = roleManager.getRole(defenderArmy.getTargetId());
			if (mainAttackerRole == null || mainDefendRole == null) {
				return;
			}
			// 如果双方是隶属于同一个服务器，不处理
			if (mainAttackerRole.getoServerId() == mainDefendRole.getoServerId()) {
				return;
			}

			killTrigger(context.getFightResult().getAttackerLostInfo());
			killTrigger(context.getFightResult().getDefenderLostInfo());
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("赛季任务,战斗后结算异常", e);
		}
	}

	private void killTrigger(FightLostInfo lostInfo) {
		var lostIds = lostInfo.getLostMap().rowKeySet();
		if (!JavaUtils.bool(lostIds)) {
			return;
		}

		for (var lostId : lostIds) {
			Role role = roleManager.getRole(Long.parseLong(lostId));
			if (role == null) {
				continue;
			}
			var detail = lostInfo.getValueDetail(lostId, FightLostType.KILL);
			// 触发任务
			trigger(role, SeasonTaskActionType.KILL_ENEMY, detail);
		}
	}

	/**
	 * 赛季结算后，对应的赛季任务处理
	 */
	public void seasonSettled() {
		if (!taskOpenCheck()) {
			return;
		}
		//
		// 赛季结算 将未领奖的任务，邮件发给玩家
		Map<Long, List<SeasonTaskConfig.SeasonTaskMeta>> autoRewardMap = new HashMap<>();
		//
		//
		Collection<RoleSeasonTaskRecord> all = roleSeasonTaskRecordDao.findAll();
		if (JavaUtils.bool(all)) {
			for (RoleSeasonTaskRecord record : all) {
				SeasonTaskConfig.SeasonTaskMeta meta = configService.getConfig(SeasonTaskConfig.class).getMetaMap().get(record.getMetaId());
				if (meta == null) {
					continue;
				}
				if (record.isReward()) {// 已经领过奖的 continue；
					continue;
				}
				Role role = roleManager.getRole(record.getRoleId());
				if (role == null) {
					logger.info("赛季结算，赛季任务发奖，玩家{}不再内存，忽略", record.getRoleId());
					continue;
				}
				if (taskFinishCheck(role, meta)) {
					record.setReward(true);
					autoRewardMap.compute(record.getRoleId(), (k, v) -> v == null ? new ArrayList<>() : v).add(meta);
					roleSeasonTaskRecordDao.save(record);
				}
				if (meta.getSettlementType() == 1) {
					record.setExpire(true);
					roleSeasonTaskRecordDao.save(record);
				}
			}
		}
		if (JavaUtils.bool(autoRewardMap)) {
			asyncOperationService.execute(new SeasonTaskSettlementOperation(this, autoRewardMap));
		}
	}

	public void sendSeasonTaskRewardMail(Map<Long, List<SeasonTaskConfig.SeasonTaskMeta>> autoRewardMap) {
		List<AbstractEmail> sendMailList = new ArrayList<>();
		for (Map.Entry<Long, List<SeasonTaskConfig.SeasonTaskMeta>> entry : autoRewardMap.entrySet()) {
			if (entry.getValue() == null)
				continue;
			Role role = roleManager.getRole(entry.getKey());
			if (role == null) {
				logger.info("赛季结算，赛季任务奖励邮件，玩家{}不再内存，忽略", entry.getKey());
				continue;
			}
			try {
				List<SimpleItem> dropTmp = new ArrayList<>();
				for (SeasonTaskConfig.SeasonTaskMeta meta : entry.getValue()) {
					dropTmp.addAll(dropService.drop(meta.getReward()));
				}
				List<String> params = new ArrayList<>();
				params.add(Application.getConfigCenter().getSeason() + "");
				sendMailList.add(mailService.createSystemMail(role, EmailConstants.SEASON_TASK_REWARD_MAIL, DropServiceImpl.dropsMerge(dropTmp), params));
			} catch (ExpectedException ignored){

			} catch (Exception e) {
				ErrorLogUtil.exceptionLog("赛季结算,赛季任务奖励邮件异常",e,"key", entry.getKey());
			}
		}
		if (JavaUtils.bool(sendMailList))
			Application.getBean(MailSender.class).sendBatchMail(sendMailList);
	}

	/**
	 * 赛季变更
	 */
	public void seasonChange() {
		roleSeasonTaskRecordDao.clearPreSeasonData();
		Application.getBean(AllianceSeasonTaskProgressDao.class).clearPreSeasonData();
		Application.getBean(ServerSeasonTaskProgressDao.class).clearPreSeasonData();
		Application.getBean(LegionSeasonTaskProgressDao.class).clearPreSeasonData();
	}

}
