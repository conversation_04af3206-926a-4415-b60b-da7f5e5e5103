package com.lc.billion.icefire.gvgcontrol.biz.model.activity.gvg;

import com.lc.billion.icefire.protocol.constant.PsGVGActivityStatus;
import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;

/**
 * <AUTHOR>
 *
 */
public enum GVGActivityStatus implements IntEnum {

	/** 普通gvg准备阶段 gvg杯赛约战阶段1*/
	READY(PsGVGActivityStatus.READY, PsGVGActivityStatus.REGISTER),
	/** 普通gvg报名阶段 gvg杯赛约战阶段2*/
	REGISTER(PsGVGActivityStatus.REGISTER,PsGVGActivityStatus.SIGNUP),
	/** 普通gvg约战阶段 */
	SIGNUP(PsGVGActivityStatus.SIGNUP, PsGVGActivityStatus.ADMITTANCE_ADVANCE),
	/** 提前入场阶段 */
	ADMITTANCE_ADVANCE(PsGVGActivityStatus.ADMITTANCE_ADVANCE, PsGVGActivityStatus.ADMITTANCE),
	/** 入场阶段 */
	ADMITTANCE(PsGVGActivityStatus.ADMITTANCE, PsGVGActivityStatus.READY),
	// /** 休赛阶段 */
	// OFFSEASON(PsGVGActivityStatus.OFFSEASON, PsGVGActivityStatus.READY),

	;

	private static final GVGActivityStatus[] INDEXES = EnumUtils.toArray(values());

	private PsGVGActivityStatus gvgActivityStatus;

	private PsGVGActivityStatus nextGvgActivityStatus;

	private GVGActivityStatus(PsGVGActivityStatus gvgActivityStatus, PsGVGActivityStatus nextGvgActivityStatus) {
		this.gvgActivityStatus = gvgActivityStatus;
		this.nextGvgActivityStatus = nextGvgActivityStatus;
	}

	@Override
	public int getId() {
		return gvgActivityStatus.getValue();
	}

	public PsGVGActivityStatus getGvgActivityStatus() {
		return gvgActivityStatus;
	}

	public static GVGActivityStatus findById(int id) {
		if (id < 0 || id >= INDEXES.length) {
			return null;
		}
		return INDEXES[id];
	}

	public PsGVGActivityStatus getNextGvgActivityStatus() {
		return nextGvgActivityStatus;
	}
}
