package com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.RZEAllianceInfo;
import com.lc.billion.icefire.gvgcontrol.biz.model.RZEQualifiedServer;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

@Repository
public class RZEAllianceInfoDao extends RootDao<RZEAllianceInfo> {
    public RZEAllianceInfoDao() {
        super(RZEAllianceInfo.class, false);
    }

    @Override
    protected MongoCursor<RZEAllianceInfo> doFindAll(int db) {
        return dbFindAllForWorldEntity(db);
    }

    @Override
    protected void putMemoryIndexes(RZEAllianceInfo entity) {

    }

    @Override
    protected void removeMemoryIndexes(RZEAllianceInfo entity) {

    }

    public RZEAllianceInfo create(Long allianceId, int serverId, long rating, long s1Prosperity, String name, String aliasName) {
        int db = Application.getServerId();
        RZEAllianceInfo rzeAllianceInfo = newEntityInstance();
        rzeAllianceInfo.setPersistKey(Long.valueOf(allianceId));
        rzeAllianceInfo.setServerId(serverId);
        rzeAllianceInfo.setS1Prosperity(s1Prosperity);
        rzeAllianceInfo.setRating(rating);
        rzeAllianceInfo.setName(name);
        rzeAllianceInfo.setAliasName(aliasName);
        return createEntity(db, rzeAllianceInfo);
    }
}
