package com.lc.billion.icefire.gvgbattle.biz.service.impl;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.model.prop.AttributeValue;
import com.lc.billion.icefire.game.biz.model.prop.CalcPropContainer;
import com.lc.billion.icefire.game.biz.model.prop.Prop;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.prop.AbstractRoleCalcPropEffect;
import com.lc.billion.icefire.game.biz.service.impl.prop.extention.PropSource;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgBuildingConfig;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.StrongHoldNode;
import com.simfun.sgf.utils.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * gvg占领建筑的效果号
 * */
@Service
public class RoleGVGCalcPropEffect extends AbstractRoleCalcPropEffect {
    @Autowired
    protected GVGStrongHoldService GVGStrongHoldService;
    @Autowired
    protected GVGBuildingService gvgBuildingService;

    @Override
    public Type getType() {
        return RoleType.GVG_BUILDING;
    }

    @Override
    public PropSource getSource() {
        return PropSource.GVG_BUILDING;
    }

    @Override
    public void calcProps(Role role, CalcPropContainer<?> propCtn, Object... extParam) {
        if(!JavaUtils.bool(role.getAllianceId())) return;
        if(!Application.isBattleServer()) return;

        propExtentionService.reCalProp(role.getPersistKey(),getSource());

        // 总buff
        Map<Prop, Double> props = GVGStrongHoldService.getAllStrongHoldBuff();
        List<StrongHoldNode> nodes =  GVGStrongHoldService.getAllianceOccupyBuildings(role.getAllianceId());
        long now = TimeUtil.getNow();
        GvgBuildingConfig gvgBuildingConfig = configService.getConfig(GvgBuildingConfig.class);
        for(StrongHoldNode node : nodes){
            // 这个判断的目的是：防止玩家在buff CD期间，利用重新登陆来刷buff，导致buff生效
            if((node.getOccupyTime() > 0 && now - node.getOccupyTime() >= 0) || node.getOccupyTime() == 0){
                List<AttributeValue> gvgBuildingAttribute = gvgBuildingService.getGVGBuildingAttribute(node.getMetaId());
                if (!JavaUtils.bool(gvgBuildingAttribute)) {
                    continue;
                }
                for(AttributeValue att : gvgBuildingAttribute) {
                    props.compute(att.getAttributeType(), (k, v) -> v != null ? v + att.getValue() : att.getValue());
                }
            }
        }

//        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
//        AttributeValue attributeHospital = gvgSettingConfig.getGvgHospitalCapacityAddRate();
//        props.compute(attributeHospital.getAttributeType(), (k,v) -> v != null ? v + attributeHospital.getValue() : attributeHospital.getValue());
//
//        AttributeValue attributeSpeed = gvgSettingConfig.getGvgSpeedAddRate();
//        props.compute(attributeSpeed.getAttributeType(), (k,v) -> v != null ? v + attributeSpeed.getValue() : attributeSpeed.getValue());
//
//        AttributeValue attributeHospitalAddSpeed = gvgSettingConfig.getGvgHospitalAddSpeedRate();
//        props.compute(attributeHospitalAddSpeed.getAttributeType(), (k,v) -> v != null ? v + attributeHospitalAddSpeed.getValue() : attributeHospitalAddSpeed.getValue());
//
//        AttributeValue attributeHospitalReduceResource = gvgSettingConfig.getGvgHospitalReduceResourceRate();
//        props.compute(attributeHospitalReduceResource.getAttributeType(), (k,v) -> v != null ? v + attributeHospitalReduceResource.getValue() : attributeHospitalReduceResource.getValue());

        for(Prop key : props.keySet()){
            propCtn.set(key, props.get(key));
            propExtentionService.addProp(role.getPersistKey(), getSource(), key, props.get(key));
        }
    }
}
