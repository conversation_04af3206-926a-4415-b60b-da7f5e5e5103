package com.lc.billion.icefire.gvgcontrol.biz.service.rpc.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGAllianceSignUpInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GVGAllianceSignUpInfoDao;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GVGBattleRecordDao;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GvgBattleServerDispatchRecordDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGBattleRecord;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgBattleServerDispatchRecord;
import com.lc.billion.icefire.gvgcontrol.biz.service.GVGControlService;
import com.longtech.ls.rpc.IWebRemoteGVGControlService;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 *
 */
@Service
public class WebRemoteGVGControlServiceImpl implements IWebRemoteGVGControlService {
	private static final Logger logger = LoggerFactory.getLogger(WebRemoteGVGControlServiceImpl.class);

	@Autowired
	private GVGBattleRecordDao gvgBattleRecordDao;
	@Autowired
	private GVGControlService gvgControlService;
	@Autowired
	private GvgBattleServerDispatchRecordDao gvgBattleServerDispatchRecordDao;
	@Autowired
	private GVGAllianceSignUpInfoDao gvgAllianceSignUpInfoDao;
	@Autowired
	private ActivityDao activityDao;

	@Override
	public List<Integer> getGVGControlServerBroadcastFailGameServers() {
		Collection<GVGBattleRecord> gvgBattleRecords = gvgBattleRecordDao.findAll();
		if (JavaUtils.bool(gvgBattleRecords)) {
			List<Integer> serverIds = new ArrayList<>();
			gvgBattleRecords.forEach(gvgBattleRecord -> {
				if (!gvgBattleRecord.isSendAward()) {
					serverIds.add(gvgBattleRecord.getBattleServerId());
				}
			});
			return serverIds;
		}
		return Collections.emptyList();
	}

	@Override
	public void updateGVGControlServerBroadcastFailInfo() {
		gvgControlService.uploadGVGBattleRecordAndDeleteDispatchRecordAgain();
	}

	@Override
	public JSONArray findBattleServers() {
		JSONArray ret = new JSONArray();
		Collection<GvgBattleServerDispatchRecord> gvgBattleServerDispatchRecords = gvgBattleServerDispatchRecordDao.findAll();
		if (JavaUtils.bool(gvgBattleServerDispatchRecords)) {
			for (GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord : gvgBattleServerDispatchRecords) {
				JSONObject jsonObject = new JSONObject();
				jsonObject.put("battleServerId", gvgBattleServerDispatchRecord.getBattleServerId());
				jsonObject.put("battleStartTime", gvgBattleServerDispatchRecord.getBattleStartTime());
				jsonObject.put("status", gvgBattleServerDispatchRecord.getStatus());
				jsonObject.put("battleTurn", gvgBattleServerDispatchRecord.getBattleTurn());
				ret.add(jsonObject);
			}
		}
		return ret;
	}

	/**请求GVG的匹配信息*/
	@Override
	public JSONArray findGVGMatchInfo(){
		JSONArray ret = new JSONArray();
		Collection<GvgBattleServerDispatchRecord> gvgBattleServerDispatchRecords = gvgBattleServerDispatchRecordDao.findAll();
		if (JavaUtils.bool(gvgBattleServerDispatchRecords)) {
			for (GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord : gvgBattleServerDispatchRecords) {
				JSONObject jsonObject = new JSONObject();
				jsonObject.put("battleServerID", gvgBattleServerDispatchRecord.getBattleServerId());

				if(JavaUtils.bool(gvgBattleServerDispatchRecord.getAllianceId1())){
					jsonObject.put("alliance1ID", gvgBattleServerDispatchRecord.getAllianceId1());
					jsonObject.put("alliance1ServerID", gvgBattleServerDispatchRecord.getAlliance1ServerId());
					GVGAllianceSignUpInfo gvgAlliance1SignUpInfo = gvgAllianceSignUpInfoDao.findById(gvgBattleServerDispatchRecord.getAllianceId1());
					if(gvgAlliance1SignUpInfo != null){
						jsonObject.put("alliance1MemCnt", gvgAlliance1SignUpInfo.getGvgAllianceLineUpInfo().getFormalMemberIds().size());
					}else {
						jsonObject.put("alliance1MemCnt","-");
					}
				}else {
					jsonObject.put("alliance1ID", "-");
					jsonObject.put("alliance1ServerID", "-");
					jsonObject.put("alliance1MemCnt", "-");
				}

				if(JavaUtils.bool(gvgBattleServerDispatchRecord.getAllianceId2())){
					jsonObject.put("alliance2ID", gvgBattleServerDispatchRecord.getAllianceId2());
					jsonObject.put("alliance2ServerID", gvgBattleServerDispatchRecord.getAlliance2ServerId());
					GVGAllianceSignUpInfo gvgAlliance2SignUpInfo = gvgAllianceSignUpInfoDao.findById(gvgBattleServerDispatchRecord.getAllianceId2());
					if(gvgAlliance2SignUpInfo != null){
						jsonObject.put("alliance2MemCnt", gvgAlliance2SignUpInfo.getGvgAllianceLineUpInfo().getFormalMemberIds().size());
					}else {
						jsonObject.put("alliance2MemCnt","-");
					}
				}else {
					jsonObject.put("alliance2ID", "-");
					jsonObject.put("alliance2ServerID", "-");
					jsonObject.put("alliance2MemCnt", "-");
				}

				jsonObject.put("battleStartTime", gvgBattleServerDispatchRecord.getBattleStartTime());
				jsonObject.put("battleEndTime", gvgBattleServerDispatchRecord.getBattleDestroyTime());
				jsonObject.put("battleTurn", gvgBattleServerDispatchRecord.getBattleTurn());
				ret.add(jsonObject);
			}
		}

		return ret;
	}

	/**清理GVG活动数据*/
	@Override
	public int dropGVGActivity(){
		Collection<Activity> activities = activityDao.findAll();
		List<Long> removeIds = new ArrayList<>();
		if(JavaUtils.bool(activities)){
			activities.forEach(activity -> {
				removeIds.add(activity.getPersistKey());
			});

			for(Long activityId : removeIds){
				activityDao.delete(activityId);
				logger.info("dropGVGActivity GVG活动{}", activityId);
			}

			return 0;
		}else {
			logger.info("dropGVGActivity GVG活动数据为空");
		}

		return -1;
	}

	@Override
	public void settleGvgTournamentGroupList(int warZoneId, int groupSize) {
		gvgControlService.settleGvgTournamentGroupList(warZoneId,groupSize);
	}
}
