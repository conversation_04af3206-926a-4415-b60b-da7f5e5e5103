package com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action;

/**
 * <AUTHOR>
 * @date 2021/11/16
 */
public enum SeasonTaskType {
	PERSONAL(1), // 个人

	ALLIANCE(2), // 联盟

	SERVER(3), // 服务器

	LEGION(4), // 军团

	;

	private int id;

	SeasonTaskType(int id) {
		this.id = id;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public static SeasonTaskType findById(int value) {
		SeasonTaskType[] values = values();
		if (values == null)
			return null;
		for (SeasonTaskType type : values)
			if (type.getId() == value)
				return type;
		return null;
	}
}
