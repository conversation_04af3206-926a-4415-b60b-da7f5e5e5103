package com.lc.billion.icefire.kvkseason.biz.dao.mongo.root;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ConcurrentMap;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.kvkseason.biz.model.kindombuff.KingdomBuff;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 * @date 2021/9/13
 */
@Repository
public class KingdomBuffDao extends RootDao<KingdomBuff> {

	// key vlaue 含义 ：<belongServerId， List<KingdomBuff>> 哪个服的王国buff集合
	private ConcurrentMap<Integer, ConcurrentLinkedQueue<KingdomBuff>> kingdomBuffMap = new ConcurrentHashMap<>();

	protected KingdomBuffDao() {
		super(KingdomBuff.class, true);
	}

	@Override
	protected void putMemoryIndexes(KingdomBuff entity) {
		kingdomBuffMap.compute(entity.getBelongServerId(), (k, v) -> v == null ? new ConcurrentLinkedQueue<>() : v).add(entity);
	}

	@Override
	protected void removeMemoryIndexes(KingdomBuff entity) {
		kingdomBuffMap.compute(entity.getBelongServerId(), (k, v) -> v == null ? new ConcurrentLinkedQueue<>() : v).remove(entity);
	}

	@Override
	protected MongoCursor<KingdomBuff> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	/**
	 * 查找属于 belobelongServerId服的 buff
	 * 
	 * @param belongServerId
	 * @param buffId
	 * @return
	 */
	public KingdomBuff findBuff(int belongServerId, String buffId) {
		Collection<KingdomBuff> buffs = kingdomBuffMap.get(belongServerId);
		if (!JavaUtils.bool(buffs))
			return null;
		for (KingdomBuff buff : buffs) {
			if (buff.getBuffId().equals(buffId))
				return buff;
		}
		return null;
	}

	/**
	 * 查找某个服的 王国buff
	 * 
	 * @param belongServerId
	 * @return
	 */
	public List<KingdomBuff> findBuff(int belongServerId) {
		ConcurrentLinkedQueue<KingdomBuff> kingdomBuffs = kingdomBuffMap.get(belongServerId);
		if (JavaUtils.bool(kingdomBuffs)) {
			List<KingdomBuff> ret = new ArrayList<>();
			for (KingdomBuff kingdomBuff : kingdomBuffs) {
				ret.add(kingdomBuff);
			}
			return ret;
		}
		return null;
	}

	/**
	 * 根据buff 所属服 和 buff 生效服，查找buff
	 * 
	 * @param belongServerId
	 * @return
	 */
	public Collection<KingdomBuff> findBuffsByBelongServerIdAndCurrentServerId(int belongServerId, int effectServerId) {
		List<KingdomBuff> ret = new ArrayList<>();
		Collection<KingdomBuff> buffs = kingdomBuffMap.get(belongServerId);
		if (!JavaUtils.bool(buffs))
			return ret;
		for (KingdomBuff buff : buffs)
			if (buff.getEffectServerIds().contains(effectServerId))
				ret.add(buff);
		return ret;

	}

	public KingdomBuff create(int belongServerId, List<Integer> effectServerIds, String buffId, long createTime, long endTime) {
		KingdomBuff buff = newEntityInstance();
		buff.setBelongServerId(belongServerId);
		buff.setEffectServerIds(effectServerIds);
		buff.setBuffId(buffId);
		buff.setEndTime(endTime);
		return createEntity(belongServerId, buff);
	}

}