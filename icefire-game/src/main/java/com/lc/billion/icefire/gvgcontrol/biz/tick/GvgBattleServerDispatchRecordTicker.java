package com.lc.billion.icefire.gvgcontrol.biz.tick;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.lc.billion.icefire.game.biz.async.asyncIO.AsyncIOThreadOperation;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.common.AbstractTicker;
import com.lc.billion.icefire.gvgcontrol.biz.GVGBattleServerCreateConfig;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GvgBattleServerDispatchRecordDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgBattleServerDispatchRecord;
import com.lc.billion.icefire.gvgcontrol.biz.service.GVGControlService;
import com.simfun.sgf.utils.JavaUtils;
import com.simfun.sgf.utils.TimeUtils;

/**
 * <AUTHOR>
 *
 */
@Service
public class GvgBattleServerDispatchRecordTicker extends AbstractTicker<Long> {

	// private static final Logger log =
	// LoggerFactory.getLogger(GvgBattleServerDispatchRecordTicker.class);

	// private static final int DESTROY_PEROID_MINUTE = 10;

	@Autowired
	private GvgBattleServerDispatchRecordDao gvgBattleServerDispatchRecordDao;
	@Autowired
	private GVGControlService gvgControlService;
	@Autowired
	private AsyncOperationServiceImpl asyncOperationService;

	// private long lastDestroyTime;

	public GvgBattleServerDispatchRecordTicker() {
		super(10 * TimeUtils.SECONDS_MILLIS);
	}

	@Override
	protected void tick(Long battleTime, long now) {
		List<GvgBattleServerDispatchRecord> gvgBattleServerDispatchRecords = gvgBattleServerDispatchRecordDao.findByTime(battleTime);
		if (!JavaUtils.bool(gvgBattleServerDispatchRecords)) {
			return;
		}
		GVGBattleServerCreateConfig gvgBattleServerCreateConfig = ServerConfigManager.getInstance().getGvgBattleServerCreateConfig();
		List<Integer> createBattleServerIds = new ArrayList<>();
		List<Integer> statusBattleServerIds = new ArrayList<>();
		List<Integer> destroyBattleServerIds = new ArrayList<>();
		int battleTurn = 0;
		for (GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord : gvgBattleServerDispatchRecords) {
			battleTurn = gvgBattleServerDispatchRecord.getBattleTurn();
			switch (gvgBattleServerDispatchRecord.getStatus()) {
			case GvgBattleServerDispatchRecord.STATUS_MATCH:
				// 60分钟内立即执行
				int checkTime = gvgBattleServerCreateConfig.getCheckTime();
				if (checkTime == 0) {
					checkTime = 60;
				}
				if (now > battleTime - checkTime * TimeUtil.MINUTE_MILLIS) {
					gvgBattleServerDispatchRecord.setStatus(GvgBattleServerDispatchRecord.STATUS_NOTICE);
					gvgBattleServerDispatchRecordDao.save(gvgBattleServerDispatchRecord);
					createBattleServerIds.add(gvgBattleServerDispatchRecord.getBattleServerId().intValue());
				}
				break;
			case GvgBattleServerDispatchRecord.STATUS_NOTICE:
				// 30分支查看状态，报警
				int statusTime = gvgBattleServerCreateConfig.getStatusTime();
				if (now > battleTime - statusTime * TimeUtil.MINUTE_MILLIS) {
					gvgBattleServerDispatchRecord.setStatus(GvgBattleServerDispatchRecord.STATUS_RENOTICE);
					gvgBattleServerDispatchRecordDao.save(gvgBattleServerDispatchRecord);
					statusBattleServerIds.add(gvgBattleServerDispatchRecord.getBattleServerId().intValue());
				}
				break;
			case GvgBattleServerDispatchRecord.STATUS_START:
				// 啥都不干
				break;
			case GvgBattleServerDispatchRecord.STATUS_RENOTICE:
				// 啥都不干
				break;
			case GvgBattleServerDispatchRecord.STATUS_END:
				// 销毁
				if (gvgBattleServerDispatchRecord.getBattleDestroyTime() < now) {
					// if (lastDestroyTime + DESTROY_PEROID_MINUTE * TimeUtil.MINUTE_MILLIS < now) {
					// lastDestroyTime = now;
					destroyBattleServerIds.add(gvgBattleServerDispatchRecord.getBattleServerId().intValue());
				}
				break;
			default:
				break;
			}
		}

		int _battleTurn = battleTurn;
		//创建、查询、销毁战斗服的操作由同步改为异步。 2021/10/18
		asyncOperationService.execute(new AsyncIOThreadOperation() {
			@Override
			public boolean run() {
				gvgControlService.noticeCreateBattleServer(createBattleServerIds, _battleTurn);
				gvgControlService.reNoticeCreateBattleServer(statusBattleServerIds, _battleTurn);
				gvgControlService.noticeDestroy(destroyBattleServerIds, _battleTurn);
				return false;
			}

			@Override
			public void finish() {
			}
		});

	}

	@Override
	protected Collection<Long> findAll() {
		return gvgBattleServerDispatchRecordDao.findBattleTimes();
	}

}
