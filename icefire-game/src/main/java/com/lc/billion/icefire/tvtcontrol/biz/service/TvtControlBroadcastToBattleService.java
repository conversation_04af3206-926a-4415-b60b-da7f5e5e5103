package com.lc.billion.icefire.tvtcontrol.biz.service;

import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgBattleServerDispatchRecord;
import com.lc.billion.icefire.rpc.service.tvt.ITvtControlRemoteTvtBattleService;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGAllianceSignUpInfoVo;
import com.lc.billion.icefire.rpc.vo.gvg.GvgBattleServerDispatchRecordVo;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtBattleServerDispatchRecord;
import com.lc.billion.icefire.tvtcontrol.biz.service.rpc.TVTControlRPCToTVTBattleProxyService;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * TVT中控服调用TVT战斗服
 */
@Service
public class TvtControlBroadcastToBattleService {
    private static final Logger logger = LoggerFactory.getLogger(TvtControlBroadcastToBattleService.class);

    @Autowired
    private TVTControlRPCToTVTBattleProxyService tvtControlRPCToTVTBattleProxyService;

    public void broadcastTVTBattleServerDispatchRecord(TvtBattleServerDispatchRecord tvtBattleServerDispatchRecord) {
        // 把TVT的匹配记录转换为GvgBattleServerDispatchRecord，对TVT战斗服透明
        GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord = toGVGBattleServerDispatchRecord(tvtBattleServerDispatchRecord);
        int serverId = gvgBattleServerDispatchRecord.getBattleServerId().intValue();
        ITvtControlRemoteTvtBattleService tvtControlRemoteTvtBattleService = tvtControlRPCToTVTBattleProxyService.getTVTControlRemoteTVTBattleService(0);
        if (tvtControlRemoteTvtBattleService != null) {
            logger.info("中控服广播对阵信息给战斗服");
            tvtControlRemoteTvtBattleService.broadcastTVTBattleServerDispatchRecord(new GvgBattleServerDispatchRecordVo(gvgBattleServerDispatchRecord));
        } else {
            ErrorLogUtil.errorLog("中控服广播对阵信息给战斗服时找不到战斗服0");
        }
    }

    /**
     * 广播TVT活动信息给所有战斗服
     */
    public void broadcastTVTActivity(Activity activity) {
        if (activity == null) {
            return;
        }
        Collection<ITvtControlRemoteTvtBattleService> tvtControlRemoteTvtBattleServices = tvtControlRPCToTVTBattleProxyService.getTVTControlRemoteTVTBattleServices();
        if (JavaUtils.bool(tvtControlRemoteTvtBattleServices)) {
            ActivityVo activityVo = new ActivityVo(activity);
            for (ITvtControlRemoteTvtBattleService tvtControlRemoteTvtBattleService : tvtControlRemoteTvtBattleServices) {
                tvtControlRemoteTvtBattleService.broadcastTVTActivity(activityVo);
            }
        } else {
            ErrorLogUtil.errorLog("中控服找不到战斗服的rpc连接不广播");
        }
    }

    /**
     * 中控服广播给战斗服，启服及分配后
     *
     */
    public void broadcastTVTAllianceSignUpInfo(TvtBattleServerDispatchRecord tvtBattleServerDispatchRecord) {
        // 把TVT的报名信息转换为GVGAllianceSignUpInfo，对TVT战斗服透明
        ITvtControlRemoteTvtBattleService tvtControlRemoteTvtBattleService = tvtControlRPCToTVTBattleProxyService.getTVTControlRemoteTVTBattleService(tvtBattleServerDispatchRecord.getBattleServerId().intValue());
        if (tvtControlRemoteTvtBattleService != null) {
            tvtControlRemoteTvtBattleService.broadcastTVTAllianceSignUpInfo(toGVGAllianceSignUpInfoVos(tvtBattleServerDispatchRecord));
        } else {
            ErrorLogUtil.errorLog("中控服找不到战斗服的rpc连接不广播");
        }
    }

    private GvgBattleServerDispatchRecord toGVGBattleServerDispatchRecord(TvtBattleServerDispatchRecord tvtBattleServerDispatchRecord){
        GvgBattleServerDispatchRecord gvgBattleServerDispatchRecord = new GvgBattleServerDispatchRecord();
        gvgBattleServerDispatchRecord.setBattleServerId(tvtBattleServerDispatchRecord.getBattleServerId());
        gvgBattleServerDispatchRecord.setBattleStartTime(tvtBattleServerDispatchRecord.getBattleStartTime());
        gvgBattleServerDispatchRecord.setBattleDestroyTime(tvtBattleServerDispatchRecord.getBattleDestroyTime());
        gvgBattleServerDispatchRecord.setStatus(tvtBattleServerDispatchRecord.getStatus());
        gvgBattleServerDispatchRecord.setBattleTurn(tvtBattleServerDispatchRecord.getBattleTurn());

        return gvgBattleServerDispatchRecord;
    }

    private List<GVGAllianceSignUpInfoVo> toGVGAllianceSignUpInfoVos(TvtBattleServerDispatchRecord tvtBattleServerDispatchRecord){
        List<GVGAllianceSignUpInfoVo> gvgAllianceSignUpInfoVos = new ArrayList<>();
        if(JavaUtils.bool(tvtBattleServerDispatchRecord.getRedLineup())){
            GVGAllianceSignUpInfoVo gvgAllianceSignUpInfoVo = new GVGAllianceSignUpInfoVo();
            List<Long> formalMemberIds = new ArrayList<>();
            tvtBattleServerDispatchRecord.getRedLineup().forEach( redMember -> formalMemberIds.add(redMember.getRoleId()));
            gvgAllianceSignUpInfoVo.getGvgAllianceLineUpInfo().setFormalMemberIds(formalMemberIds);
            gvgAllianceSignUpInfoVo.setTeamId(1);
            gvgAllianceSignUpInfoVos.add(gvgAllianceSignUpInfoVo);
        }

        if(JavaUtils.bool(tvtBattleServerDispatchRecord.getBlueLineup())){
            GVGAllianceSignUpInfoVo gvgAllianceSignUpInfoVo = new GVGAllianceSignUpInfoVo();
            List<Long> formalMemberIds = new ArrayList<>();
            tvtBattleServerDispatchRecord.getBlueLineup().forEach( blueMember -> formalMemberIds.add(blueMember.getRoleId()));
            gvgAllianceSignUpInfoVo.getGvgAllianceLineUpInfo().setFormalMemberIds(formalMemberIds);
            gvgAllianceSignUpInfoVo.setTeamId(2);
            gvgAllianceSignUpInfoVos.add(gvgAllianceSignUpInfoVo);
        }

        return gvgAllianceSignUpInfoVos;
    }

}
