package com.lc.billion.icefire.gvgcontrol.biz.model.activity.context;

import java.util.HashMap;
import java.util.Map;

import com.lc.billion.icefire.game.biz.model.activity.ActivityContext;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.gvg.GVGActivityStatus;
import com.lc.billion.icefire.protocol.constant.PsGvgMatchType;
import lombok.Getter;

/**
 * <AUTHOR>
 *
 */
public class GVGActivityContext extends ActivityContext {

	private int round;
	/**
	 * 当前阶段
	 */
	private GVGActivityStatus gvgActivityStatus;
	/**
	 * 下一阶段时间
	 */
	private long nextGvgActivityStatusTime;
	@Deprecated
	private long battleStartTime;

	private Map<Integer, GVGActivitySignUpInfo> signUpInfos;

	@Getter
	private final Map<GvgMatchType, Long> battleStartTimes = new HashMap<>();

	public int getRound() {
		return round;
	}

	public void setRound(int round) {
		this.round = round;
	}

	public GVGActivityStatus getGvgActivityStatus() {
		return gvgActivityStatus;
	}

	public void setGvgActivityStatus(GVGActivityStatus gvgActivityStatus) {
		this.gvgActivityStatus = gvgActivityStatus;
	}

	public long getNextGvgActivityStatusTime() {
		return nextGvgActivityStatusTime;
	}

	public void setNextGvgActivityStatusTime(long nextGvgActivityStatusTime) {
		this.nextGvgActivityStatusTime = nextGvgActivityStatusTime;
	}

	public Map<Integer, GVGActivitySignUpInfo> getSignUpInfos() {
		return signUpInfos;
	}

	public void setSignUpInfos(Map<Integer, GVGActivitySignUpInfo> signUpInfos) {
		this.signUpInfos = signUpInfos;
	}


	@Override
	public String toString(){
		return "GVGActivityContext [round=" + round
				+ ", gvgActivityStatus=" + gvgActivityStatus
				+ ", nextGvgActivityStatusTime=" + nextGvgActivityStatusTime
				+ ", battleStartTime=" + battleStartTime
				+ "]";
	}
}
