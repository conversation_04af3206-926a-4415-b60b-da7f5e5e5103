package com.lc.billion.icefire.gvgcontrol.biz.config;

import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;

import java.util.List;
import java.util.Map;

/**
 * gvg 观赛 相关配置
 */
@Config(name = "GvgObServer", metaClass = GvgObServerConfig.GvgObServerMeta.class)
public class GvgObServerConfig {

    //通过obId配置的比赛
    private String[] matchFromObId;
    //通过战斗服id配置的比赛
    private String[] matchFromBattleServerId;
    //单场最大可进入观众人数
    private int maxObservers;
    //观赛消耗门票的道具id
    private String obTicketItemId;
    /**
     * matchFromObId
     * matchFromBattleServerId
     * maxObservers
     * ticketItemId
     */
    @MetaMap
    private Map<String, GvgObServerConfig.GvgObServerMeta> idMap;

    public void init(List<GvgObServerConfig.GvgObServerMeta> list) {

        list.forEach(m -> {
            String name = m.getName();
            String value = m.getValue();
            if (name.equals("matchFromObId")) {
                if (!value.equals("-1")) {
                    matchFromObId = value.split("[|]");
                }
            } else if (name.equals("matchFromBattleServerId")) {
                if (!value.equals("-1")) {
                    matchFromBattleServerId = value.split("[|]");
                }
            } else if (name.equals("maxObservers")) {
                maxObservers = Integer.parseInt(value);
            } else if (name.equals("ticketItemId")) {
                obTicketItemId = value;
            }
        });

    }

    public String[] getMatchFromObId() {
        return matchFromObId;
    }

    public String[] getMatchFromBattleServerId() {
        return matchFromBattleServerId;
    }

    public int getMaxObservers() {
        return maxObservers;
    }

    public String getObTicketItemId() {
        return obTicketItemId;
    }

    public Map<String, GvgObServerMeta> getIdMap() {
        return idMap;
    }

    public static class GvgObServerMeta extends AbstractMeta {
        private String name;
        private String value;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

}
