package com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.impl;

import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.kvk.SeasonTaskConfig;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.KvkHonorLegionDao;
import com.lc.billion.icefire.kvkseason.biz.model.honor.KvkHonorLegion;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.RoleSeasonTaskRecord;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.AbstractSeasonTaskAction;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.SeasonTaskActionType;

/**
 * <AUTHOR>
 * @Date 2022/3/16
 */
@Service
public class LegionHonor extends AbstractSeasonTaskAction {
	@Override
	public SeasonTaskActionType getType() {
		return SeasonTaskActionType.LEGION_HONOR;
	}

	@Override
	public long getProgress(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		KvkHonorLegion kvkHonorLegion = Application.getBean(KvkHonorLegionDao.class).findById(legionService.getLegionId(role));
		if (kvkHonorLegion == null) {
			return 0;
		}
		return kvkHonorLegion.getHonorValue();
	}

	@Override
	protected void updateProgress(Role role, RoleSeasonTaskRecord taskRecord, Object... params) {
		// do nothing
	}

	@Override
	public boolean actionFinishCheck(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		int i = meta.getConditions().indexOf(getType().getId());
		int param1 = meta.getParam1s().get(i);
		return getProgress(role, meta) >= param1;
	}
}
