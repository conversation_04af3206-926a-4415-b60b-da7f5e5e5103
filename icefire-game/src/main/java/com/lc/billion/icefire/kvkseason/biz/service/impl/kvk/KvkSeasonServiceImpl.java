package com.lc.billion.icefire.kvkseason.biz.service.impl.kvk;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.config.SeasonConfig;
import com.lc.billion.icefire.game.biz.config.SeasonSafeZoneConfig;
import com.lc.billion.icefire.game.biz.config.SeasonTimeConfig;
import com.lc.billion.icefire.game.biz.config.kvk.KvkGroupConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.KvkHistoryAllianceProsperityDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.KvkHistoryLegionProsperityDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ServerInfoDao;
import com.lc.billion.icefire.game.biz.manager.AllianceManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.manager.SeasonWarmUpActivityService;
import com.lc.billion.icefire.game.biz.model.ServerInfo;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.kvk.KvkHistoryLegionProsperity;
import com.lc.billion.icefire.game.biz.model.role.King;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.ServerInfoServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.officials.UpdateKingOperation;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankContext;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankType;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.kvkseason.biz.async.KvkStageChangeNoticeOperation;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.roles.RoleSeasonInfoDao;
import com.lc.billion.icefire.kvkseason.biz.model.KvkStage;
import com.lc.billion.icefire.kvkseason.biz.service.impl.legion.LegionOutput;
import com.lc.billion.icefire.kvkseason.biz.service.impl.legion.LegionService;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.constant.PsKvkStage;
import com.lc.billion.icefire.protocol.structure.PsAllianceFlagInfo;
import com.lc.billion.icefire.protocol.structure.PsKvkGroupServerSimpleInfo;
import com.lc.billion.icefire.protocol.structure.PsLegionRankMember;
import com.lc.billion.icefire.protocol.structure.PsRoleSimpleInfo;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.*;
import com.simfun.sgf.utils.JavaUtils;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * KVK赛季 k服service
 *
 * <AUTHOR>
 * @date 2021/8/25
 */
@Service
public class KvkSeasonServiceImpl {
    private static final Logger logger = LoggerFactory.getLogger(KvkSeasonServiceImpl.class);

    @Autowired
    private AllianceDao allianceDao;
    @Autowired
    private KvkHistoryAllianceProsperityDao kvkHistoryAllianceProsperityDao;
    @Autowired
    private KvkHistoryLegionProsperityDao legionProsperityHistoryDao;
    @Autowired
    private RoleManager roleManager;
    @Autowired
    private AsyncOperationServiceImpl asyncOperationService;
    @Autowired
    private ServerInfoDao serverInfoDao;
    @Autowired
    private AllianceManager allianceManager;
    @Autowired
    private BiLogUtil biLogUtil;
    @Autowired
    private LegionService legionService;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private ServerInfoServiceImpl serverInfoService;
    @Autowired
    private ServiceDependency srvdp;
    @Autowired
    private RoleSeasonInfoDao roleSeasonInfoDao;
    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private SeasonWarmUpActivityService seasonWarmUpActivityService;


    @Setter
    private long gmSeasonTime = 0;
    private static final long MAX_COMPUTE_TIME = 7 * TimeUtil.DAY_MILLIS;
    /**
     * serverId -> 安全区
     */
    private Map<Integer, List<Integer>> serverSafeRegionMap = new HashMap<>();
    /**
     * serverId -> 安全区
     * 赛季备战选地结果
     */
    private Map<Integer, List<Integer>> serverSafeRegionMapSeasonWarmUp = new HashMap<>();
    /**
     * serverId -> 安全区
     */
    private Map<Integer, String> serverIdToColor = new HashMap<>();
    /**
     * 根据区域ID查找安全区对应的哪个服
     */
    private Map<Integer, Integer> safeRegionToServerId = new HashMap<>();

    /**
     * 安全区 -> serverId 赛季备战选地结果
     */
    private Map<Integer, Integer> safeRegionToServerIdSeasonWarmUp = new HashMap<>();

    public void startService() {
        if (Application.getServerType() != ServerType.KVK_SEASON) {
            return;
        }
        var kvkServerId = Application.getServerId();
        var kvkGroupConfig = configService.getConfig(KvkGroupConfig.class);
        var meta = kvkGroupConfig.getKvkServerMeta(Application.getServerId());
        var safeZoneConfig = configService.getConfig(SeasonSafeZoneConfig.class).getSeasonMetaBySeason(meta.getServerList().size());
        //检查所有安全区是否配置正确
        for (var regionId : safeZoneConfig.getAllSafeZoneList()) {
            var regionGrid = srvdp.getWorldService().getWorld().getResourceZoneManager(kvkServerId).getRegionGrid(regionId);
            if (regionGrid == null) {
                throw new AlertException("启动失败 K服加载安全区失败 安全区配置的区域不存在","regionId",regionId);
            }
        }
        for (int i = 0; i < meta.getGroups().length; i++) {
            var regionIdList = safeZoneConfig.getRegionIdList(i);
            for (var regionId : regionIdList) {
                safeRegionToServerId.put(regionId, meta.getGroups()[i]);
            }
            serverSafeRegionMap.put(meta.getGroups()[i], regionIdList);
        }

        // 使用赛季备战结果 来初始化选服信息
        var season = Application.getSeason();
        for (var serverId : serverSafeRegionMap.keySet()) {
            serverInfoService.initServerSafeRegionInfo(serverId, season, safeZoneConfig, serverSafeRegionMapSeasonWarmUp, serverIdToColor);
            for (var entry : serverSafeRegionMapSeasonWarmUp.entrySet()) {
                for (var regionId : entry.getValue()) {
                    safeRegionToServerIdSeasonWarmUp.put(regionId, entry.getKey());
                }
            }
        }
    }

    /**
     * 在赛季服登陆时候，下发些相关信息
     *
     * @param role
     */
    public void onEnterWorld(Role role) {
        long now = System.currentTimeMillis();
        srvdp.getKvkHonorService().pushHonorRewardRecord(role);
        // 非赛季服 不需要做什么
            // 如果是game服
        if (Application.getServerType() == ServerType.GAME) {// 说明在1赛季 原服
            role.send(getGameKvkStageInfo(role));
            return;
        }
        // 登陆下发赛季阶段信息
        role.send(wrapperKvkStageInfo(role));
        role.send(wrapperSafeRegion());
    }

    private GcKvkSafeRegion wrapperSafeRegion() {
        GcKvkSafeRegion gcKvkSafeRegion = new GcKvkSafeRegion();
        if (this.safeRegionToServerIdSeasonWarmUp != null && !this.safeRegionToServerIdSeasonWarmUp.isEmpty()) {
            gcKvkSafeRegion.setRegionToServerMap(this.safeRegionToServerIdSeasonWarmUp);
        } else {
            gcKvkSafeRegion.setRegionToServerMap(this.safeRegionToServerId);
        }
        return gcKvkSafeRegion;
    }

    /**
     * 组装kvk阶段协议信息
     *
     * @return
     */
    public GcKVKStageInfo wrapperKvkStageInfo(Role role) {
        return getGameKvkStageInfo(role);
    }

    /**
     * @param
     * @param rankList 排名id列表
     * @param scoreMap 排名id对应的积分
     */
    public void sendReward(RankType rankType, List<Long> rankList, Map<Long, Double> scoreMap) {
        switch (rankType) {
            case ALLIANCE_PROSPERITY:
            case ALLIANCE_PROSPERITY_KVK:
                sendAllianceProsperitySeasonReward(rankList, scoreMap);
                break;
            default:
                break;
        }
    }

    /**
     * @param rankList
     * @param scoreMap
     */
    private void sendAllianceProsperitySeasonReward(List<Long> rankList, Map<Long, Double> scoreMap) {
        // long now = TimeUtil.getNow();
        if (rankList == null || scoreMap == null) {
            ErrorLogUtil.errorLog("发送赛季结算奖励,传入参数为空,没有排名");
            return;
        }
        logger.info("赛季结算3-联盟列表={} 分数列表={}", rankList, scoreMap);
        // 将排行榜数据持久化到db。
        // 需要创建 Application.getAllServerIds().size() 份，排行榜记录
        final int season = Application.getSeason();
        Application.getAllServerIds().forEach(serverId -> {
            int count = 0;
            int index = 0;
            for (Long id : rankList) {
                count++;
                index++;
                //
                Alliance alliance = allianceDao.findById(id);
                if (alliance == null) {
                    continue;
                }
                alliance.setRank(season, count);
                allianceDao.save(alliance);
                logger.info("賽季结算 season={} 联盟={} 排名={} index={}", id, season, index);
                kvkHistoryAllianceProsperityDao.create(season, alliance.getoServerId(), alliance, scoreMap.get(alliance.getPersistKey()), count, serverId,
                        alliance.getMaxMember(), allianceManager.getCurMember(alliance));
                biLogUtil.seasonSettle(serverId, season, alliance.getId(), count);
            }
        });
        logger.info("赛季结算4-联盟列表={} 分数列表={}", rankList, scoreMap);
    }

    /**
     * 广播整个赛季服 KVK阶段变更
     */
    public void broadcastKvkStageChange() {
        asyncOperationService.execute(new KvkStageChangeNoticeOperation(this, Application.getBean(WorldServiceImpl.class)));
    }


    /**
     * 获取历史军团势力值排行榜
     */
    public void getKvkLegionProsperityRank(Role role, int serverId, int season, RankContext ctx) {
        List<KvkHistoryLegionProsperity> historyList = legionProsperityHistoryDao.getByServerIdAndSeason(serverId, season);
        if (!JavaUtils.bool(historyList))
            return;
        //
        //
        GcLegionRank gcLegionRank = new GcLegionRank();
        Map<Integer, KvkHistoryLegionProsperity> rankMap = new HashMap<>();
        for (KvkHistoryLegionProsperity history : historyList) {
            rankMap.put(history.getRank(), history);
            if (legionService.getLegionId(role) != null && legionService.getLegionId(role).equals(history.getLegionId())) {
                gcLegionRank.setOwnerRank(history.getRank());
                gcLegionRank.setOwnerValue((long) history.getScore());
            }
        }
        for (int i = ctx.getStart() + 1; i <= ctx.getEnd(); i++) {
            KvkHistoryLegionProsperity kvkHistoryLegionProsperity = rankMap.get(i);
            if (kvkHistoryLegionProsperity == null)
                continue;
            PsLegionRankMember psLegionRankMember = new PsLegionRankMember();
            psLegionRankMember.setRank(i);
            psLegionRankMember.setInfo(LegionOutput.toPsLegionInfo(kvkHistoryLegionProsperity));
            psLegionRankMember.setValue((long) kvkHistoryLegionProsperity.getScore());
            gcLegionRank.addToMembers(psLegionRankMember);
        }
        gcLegionRank.setPage(ctx.getPage());
        gcLegionRank.setType(ctx.getType().getType());
        role.send(gcLegionRank);
    }

    /**
     * 赛季分组信息 根据所在阶段区分下发是 当前赛季分组，还是 下个赛季分组
     * <p>
     * 如果在非结算阶段，发送当前赛季分组； 如果在结算阶段及以后，发送下个赛季分组信息
     *
     * @param role
     * @return
     */
    public GcKvkMatchGroupInfo wrapperGroupInfo(Role role) {
        var stage = getKvkStage();
        if (stage == KvkStage.MATCHING || stage == KvkStage.MATCHED) {
            // 已经结算的发送下赛季信息
            return wrapperMatchGroupInfo(role);
        } else {
            // 还没有结算的发送当前赛季分组信息
            return wrapperCurrentSeasonMatchGroupInfo(role);
        }
    }
    public GcKvkMatchGroupInfo wrapperGroupInfo1(long roleId) {
       return wrapperGroupInfo(srvdp.getRoleDao().findById(roleId));
    }
    /**
     * 当前玩家所在服，下个赛季分组信息情况。
     *
     * @param role
     * @return
     */
    private GcKvkMatchGroupInfo wrapperMatchGroupInfo(Role role) {
        if (!configCenter.currentServerTypeIsGAME_or_KVKSEASON()) {
            return null;
        }
        GcKvkMatchGroupInfo gcInfo = new GcKvkMatchGroupInfo();
        long now = TimeUtil.getNow();
        int season = Application.getSeason();
        KvkSeasonsConfig kvkSeasonsConfig = configCenter.getLsConfig().getKvkSeasons();
        Set<Integer> nextSeasonOServerIds = new HashSet<>();
        // 如果是game服表示在第一赛季
        // 在赛季服, 取当前赛季kvkConfig, 获取下一赛季的OserverIds
        KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = kvkSeasonsConfig.getKvkSeasonServerGroupConfigByOServerIdAndSeason(role.getoServerId(), season + 1);
        if (kvkSeasonServerGroupConfig != null) {
            nextSeasonOServerIds = kvkSeasonServerGroupConfig.getOServerIds();
        }
        Set<Integer> curSeasonOServerIds = new HashSet<>();
        var curKvkGroupConfig = kvkSeasonsConfig.getKvkSeasonServerGroupConfigByOServerIdAndSeason(role.getoServerId(), season);
        if (curKvkGroupConfig != null) {
            curSeasonOServerIds = curKvkGroupConfig.getOServerIds();
        }
        var gameServers = configCenter.getLsConfig().getGameServers();
        curSeasonOServerIds.forEach(serverId -> {
            GameServerConfig gameServer = gameServers.get(serverId);
            var info = toPsKvkGroupServerSimpleInfo(serverId, gameServer);
            if (info != null) {
                gcInfo.addToServerList(info);
            }
        });

        nextSeasonOServerIds.forEach(serverId -> {
            GameServerConfig gameServer = gameServers.get(serverId);
            if (gameServer == null) {
                ErrorLogUtil.errorLog("获取下个赛季kvk分组信息时候,下个赛季分组中的服在gameServerConfig中不存在", new RuntimeException(),"serverId",serverId);
                return;
            }
            PsKvkGroupServerSimpleInfo info = new PsKvkGroupServerSimpleInfo();
            info.setServerId(serverId);
            //
            GameServerConfig.KingdomInfo kingdomInfo = gameServer.getKingdomInfo();
            if (kingdomInfo != null) {
                if (kingdomInfo.getCurrentKingName() != null) {
                    info.setKingName(kingdomInfo.getCurrentKingName());
                }
                if (kingdomInfo.getCurrentKingAllianceAliasName() != null) {
                    info.setKingAllianceAliasName(kingdomInfo.getCurrentKingAllianceAliasName());
                }
                if (kingdomInfo.getCurrentKingHead() != null) {
                    info.setKingHeadInfo(kingdomInfo.getCurrentKingHead());
                }
                if (kingdomInfo.getAllianceFlag() != null) {
                    info.setKingAlliance(transform(kingdomInfo.getAllianceFlag()));
                }
            }
            info.setServerOpenTime(gameServer.getOpenTimeMs());
            //
            if (JavaUtils.bool(Application.getKVKGroupServerIds())) {// 有值 说明当前在赛季服
                KvkSeasonServerGroupConfig kvkGroupInfo = kvkSeasonsConfig.getServerGroupByOServerIdAndTime(serverId, now);
                if (kvkGroupInfo != null) {
                    info.setIsReward(TimeUtil.getNow() >= kvkGroupInfo.getSettleTime());
                }
            } else { // 没有值，说明这个serverId 在第一赛季
//				long rewardTime = KvkSeasonTicker.getRewardTime(serverId);
//				info.setIsReward(TimeUtil.getNow() >= rewardTime);
            }
            //
            gcInfo.addToNextSeasonServerList(info);
        });
        gcInfo.setSeason(season);
        return gcInfo;
    }

    /**
     * 当前玩家所在服，本赛季分组情况
     *
     * @return
     */
    private GcKvkMatchGroupInfo wrapperCurrentSeasonMatchGroupInfo(Role role) {
        Set<Integer> currentSeasonOServerIds = new HashSet<>();
        if (Application.getServerType() == ServerType.GAME) {// 如果是game服表示在第一赛季，第一赛季不用发
            return null;
        } else if (Application.getServerType() == ServerType.KVK_SEASON) {// 在赛季服, 取当前赛季kvkConfig, 获取下一赛季的OserverIds
            currentSeasonOServerIds = configCenter.getCurrentKvkSeasonServerGroupConfig().getOServerIds();
        }
        if (!JavaUtils.bool(currentSeasonOServerIds)) {
            return null;
        }
        Map<Integer, GameServerConfig> gameServers = configCenter.getLsConfig().getGameServers();
        List<PsKvkGroupServerSimpleInfo> simpleInfoList = new ArrayList<>();
        currentSeasonOServerIds.forEach(serverId -> {
            GameServerConfig gameServer = gameServers.get(serverId);
            var info = toPsKvkGroupServerSimpleInfo(serverId, gameServer);
            if (info != null) {
                simpleInfoList.add(info);
            }
        });
        GcKvkMatchGroupInfo gcInfo = new GcKvkMatchGroupInfo();
        gcInfo.setServerList(simpleInfoList);
        gcInfo.setSeason(configCenter.getSeason());
        return gcInfo;
    }

    /**
     * 获取一个赛季的各个时间阶段
     * @param serverId
     * @return
     */
    public StageTime getGameKvkStageTime(int serverId) {
        int serverOpenDayOfWeek = serverInfoService.getOpenServerDayWeek();
        int season = Application.getSeason();
        var serverType = ServerConfigManager.getInstance().getServerTypeConfig().getServerType(serverId);
        var serverInfo = serverInfoDao.findById((long) Application.getServerId());
        if (season > 1) {
            serverOpenDayOfWeek = 0;
        }
        SeasonTimeConfig.SeasonTimeMeta metaBySeason = configService.getConfig(SeasonTimeConfig.class).getMetaBySeason(serverOpenDayOfWeek, season);
        if (metaBySeason == null) {
            return null;
        }
        KvkSeasonsConfig kvkSeasonsConfig = configCenter.getLsConfig().getKvkSeasons();
        StageTime stageTime = null;
        switch (serverType) {
            case GAME:
                var seasonNextConfig = kvkSeasonsConfig.getKvkSeasonServerGroupConfigByOServerIdAndSeason(serverId, season + 1);
                stageTime = new StageTime(TimeUtil.getBeginOfDay(serverInfo.getOpenTimeMs()) + serverInfo.getOffsetDay() * TimeUtil.DAY_MILLIS, metaBySeason, seasonNextConfig);
                break;
            case KVK_SEASON:
                var kServer = kvkSeasonsConfig.getServerGroupByKServerId(serverId);
                if (kServer != null) {
                    int oServerId = Application.getKVKGroupServerIds().getFirst();
                    var nextSeasonConfig = kvkSeasonsConfig.getKvkSeasonServerGroupConfigByOServerIdAndSeason(oServerId, season + 1);
                    stageTime = new StageTime(kServer.getStartTime(), metaBySeason, nextSeasonConfig);
                }
                break;
        }
        if (stageTime != null) {
            logger.info("getGameKvkStageTime serverId {} stageTime [{}]", serverId, stageTime.toString());
        }
        return stageTime;
    }

    /**
     * @return
     */
    public GcKVKStageInfo getGameKvkStageInfo(Role role) {
        GcKVKStageInfo ret = new GcKVKStageInfo();
        int season = Application.getSeason();
        ret.setStage(PsKvkStage.BATTLE);
        ret.setSeason(season);
        var roleSeasonInfo = roleSeasonInfoDao.findById(role.getRoleId());
        var alliance = allianceDao.findById(role.getAllianceId());
        var allianceRank = alliance != null ? alliance.getSeasonRank(season) : null;
        ret.setKvkEnterTimes(roleSeasonInfo.getKvkEnterTime().getOrDefault(season, 0));
        if (allianceRank != null) {
            ret.setAllianceRank(allianceRank);
        }
        StageTime stageTime = getGameKvkStageTime(Application.getServerId());
        if (stageTime == null) {
            return ret;
        }
        long now = TimeUtil.getNow();
        KvkStage stage = stageTime.getStage(now);
        ret.setStageStartTime(stageTime.getStageTime(stage));
        long nextStageTime = stageTime.getNextStageTime(stage);
        ret.setSeasonSettleTime(stageTime.getRewardStartTime());
        ret.setStage(stage.getPsType());
        ret.setNextStageBeginTime(nextStageTime);
        return ret;
    }

    public void changeKvkStage(int day) {
        int serverId = Application.getServerId();
        StageTime stageTime = getGameKvkStageTime(serverId);
        var oldStage = stageTime.getStage(TimeUtil.getNow());
        long now = TimeUtil.getNow();
        if (stageTime == null) {
            logger.info("stageTime 不存在 serverId={}", serverId);
            return;
        }
        ServerInfo serverInfo = this.serverInfoDao.findById(Long.valueOf(serverId));
        serverInfo.setOffsetDay(day);
        serverInfoDao.save(serverInfo);
        var newStageTime = getGameKvkStageTime(serverId);
        logger.info("重置kvk阶段 oldStage={} new Stage={} 结算时间={} 开服时间={}", oldStage, newStageTime.getStage(now), TimeUtil.formatTime(newStageTime.getStageTime(KvkStage.SETTLEMENT), "yyyy-MM-dd"));
    }

    /**
     * @param alliance
     */
    public void pushAllianceSeasonReward(Role role, Alliance alliance) {
        int season = Application.getSeason();
        GcAllocAllianceReward gcAllocAllianceReward = new GcAllocAllianceReward();
        gcAllocAllianceReward.setSeason(season);
        var rank = alliance.getSeasonRank(season);
        if (rank == null) {
            rank = -1;
        }
        gcAllocAllianceReward.setSeaonRank(rank);
        var record = alliance.getSeasonRankRewardRecordMap(season);
        if (record != null) {
            Map<String, Integer> allocMap = new HashMap<>();
            for (var entry : record.getRewardMap().entrySet()) {
                allocMap.put(entry.getKey().toString(), entry.getValue());
            }
            gcAllocAllianceReward.setRewardMap(allocMap);
        }
        role.send(gcAllocAllianceReward);
    }

    public void gcGetKingInfo(Role role) {
        var serverinfo = serverInfoDao.findById((long) Application.getServerId());
        int season = Application.getSeason();
        var king = serverinfo.getKing(season);
        if (king == null) {
            return;
        }
        var roleSeasonInfo = roleSeasonInfoDao.findById(role.getId());
        GcGetKingInfo gcGetKingInfo = new GcGetKingInfo();
        PsRoleSimpleInfo kingPsSimpleInfo = king.toPsSimpleInfo();
        var kingRole = srvdp.getRoleDao().findById(king.getId());
        if (kingRole != null) {
            kingPsSimpleInfo.setHead(kingRole.getHead());
            kingPsSimpleInfo.setName(kingRole.getName());
            kingPsSimpleInfo.setRoleInfo(kingRole.toPsRoleInfo());
        }
        gcGetKingInfo.setKing(kingPsSimpleInfo);
        gcGetKingInfo.setLikes(king.getLikes());
        gcGetKingInfo.setLiked(roleSeasonInfo.getLikeMap().containsKey(season));
        role.send(gcGetKingInfo);
    }

    public void cgLikeKing(Role role) {
        var serverinfo = serverInfoDao.findById(Application.getServerId() * 1L);
        int season = Application.getSeason();
        var king = serverinfo.getKing(season);
        if (king == null) {
            logger.info("cgLikeKing | role={} season={} king is null", role.getPersistKey(), season);
            return;
        }
        var roleSeason = roleSeasonInfoDao.findById(role.getId());
        if (roleSeason.getLikeMap().containsKey(season)) {
            logger.info("cgLikeKing | role={} season={} already liked king={}", role.getPersistKey(), season, king.getId());
            return;
        }

        roleSeason.getLikeMap().put(season, TimeUtil.getNow());
        roleSeasonInfoDao.save(roleSeason);
        king.addLike();
        serverInfoDao.save(serverinfo);
        gcGetKingInfo(role);
        var seasonMeta = configService.getConfig(SeasonConfig.class).getSeasonMetaBySeason(season);
        String rewardId = seasonMeta == null ? null : seasonMeta.getLikeReward();
        if (JavaUtils.bool(rewardId)) {
            List<SimpleItem> rewardList = srvdp.getDropService().drop(rewardId);
            if (JavaUtils.bool(rewardList)) {
                srvdp.getItemService().give(role, rewardList, LogReasons.ItemLogReason.LIKE_KING_REWARD);
            }
        }
        srvdp.getBiLogUtil().seasonLikeKing(role, king.getId(), season, rewardId);
    }

    public void setKing(Role role, int season) {
        var serverId = role.getoServerId();
        if (season > 1) {
            serverId = Application.getServerId();
        }
        var alliance = allianceDao.findById(role.getAllianceId());
        if (alliance == null) {
            logger.warn("设置国王| fail role={} 没有联盟{}", role.getPersistKey(), role.getAllianceId());
            return;
        }
        var serverinfo = serverInfoDao.findById(serverId * 1L);
        serverinfo.setKing(season, new King(role, alliance));
        serverInfoDao.save(serverinfo);
        biLogUtil.seasonSetKing(role, season);
        logger.info("设置国王| success role={} 联盟{} season={} server={}", role.getPersistKey(), role.getAllianceId(), season, role.getoServerId());
        updateKingInfoToZK(serverId, season);
    }

    public void setKing(long roleId) {
        var role = roleManager.getRole(roleId);
        setKing(role, Application.getSeason());
    }

    public void clearKvk() {
        int season = Application.getSeason();
        clearKvk(season);
    }

    public void clearKvk(int season) {
        logger.info("清理kvk信息方便测试");
        for (var serverId : Application.getAllServerIds()) {
            var server = serverInfoDao.findById(serverId * 1L);
            if (server == null) {
                continue;
            }
            var king = server.getKing(season);
            logger.info("clearKvk season={} 清理服务器{} 的国王ID={}", season, serverId, king == null ? "null" : king.getId());
            server.resetKvk(season);
            serverInfoDao.save(server);
        }
        for (var alliance : allianceDao.findAll()) {
            if (alliance == null || srvdp.getAllianceService().isMirrorAlliance(alliance)) {
                continue;
            }
            var rank = alliance.getSeasonRank(season);
            logger.info("clearKvk season={} 赛季的联盟排名和奖励分配记录 allianceId={} rank={}", season, alliance.getPersistKey(), rank);
            alliance.resetKvk(season);
            allianceDao.save(alliance);
        }
        for (var roleSeasonInfo : roleSeasonInfoDao.findAll()) {
            logger.info("clearKvk season={} 赛季的个人奖励分配信息和点赞和赛季荣誉结算 roleId={} allianceId={}", season, roleSeasonInfo.getPersistKey(), roleSeasonInfo.getRewardAllianceId(season));
            roleSeasonInfo.resetKvk(season);
            roleSeasonInfoDao.save(roleSeasonInfo);
        }
    }

    public KvkStage getKvkStage() {
        long now = TimeUtil.getNow();
        var kvkStageTime = getGameKvkStageTime(Application.getServerId());
        return kvkStageTime == null ? null : kvkStageTime.getStage(now);
    }

    /**
     * 按照一个玩家的原服ID 找到在K服所在的安全区
     *
     * @param oServerId
     * @return
     */
    public List<Integer> getSafeRegionListByOserverId(int oServerId) {
        return serverSafeRegionMap.get(oServerId);
    }

    /**
     * 根据K服的一个区域id找到对应的原服ID
     * 如果区域id不是安全区则返回null
     *
     * @param regionId
     * @return
     */
    public Integer getOServerIdByRegionId(Integer regionId) {
        // todo 有赛季备战记录优先使用
        if (safeRegionToServerIdSeasonWarmUp.containsKey(regionId)) {
            return safeRegionToServerIdSeasonWarmUp.get(regionId);
        }

        return this.safeRegionToServerId.get(regionId);
    }

    /**
     * 确定一个目标是否有效
     *
     * @param role
     * @param x
     * @param y
     * @return true: 目标有效，可以继续下一步操作 false 目标在K服别国的安全区
     */
    public boolean isCanEnterArea(Role role, int x, int y) {
        //不是赛季服或者玩家不在K服上
        return isCanEnterArea(role.getoServerId(), role.getCurrentServerId(), x, y);
    }

    /**
     * 检查传入的坐标是否可以当做目标点
     * 可以被当做目标点的意思是 要么不是安全区，要么是己方安全区
     *
     * @param x
     * @param y
     * @return true: 目标有效，可以继续下一步操作 false 目标为阻挡或者在K服别国的安全区
     */
    public boolean isCanEnterArea(int oServerId, int currenServerId, int x, int y) {
        if (Application.getServerType() != ServerType.KVK_SEASON) {
            return true;
        }
        //不是赛季服或者玩家不在K服上
        var safeServerId = getSafeRegionServerId(currenServerId, x, y);
        if (safeServerId != null && oServerId != safeServerId) {
            // 目标区域位于安全区且不是自己服的安全区不允许行军
            logger.debug("目标处在别的服安全区 x={} y={} safeServerId={} role Oserver={}", x, y, safeServerId, oServerId);
            return false;
        }
        return true;
    }

    /**
     * 给定一个点，检查是否安全区
     *
     * @param x
     * @param y
     * @return 返回
     */
    public Integer getSafeRegionServerId(int currenServerId, int x, int y) {
        //不是赛季服或者玩家不在K服上
        var mapGrid = srvdp.getWorldService().getGrid(currenServerId, x, y);
        if (mapGrid == null) {
            //目标点不存在
            return null;
        }
        if (Application.getServerType() != ServerType.KVK_SEASON || currenServerId != Application.getServerId()) {
            return null;
        }
        var safeServerId = getOServerIdByRegionId(mapGrid.getRegionId());
        return safeServerId;
    }

    public List<Integer> getSafeRegionList(int oServerId) {
        List<Integer> regionList = new ArrayList<Integer>();

        // 如果赛季备战有结果 则优先使用赛季备战选地结果
        if (serverSafeRegionMapSeasonWarmUp.containsKey(oServerId)) {
            regionList = serverSafeRegionMapSeasonWarmUp.get(oServerId);
            logger.info("getSafeRegionList seasonWarmUp oServerId {} regionList {}", oServerId, regionList);
            return regionList;
        }

        regionList = this.serverSafeRegionMap.get(oServerId);
        logger.info("getSafeRegionList default oServerId {} regionList {}", oServerId, regionList);
        return regionList;
    }

    /**
     * 更新国王信息到zk
     */
    public void updateKingInfoToZK(int serverId, int season) {
        // 更新zk中国王信息
        String leaderName = "";
        String aliasName = "";
        String leaderHead = "";
        Long kingId = null;
        AllianceFlag allianceFlag = null;
        var kingRole = serverInfoDao.findById(serverId * 1L).getKing(season);
        if (kingRole != null) {
            kingId = kingRole.getId();
            leaderName = kingRole.getName();
            leaderHead = kingRole.getHead();
            var alliance = allianceDao.findById(kingRole.getAllianceId());
            if (alliance != null) {
                aliasName = alliance.getAliasName();
                allianceFlag = new AllianceFlag(alliance.getBannerColor(), alliance.getBanner(), alliance.getBadgeColor(), alliance.getBadge());
            }
        }
        asyncOperationService.execute(new UpdateKingOperation(serverId + "", leaderName, aliasName, leaderHead, kingId, allianceFlag));
    }

    private PsAllianceFlagInfo transform(AllianceFlag allianceFlag) {
        PsAllianceFlagInfo ps = new PsAllianceFlagInfo();
        ps.setBadge(allianceFlag.getBadge());
        ps.setBadgeColor(allianceFlag.getBadgeColor());
        ps.setBanner(allianceFlag.getBanner());
        ps.setBannerColor(allianceFlag.getBannerColor());
        return ps;
    }

    /**
     * 有bug的时候热更后调用
     */
    private void forHotFixCode(String reason) {

    }

    /**
     * 获取本赛季服结束的时间
     *
     * @return
     */
    public long getSeasonEndTime() {
        if(gmSeasonTime > 0){
            return gmSeasonTime;
        }
        var serverId = Application.getServerId();
        var kvkStageTime = getGameKvkStageTime(serverId);
        if (kvkStageTime == null) {
            //没有赛季分组配置，活动可以一直持续
            return Long.MAX_VALUE;
        }
        return kvkStageTime.getOverTime();
    }

    /**
     * 当前分组的信息 匹配到的数据不能从这里取，不在内存只能从zk里拿
     * @param serverId
     * @param gameServer
     * @return
     */
    public PsKvkGroupServerSimpleInfo toPsKvkGroupServerSimpleInfo(int serverId, GameServerConfig gameServer) {
        if (gameServer == null) {
            return null;
        }
        var kingdom = gameServer.getKingdomInfo();
        var king = kingdom != null ? roleManager.getRole(kingdom.getKingId()) : null;
        var kingAlliance = king == null ? null : allianceDao.findById(king.getAllianceId());
        PsKvkGroupServerSimpleInfo info = new PsKvkGroupServerSimpleInfo();
        info.setServerId(serverId);
        //
        if (king != null) {
            info.setKingName(king.getName());
            info.setKingHeadInfo(king.getHead());
            info.setKingId(king.getId());
        }
        if (kingAlliance != null) {
            info.setKingAlliance(kingAlliance.toPsAllianceFlagInfo());
            info.setKingAllianceAliasName(kingAlliance.getAliasName());
        }
        info.setServerOpenTime(gameServer.getOpenTimeMs());
        info.setColor(serverIdToColor.get(serverId));
        return info;
    }
}
