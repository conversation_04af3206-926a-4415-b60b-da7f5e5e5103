package com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.impl;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.kvk.SeasonTaskConfig;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.node.RegionCapitalNode;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.LegionSeasonTaskProgress;
import com.lc.billion.icefire.kvkseason.biz.model.seasonTask.RoleSeasonTaskRecord;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.AbstractSeasonTaskAction;
import com.lc.billion.icefire.kvkseason.biz.service.impl.seasonTask.action.SeasonTaskActionType;
import com.longtech.ls.config.ServerType;
import com.simfun.sgf.utils.JavaUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/3/16
 */
@Service
public class LegionOccupyRegionCapitalNumFinal extends AbstractSeasonTaskAction {
	@Override
	public SeasonTaskActionType getType() {
		return SeasonTaskActionType.LEGION_OCCUPY_REGION_CAPITAL_NUM_FINAL;
	}

	@Override
	public long getProgress(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		Long legionId = legionService.getLegionId(role);
		if (legionId == null) {
			return 0L;
		}
		LegionSeasonTaskProgress legionRecord = legionSeasonTaskProgressDao.getByLegionId(legionId);
		int i = meta.getConditions().indexOf(getType().getId());
		int param2 = meta.getParam2s().get(i);
		return legionRecord.getOccupyCount(param2);
	}

	@Override
	protected void updateProgress(Role role, RoleSeasonTaskRecord taskRecord, Object... params) {
		Long legionId = (Long) params[0];
		int serverId = (int) params[1];
		if (Application.getConfigCenter().getServerType(serverId) != ServerType.KVK_SEASON) {
			return;
		}

		Collection<RegionCapitalNode> nodes = sdp.getRegionCapitalNodeDao().findByLegionId(legionId);
		Map<Integer, Integer> cityOccupy = new HashMap<>();
		if (JavaUtils.bool(nodes)) {
			for (RegionCapitalNode node : nodes) {
				if (Application.getConfigCenter().getServerType(node.getCurrentServerId()) != ServerType.KVK_SEASON) {
					continue;
				}
				cityOccupy.compute(node.getLevel(), (k, v) -> v == null ? 1 : v + 1);
			}
		}
		//
		LegionSeasonTaskProgress legionSeasonTaskProgress = legionSeasonTaskProgressDao.getByLegionId(legionId);
		legionSeasonTaskProgress.setOccupyCity(cityOccupy);
		legionSeasonTaskProgressDao.save(legionSeasonTaskProgress);

	}

	@Override
	public boolean actionFinishCheck(Role role, SeasonTaskConfig.SeasonTaskMeta meta) {
		int i = meta.getConditions().indexOf(getType().getId());
		int param1 = meta.getParam1s().get(i);
		return getProgress(role, meta) >= param1;
	}
}
