package com.lc.billion.icefire.kvkseason.biz.model.honor;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;

/**
 * <AUTHOR>
 *
 */
public class KvkHonorLegion extends AbstractEntity {

	private static final long serialVersionUID = -6874276156169233767L;

	@MongoId
	private Long legionId;
	/** 荣誉值 */
	private long honorValue;

	@Override
	public void setPersistKey(Long id) {
		legionId = id;
	}

	@Override
	public Long getPersistKey() {
		return legionId;
	}

	@Override
	public Long getGroupingId() {
		return legionId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public long getHonorValue() {
		return honorValue;
	}

	public void addHonorValue(long honorValue) {
		this.honorValue += honorValue;
	}

}
