package com.lc.billion.icefire.gvgcontrol.biz.model;

import java.util.List;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;

/**
 * <AUTHOR>
 *
 */
public class GVGActivityTurn extends AbstractEntity {

	private static final long serialVersionUID = 4435816324980794548L;

	@MongoId
	private Long id;
	private int turn;
	private Long time;
	private List<Integer> battleServerIds;
	private int week;

	@Override
	public void setPersistKey(Long id) {
		this.id = id;
	}

	@Override
	public Long getPersistKey() {
		return id;
	}

	@Override
	public Long getGroupingId() {
		return id;
	}

	public int getTurn() {
		return turn;
	}

	public void setTurn(int turn) {
		this.turn = turn;
	}

	public void setTime(Long time) {
		this.time = time;
	}


	public void setBattleServerIds(List<Integer> battleServerIds) {
		this.battleServerIds = battleServerIds;
	}

	@Override
	public int hashCodeImpl() {
		return hashCode(turn);
	}

	public int getWeek() {
		return week;
	}

	public void setWeek(int week) {
		this.week = week;
	}

	@Override
	public boolean equalsImpl(Object obj) {
		GVGActivityTurn o = (GVGActivityTurn) obj;
		return equals(turn, o.getTurn());
	}

}
