package com.lc.billion.icefire.game.biz.service.impl.tech;

import com.google.common.collect.Maps;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.model.IntKeyIntValue;
import com.lc.billion.icefire.core.config.model.IntKeyStringValue;
import com.lc.billion.icefire.game.biz.config.BuildingConfig;
import com.lc.billion.icefire.game.biz.config.ScienceConditionConfig;
import com.lc.billion.icefire.game.biz.config.ScienceConfig;
import com.lc.billion.icefire.game.biz.config.ScienceConfig.ScienceMeta;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.CityBuildDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleTechDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.WorkProgressDao;
import com.lc.billion.icefire.game.biz.manager.*;
import com.lc.billion.icefire.game.biz.manager.prop.base.ICalcProp;
import com.lc.billion.icefire.game.biz.model.city.BuildingGroup;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.currency.CurrencyValue;
import com.lc.billion.icefire.game.biz.model.fightpower.FightPowerType;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.libao.LiBaoFunctionType;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.prop.AttributeValue;
import com.lc.billion.icefire.game.biz.model.prop.Prop;
import com.lc.billion.icefire.game.biz.model.record.type.RoleTotalRecordType;
import com.lc.billion.icefire.game.biz.model.role.CityBuild;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleCity;
import com.lc.billion.icefire.game.biz.model.sience.ScienceInfo;
import com.lc.billion.icefire.game.biz.model.work.WorkProgress;
import com.lc.billion.icefire.game.biz.model.work.WorkQueueType;
import com.lc.billion.icefire.game.biz.model.work.WorkState;
import com.lc.billion.icefire.game.biz.model.work.WorkType;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.mission.MissionServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.push.PushHelper;
import com.lc.billion.icefire.game.biz.service.impl.work.WorkServiceImpl;
import com.lc.billion.icefire.game.exception.ToClientException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.game.support.LogReasons.MoneyLogReason;
import com.lc.billion.icefire.protocol.GcCityUpdated;
import com.lc.billion.icefire.protocol.GcTechList;
import com.lc.billion.icefire.protocol.GcTechUpgrade;
import com.lc.billion.icefire.protocol.constant.PsErrorCode;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 科技
 */
@Service
public class TechServiceImpl {
    private static final Logger logger = LoggerFactory.getLogger(TechServiceImpl.class);

    @Autowired
    private WorkServiceImpl workSrv;
    @Autowired
    private ServiceDependency srvDep;
    @Autowired
    private RoleCalcPropManager roleCalcPropManager;
    @Autowired
    private RoleCityManager roleCityManager;
    @Autowired
    private RoleCurrencyManager roleCurrencyManager;
    @Autowired
    private RoleTechDao roleTechDao;
    @Autowired
    private RoleTechManager roleTechManager;
    @Autowired
    private WorkProgressManager workProgressManager;
    @Autowired
    private WorkProgressDao workProgressDao;
    @Autowired
    private RoleItemManager roleItemManager;
    @Autowired
    private CityBuildDao cityBuildDao;
     @Autowired
     private MissionServiceImpl missionService;
    @Autowired
    private PushHelper pushHelper;

    public void onEnterWorld(Role role) {
        this.sendTechList(role);
    }

    /**
     * 添加科技
     *
     * @param role
     * @param scienceMetaId
     */
    public void addSience(Role role, String scienceMetaId) {
        ScienceMeta scienceMeta = this.srvDep.getConfigService().getConfig(ScienceConfig.class).get(scienceMetaId);
        if (scienceMeta == null) {
            ErrorLogUtil.errorLog("addSience sienceMeta is null");
            return;
        }
        ScienceInfo scienceInfo = roleTechManager.getScienceInfo(role.getPersistKey(), scienceMeta.getGroup());
        if (scienceInfo == null) {
            roleTechDao.create(role, scienceMeta.getGroup(), scienceMeta.getLevel());
        } else {
            scienceInfo.setScienceLevel(scienceMeta.getLevel());
            roleTechDao.save(scienceInfo);
        }
        this.srvDep.getFightPowerServiceImpl().change(role, FightPowerType.TECHNOLOGY);

        // 修改科技属性之前先做一次结算
        roleCalcPropManager.addChangeType(role, ICalcProp.RoleType.TECH);
        if (role.isOnline()) {
            this.sendTechList(role);
        }
        missionService.onMissionFinish(role, MissionType.SCIENCE_LEVEL_UP, role);
        srvDep.getLiBaoService().liBaoUpdateOnCheck(role, "科研升级", LiBaoFunctionType.UPDATE_RESEARCH_LV, scienceMeta.getGroup());

        // 消息推送 科技完成
        pushHelper.growthConstruction(role, List.of("@WECHAT_PUSH_TITLE_TECH@"), List.of("@" + scienceMeta.getName() + "@", "@SUBSCRIBE_PARAM_3@"));

        afterAddScience(role, scienceMeta.getAttributeValues());

        // 更新资源产出速度
        srvDep.getResourceOutputManager().updateResourceOutput(role);
    }

    // 这快将来改成策划配置，不要写死
    @Deprecated // 2020-3-25 策划删除部分prop
    private void afterAddScience(Role role, AttributeValue[] attributeValues) {
        /*
         * if (attributeValues == null) { return; } for (AttributeValue av :
         * attributeValues) { if (av == null || av.getAttributeType() == null) {
         * continue; } // 2020-1-6 策划优化公式 switch (av.getAttributeType()) { // case
         * UNLOCK_JOB_AND_ENHANCE_RATIO_FOOD_10178: // case
         * UNLOCK_JOB_AND_ENHANCE_VALUE_FOOD_10238: case
         * CROPLAND_ROAD_CONNECT_PRODUCTION_ADD_PERCENT_10167:
         * resourceOutputManager.updateResourceOutputSpeed(role.getId(), Currency.FOOD);
         * break; // case UNLOCK_JOB_AND_ENHANCE_RATIO_WATER_10179: // case
         * UNLOCK_JOB_AND_ENHANCE_VALUE_WATER_10239: case
         * WATER_ROAD_CONNECT_PRODUCTION_ADD_PERCENT_10168:
         * resourceOutputManager.updateResourceOutputSpeed(role.getId(),
         * Currency.WATER); break; // case UNLOCK_JOB_AND_ENHANCE_RATIO_WOOD_10225: // case
         * UNLOCK_JOB_AND_ENHANCE_VALUE_WOOD_10248: case
         * LUMBERING_ROAD_CONNECT_PRODUCTION_ADD_PERCENT_10208:
         * resourceOutputManager.updateResourceOutputSpeed(role.getId(), Currency.WOOD);
         * break; // case UNLOCK_JOB_AND_ENHANCE_RATIO_IRON_10226: // case
         * UNLOCK_JOB_AND_ENHANCE_VALUE_IRON_10249: // case UNLOCK_JOB_AND_ENHANCE_RATIO_BANK_10227: // case
         * UNLOCK_JOB_AND_ENHANCE_VALUE_BANK_10254: //
         * resourceOutputManager.updateResourceOutputSpeed(role.getId(), Currency.GOLD);
         * // break; } }
         */
    }

    /**
     * 发送科技列表
     */
    public void sendTechList(Role role) {
        GcTechList msg = new GcTechList();
        Map<Integer, ScienceInfo> scienceInfoMap = roleTechManager.getScienceInfoMap(role.getId());
        if (scienceInfoMap == null) {
            return;
        }
        for (ScienceInfo scienceInfo : scienceInfoMap.values()) {
            msg.addToTechnologies(RoleTechManager.buildGroupLevelToMetaId(scienceInfo.getGroupId(), scienceInfo.getScienceLevel()));
        }
        if (msg.getTechnologies() == null) {
            msg.setTechnologies(Arrays.asList());
        }
        role.send(msg);
    }

    /**
     * 升级科技
     * @param scienceMetaId
     * @param immediate
     * @param immediate
     */
    public void upgradeSience(Role role, String scienceMetaId, boolean immediate, boolean isFree, String queueId) {
        ScienceMeta scienceMeta = this.srvDep.getConfigService().getConfig(ScienceConfig.class).get(scienceMetaId);
        if (scienceMeta == null) {
            ErrorLogUtil.errorLog("upgradeSience sienceMeta is null");
            return;
        }
        RoleCity city = roleCityManager.getRoleCity(role.getId());
        if (city == null) {
            ErrorLogUtil.errorLog("upgradeBuilding city is null");
            return;
        }

        //根据 queueId 或者 新增buildingId 获取CityBuild 新消息
        WorkProgress workProgress = null;
        if (JavaUtils.bool(queueId)) { // --queueMetaId
            workProgress = workProgressManager.getWorkProgress(role.getRoleId(), queueId);
        } else {
            workProgress = workProgressManager.getWorkProgress(role.getPersistKey(), WorkQueueType.TECH);
        }

        if (workProgress == null) {
            Queue<CityBuild> scienceBuild = cityBuildDao.findByRoleIdAndGroupId(role.getId(), BuildingGroup.SCIENCE.getGroup());
            ErrorLogUtil.errorLog("zhouhuiqin:非常奇怪，为什么会null", "roleId",role.getId(), "scienceBuildLevel",scienceBuild != null ? scienceBuild.element().getLevel() : 0);
            return;
        }

        CityBuild cityBuild = null;
        if (workProgress.getBindBuildingId() == null) {
            //老玩家数据 兼容
            if (workProgress.getWorkQueueType() == WorkQueueType.TECH) {
                cityBuild = roleCityManager.getBuildingMaxByGroupId(role.getPersistKey(), BuildingGroup.SCIENCE.getGroup());
            }
        } else {
            //科技队列绑定建筑检测
            cityBuild = roleCityManager.getBuildingById(workProgress.getBindBuildingId());
        }
        if (cityBuild == null) {
            ErrorLogUtil.errorLog("upgradeSience Not found Build.", "role",role);
            return;
        }

        // 2020-6-3 加入已研发完成但为领取的科技判断，并推送建筑更新消息，防止重复研发
        CityBuild unCollectionBuild = getCityBuildTheSameUncollectScience(role, scienceMetaId);
        if (unCollectionBuild != null) {
            ErrorLogUtil.errorLog("upgradeScience science already upgrade.", "scienceMetaId", scienceMetaId, "role",role);
            GcCityUpdated gcCityUpdated = roleCityManager.toSomeBuildUpdate(role.getId(), unCollectionBuild);
            role.send(gcCityUpdated);
            return;
        }

        ScienceConditionConfig.ScienceCondition scienceCondition = srvDep.getConfigService().getConfig(ScienceConditionConfig.class).getScienceCondition(scienceMeta.getType());
        if (scienceCondition != null) {
            IntKeyStringValue unlock = scienceCondition.getUnlock();
            if (unlock.getId() == 1) {
                int mainBuildingLevel = roleCityManager.getMainBuildingLevel(role.getId());
                BuildingConfig.BuildingMeta buildingMeta = srvDep.getConfigService().getConfig(BuildingConfig.class).get(unlock.getCount());
                if (mainBuildingLevel < buildingMeta.getLevel()) {
                    ErrorLogUtil.errorLog("upgradeScience CASTLE level < condition", "level",mainBuildingLevel, "condition",buildingMeta.getLevel());
                    return;
                }
            } else if (unlock.getId() == 3) {
                int type = Integer.parseInt(unlock.getCount().split(",")[0]);
                double process = Double.parseDouble(unlock.getCount().split(",")[1]);
                double currentProcess = roleTechManager.getScienceProcessByType(role.getId(), type);
                if (currentProcess < process) {
                    ErrorLogUtil.errorLog("upgradeScience CASTLE currentProcess < condition", "currentProgress",currentProcess, "condition",process);
                    return;
                }

            }
        }
        int nowLevel = roleTechManager.getScienceLevel(role.getId(), scienceMeta.getGroup());
        if (nowLevel >= scienceMeta.getLevel()) {
            ErrorLogUtil.errorLog("upgradeScience nowLevel >= scienceMeta.getLevel().", "scienceMetaId",scienceMetaId,"role", role);
            return;
        }

        // 前置建筑检查
        if (!this.srvDep.getRoleCityService().checkBuildingCondition(city, scienceMeta.getBuildingConditions())
                || !this.checkSienceCondition(role, scienceMeta.getSienceConditions()) || !checkSienceTreeCondition(role, scienceMeta.getSienceTreeConditions())) {
            ErrorLogUtil.errorLog("upgradeScience preconditions don't pass");
            return;
        }

        if (immediateFinishWork(role, scienceMetaId, immediate, workProgress)) {
            return;
        }

        // 资源
        Map<Currency, Long> costMap = new HashMap<>();
        for (CurrencyValue cv : scienceMeta.getNeedResources()) {
            double addRatio = role.getNumberProps().getDouble(Prop.TECH_RES_SUB_RATION_11043);
            double addExceptGoldRatio = role.getNumberProps().getDouble(Prop.TECH_RES_EXCEPT_GOLD_SUB_RATIO_11046);
            int subRes = role.getNumberProps().getInt(Prop.TECH_RES_SUB_VALUE_11042);
            double addRatioNew = role.getNumberProps().getDouble(Prop.TECH_RES_SUB_RATIO_NEW_11050);
            switch (cv.getCurrency()) {
                case FOOD:
                    addRatio += role.getNumberProps().getDouble(Prop.TECH_FOOD_SUB_RATIO_11037);
                    addRatio += addExceptGoldRatio;
                    subRes += role.getNumberProps().getInt(Prop.TECH_FOOD_SUB_VALUE_11033);
                    break;
                case WOOD:
                    addRatio += role.getNumberProps().getDouble(Prop.TECH_WOOD_SUB_RATIO_11038);
                    addRatio += addExceptGoldRatio;
                    subRes += role.getNumberProps().getInt(Prop.TECH_WOOD_SUB_VALUE_11034);
                    break;
                case WATER:
                    addRatio += role.getNumberProps().getDouble(Prop.TECH_STONE_SUB_RATIO_11039);
                    addRatio += addExceptGoldRatio;
                    subRes += role.getNumberProps().getInt(Prop.TECH_STONE_SUB_VALUE_11035);
                    break;
                case IRON:
                    addRatio += addExceptGoldRatio;
                    break;
                case GOLD:
                    addRatio += role.getNumberProps().getDouble(Prop.TECH_GOLD_SUB_RATIO_11040);
                    subRes += role.getNumberProps().getInt(Prop.TECH_GOLD_SUB_VALUE_11036);
                    addRatioNew = 0;
                    break;
                default:
                    break;
            }
            long needRes = (long) (cv.getValue() / (1 + addRatio) * (1 - addRatioNew) + subRes);
            if (needRes <= 0) {
                needRes = 0;
            }
            costMap.put(cv.getCurrency(), needRes);
        }

        // 道具
        List<SimpleItem> needItem = scienceMeta.getNeedItem();
        needItem = needItem.stream().map(SimpleItem::copy).collect(Collectors.toList());
        for (SimpleItem simpleItem : needItem) {
            int count = simpleItem.getCount();
            count /= (1 + role.getNumberProps().getDouble(Prop.TECH_ITEM_SUB_PERCENT_11047));
            simpleItem.setCount(count);
        }
        long buildingTime = (long) Math.floor(scienceMeta.getUpgrade() * TimeUtil.SECONDS_MILLIS / workSrv.getWorkTimeRatio(role, WorkType.TECH, null, cityBuild));
        long buildSubTime = role.getNumberProps().getInt(Prop.TECH_TIME_SUB_SECOND_VALUE_11024) * TimeUtil.SECONDS_MILLIS;
        buildingTime = Math.max(buildingTime + buildSubTime, 0);

        long needDiamond = 0;
        if (immediate) {
            // 资源是否足够
            needDiamond += workSrv.fastCostDiamond(role, WorkType.TECH, costMap, buildingTime, isFree);
            needDiamond = Math.max(1, needDiamond);
            if (!roleCurrencyManager.check(role, Currency.DIAMOND, needDiamond)) {
                throw new ToClientException(PsErrorCode.RES_NOT_ENOUGH_DIAMOND, "diamond not enough need : {}", needDiamond);
            }
        }
        for (Map.Entry<Currency, Long> entry : costMap.entrySet()) {
            if (!roleCurrencyManager.check(role, entry.getKey(), entry.getValue())) {
                // logger.error("upgradeSience not enough currency {}" + entry.getKey());
                throw new ToClientException(ToClientException.getResNotEnough(entry.getKey()), "{} not enough need : {}", entry.getKey().name(), entry.getValue());
            }
        }
        for (SimpleItem simpleItem : needItem) {
            boolean hasItem = roleItemManager.hasItem(role, simpleItem.getMetaId(), simpleItem.getCount(), false);
            if (!hasItem) {
                throw new ToClientException(PsErrorCode.RES_NOT_ENOUGH_DIAMOND, "item :{} not enough need : {}", simpleItem.getMetaId(), simpleItem.getCount());
            }
        }

        if (workProgress.getWorkState() == WorkState.RUNNING) {
            ErrorLogUtil.errorLog("upgradeScience WorkProgress is null or is running");
            return;
        }

        for (Map.Entry<Currency, Long> entry : costMap.entrySet()) {
            // roleCurrencyManager.checkAndCost(role, entry.getKey(), entry.getValue(),
            // MoneyLogReason.UPGRADE_SIENCE, scienceMetaId);
            roleCurrencyManager.onlyCostAll(role, entry.getKey(), entry.getValue(), MoneyLogReason.UPGRADE_SIENCE, scienceMetaId);
        }
        for (SimpleItem simpleItem : needItem) {
            roleItemManager.removeItem(role, simpleItem.getMetaId(), simpleItem.getCount(), LogReasons.ItemLogReason.TECH_UPGRADE_USE);
        }

        // 扣除资源
        if (immediate) {
            // roleCurrencyManager.checkAndCost(role, Currency.DIAMOND, needDiamond,
            // MoneyLogReason.UPGRADE_SIENCE_IMMEDIATE, scienceMetaId);
            roleCurrencyManager.onlyCost(role, Currency.DIAMOND, needDiamond, MoneyLogReason.UPGRADE_SIENCE_IMMEDIATE, scienceMetaId);
            this.addSience(role, scienceMetaId);
            role.send(new GcTechUpgrade(scienceMetaId));
            this.srvDep.getRecordService().addRoleTotalNum(role, RoleTotalRecordType.SCIENCE_LEVEL_UP_NUM, 1);
            missionService.onMissionFinish(role, MissionType.SCIENCE_LEVEL_UP, role);
            missionService.onMissionFinish(role, MissionType.SCIENCE_LEVEL_UP_NUM, role);
            missionService.onMissionFinish(role, MissionType.RESEARCH_TECH_TIMES, Integer.parseInt(scienceMetaId), role);
            missionService.onMissionFinish(role, MissionType.START_SCIENCE_LEVEL_UP, scienceMeta.getGroup(), scienceMeta.getLevel());
            // 科技增加战力任务触发
            missionService.onMissionFinish(role, MissionType.TECH_ADD_POWER, getScienceLevelupAddPower(scienceMetaId));
            missionService.onMissionFinish(role, MissionType.SCIENCE_LEVEL_UP_ONCE);

            srvDep.getGameEventScoreService().afterSpeedUp(role, WorkType.TECH, buildingTime);
            logger.info("【GM打点需求】：玩家{}升级科技{}达到{}级，用时{}分钟", role.getRoleId(), scienceMetaId, scienceMeta.getLevel(), 0);
            this.srvDep.getBiLogUtil().technologyComplete(role, scienceMetaId, scienceMeta.getLevel(), 0);

        } else {
            // 初始化时间进度
            TechWorkContext techWorkContext = new TechWorkContext();
            techWorkContext.setMetaId(scienceMetaId);
            techWorkContext.setBuildingId(cityBuild.getPersistKey());
            workProgressManager.startWorkProgress(role, workProgress, WorkType.TECH, buildingTime, costMap, techWorkContext);
            missionService.onMissionFinish(role, MissionType.START_SCIENCE_LEVEL_UP, scienceMeta.getGroup(), scienceMeta.getLevel());
        }
    }

    /**
     * @param role
     * @param scienceMetaId 科研ID
     * @return 有指定已完成未领取的科研ID的科研建筑
     */
    private CityBuild getCityBuildTheSameUncollectScience(Role role, String scienceMetaId) {
        CityBuild cityBuild = roleCityManager.getBuildingMaxByGroupId(role.getPersistKey(), BuildingGroup.SCIENCE.getGroup());
        // 2020-6-3 加入已研发完成但为领取的科技判断，并推送建筑更新消息，防止重复研发
        if (cityBuild != null && scienceMetaId.equals(cityBuild.getSienceId())) {
            return cityBuild;
        }
        return null;
    }


    public void onWorkFinish(WorkProgress progress) {
        WorkType type = progress.getType();
        if (type != WorkType.TECH) {
            return;
        }
        TechWorkContext ctx = progress.getContext();
        long roleId = progress.getRoleId();

        Role role = this.srvDep.getWorldService().getWorld().getRole(roleId);
        if (role == null) {
            ErrorLogUtil.errorLog("ScienceWork完成时，没有发现玩家", "roleId",roleId);
            return;
        }

//		CityBuild cityBuild = roleCityManager.getBuildingMaxByGroupId(roleId, BuildingGroup.SCIENCE.getGroup());
        CityBuild cityBuild = null;
        if (progress.getBindBuildingId() == null) {
            //老玩家数据 兼容
            if (progress.getWorkQueueType() == WorkQueueType.TECH) {
                cityBuild = roleCityManager.getBuildingMaxByGroupId(role.getPersistKey(), BuildingGroup.SCIENCE.getGroup());
            }
        } else {
            //科技队列绑定建筑检测
            cityBuild = roleCityManager.getBuildingById(progress.getBindBuildingId());
        }
        if (cityBuild == null) {
//			ErrorLogUtil.errorLog("SienceWork finish Not found Build. buildid={}, roleId={}", BuildingGroup.SCIENCE.getGroup(), role.getPersistKey());
            ErrorLogUtil.errorLog("ScienceWork finish Not found Build.", "buildId",cityBuild.getGroupId(), "roleId",role.getPersistKey());
            return;
        }
        addSience(role, ctx.getMetaId());
//		cityBuild.setSienceId(ctx.getMetaId());
//		cityBuildDao.save(cityBuild);
        if (role.isOnline()) {
            this.sendTechList(role);
            role.send(new GcTechUpgrade(ctx.getMetaId()));
            GcCityUpdated gcCityUpdated = roleCityManager.toSomeBuildUpdate(roleId, cityBuild);
            role.send(gcCityUpdated);
        }
        this.srvDep.getRecordService().addRoleTotalNum(role, RoleTotalRecordType.SCIENCE_LEVEL_UP_NUM, 1);
        missionService.onMissionFinish(role, MissionType.SCIENCE_LEVEL_UP, role);
        missionService.onMissionFinish(role, MissionType.SCIENCE_LEVEL_UP_NUM, role);
        missionService.onMissionFinish(role, MissionType.RESEARCH_TECH_TIMES, Integer.parseInt(ctx.getMetaId()), role);
        missionService.onMissionFinish(role, MissionType.SCIENCE_LEVEL_UP_ONCE);

        // 科技增加战力任务触发
        missionService.onMissionFinish(role, MissionType.TECH_ADD_POWER, getScienceLevelupAddPower(ctx.getMetaId()));

        // BIlog
        ScienceMeta scienceMeta = this.srvDep.getConfigService().getConfig(ScienceConfig.class).get(ctx.getMetaId());
        if (scienceMeta != null) {
            srvDep.getBiLogUtil().technologyComplete(role, scienceMeta.getId(), scienceMeta.getLevel(), (TimeUtil.getNow() - progress.getStartTime()) / TimeUtil.MINUTE_MILLIS);
            // 更新role属性
            srvDep.getBiLogUtil().roleProfileAllScienceInfo(role, getScienceTree(role));
        }
    }

    private Map<String, Object> getScienceTree(Role role) {
        Map<Integer, ScienceInfo> map = roleTechManager.getScienceInfoMap(role.getRoleId());
        if (map == null) {
            return Collections.emptyMap();
        }
        Map<String, Object> extraMap = Maps.newHashMapWithExpectedSize(map.size());
        map.forEach((key, value) -> extraMap.put(String.valueOf(value.getGroupId()), value.getScienceLevel()));
        return extraMap;
    }

    /**
     * 获取科技升级到该级别后增加的科技的战斗力
     *
     * @param currentMetaId
     * @return
     */
    public int getScienceLevelupAddPower(String currentMetaId) {
        ScienceConfig.ScienceMeta currentMeta = srvDep.getConfigService().getConfig(ScienceConfig.class).get(currentMetaId);
        if (currentMeta == null) {
            ErrorLogUtil.errorLog("science levelup finish~current scienceMeta is null", "currentMetaId",currentMetaId);
        }

        ScienceConfig.ScienceMeta oldMeta = srvDep.getConfigService().getConfig(ScienceConfig.class).get(currentMeta.getGroup(), currentMeta.getLevel() - 1);

        int currentPower = currentMeta.getPower();
        int oldPower = oldMeta == null ? 0 : oldMeta.getPower();

        int addPower = currentPower - oldPower;
        if (addPower > 0) {
            return addPower;
        } else {
            ErrorLogUtil.errorLog("science levelup finish~current power less than oldPower", "currentMeta",currentMeta.getId(), "currentPower",currentPower, "oldPower",oldPower);
            return 0;
        }
    }

    /**
     * 科技等级是否达到
     *
     * @param role
     * @param group
     * @param level
     * @return true 达到 false 未达到
     */
    public boolean isTechLevelEnough(Role role, int group, int level) {
        return level <= roleTechManager.getScienceLevel(role.getPersistKey(), group);
    }

    /**
     * 获取科技等级
     *
     * @param role
     * @param group
     * @return
     */
    public int getTechLevel(Role role, int group) {
        return roleTechManager.getScienceLevel(role.getPersistKey(), group);
    }

    /**
     * 检查前置科技
     *
     * @param role
     * @param conditions
     * @return
     */
    public boolean checkSienceCondition(Role role, IntKeyIntValue[] conditions) {
        if (conditions == null || conditions.length == 0) {
            return true;
        }
        for (IntKeyIntValue v : conditions) {
            int level = roleTechManager.getScienceLevel(role.getPersistKey(), v.getId());
            if (level < v.getCount()) {
                // logger.error("upgradeSience checkSienceCondition error. role={}," +
                // "needid={}, needlevel={}", role.getPersistKey(), v.getId(), v.getCount());
                return false;
            }
        }
        return true;
    }

    /**
     * 检查科技条件是否达成（为礼包条件检测添加）
     *
     * @param role
     * @param conditions
     * @return
     */
    public boolean checkScienceConditionForFunction(Role role, IntKeyIntValue[] conditions, int checkGroupId) {
        if (conditions == null || conditions.length == 0) {
            return true;
        }
        // 是否检测到对应科技， 这里对要检测的科技只进行等于判断，因为策划只要求在满足触发的条件时再触发，之前历史满足的不再触发
        boolean checkValid = false;
        for (IntKeyIntValue v : conditions) {
            int level = roleTechManager.getScienceLevel(role.getPersistKey(), v.getId());
            if (v.getId() == checkGroupId) {
                if (level != v.getCount()) {
                    return false;
                }
                checkValid = true;
            } else {
                if (level < v.getCount()) {
                    return false;
                }
            }

        }
        return checkValid;
    }

    /**
     * 检查前置科技树
     *
     * @param role
     * @param conditions
     * @return
     */
    public boolean checkSienceTreeCondition(Role role, int[] conditions) {
        if (conditions == null || conditions.length == 0) {
            return true;
        }
        for (int condition : conditions) {
            if (!isMaxLevel(role.getId(), condition)) {
                return false;
            }
        }
        return true;
    }

    private boolean isMaxLevel(Long roleId, int type) {
        Map<Integer, ScienceMeta> metaMap = srvDep.getConfigService().getConfig(ScienceConfig.class).getType(type);
        for (Map.Entry<Integer, ScienceMeta> v : metaMap.entrySet()) {
            ScienceInfo scienceInfo = roleTechManager.getScienceInfo(roleId, v.getKey());
            if (scienceInfo == null || scienceInfo.getScienceLevel() < v.getValue().getLevel()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 根据科技组添加科技(掉落用)
     *
     * @param role
     * @param group
     */
    public void addTechByGroup(Role role, int group) {
        // Integer nowLevel = role.getTechManager().getTechGroupLevelMap().get(group);
        // if (nowLevel == null) {
        // nowLevel = 0;
        // }
        int nowLevel = roleTechManager.getScienceLevel(role.getPersistKey(), group);
        ScienceMeta meta = this.srvDep.getConfigService().getConfig(ScienceConfig.class).get(group, nowLevel + 1);
        if (meta == null) {
            ErrorLogUtil.errorLog("addTechByGroup meta is null", "group",group, "level",nowLevel + 1);
            return;
        }
        // List<WorkProgress> works = role.getWorkManager().getSienceWorks();
        // if (works != null) {
        // for (WorkProgress wp : works) {
        // TechWorkContext sienceCtx = wp.getContext();
        // if (sienceCtx.getMetaId().equals(meta.getId())) {
        // wp.incrementValue(wp.getRemainProgress());
        // return;
        // }
        // }
        // }

        this.addSience(role, meta.getId());
    }

    private boolean immediateFinishWork(Role role, String scienceMetaId, boolean immediate, WorkProgress workProgress) {
        if (!immediate) {
            return false;
        }
//		WorkProgress workProgress = workProgressManager.getWorkProgress(role.getId(), WorkQueueType.TECH);
        if (workProgress == null) {
            CityBuild scienceBuild = cityBuildDao.findById(workProgress.getBindBuildingId());
//			Queue<CityBuild> scienceBuild = cityBuildDao.findByRoleIdAndGroupId(role.getId(), BuildingGroup.SCIENCE.getGroup());
            ErrorLogUtil.errorLog("zhouhuiqin:非常奇怪，为什么会null", "roleId",role.getId(), "scienceBuildLevel",scienceBuild != null ? scienceBuild.getLevel() : 0);
            return false;
        }

        TechWorkContext context = workProgress.getContext();
        if (workProgress.getWorkState() != WorkState.RUNNING || context == null || !context.getMetaId().equals(scienceMetaId)) {
            return false;
        }
        long roleTime = role.getNumberProps().getInt(Prop.TECH_FREE_TIME_ADD_SECOND_VALUE_11041) * TimeUtil.SECONDS_MILLIS;
        if (roleTime >= workProgress.getRealRemainTime()) {
            workProgress.setFinish();
            workProgressDao.save(workProgress);
            return true;
        }

        return false;
    }

    // 所有科技全满
    public void GmTechAll(Role role) {
        var techConfig = srvDep.getConfigService().getConfig(ScienceConfig.class);
        var metaGroups = techConfig.getGroups();
        var roleTechByGroup = roleTechDao.getScienceInfoMap(role.getRoleId());
        if (roleTechByGroup != null) {
            for (var tech : roleTechByGroup.values()) {
                roleTechDao.delete(tech);
            }
        }
        for (var groupId : metaGroups.keySet()) {
            var techByGroup = techConfig.getGroup(groupId);
            var maxLevel = Collections.max(techByGroup.keySet());
            var meta = techConfig.get(groupId, maxLevel);
            addSience(role, meta.getId());
        }
    }
}
