package com.lc.billion.icefire.kvkseason.biz.model.legion;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lc.billion.icefire.game.biz.manager.prop.base.CalcPropHelper;
import com.lc.billion.icefire.game.biz.manager.prop.base.ICalcProp;
import com.lc.billion.icefire.game.biz.manager.prop.base.ICalcProp.LegionType;
import com.lc.billion.icefire.game.biz.manager.prop.base.IPropBean;
import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.alliance.benefitevent.AllianceBenefitsEvent;
import com.lc.billion.icefire.game.biz.model.prop.NumberPropsContainer;
import com.lc.billion.icefire.protocol.structure.PsAllianceFlagInfo;
import org.jongo.marshall.jackson.oid.MongoId;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 *
 */
public class Legion extends AbstractEntity implements IPropBean<ICalcProp.LegionType> {

	private static final long serialVersionUID = 1441625720929533840L;

	@MongoId
	private Long id;
	// 所属服务器
	private int serverId;
	private String name;
	private String banner;
	private int bannerColor;
	private String badge;
	private int badgeColor;
	// 军团长信息
	private LegionLeader leader;
	private String language;
	// 宣言
	private String declaration;
	private String country;
	// 势力值，只有赛季的
	private long prosperity;

	// 军团福利事件（直接使用 联盟福利事件结构）
	private Map<Long, AllianceBenefitsEvent> benefitsEventMap = new ConcurrentHashMap<>();

	@JsonIgnore
	protected NumberPropsContainer numberProps;
	@JsonIgnore
	private CalcPropHelper<ICalcProp.LegionType> legionCalcPropHelper;

	private List<LegionAnnouncement> announcementList = new ArrayList<>();

	public Legion() {
		numberProps = new NumberPropsContainer();
		legionCalcPropHelper = createCalcPropHelper();
	}

	public PsAllianceFlagInfo toPsAllianceFlagInfo() {
		PsAllianceFlagInfo allianceFlagInfo = new PsAllianceFlagInfo();
		allianceFlagInfo.setBadge(badge);
		allianceFlagInfo.setBadgeColor(badgeColor);
		allianceFlagInfo.setBanner(banner);
		allianceFlagInfo.setBannerColor(bannerColor);
		return allianceFlagInfo;
	}

	@Override
	public void setPersistKey(Long id) {
		this.id = id;
	}

	@Override
	public Long getPersistKey() {
		return id;
	}

	@Override
	public CalcPropHelper<LegionType> getCalcPropHelper() {
		return legionCalcPropHelper;
	}

	@Override
	public CalcPropHelper<LegionType> createCalcPropHelper() {
		return new CalcPropHelper<>(ICalcProp.LegionType.ALLIANCE);
	}

	@Override
	public void setCalcPropHelper(CalcPropHelper<LegionType> helper) {
		legionCalcPropHelper = helper;
	}

	@Override
	public NumberPropsContainer getNumberProps() {
		return numberProps;
	}

	@Override
	public Long getId() {
		return getPersistKey();
	}

	@Override
	public Long getGroupingId() {
		return getPersistKey();
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public int getServerId() {
		return serverId;
	}

	public void setServerId(int serverId) {
		this.serverId = serverId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getBanner() {
		return banner;
	}

	public void setBanner(String banner) {
		this.banner = banner;
	}

	public int getBannerColor() {
		return bannerColor;
	}

	public void setBannerColor(int bannerColor) {
		this.bannerColor = bannerColor;
	}

	public String getBadge() {
		return badge;
	}

	public void setBadge(String badge) {
		this.badge = badge;
	}

	public int getBadgeColor() {
		return badgeColor;
	}

	public void setBadgeColor(int badgeColor) {
		this.badgeColor = badgeColor;
	}

	public LegionLeader getLeader() {
		return leader;
	}

	public Long getLeaderRoleId() {
		return leader.getRoleId();
	}

	public Long getLeaderAllianceId() {
		return leader.getAllianceId();
	}

	public void setLeader(LegionLeader leader) {
		this.leader = leader;
	}

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public String getDeclaration() {
		return declaration;
	}

	public void setDeclaration(String declaration) {
		this.declaration = declaration;
	}

	public long getProsperity() {
		return prosperity;
	}

	public void setProsperity(long prosperity) {
		this.prosperity = prosperity;
	}

	public Map<Long, AllianceBenefitsEvent> getBenefitsEventMap() {
		return benefitsEventMap;
	}

	public void setBenefitsEventMap(Map<Long, AllianceBenefitsEvent> benefitsEventMap) {
		this.benefitsEventMap = benefitsEventMap;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public List<LegionAnnouncement> getAnnouncementList() {
		return announcementList;
	}

	public void setAnnouncementList(List<LegionAnnouncement> announcementList) {
		this.announcementList = announcementList;
	}
}
