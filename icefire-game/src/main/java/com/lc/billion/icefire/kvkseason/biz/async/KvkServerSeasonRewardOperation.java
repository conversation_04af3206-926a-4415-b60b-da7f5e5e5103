package com.lc.billion.icefire.kvkseason.biz.async;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.biz.config.kvk.KvkSeasonServerRewardConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.model.email.AbstractEmail;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.service.impl.drop.DropServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.email.MailCreator;
import com.lc.billion.icefire.game.biz.service.impl.email.MailSender;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.rank.impl.ServerProsperityRank;
import com.lc.billion.icefire.game.support.LogReasons;
import com.simfun.sgf.utils.JavaUtils;

import java.util.ArrayList;
import java.util.List;

public class KvkServerSeasonRewardOperation implements AsyncOperation {
    @Override
    public boolean run() {
        ServerProsperityRank serverProsperityRank = Application.getBean(ServerProsperityRank.class);
        var rankItems = serverProsperityRank.getServerItems();
        logger.info("settle begin data:{}", rankItems);
        if (rankItems == null || rankItems.isEmpty()) {
            return false;
        }

        ConfigServiceImpl configService = Application.getBean(ConfigServiceImpl.class);
        var serverRewardConfig = configService.getConfig(KvkSeasonServerRewardConfig.class);
        RoleDao roleDao = Application.getBean(RoleDao.class);
        DropServiceImpl dropService = Application.getBean(DropServiceImpl.class);
        String mailId = EmailConstants.SEASON_SERVER_REWARD_RANK_MAIL;
        MailCreator mailCreator = Application.getBean(MailCreator.class);
        MailSender mailSender = Application.getBean(MailSender.class);
        int rank = 1;
        List<AbstractEmail> sendMailList = new ArrayList<>(100);
        for (var t : rankItems) {
            Integer serverId = t.getFirst();
            var rankMeta = serverRewardConfig.getRankMeta(rank);
            if (null == rankMeta) {
                ErrorLogUtil.errorLog("settle meta is error", "serverId",serverId, "rank",rank);
                continue;
            }

            var dropItems = dropService.drop(rankMeta.getReward());
            var roles = roleDao.findByOServerId(serverId);
            for (var role : roles) {
                List<String> params = new ArrayList<>();
                params.add(role.getName());
                params.add(String.valueOf(rank));
                var systemMail = mailCreator.createSystemMail(role.getPersistKey(), role.getCurrentServerId(), mailId, dropItems,
                        params, LogReasons.ItemLogReason.SEASON_SERVER_REWARD);
                sendMailList.add(systemMail);
                //每100个为1组，不足100的在下面发
                if (sendMailList.size() >= 100) {
                    mailSender.sendBatchMail(sendMailList);
                    sendMailList = new ArrayList<>(100);
                }
                logger.info("settle serverId:{} rank:{} role:{}", serverId, rank, role.getRoleId());
            }
            logger.info("settle serverId:{} rank:{} end", serverId, rank);
            rank++;
        }

        if (JavaUtils.bool(sendMailList)) {
            mailSender.sendBatchMail(sendMailList);
        }

        logger.info("settle end");
        return false;
    }
}
