package com.lc.billion.icefire.kvkseason.biz.manager.legion;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lc.billion.icefire.game.biz.manager.AllianceManager;
import com.lc.billion.icefire.game.biz.manager.AllianceMemberManager;
import com.lc.billion.icefire.game.biz.manager.IRoleAllianceManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceMember;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.kvkseason.biz.dao.mongo.root.LegionMarkDao;
import com.lc.billion.icefire.kvkseason.biz.model.legion.Legion;
import com.lc.billion.icefire.kvkseason.biz.model.legion.LegionMark;
import com.lc.billion.icefire.protocol.GcLegionMarkList;
import com.simfun.sgf.common.tuple.Tuple;
import com.simfun.sgf.common.tuple.TwoTuple;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 *
 */
@Component
public class LegionMarkManager implements IRoleAllianceManager {

	@Autowired
	private AllianceManager allianceManager;
	@Autowired
	private LegionManager legionManager;
	@Autowired
	private LegionMarkDao legionMarkDao;
	@Autowired
	private AllianceMemberManager allianceMemberManager;
	@Autowired
	private RoleManager roleManager;

	public void onLegionDismiss(Legion legion) {
		List<LegionMark> legionMarks = legionMarkDao.findByLegionId(legion.getId());
		if (JavaUtils.bool(legionMarks)) {
			legionMarkDao.delete(legionMarks);
		}
	}

	@Override
	public void onAllianceJoin(Long roleId, Long allianceId) {
		// TODO Auto-generated method stub

	}

	@Override
	public void onAllianceLeave(Long roleId, Long allianceId) {
		// TODO Auto-generated method stub

	}

	@Override
	public void onLegionJoin(Long allianceId, Long legionId) {
		if (!JavaUtils.bool(allianceId)) {
			return;
		}
		Legion legion = legionManager.findLegionByAllianceId(allianceId);
		if (legion == null) {
			return;
		}
		Alliance alliance = allianceManager.getAllianceById(allianceId);
		if (alliance == null) {
			return;
		}
		Map<Integer, TwoTuple<Boolean, GcLegionMarkList>> map = new HashMap<>();
		List<AllianceMember> onlineMembers = allianceMemberManager.getOnlineMembers(alliance);
		for (AllianceMember allianceMember : onlineMembers) {
			Role role = roleManager.getRole(allianceMember.getPersistKey());
			if (role == null) {
				continue;
			}
			GcLegionMarkList gcLegionMarkList = null;
			int currentServerId = role.getCurrentServerId();
			TwoTuple<Boolean, GcLegionMarkList> twoTuple = map.get(currentServerId);
			if (twoTuple == null) {
				List<LegionMark> legionMarks = legionMarkDao.findByLegionIdAndServerId(legionId, currentServerId);
				if (JavaUtils.bool(legionMarks)) {
					gcLegionMarkList = new GcLegionMarkList();
					for (LegionMark mark : legionMarks) {
						gcLegionMarkList.addToMarks(mark.toPsLegionMark());
					}

				}
				map.put(currentServerId, Tuple.tuple(gcLegionMarkList != null, gcLegionMarkList));
			} else {
				gcLegionMarkList = twoTuple.getSecond();
			}
			if (gcLegionMarkList != null) {
				role.send(gcLegionMarkList);
			}
		}
	}

	@Override
	public void onLegionLeave(Long allianceId, Long legionId) {
		// TODO Auto-generated method stub

	}

}
