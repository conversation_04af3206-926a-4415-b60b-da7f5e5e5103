package com.lc.billion.icefire.kvkseason.biz.model.seasonTask;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.annotation.MongoIndex;
import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.IRolesEntity;

/**
 * 玩家赛季任务相关的累计，累计记录
 * 
 * <AUTHOR>
 * @date 2021/11/15
 */
public class RoleSeasonTaskRecord extends AbstractEntity implements IRolesEntity {

	@MongoId
	private Long id;
	@MongoIndex
	private Long roleId;
	private String metaId;
	private List<Integer> conditions = new ArrayList<>();// 这个任务对应的行为积分类型，配置表中的数据，放在这里缓存
	//
	// 这里进度信息缓存，只缓存个人的。 如果Key类型是 联盟或者服务器的，需要去对应实现里面去取进度
	// 原因：如果服务器的信息也在这里缓存。服务器进度一旦变化，会需要更新这个服务器所有玩家的相关任务，耗费比较大且频繁。联盟也是这个问题。所以将这两个单独存储，变化的时候无需同步。
	private Map<Integer, Long> progress = new HashMap<>();
	private boolean reward;// 是否领奖
	private boolean finish;// 是否完成， 如果是true 一定完成。 如果是false ，不一定未完成
	private boolean expire;// 是否过期

	@Override
	public void setPersistKey(Long id) {
		this.id = id;
	}

	@Override
	public Long getPersistKey() {
		return this.id;
	}

	@Override
	public Long getGroupingId() {
		return this.id;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	@Override
	public Long getRoleId() {
		return this.roleId;
	}

	@Override
	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	public String getMetaId() {
		return metaId;
	}

	public void setMetaId(String metaId) {
		this.metaId = metaId;
	}

	public List<Integer> getConditions() {
		return conditions;
	}

	public void setConditions(List<Integer> conditions) {
		this.conditions = conditions;
	}

	public Map<Integer, Long> getProgress() {
		return progress;
	}

	public void setProgress(Map<Integer, Long> progress) {
		this.progress = progress;
	}

	public boolean isReward() {
		return reward;
	}

	public void setReward(boolean reward) {
		this.reward = reward;
	}

	public boolean isFinish() {
		return finish;
	}

	public void setFinish(boolean finish) {
		this.finish = finish;
	}

	public void updateProgress(int actionType, long value) {
		progress.put(actionType, value);
	}

	public long getProgress(int actionType) {
		Long ret = progress.get(actionType);
		if (ret == null)
			return 0;
		return ret;
	}

	public boolean isExpire() {
		return expire;
	}

	public void setExpire(boolean expire) {
		this.expire = expire;
	}
}
