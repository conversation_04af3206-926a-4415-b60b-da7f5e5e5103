package com.lc.billion.icefire.gvgcontrol.biz.model;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import lombok.Getter;
import lombok.Setter;
import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.IAlliancesEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 *
 * 参加gvg的联盟相关记录
 * 
 * 排行榜信息也存在这里，三种情况同步：1、计算之后中控广播所有game；2、game服启动rpc到中控取；3、中控服启服广播给所有game
 *
 * <AUTHOR>
 * @date 2020/12/8
 */
public class GvgAllianceRecord extends AbstractEntity implements IAlliancesEntity {
	private static final long serialVersionUID = 7741647706452760018L;

	@Getter
    @Setter
    @MongoId
	private Long allianceId;// 联盟id
	// 报名相关信息
	@Setter
    @Getter
    private int serverId;// 联盟是几服的

	@Getter @Setter
	private Map<GvgMatchType, AllianceBattleRecord> recordGroupByMatchType = new MyConcurrentMap<>();

	@Getter @Setter
	private static class AllianceBattleRecord {
		private int continuousCount;// 连胜或连败次数，正负值区分
		private int winCount;// 对战胜利次数(积分)
		private long score;// 净胜分
		private boolean win;// 最近一次是否胜利
		List<Integer> winLoseRecord = new ArrayList<>();

		@Override
		public String toString(){
 			return "AllianceBattleRecord{" +
					"continuousCount=" + continuousCount +
					", winCount=" + winCount +
					", score=" + score +
					", win=" + win +
					", winLoseRecord=" + winLoseRecord +
					'}';
		}
	}

	@Deprecated
	private int continuousCount;// 连胜或连败次数，正负值区分
	@Deprecated
	private int winCount;// 对战胜利次数(积分)
	@Deprecated
	private long score;// 净胜分
	@Deprecated
	private boolean win;// 最近一次是否胜利
	@Deprecated
	private List<Integer> winLoseRecord = new ArrayList<>();

	public Long getAllianceId() {
		return allianceId;
	}

	public void setAllianceId(Long allianceId) {
		this.allianceId = allianceId;
	}

	public boolean fixOldData(){
		if(recordGroupByMatchType.containsKey(GvgMatchType.FRIENDLY)){
			return false;
		}

		if(winLoseRecord == null || winLoseRecord.isEmpty()){
			return false;
		}

		var newAllianceRecord = new AllianceBattleRecord();
		newAllianceRecord.setContinuousCount(continuousCount);
		newAllianceRecord.setWinCount(winCount);
		newAllianceRecord.setScore(score);
		newAllianceRecord.setWin(win);
		newAllianceRecord.setWinLoseRecord(winLoseRecord);

		recordGroupByMatchType.put(GvgMatchType.FRIENDLY, newAllianceRecord);
		return true;
	}

	public long getScore(GvgMatchType matchType) {
		var record = recordGroupByMatchType.get(matchType);
		if(record == null) {
			return 0;
		}
		return record.score;
	}

    @Override
	public void setPersistKey(Long id) {
		this.allianceId = id;
	}

	@Override
	public Long getPersistKey() {
		return this.allianceId;
	}

	@Override
	public Long getGroupingId() {
		return this.allianceId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public void addResult(GvgMatchType matchType, boolean isWin, long score) {
		int result = isWin ? 1 : -1;
		recordGroupByMatchType.compute(matchType, (k, record) -> {
			if(record == null){
				record = new AllianceBattleRecord();
			}

			while (record.winLoseRecord.size() >= 5) {
				record.winLoseRecord.removeFirst();
			}
			record.winLoseRecord.add(result);
			record.setScore(score);

			return record;
		});
	}

	public List<Integer> getWinLoseRecordOfType(GvgMatchType matchType) {
		if(recordGroupByMatchType.containsKey(matchType)){
			return recordGroupByMatchType.get(matchType).getWinLoseRecord();
		}

		return Collections.emptyList();
	}
}
