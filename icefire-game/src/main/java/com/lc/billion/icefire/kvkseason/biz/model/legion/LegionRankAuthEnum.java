package com.lc.billion.icefire.kvkseason.biz.model.legion;

import java.util.Map;

import com.lc.billion.icefire.game.exception.AlertException;
import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IdEnum;

/**
 * <AUTHOR>
 *
 */
public enum LegionRankAuthEnum implements IdEnum<String> {

	/** 解散军团 */
	AUTH_LEGION_DISBAND("legionDisband", "解散军团"),
	/** 军团审核 */
	AUTH_LEGION_AUDIT("legionAudit", "军团审核"),
	/** 军团踢联盟 */
	AUTH_LEGION_KICKS("legionKicks", "军团踢联盟"),
	/** 军团官衔任命（还需要判断对应分支） */
	AUTH_LEGION_RANK_APPOINTMENT("legionRankAppointment", "军团官衔任命（还需要判断对应分支）"),
	/** 军团基本信息修改 */
	AUTH_LEGION_EDIT("legionEdit", "军团基本信息修改"),
	/** 军团可分配奖励的 分配权限 */
	AUTH_ALLOCATE_LEGION_BENEFITS("allocateLegionBenefits", "分配军团大事件"),
	/** 军团转让主联盟 */
	AUTH_LEGION_TRANSFER("legionTransfer", "军团转让主联盟"),
	/** 军团事务增加权限 */
	AUTH_LEGION_AFFAIRS_01("legionAffairs01", "军团事务增加权限"),
	/** 军团事务置顶权限 */
	AUTH_LEGION_AFFAIRS_02("legionAffairs02", "军团事务置顶权限"),
	/** 军团事务修改权限 */
	AUTH_LEGION_AFFAIRS_03("legionAffairs03", "军团事务修改权限"),
	/** 军团事务删除权限 */
	AUTH_LEGION_AFFAIRS_04("legionAffairs04", "军团事务删除权限"),
	/** 军团驻防踢人 */
	AUTH_LEGION_KICK_ARMY_OUT("legionKickArmyOut", "建筑驻防踢人"),

	AUTH_ANNOUNCEMENT_EDIT("legionPermission", "处理军团公告"),

	AUTH_MAIL_ALL("legionMailAll", "军团全体邮件"),

	AUTH_LEGION_MARK("legionFlagSet", "军团标记"),

	;

	private static final Map<String, LegionRankAuthEnum> map = EnumUtils.toMap(values());

	private String id;
	private String desc;

	private LegionRankAuthEnum(String id, String desc) {
		this.id = id;
		this.desc = desc;
	}

	@Override
	public String getId() {
		return id;
	}

	public static LegionRankAuthEnum findById(String id) {
		LegionRankAuthEnum auth = map.get(id);
		if (auth == null) {
			throw new AlertException("枚举LegionRankAuthEnum缺少定义","id",id);
		}
		return auth;
	}

	@Override
	public String toString() {
		return id + "-" + desc;
	}

}
