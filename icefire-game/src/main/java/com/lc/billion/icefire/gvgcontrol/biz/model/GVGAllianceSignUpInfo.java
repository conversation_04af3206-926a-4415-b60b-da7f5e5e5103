package com.lc.billion.icefire.gvgcontrol.biz.model;

import java.util.ArrayList;
import java.util.List;

import com.lc.billion.icefire.game.biz.model.gvg.GVGAllianceLineUpInfo;
import lombok.Getter;
import lombok.Setter;
import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.annotation.MongoIndex;
import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.IAlliancesEntity;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;

/**
 * <AUTHOR>
 *
 */
public class GVGAllianceSignUpInfo extends AbstractEntity implements IAlliancesEntity {

	private static final long serialVersionUID = 4470862642185407758L;

	@Setter @Getter
    @MongoId
	private Long id;
	private Long allianceId;
	@Setter @Getter
    private int serverId;
	@Setter @Getter
    private long fixedMatchScore;// 修正后的匹配积分
	@Setter @Getter
    private GVGAllianceLineUpInfo gvgAllianceLineUpInfo;
	@Getter
    private final List<String> selectMemberParam = new ArrayList<>();// 选人log 角色名、操作时间
	@Getter
    private final List<String> selectTimeParam = new ArrayList<>();// 选时间log 角色名、操作时间
	
	//是否轮空， gvg杯赛进入淘汰赛阶段 ，胜者组联盟可能有轮空的情况。 用此字段表示。
	@Setter @Getter
	private boolean bye;

	// 新加字段：区分 这个报名信息是 原GVG 还是 杯赛周六场或杯赛周日场
	@Setter @Getter
	private GvgMatchType matchType;

	// 新加字段：gvg杯赛 ：当前报名信息版本号：WeekOfYear
	@Setter @Getter
    private int gameRound;

	//新加字段：gvg杯赛真比赛轮次
	@Setter @Getter
    private int round;

	@Setter @Getter
    private int warZoneId;

	@Deprecated
	private long baseMatchScore;// 基础积分

	@Deprecated
	private int signUpTimeIndex;// 选择的时间点，具体类型待定

	//可选时间所属组号
	@Deprecated
	private int timeSelectGroupIndex;

	@Deprecated
	private int signUpTimeIndex2;

	// 选择时间列表
	@Deprecated
	private final List<Integer> selectTimeIndexs = new ArrayList<>();

	// 选择时间权重
	@Deprecated
	private final List<Integer> selectTimeWeights = new ArrayList<>();

    public long getBaseMatchScore() {
		return gvgAllianceLineUpInfo.getTotalFightPower();
	}

    @Override
	public void setPersistKey(Long id) {
		this.id = id;
	}

	@Override
	public Long getPersistKey() {
		return id;
	}

	@Override
	public Long getGroupingId() {
		return allianceId;
	}

	@Override
	public Long getAllianceId() {
		return allianceId;
	}

	@Override
	public void setAllianceId(Long allianceId) {
		this.allianceId = allianceId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}
	
	@Override
	public String toString(){
		return "GVGAllianceSignUpInfo [id=" + id
				+ ", allianceId=" + allianceId
				+ ", serverId=" + serverId 
				+ ", fixedMatchScore=" + fixedMatchScore 
				+ ", gvgAllianceLineUpInfo=" + gvgAllianceLineUpInfo 
				+ ", selectMemberParam=" + selectMemberParam 
				+ ", selectTimeParam=" + selectTimeParam 
				+ ", matchType=" + matchType
				+ ", gameRound=" + gameRound
				+ ", round=" + round 
				+ ", warZoneId=" + warZoneId 
				+ ", baseMatchScore=" + baseMatchScore
				+ ", signUpTimeIndex=" + signUpTimeIndex 
				+ ", timeSelectGroupIndex=" + timeSelectGroupIndex
				+ ", signUpTimeIndex2=" + signUpTimeIndex2 
				+ ", bye=" + bye 
				+ "]";
	}
}
