package com.lc.billion.icefire.gvgbattle.biz.service.impl;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.graph.Position;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgBuildingConfig;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.manager.gvg.GVGBattleDataVoManager;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.BuildingType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.function.Consumer;

@Service
public class GVGWuChaoManager {
    private static final Logger logger = LoggerFactory.getLogger(GVGWuChaoManager.class);
    @Autowired
    private GVGBattleDataVoManager gvgBattleDataVoManager;
    @Autowired
    private ConfigServiceImpl configService;
    private GVGSupplyWagon supplyWagon;
    private Consumer<GVGWuChaoManager> onSupplyWagonArrive;
    private int wuChaoCompleteCount = 0;
    private long nextOpenTime;
    private Point wuChaoPos;

    public void beginSupplyWagon(Long occupierAllianceId, Consumer<GVGWuChaoManager> onSupplyWagonArrive) {
        var challengerAllianceId = gvgBattleDataVoManager.getDispatchRecordInfo().getOpponentAllianceId(occupierAllianceId);
        var occupierDaYingPos = getDaYingPos(occupierAllianceId);
        var challengerDaYingPos = getDaYingPos(challengerAllianceId);
        wuChaoPos = getWuChaoPos();

        var posList = new Position[] {
                challengerDaYingPos.toPosition(),   // 往占领乌巢联盟的大营开
                wuChaoPos.toPosition(),
                occupierDaYingPos.toPosition()};
        if(supplyWagon == null) {
            supplyWagon = new GVGSupplyWagon(occupierAllianceId, challengerAllianceId, totalTravelTime(), 0.5f, posList);
            this.onSupplyWagonArrive = onSupplyWagonArrive;
            logger.info("[GVG]beginSupplyWagon new:{}", occupierAllianceId);
        } else {
            if(!Objects.equals(occupierAllianceId, supplyWagon.occupierAllianceId)){
                logger.info("[GVG]beginSupplyWagon reverse:{}->{}", supplyWagon.occupierAllianceId, occupierAllianceId);
                supplyWagon.reverse();
            } else {
                ErrorLogUtil.errorLog("[GVG]beginSupplyWagon, same alliance", "allianceId",occupierAllianceId);
            }
        }
        nextOpenTime = 0;
    }

    public void tick() {
        if(supplyWagon != null){
            if(supplyWagon.arrived()){
                try {       // 为了确保supplyWagon一定会被置为空，加个内部try-catch
                    wuChaoCompleteCount++;
                    var gvgSetting = configService.getConfig(GvgSettingConfig.class);
                    nextOpenTime = TimeUtil.getNow() + gvgSetting.getWuChaoCDTime() * TimeUtil.SECONDS_MILLIS;
                    logger.info("[GVG]supplyWagon.arrived completeCount:{}, nextOpenTime:{}({})", wuChaoCompleteCount, nextOpenTime, gvgSetting.getWuChaoCDTime());
                    onSupplyWagonArrive.accept(this);
                } catch (ExpectedException ignored){

                } catch(Exception e) {
                    ErrorLogUtil.exceptionLog("[GVG]GVGWuChaoManager tick exception", e,
                            "armyId",supplyWagon.armyId, "beginTime",supplyWagon.beginTime,
                            "endTime",supplyWagon.endTime, "totalTime",supplyWagon.totalTime);
                }
                supplyWagon = null;
            }
        }
    }

    public GVGSupplyWagon getSupplyWagon(){
        return supplyWagon;
    }

    public Point wuChaoPos(){
        return wuChaoPos;
    }

    private Point getDaYingPos(Long allianceId){
        var daYingMetaId = gvgBattleDataVoManager.findGvgBirthArea(allianceId).getDaYingMetaId();
        var gvgBuildingConfig = configService.getConfig(GvgBuildingConfig.class);
        var daYingMeta = gvgBuildingConfig.get(daYingMetaId);
        return daYingMeta.getBuildingPosition();
    }

    public int getDaYingPosSize(Long allianceId){
        var daYingMetaId = gvgBattleDataVoManager.findGvgBirthArea(allianceId).getDaYingMetaId();
        var gvgBuildingConfig = configService.getConfig(GvgBuildingConfig.class);
        var daYingMeta = gvgBuildingConfig.get(daYingMetaId);
        int buildingRange = daYingMeta.getBuildingRange();
        return buildingRange <= 0 ? 1 : buildingRange;
    }

    private Point getWuChaoPos(){
        var gvgBuildingConfig = configService.getConfig(GvgBuildingConfig.class);
        var wuChaoMeta = gvgBuildingConfig.getBuildingMetaByType(BuildingType.WuChao.getId());
        return wuChaoMeta.getBuildingPosition();
    }

    public int getWuChaoPosSize(){
        var gvgBuildingConfig = configService.getConfig(GvgBuildingConfig.class);
        var wuChaoMeta = gvgBuildingConfig.getBuildingMetaByType(BuildingType.WuChao.getId());
        int buildingRange = wuChaoMeta.getBuildingRange();
        return buildingRange <= 0 ? 1 : buildingRange;
    }

    private long totalTravelTime(){
        var gvgSetting = configService.getConfig(GvgSettingConfig.class);
        return gvgSetting.getSupplyWagonTravelTime() * TimeUtil.SECONDS_MILLIS;
    }

    public long getNextOpenTime(){
        return nextOpenTime;
    }

    public int getCompleteCount() {
        return wuChaoCompleteCount;
    }

    public boolean inCD(){
        return nextOpenTime > 0 && TimeUtil.getNow() < nextOpenTime;
    }
}
