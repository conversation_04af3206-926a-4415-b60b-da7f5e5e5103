package com.lc.billion.icefire.gvgcontrol.biz.model;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;

/**
 * 有资格参赛的服务器
 * 
 * <AUTHOR>
 *
 */
public class GVGQualifiedServer extends AbstractEntity {

	private static final long serialVersionUID = -1527799930914044041L;

	@MongoId
	private Long serverId;

	private boolean firstJoin;

	public int getServerId() {
		return Long.valueOf(serverId).intValue();
	}

	@Override
	public void setPersistKey(Long id) {
		serverId = id;
	}

	@Override
	public Long getPersistKey() {
		return serverId;
	}

	@Override
	public Long getGroupingId() {
		return serverId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public boolean isFirstJoin() {
		return firstJoin;
	}

	public void setFirstJoin(boolean firstJoin) {
		this.firstJoin = firstJoin;
	}
}
