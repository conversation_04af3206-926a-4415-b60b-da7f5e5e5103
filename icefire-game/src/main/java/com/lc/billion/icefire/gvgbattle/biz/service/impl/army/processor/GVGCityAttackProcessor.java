package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.processor;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleCity;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.login.LoginServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.notice.NoticeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 
 * GVG攻击玩家
 * 
 * <AUTHOR>
 * @date 2021/2/4
 */
@Service
public class GVGCityAttackProcessor extends AbstractGvgArmyProcessor {
	@Autowired
	private AllianceServiceImpl allianceService;

	@Override
	public ArmyType getArmyType() {
		return ArmyType.GVG_ATTACK_PLAYER;
	}

	@Override
	protected boolean check(Role role, SceneNode targetNode, ArmySetoutParam param) {
		if (!Application.getBean(LoginServiceImpl.class).isLoginSwitch()) {// gvg战斗服停了，战斗结束了
			return false;
		}
		if (!gvgStrongHoldService.isMoveGvgActivityStage(TimeUtil.getNow()))// 入场阶段，不允许行军
			return false;
		// 目标检测
		if (targetNode == null || targetNode.getNodeType() != SceneNodeType.CITY) {
			return false;
		}
		// 保护罩检测
		RoleCity targetCity = (RoleCity) targetNode;
		if (sdp.getProtectionService().hasProtection(targetCity)) {
			sdp.getNoticeService().notice(role, NoticeConstants.TARGET_IS_PROTECT, true);
			return false;
		}
		// 同联盟检测
		if (allianceService.isSameAlliance(role.getPersistKey(), targetCity.getPersistKey())) {
			return false;
		}

		// 判断玩家是否在出生区
		if(gvgStrongHoldService.isInBirthArea(targetNode.getPersistKey(), targetNode.getPosition())){
			ErrorLogUtil.errorLog("GVG攻击玩家在出生区", "attackRoleId",role.getPersistKey(),
					"targetRoleId",targetNode.getPersistKey(), "posX",targetNode.getPosition().getX(), "posY",targetNode.getPosition().getY());
			return false;
		}
		return true;
	}

	@Override
	protected void start(ArmyInfo army) {
		sdp.getArmyServiceImpl().addArmyToAoi(army, addToAoi());
		// 添加到联盟战争
		warService.createWar(army);
	}
}
