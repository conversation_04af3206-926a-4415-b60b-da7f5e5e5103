package com.lc.billion.icefire.gvgbattle.biz.service.impl;

import com.google.api.client.util.Lists;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.RoleServerInfoDao;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleServerInfo;
import com.lc.billion.icefire.game.biz.schedule.ScheduleOperation;
import com.lc.billion.icefire.game.biz.service.ScheduleService;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.tvt.TVTGameService;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.AllianceBattlePointDao;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.RoleGVGBattleDao;
import com.lc.billion.icefire.gvgbattle.biz.manager.gvg.GVGBattleDataVoManager;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.AllianceBattlePoint;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.RoleGVGBattle;
import com.lc.billion.icefire.gvgbattle.biz.service.GVGBattleToGVGControlService;
import com.lc.billion.icefire.protocol.GcTVTSettlementInfo;
import com.lc.billion.icefire.protocol.structure.*;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGAllianceSignUpInfoVo;
import com.lc.billion.icefire.rpc.vo.gvg.GvgBattleServerDispatchRecordVo;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.config.WorldMapConfig;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TVTBattleService {
	private static final Logger logger = LoggerFactory.getLogger(TVTBattleService.class);

	@Autowired
	private RoleGVGBattleDao roleGVGBattleDao;
	@Autowired
	private RoleDao roleDao;
	@Autowired
	private GVGBattleDataVoManager gvgBattleDataVoManager;
	@Autowired
	private GVGStrongHoldService GVGStrongHoldService;
	@Autowired
	private GVGBattleToGVGControlService gvgBattleToGVGControlService;
	@Autowired
	private GVGBattleService gvgBattleService;
	@Autowired
	private ScheduleService scheduleService;
	@Autowired
	private RoleServerInfoDao roleServerInfoDao;
	@Autowired
	private AllianceBattlePointDao allianceBattlePointDao;
	@Autowired
	private TVTGameService tvtGameService;
	@Autowired
	private ServiceDependency srvDpd;

	private boolean isTVTBattleServer;

	public void startService() {
		WorldMapConfig worldMapConfig = ServerConfigManager.getInstance().getWorldMapConfig();
		isTVTBattleServer = worldMapConfig.getServerType() == ServerType.TVT_BATTLE;

		if (isTVTBattleServer) {
			int serverId = Application.getServerId();
			logger.info("战斗服{}启服，取中控服注册", serverId);

			// 启服通知中控服
			ActivityVo activityVo = gvgBattleToGVGControlService.findActivity();
			gvgBattleDataVoManager.updateActivityVo(activityVo);
			GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo = gvgBattleToGVGControlService.registerGVGBattleServerToGVGControlServer();
			if(gvgBattleServerDispatchRecordVo == null){
				logger.info("TVT 战斗服启动 GvgBattleServerDispatchRecordVo为空");
			}else {
				logger.info("TVT 战斗服启动 GvgBattleServerDispatchRecordVo非空");
			}

			gvgBattleDataVoManager.updateGvgBattleServerDispatchRecordVo(gvgBattleServerDispatchRecordVo);

			// 拉取对阵双方数据
			List<GVGAllianceSignUpInfoVo> gvgAllianceSignUpInfoVos = gvgBattleToGVGControlService.findGVGAllianceSignUpInfoVo(serverId);
			if(!JavaUtils.bool(gvgAllianceSignUpInfoVos)){
				logger.info("TVT 战斗服启动 GVGAllianceSignUpInfoVo为空");
			}else {
				logger.info("TVT 战斗服启动 GVGAllianceSignUpInfoV非空");
			}

			gvgBattleDataVoManager.updateGvgAllianceSignUpInfoVo(gvgAllianceSignUpInfoVos);

			// 初始化据点
			gvgBattleService.initStrongHoldNodes();
			//
			gvgBattleDataVoManager.initBattleFieldTimeLine();

			if (gvgBattleServerDispatchRecordVo == null) {
				// 战斗服启服没有匹配信息，30分钟后销毁
				int delay = 300;
				ErrorLogUtil.errorLog("战斗服启服没有匹配信息,定时销毁", "delay",delay);
				scheduleService.schedule(new ScheduleOperation() {
					@Override
					public void execute() {
						// 10秒检测一次人是否清干净了
						boolean checkAllRoleGoBack = gvgBattleService.checkAllRoleGoBack();
						if (checkAllRoleGoBack) {
							// 通知中控服关闭战斗服
							logger.info("所有人已被踢，通知中控服销毁");
							gvgBattleToGVGControlService.noticeControlServerDestroyBattleServer(Application.getServerId());
						} else {
							logger.info("战斗服还有人在，回收失败，注意关注{}", Application.getServerId());
						}
					}
				}, delay * TimeUtil.MINUTE_MILLIS);
			}
		}
	}

	/**
	 * 杀敌积分
	 * */
	public void addKillEnemyScore(Role role, int addScore){
		RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(role.getPersistKey());
		if(roleGVGBattle != null){
			roleGVGBattle.setKillEnemyScore(roleGVGBattle.getKillEnemyScore() + addScore);
			roleGVGBattleDao.save(roleGVGBattle);
		}
	}

	/**
	 * 杀怪积分
	 * */
	public void addKillNpcScore(Role role, int addScore){
		RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(role.getPersistKey());
		if(roleGVGBattle != null){
			roleGVGBattle.setKillNpcScore(roleGVGBattle.getKillNpcScore() + addScore);
			roleGVGBattleDao.save(roleGVGBattle);
		}
	}

	/**
	 * 采集积分
	 * */
	public void addGatherScore(Role role, int addScore){
		RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(role.getPersistKey());
		if(roleGVGBattle != null){
			roleGVGBattle.setGatherScore(roleGVGBattle.getGatherScore() + addScore);
			roleGVGBattleDao.save(roleGVGBattle);
		}
	}

	/**
	 * TVT战斗结算
	 * */
	public void tvtSettlement(Long victoryAllianceId, Long failAllianceId, int victoryAlliancePoint, int failAlliancePoint){
		Collection<RoleGVGBattle> roleGVGBattles = roleGVGBattleDao.findAll();
		List<RoleGVGBattle> victoryRoleBattles = Lists.newArrayList();
		List<RoleGVGBattle> failRoleBattles = Lists.newArrayList();
		if (JavaUtils.bool(roleGVGBattles)) {
			for (RoleGVGBattle roleGVGBattle : roleGVGBattles) {
				if (roleGVGBattle.getAllianceId() != null) {
					int result = 0;
					if(roleGVGBattle.getAllianceId().longValue() != victoryAllianceId.longValue()){
						result = 1;
					}

					logger.info("TVT结算 Role:{}/{} 杀怪积分:{} 杀NPC积分:{} 采集积分:{} 建筑积分:{}",
							roleGVGBattle.getRoleId(), roleGVGBattle.getBattlePoint(),
							roleGVGBattle.getKillEnemyScore(), roleGVGBattle.getKillNpcScore(), roleGVGBattle.getGatherScore(),
							roleGVGBattle.getBattlePoint() - roleGVGBattle.getKillEnemyScore() - roleGVGBattle.getKillNpcScore() - roleGVGBattle.getGatherScore());
					
					roleGVGBattle.setResult(result);

					int oldHideScore = roleGVGBattle.getTvtHideScore();

					// 1.计算隐藏积分
					calcTVTHideScore(roleGVGBattle.getRoleId(), roleGVGBattle.getAllianceId(), result);

					int changeHideScore = roleGVGBattle.getTvtHideScore() - oldHideScore;

					// 2.计算实力积分
					calcTVTPowerScore(roleGVGBattle.getRoleId(), roleGVGBattle.getAllianceId(), result,changeHideScore);

					if(roleGVGBattle.getAllianceId().longValue() == victoryAllianceId.longValue()){
						victoryRoleBattles.add(roleGVGBattle);
					}else {
						failRoleBattles.add(roleGVGBattle);
					}

				}
			}
		}

		// 3.计算MVP/SVP
		updateMvpSvp(victoryRoleBattles, failRoleBattles);

		//分数计算完之后
		if (JavaUtils.bool(roleGVGBattles)) {
			for (RoleGVGBattle roleGVGBattle : roleGVGBattles) {
				if (roleGVGBattle.getAllianceId() != null) {
					//宝箱相关的结算
					tvtGameService.onTvtBattleFinish(roleGVGBattle);
				}
			}
		}

		// 4.同步给客户端
		sendTVTSettlementInfo(victoryAllianceId, failAllianceId);

		// 5.战场结束，玩家信息打点
		try{
			victoryRoleBattles.stream().sorted(Comparator.comparing(RoleGVGBattle::getBattlePoint)).collect(Collectors.toList());
			failRoleBattles.stream().sorted(Comparator.comparing(RoleGVGBattle::getBattlePoint)).collect(Collectors.toList());

			int rank = 1;
			for(RoleGVGBattle roleGVGBattle : victoryRoleBattles) {
				srvDpd.getBiLogUtil().tvtBattleResult(roleGVGBattle.getRoleId(), Application.getServerId(), roleGVGBattle.getTeamId(), victoryAlliancePoint, rank,
						roleGVGBattle.getBattlePoint() - roleGVGBattle.getKillEnemyScore() - roleGVGBattle.getKillNpcScore() - roleGVGBattle.getGatherScore(),
						roleGVGBattle.getKillNpcScore(), roleGVGBattle.getKillEnemyScore(), roleGVGBattle.getGatherScore(),
						roleGVGBattle.getScore(), roleGVGBattle.getChangeScore());
				rank ++;
			}

			rank = 1;
			for(RoleGVGBattle roleGVGBattle : failRoleBattles) {
				srvDpd.getBiLogUtil().tvtBattleResult(roleGVGBattle.getRoleId(), Application.getServerId(), roleGVGBattle.getTeamId(), failAlliancePoint, rank,
						roleGVGBattle.getBattlePoint() - roleGVGBattle.getKillEnemyScore() - roleGVGBattle.getKillNpcScore() - roleGVGBattle.getGatherScore(),
						roleGVGBattle.getKillNpcScore(), roleGVGBattle.getKillEnemyScore(), roleGVGBattle.getGatherScore(),
						roleGVGBattle.getScore(), roleGVGBattle.getChangeScore());
				rank ++;
			}
		} catch (ExpectedException ignored){

		} catch (Exception e){
			ErrorLogUtil.exceptionLog("TVT战场结束 打点玩家信息异常", e);
		}
	}

	public void updateMvpSvp(List<RoleGVGBattle> victoryRoleBattles, List<RoleGVGBattle> failRoleBattles){
		victoryRoleBattles.stream().sorted(Comparator.comparing(RoleGVGBattle::getKillEnemyScore)).collect(Collectors.toList());
		failRoleBattles.stream().sorted(Comparator.comparing(RoleGVGBattle::getKillEnemyScore)).collect(Collectors.toList());

		if(JavaUtils.bool(victoryRoleBattles)){
			RoleGVGBattle mvpRoleBattle = victoryRoleBattles.get(0);
			if(mvpRoleBattle != null){
				mvpRoleBattle.setMvp(1);
				roleGVGBattleDao.save(mvpRoleBattle);
			}
		}

		if(JavaUtils.bool(failRoleBattles)){
			RoleGVGBattle svpRoleBattle = failRoleBattles.get(0);
			if(svpRoleBattle != null){
				svpRoleBattle.setMvp(2);
				roleGVGBattleDao.save(svpRoleBattle);
			}
		}
	}

	/**
	 * TODO 计算隐藏积分
	 */
	public int calcTVTHideScore(Long roleId, Long roleAllianceId, int result){
		//	隐藏分计算
		int aAlliancePoint = 0;
		int bAlliancePoint = 0;
		Collection<RoleGVGBattle> roleGVGBattles = roleGVGBattleDao.findAll();
		if (JavaUtils.bool(roleGVGBattles)) {
			for (RoleGVGBattle roleGVGBattle : roleGVGBattles) {
				if (roleGVGBattle.getAllianceId() != null) {
					if(roleGVGBattle.getAllianceId().longValue() == roleAllianceId.longValue()){
						aAlliancePoint += roleGVGBattle.getTvtHideScore();
					}else {
						bAlliancePoint += roleGVGBattle.getTvtHideScore();
					}
				}
			}
		}

		RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(roleId);
		int myTvtHideScore = roleGVGBattle.getTvtHideScore();

		float D = myTvtHideScore * -1;
		if (aAlliancePoint != 0){
			D = (bAlliancePoint * 1.0f / aAlliancePoint - 1) * myTvtHideScore;
		}
		double P = 1 * 1.0f / (1 + Math.pow(10,D / 400));

		float G = 0.5f;//获胜 1 失败 0.5
		if(result == 0)
			G = 1;

		//根据自己的隐藏分 取表
		float K = tvtGameService.getHidePointK(myTvtHideScore);

		double newHidePower = myTvtHideScore + K * (G - P);
		roleGVGBattle.setTvtHideScore((int)newHidePower);
		roleGVGBattleDao.save(roleGVGBattle);

		logger.info("TVT战斗，隐藏分计算，roleId:{},oldHidePower:{},newHidePower:{}",roleGVGBattle.getRoleId(),myTvtHideScore,newHidePower);

		return (int)newHidePower;
	}

	/**
	 * TODO 计算实力积分
	 *
	 * result 0:胜利  1:失败
	 *
	 */
	public int calcTVTPowerScore(Long roleId, Long roleAllianceId, int result,int changeHideScore){
		int powerScore = 0;

		RoleGVGBattle selfGVGBattle = roleGVGBattleDao.findById(roleId);
		int oldScore = selfGVGBattle.getScore();

		if(result == 0){
			int myAllianceKillScore = 0;
			List<RoleGVGBattle> myAllianceBattles = Lists.newArrayList();
			Collection<RoleGVGBattle> roleGVGBattles = roleGVGBattleDao.findAll();
			if (JavaUtils.bool(roleGVGBattles)) {
				for (RoleGVGBattle roleGVGBattle : roleGVGBattles) {
					if (roleGVGBattle.getAllianceId() != null) {
						if(roleGVGBattle.getAllianceId().longValue() == roleAllianceId.longValue()){
							myAllianceKillScore += roleGVGBattle.getKillEnemyScore();
							myAllianceBattles.add(roleGVGBattle);
						}
					}
				}
			}

			myAllianceBattles.stream().sorted(Comparator.comparing(RoleGVGBattle::getKillEnemyScore)).collect(Collectors.toList());


			int rank = myAllianceBattles.indexOf(selfGVGBattle) + 1;

			//根据排名读积分配置 取表
			int configScore = tvtGameService.getPowerRankScore(rank);
			float tempParam = 0;
			if (myAllianceKillScore != 0){
				tempParam = selfGVGBattle.getKillEnemyScore() * 1.0f / myAllianceKillScore;
			}
			powerScore = selfGVGBattle.getTvtHideScore() + (int)(changeHideScore * (tempParam + 1)) + configScore;

			selfGVGBattle.setScore(powerScore);
		}else {
			int myAllianceKillScore = 0;
			Collection<RoleGVGBattle> roleGVGBattles = roleGVGBattleDao.findAll();
			if (JavaUtils.bool(roleGVGBattles)) {
				for (RoleGVGBattle roleGVGBattle : roleGVGBattles) {
					if (roleGVGBattle.getAllianceId() != null) {
						if(roleGVGBattle.getAllianceId().longValue() == roleAllianceId.longValue()){
							myAllianceKillScore += roleGVGBattle.getKillEnemyScore();
						}
					}
				}
			}
			float tempParam = 0;
			if (myAllianceKillScore != 0){
				tempParam = selfGVGBattle.getKillEnemyScore() / myAllianceKillScore;
			}
			powerScore = selfGVGBattle.getTvtHideScore() + (int)(changeHideScore * (1 - tempParam));
			selfGVGBattle.setScore(powerScore);
		}

		//更新排行榜
		RoleServerInfo roleServerInfo = roleServerInfoDao.findById(selfGVGBattle.getRoleId());
		if (roleServerInfo != null){
			tvtGameService.updateRolePowerRank(selfGVGBattle.getRoleId(),selfGVGBattle.getScore(),roleServerInfo.getRoleOServerId(),false);
		}

		selfGVGBattle.setChangeScore(selfGVGBattle.getScore() - oldScore);
		roleGVGBattleDao.save(selfGVGBattle);

		logger.info("TVT战斗，实力分计算，roleId:{},oldScore:{},newPowerScore:{}",roleId, oldScore, powerScore);

		//////结算流程
		//1. 计算隐藏分和实力分
		//2. 更新排行榜，获得变化的名次
		//3. RoleGVGBattle赋值  隐藏分，实力分，旧排名，新排名，旧实力积分
		//4. 结算信息给客户端
		//5. RoleGVGBattle赋值 rpc回Game
		//6. Game做积分变化处理 领宝箱用

		return powerScore;
	}

	/**
	 * 战斗结束：给玩家发送tvt数据
	 * */
	public void sendTVTSettlementInfo(Long victoryAllianceId, Long failAllianceId){
		GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo = gvgBattleDataVoManager.findGvgBattleServerDispatchRecordVo();

		Collection<Role> all = roleDao.findAll();
		for (Role role : all){
			if (role.isOnline()){
				GcTVTSettlementInfo msg = new GcTVTSettlementInfo();
				PsTVTRoleSettlementData roleSettlementData = new PsTVTRoleSettlementData();

				// 队伍个人数据
				Collection<RoleGVGBattle> roleGVGBattles = roleGVGBattleDao.findAll();
				if (JavaUtils.bool(roleGVGBattles)) {
					for (RoleGVGBattle roleGVGBattle : roleGVGBattles) {
						PsTVTRoleScoreData roleScoreData = new PsTVTRoleScoreData();
						roleScoreData.setRoleId(roleGVGBattle.getRoleId());
						roleScoreData.setName(roleGVGBattle.getName());
						roleScoreData.setHead(roleGVGBattle.getHead());
						roleScoreData.setScore(roleGVGBattle.getBattlePoint());
						roleScoreData.setSex(roleGVGBattle.getSex());

						if(roleGVGBattle.getAllianceId().equals(gvgBattleServerDispatchRecordVo.getAllianceId1())){
							roleSettlementData.addToTeam1RoleScoreData(roleScoreData);
						}else if(roleGVGBattle.getAllianceId().equals(gvgBattleServerDispatchRecordVo.getAllianceId2())){
							roleSettlementData.addToTeam2RoleScoreData(roleScoreData);
						}
					}
				}

				// 队伍数据
				AllianceBattlePoint allianceBattlePoint1;
				AllianceBattlePoint allianceBattlePoint2;
				// 1联盟成功与否
				boolean result = false;
				if(victoryAllianceId.equals(gvgBattleServerDispatchRecordVo.getAllianceId1())){
					result = true;
					allianceBattlePoint1 = allianceBattlePointDao.findById(victoryAllianceId);
					allianceBattlePoint2 = allianceBattlePointDao.findById(failAllianceId);
				}else{
					allianceBattlePoint1 = allianceBattlePointDao.findById(failAllianceId);
					allianceBattlePoint2 = allianceBattlePointDao.findById(victoryAllianceId);
				}

				roleSettlementData.setTeam1Data(toTeamStatisticalData(allianceBattlePoint1,1, result ? 0 : 1));
				roleSettlementData.setTeam2Data(toTeamStatisticalData(allianceBattlePoint2, 2, result ? 1 : 0));

				// 个人数据
				toRoleTVTData(role, roleSettlementData);

				// 发送数据
				msg.setSettlementeData(roleSettlementData);
				role.send(msg);
			}
		}
	}

	// 玩家本场战斗个人数据
	private void toRoleTVTData(Role role, PsTVTRoleSettlementData roleSettlementData){
		RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(role.getPersistKey());
		if(roleGVGBattle != null){
			// 设置个人统计数据
			PsTVTRoleStatisticalData roleStatisticalData = new PsTVTRoleStatisticalData();
			roleStatisticalData.setTeamId(roleGVGBattle.getTeamId());
			roleStatisticalData.setServerId(role.getoServerId());
			roleStatisticalData.setKillEnemyScore(roleGVGBattle.getKillEnemyScore());
			roleStatisticalData.setKillNpcScore(roleGVGBattle.getKillNpcScore());
			roleStatisticalData.setGatherScore(roleGVGBattle.getGatherScore());
			roleStatisticalData.setBuildScore(roleGVGBattle.getBattlePoint() - roleGVGBattle.getKillEnemyScore() - roleGVGBattle.getKillNpcScore() - roleGVGBattle.getGatherScore());
			roleSettlementData.setStatisticalData(roleStatisticalData);

			// 设置个人战场积分/排名
			roleSettlementData.setResult(roleGVGBattle.getResult());
			roleSettlementData.setScore(roleGVGBattle.getScore());
			roleSettlementData.setChangeScore(roleGVGBattle.getChangeScore());
			roleSettlementData.setRank(roleGVGBattle.getRank());
			roleSettlementData.setChangeRank(roleGVGBattle.getChangeRank());

			// 设置mvp或者svp数据
			if(roleGVGBattle.getMvp() == 1){
				// MVP
				PsTVTRoleMVPData mvpData = new PsTVTRoleMVPData();
				mvpData.setMvp(1);
				mvpData.setScore(roleGVGBattle.getBattlePoint());
				roleSettlementData.setMvpData(mvpData);
			}else if(roleGVGBattle.getMvp() == 2){
				// SVP
				PsTVTRoleMVPData svpData = new PsTVTRoleMVPData();
				svpData.setMvp(2);
				svpData.setScore(roleGVGBattle.getBattlePoint());
				roleSettlementData.setMvpData(svpData);
			}
		}
	}

	// 队伍数据
	private PsTVTTeamStatisticalData toTeamStatisticalData(AllianceBattlePoint allianceBattlePoint, int teamId, int result){
		PsTVTTeamStatisticalData teamStatisticalData = new PsTVTTeamStatisticalData();
		teamStatisticalData.setTeamId(teamId);
		teamStatisticalData.setResult(result);
		teamStatisticalData.setOccupyBuildCount(GVGStrongHoldService.getOccupyBuildCount(allianceBattlePoint.getPersistKey()));
		teamStatisticalData.setArmyFactoryScore(allianceBattlePoint.getArmyFactoryScore());
		teamStatisticalData.setTotalBuildScore(GVGStrongHoldService.getBuildScore(allianceBattlePoint));
		teamStatisticalData.setKillNpcScore(allianceBattlePoint.getKillNpcScore());
		teamStatisticalData.setKillEnemyScore(allianceBattlePoint.getKillEnemyScore());
		teamStatisticalData.setTotalCureScore(allianceBattlePoint.getTotalCureScore());
		teamStatisticalData.setGatherScore(allianceBattlePoint.getGatherScore());
		teamStatisticalData.setKillEnemyCount(allianceBattlePoint.getKillEnemyCount());
		teamStatisticalData.setTotalRallyCount(allianceBattlePoint.getTotalRallyCount());
		return teamStatisticalData;
	}

	public void debugSettlement(){
		Collection<AllianceBattlePoint> allianceBattlePoints = allianceBattlePointDao.findAll();
		if (!JavaUtils.bool(allianceBattlePoints)) {
			logger.info("debugSettlement  allianceBattlePoints null");
			return;
		}

		List<AllianceBattlePoint> list = allianceBattlePoints.stream().sorted((a1, a2) -> { return a2.getValue() - a1.getValue(); }).collect(Collectors.toList());
		AllianceBattlePoint failAllianceBattlePoint = list.get(1);
		AllianceBattlePoint victoryAllianceBattlePoint = list.get(0);
		Long failAllianceId = failAllianceBattlePoint.getPersistKey();
		Long victoryAllianceId = victoryAllianceBattlePoint.getPersistKey();

		logger.info("TVT 结算");
		tvtSettlement(victoryAllianceId, failAllianceId, victoryAllianceBattlePoint.getValue(), failAllianceBattlePoint.getValue());
	}

}
