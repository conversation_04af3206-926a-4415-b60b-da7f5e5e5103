package com.lc.billion.icefire.tvtcontrol.biz.service.impl.activity.handler;

import com.lc.billion.icefire.core.common.RandomUtils;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.biz.config.ActivityListConfig;
import com.lc.billion.icefire.game.biz.manager.tvt.TvtGameDataManager;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.activity.AbstractActivityHandler;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.tvt.TVTGameService;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.TvtSettingConfig;
import com.lc.billion.icefire.protocol.GcTvtStageInfo;
import com.lc.billion.icefire.protocol.constant.PsTvtActivityStatus;
import com.lc.billion.icefire.protocol.structure.PsActivityInfo;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtBattleServerDispatchRecordDao;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtPlayerSignupInfoDao;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtPlayerSimpleInfoDao;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtQualifiedServerDao;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtBattleServerDispatchRecord;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtPlayerSignupInfo;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtPlayerSimpleInfo;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtQualifiedServer;
import com.lc.billion.icefire.tvtcontrol.biz.model.activity.TvtActivityStatus;
import com.lc.billion.icefire.tvtcontrol.biz.model.activity.context.TvtActivityContext;
import com.lc.billion.icefire.tvtcontrol.biz.service.TvtControlBroadcastToBattleService;
import com.lc.billion.icefire.tvtcontrol.biz.service.TvtControlBroadcastToGameService;
import com.lc.billion.icefire.tvtcontrol.biz.service.TvtControlService;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.GameServerConfig;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import com.longtech.ls.zookeeper.KvkSeasonsConfig;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.thrift.TBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.*;


/**
 * <AUTHOR>
 * @date 2022/04/06
 * TvT 活动处理器
 */
@Service
public class TvtActivityHandler extends AbstractActivityHandler<Activity> {
    @Autowired
    private TvtControlBroadcastToGameService tvtControlBroadcastToGameService;
    @Autowired
    private TvtControlBroadcastToBattleService tvtControlBroadcastToBattleService;
    @Autowired
    private TvtBattleServerDispatchRecordDao tvtBattleServerDispatchRecordDao;
    @Autowired
    private TvtControlService tvtControlService;
    @Autowired
    private TvtPlayerSignupInfoDao tvtPlayerSignupInfoDao;
    @Autowired
    private TvtGameDataManager tvtGameDataManager;
    @Autowired
    private TVTGameService tvtGameService;
    @Autowired
    private TvtQualifiedServerDao tvtQualifiedServerDao;
    @Autowired
    private TvtPlayerSimpleInfoDao tvtPlayerSimpleInfoDao;

    @Override
    protected void onStartImpl(Activity activity) {
        asyncOperationService.execute(() -> {
            AsyncOperation.logger.info("tvt 活动 onStartImpl， 选择有资格服务器");
            try {
                onDailyReset(activity);
            } catch (ExpectedException ignored){

            } catch (Exception ex) {
                ErrorLogUtil.exceptionLog("tvt on DailyReset", ex);
            }
            AsyncOperation.logger.info("tvt 活动 onStartImpl， 计算当前阶段");
            tvtActivityStatusContinue(activity);
            return false;
        });
    }

    @Override
    protected void onStopImpl(Activity activity) {
        asyncOperationService.execute(() -> {
            AsyncOperation.logger.info("tvt 活动 onStopImpl， 广播活动信息给game和战斗服");
            broadcastTvtActivity(activity);
            return false;
        });

    }

    @Override
    public void onAdd(Activity activity) {
        asyncOperationService.execute(() -> {
            AsyncOperation.logger.info("tvt 活动 onAdd， 广播活动信息给game和战斗服");
            broadcastTvtActivity(activity);
            return false;
        });
    }

    @Override
    public ActivityType getType() {
        return ActivityType.TVT;
    }

    @Override
    public void tick(Activity activity, long now) {
        // TVT阶段处理
        TvtActivityContext tvtActivityContext = activity.getActivityContext();
        if (tvtActivityContext == null) {
            return;
        }
        TvtActivityStatus tvtActivityStatus = tvtActivityContext.getTvtActivityStatus();
        if (tvtActivityStatus == null) {
            return;
        }

        long nextTvtActivityStatusTime = tvtActivityContext.getNextTvtActivityStatusTime();
        if (nextTvtActivityStatusTime <= now) {
            tvtActivityStatusContinue(activity);
        }
    }

    @Override
    public TBase<?, ?> getActivityInfo(Role role, String activityTypeMetaId) {
        ActivityVo tvtActivityVo = tvtGameDataManager.findTvtActivityVo();
        if (tvtActivityVo != null) {
            TvtActivityContext activityContext = tvtActivityVo.getActivityContext();
            if (activityContext == null || activityContext.getTvtActivityStatus() == null) {
                ErrorLogUtil.errorLog("tvt 活动数据获取失败");
                return null;
            }
            GcTvtStageInfo gcMsg = new GcTvtStageInfo();
            gcMsg.setStatus(activityContext.getTvtActivityStatus().getTvtActivityStatus());
            gcMsg.setNextStatusTime(activityContext.getNextTvtActivityStatusTime());

//            tvtGameService.addTVTActivityExtraInfo(role,gcMsg);

            return gcMsg;
        }
        return null;
    }

    @Override
    public void receiveReward(Role role, String activityTypeMetaId, String goalMetaId) {
        tvtGameService.receiveReward(role, activityTypeMetaId, goalMetaId);
    }

    /**
     * 广播tvt活动
     *
     * @param activity
     */
    public void broadcastTvtActivity(Activity activity) {
        logger.info("tvt broadcastTvtActivity to game server");
        tvtControlBroadcastToGameService.broadcastTvtActivity(activity);
        logger.info("tvt broadcastTvtActivity to battle server");
        tvtControlBroadcastToBattleService.broadcastTVTActivity(activity);
    }

    /**
     * tvt活动阶段流转
     *
     * @param activity
     */
    public void tvtActivityStatusContinue(Activity activity) {
        TvtActivityContext activityContext = activity.getActivityContext();
        TvtActivityStatus currentTvtActivityStatus = activityContext.getTvtActivityStatus();
        TvtActivityStatus tvtActivityStatus = null;
        LocalTime tvtActivityTime = null;

        boolean isNew = false;
        if (currentTvtActivityStatus == null) {
            isNew = true;
            //不满足开赛条件， 就不开了。
            if (!onTvtRoundStart(activity)) {
                logger.info("tvt 不满足开赛条件， 就不开了 删除活动");
                activityDao.delete(activity);
                return;
            }
            tvtActivityTime = activityContext.getTvtActivityTimes().get(0);
            tvtActivityStatus = TvtActivityStatus.READY;
        } else {
            PsTvtActivityStatus nextTvtActivityStatus = currentTvtActivityStatus.getNextTvtActivityStatus();
            switch (nextTvtActivityStatus) {
                case READY:
                    tvtActivityTime = activityContext.getTvtActivityTimes().get(0);
                    break;
                case SIGNUP:
                    tvtActivityTime = activityContext.getTvtActivityTimes().get(1);
                    break;
                case ADMITTANCE:
                    tvtActivityTime = activityContext.getTvtActivityTimes().get(3);
                    break;
            }
            tvtActivityStatus = TvtActivityStatus.findById(nextTvtActivityStatus.getValue());
        }

        activityContext.setNextTvtActivityStatusTime(getCalendarFromTodayLocalTime(tvtActivityTime).getTimeInMillis());
        activityContext.setTvtActivityStatus(tvtActivityStatus);

        logger.info("TVT活动流程：status={}，time={}", activityContext.getTvtActivityStatus(), new Date(activityContext.getNextTvtActivityStatusTime()));
        switch (activityContext.getTvtActivityStatus()) {
            case READY:
                boolean finalIsNew = isNew;
                asyncOperationService.execute(new AsyncOperation() {
                    @Override
                    public boolean run() {
                        return onActivityStatusReady(activity, finalIsNew);
                    }

                    @Override
                    public void finish() {
                        afterGvgActivityStatusContinue(activity);
                    }
                });
                break;
            case SIGNUP:
                asyncOperationService.execute(new AsyncOperation() {
                    @Override
                    public boolean run() {
                        onActivityStatusSignup(activity);
                        return true;
                    }

                    @Override
                    public void finish() {
                        afterGvgActivityStatusContinue(activity);
                    }
                });
                break;
            case ADMITTANCE:
                asyncOperationService.execute(new AsyncOperation() {
                    @Override
                    public boolean run() {
                        onActivityStatusAdmittance(activity);
                        return true;
                    }

                    @Override
                    public void finish() {
                        afterGvgActivityStatusContinue(activity);
                    }
                });
                break;
            default:
                break;
        }
    }

    private Calendar getCalendarFromTodayLocalTime(LocalTime tvtActivityTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, tvtActivityTime.getHour());
        calendar.set(Calendar.MINUTE, tvtActivityTime.getMinute());
        calendar.set(Calendar.SECOND, tvtActivityTime.getSecond());
        return calendar;
    }

    private void afterGvgActivityStatusContinue(Activity activity) {
        activityDao.save(activity);
        broadcastTvtActivity(activity);
    }

    /**
     * tvt活动每次进入ready都需要在这里进行活动时间点获取以及context数据重置。
     *
     * @param activity
     * @return
     */
    private boolean onTvtRoundStart(Activity activity) {
        logger.info("tvt 活动初始化ActivityContext");
        TvtSettingConfig tvtSettingConfig = configService.getConfig(TvtSettingConfig.class);
        int dayOfWeek = Calendar.getInstance().get(Calendar.DAY_OF_WEEK);
        dayOfWeek -= 1;
        if (dayOfWeek == 0) {
            dayOfWeek = 7;
        }
        List<LocalTime> allTimes = tvtSettingConfig.getTvtActivityTimeByWeek(dayOfWeek);
        if (allTimes == null) {
            ErrorLogUtil.errorLog("tvtActivityStatusContinue 获取不到当天的时间配置", "dayOfWeek",dayOfWeek);
            return false;
        }

        TvtActivityContext activityContext = activity.getActivityContext();
        //单日活动 跨轮次
        activityContext.addRoundOfDay();

        int roundOfDay = activityContext.getRoundOfDay();
        int maxRoundOfDay = allTimes.size() / 4;
        //超过最大轮次 不处理
        if (roundOfDay > maxRoundOfDay) {
            activityDao.delete(activity);
            logger.info("tvt 超过当日最大轮次{}/{}， 活动停止", roundOfDay, maxRoundOfDay);
            return false;
        }

        List<LocalTime> tvtActivityTimes = getActivityTimesByRound(roundOfDay, allTimes);
        LocalTime tvtActivityTime = tvtActivityTimes.get(0);
        long targetTime = getCalendarFromTodayLocalTime(tvtActivityTime).getTimeInMillis();
        if (System.currentTimeMillis() > targetTime - 4 * 60 * 1000L) {
            logger.info("tvt 活动，距离报名不足4分钟， 不开活动了");
            return false;
        }

        activityContext.setTvtActivityTimes(tvtActivityTimes);
        activityContext.setSignupFull(false);
        long battleStartTime = getCalendarFromTodayLocalTime(tvtActivityTimes.get(2)).getTimeInMillis();
        activityContext.setBattleStartTime(battleStartTime);
        activityContext.setCurrentSignupCount(0);
        activityContext.setSignupCapacity(tvtSettingConfig.getTvtGameRegistrationLimit());

        if (activityContext.getRoundOfDay() == 1) {
            logger.info("tvt 当日第一轮 ready状态， 选择有资格参赛的服务器");
            // 选择有资格参赛的服务器
            selectTvtQualifiedServer(activity);
        }
        return true;
    }

    /**
     * tvt进入READY的处理
     *
     * @param activity
     */
    private boolean onActivityStatusReady(Activity activity, boolean isNew) {
        logger.info("tvt 活动进入准备阶段");
        //如果是新活动， 不需要在这走一边start逻辑了， 在activityContinue中已经走过一遍了。
        if (!isNew) {
            boolean res = onTvtRoundStart(activity);
            //活动停止
            if (!res) {
                logger.info("tvt onActivityStatusReady#onTvtRoundStart return false  activity stop.");
                return true;
            }
        }

        //销毁所有战斗服
        Collection<TvtBattleServerDispatchRecord> records = tvtBattleServerDispatchRecordDao.findAll();
        if (JavaUtils.bool(records)) {
            List<Integer> battleServerIds = new ArrayList<>();
            records.forEach(r -> {
                battleServerIds.add(r.getBattleServerId().intValue());
            });
            tvtControlService.noticeDestroy(battleServerIds, 0);
            logger.info("tvt 销毁所有战斗服{}", battleServerIds);
        }
        logger.info("tvt 清理报名信息");
        tvtPlayerSignupInfoDao.deleteAll();
        tvtControlBroadcastToGameService.broadcastTvtPlayerSignupInfoClear();
        logger.info("tvt 清理玩家简略信息");
        tvtPlayerSimpleInfoDao.deleteAll();

        logger.info("tvt 清理战斗服分配信息");
        tvtControlBroadcastToGameService.broadcastTvtBattleServerDispatchRecordClear();

        this.canUseBattleServerIds = null;
        this.battleStartTime = null;
        this.battleServerDestroyTime = null;

        return true;
    }

    /**
     * tvt活动进入报名阶段处理
     *
     * @param activity
     */
    private void onActivityStatusSignup(Activity activity) {
        logger.info("tvt 活动进入报名阶段");
        //TODO 目前暂无需要特殊处理的
    }

    // 可使用战斗服id
    private List<Integer> canUseBattleServerIds = null;

    private Long battleStartTime = null;

    private Long battleServerDestroyTime = null;


    /**
     * tvt活动进入入场阶段处理
     *
     * @param activity
     */
    private void onActivityStatusAdmittance(Activity activity) {
        logger.info("tvt 活动进入入场阶段");
        logger.info("tvt activity matching start");
        //匹配数据准备
        Map<Integer, List<TvtPlayerSignupInfo>> signupInfosByServerId = tvtPlayerSignupInfoDao.getDataByServerId();
        if (signupInfosByServerId == null || signupInfosByServerId.isEmpty()) {
            logger.info("tvt can't find signup info,  matching stop");
            return;
        }
        //最大赛季号
        int maxSeason = 1;
        //报名玩家总数
        int totalSignupCount = 0;
        //按照赛季分组的报名数据
        Map<Integer, Collection<TvtPlayerSignupInfo>> signupInfosBySeason = new HashMap<>();
        for (Map.Entry<Integer, List<TvtPlayerSignupInfo>> entry : signupInfosByServerId.entrySet()) {
            int serverId = entry.getKey();
            int season = this.getSeasonByServerId(serverId);
            List<TvtPlayerSignupInfo> list = entry.getValue();
            signupInfosBySeason.compute(season, (k, v) -> v == null ? new ArrayList<>() : v).addAll(list);
            maxSeason = Math.max(maxSeason, season);
            logger.info("tvt matching data : serverId {} , player:{}", serverId, list.size());
            totalSignupCount += list.size();
        }
        logger.info("tvt matching : max season is : {}", maxSeason);
        TvtSettingConfig tvtSettingConfig = configService.getConfig(TvtSettingConfig.class);
        // 共有多少个分段
        int levels = tvtSettingConfig.getTvtGameMatchPointStandard().length;
        // 计算需要战斗服场次： 报名人数/tvtSettingConfig.getTvtGameOneMatchNumLimitMax()  + 赛季数*分段数
        int needBattleServerCount = (int) Math.floor(totalSignupCount / tvtSettingConfig.getTvtGameOneMatchNumLimitMax()) + maxSeason * levels;
        logger.info("tvt matching 共需要 : {}/{} + {}*{} = {} 台战斗服", totalSignupCount, tvtSettingConfig.getTvtGameOneMatchNumLimitMax(), maxSeason, levels, needBattleServerCount);
        List<Integer> needBattleServerIds = tvtControlService.getCanUseBattleServerIds(needBattleServerCount);
        logger.info("tvt 预分配战斗服id{}", needBattleServerIds);
        this.canUseBattleServerIds = new ArrayList<>(needBattleServerIds);
        TvtActivityContext activityContext = activity.getActivityContext();
        this.battleStartTime = activityContext.getBattleStartTime();
        logger.info("tvt 本轮战斗开始时间 {}", battleStartTime);

        for (int s = 1; s <= maxSeason; ++s) {
            logger.info("tvt matching executing start : season {}", s);
            match(signupInfosBySeason.get(s));
            logger.info("tvt matching executing finish : season {}", s);
        }
        //3.RPC广播给各game
        //广播战斗分配记录
        logger.info("tvt matching broadcastTvtBattleServerDispatchRecords");
        tvtControlBroadcastToGameService.broadcastTvtBattleServerDispatchRecords();
        //广播报名信息
        logger.info("tvt matching broadcastTvtPlayerSignupInfos");
        tvtControlBroadcastToGameService.broadcastTvtPlayerSignupInfos();

    }

    /**
     * tvt匹配用 bean
     */
    private class TvtMatchBean {
        //所属分段
        private int matchLevel;
        //报名信息
        private TvtPlayerSignupInfo info;

        public TvtMatchBean(int matchLevel, TvtPlayerSignupInfo info) {
            this.matchLevel = matchLevel;
            this.info = info;
        }

        public int getMatchLevel() {
            return matchLevel;
        }

        public TvtPlayerSignupInfo getInfo() {
            return info;
        }
    }

    /**
     * 赛季内匹配
     *
     */
    private void match(Collection<TvtPlayerSignupInfo> list) {
        if (list == null || list.isEmpty()) {
            logger.info("tvt match : Collection<TvtPlayerSignupInfo> is null or is empty 跳过这个赛季");
            return;
        }

        TvtSettingConfig tvtSettingConfig = configService.getConfig(TvtSettingConfig.class);
        //按隐藏积分分段,  降序排列
        Map<Integer, PriorityQueue<TvtMatchBean>> matchQueueMapper = new HashMap<>();

        // 所有分段
        Pair<Integer, Integer>[] matchLevels = tvtSettingConfig.getTvtGameMatchPointStandard();
        logger.info("tvt match: 共有 {} 个 匹配分数段", matchLevels.length);
        for (TvtPlayerSignupInfo tvtPlayerSignupInfo : list) {
            int matchLevel = tvtSettingConfig.getMatchPointLevelByHiddenScore(tvtPlayerSignupInfo.getHideScore());
            logger.info("tvt match: tvtPlayerSignupInfo {} , 所属匹配分数段 {}", tvtPlayerSignupInfo, matchLevel);
            matchQueueMapper.compute(matchLevel, (k, v) -> v == null ? new PriorityQueue<>((o1, o2) -> Integer.compareUnsigned(o2.getInfo().getHideScore(), o1.getInfo().getHideScore())) : v).offer(new TvtMatchBean(matchLevel, tvtPlayerSignupInfo));
        }

        int bucketCapacity = tvtSettingConfig.getTvtGameMatchOneGroupNum();
        logger.info("tvt match: bucketCapacity {}", bucketCapacity);
        List<TvtMatchBean> matchBucket = new ArrayList<>();
        // 按分段从分高的开始匹配
        for (int currentMatchLevel = matchLevels.length - 1; currentMatchLevel >= 0; --currentMatchLevel) {
            PriorityQueue<TvtMatchBean> queue = matchQueueMapper.get(currentMatchLevel);
            int queueSize = queue == null ? 0 : queue.size();
            int matchBucketSize = matchBucket == null ? 0 : matchBucket.size();
            logger.info("tvt matching : 第 {} 匹配段，报名人数 {} ,从上个分段下沉下来的玩家 {}，共 {} ", currentMatchLevel, queueSize, matchBucketSize, queueSize + matchBucketSize);
            // 本分数段没人报名， 且没有从上个分数段下来的玩家， 直接跳出
            if (queueSize == 0 && matchBucketSize == 0) {
                continue;
            }
            if (queueSize > 0) {
                while (!queue.isEmpty()) {
                    matchBucket.add(queue.poll());
                    if (matchBucket.size() == bucketCapacity) {
                        //此处不可能返回未匹配成功的玩家， 因为匹配桶是满的。
                        matchInBucket(matchBucket);
                    }
                }
            }

            if (!matchBucket.isEmpty()) {
                //匹配桶不是满的
                //执行匹配， 返回未匹配成功的玩家
                Collection<TvtMatchBean> res = matchInBucket(matchBucket);
                if (res == null) {
                    logger.info("tvt matching matchInBucket 后 没有剩余玩家， 不需要后续处理");
                    continue;
                }
                logger.info("tvt matching {} 人未匹配成功", res.size());

                for (TvtMatchBean re : res) {
                    //不是最后一个分段，且未匹配成功玩家本就属于这个匹配分段，而不是从上面下沉下来的， 则把这些玩家重新放到matchBucket中。 交给下个匹配分数段处理。
                    if (currentMatchLevel > 0 && re.getMatchLevel() == currentMatchLevel) {
                        matchBucket.add(re);
                    } else {
                        logger.info("tvt matching role: {} match fail ", re.getInfo().getRoleId());
                        // 最后一个匹配分数段，或者玩家不属于当前匹配分数段 判定为匹配失败
                        handlePlayerMatchFail(re.getInfo().getRoleId());
                    }
                }
            }
            logger.info("tvt matching : 第 {} 匹配分数段 处理完毕", currentMatchLevel);
        }
    }

    private void handlePlayerMatchFail(Long roleId) {
        TvtPlayerSignupInfo signupInfo = tvtPlayerSignupInfoDao.findByRoleId(roleId);
        if (signupInfo != null) {
            signupInfo.setSignupStatus(3);
            tvtPlayerSignupInfoDao.save(signupInfo);
        }
    }

    /**
     * 桶内匹配 90人一桶
     * 每30人为1组
     *
     * @param matchBucket
     */
    private PriorityQueue<TvtMatchBean> matchInBucket(List<TvtMatchBean> matchBucket) {
        logger.info("tvt matching : 开始分配比赛以及对手");
        PriorityQueue<TvtMatchBean> matchGroupQueue = new PriorityQueue<>((o1, o2) -> Integer.compareUnsigned(o2.getInfo().getHideScore(), o1.getInfo().getHideScore()));
        Collections.shuffle(matchBucket);
        TvtSettingConfig tvtSettingConfig = configService.getConfig(TvtSettingConfig.class);
        int battleFieldPlayerCapacity = tvtSettingConfig.getTvtGameOneMatchNumLimitMax();

        for (TvtMatchBean bean : matchBucket) {
            matchGroupQueue.offer(bean);
            //向queue中添加30名玩家
            if (matchGroupQueue.size() == battleFieldPlayerCapacity) {
                logger.info("tvt match matchGroupQueue.size == battleFieldPlayerCapacity,   execute matchInGroup");
                // 执行玩家分配
                matchInGroup(matchGroupQueue);
            }
        }

        matchBucket.clear();

        int matchMinPlayers = configService.getConfig(TvtSettingConfig.class).getTvtGameOneMatchNumLimit();
        //匹配组不满时，如果小于最小开战人数， 则下沉这些玩家， 否则继续分配
        if (!matchGroupQueue.isEmpty()) {
            if (matchGroupQueue.size() < matchMinPlayers) {
                logger.info("tvt 匹配 匹配组 剩余人数 {}  < 开战最小人数 {}. 不满足开战条件", matchGroupQueue.size(), matchMinPlayers);
                return matchGroupQueue;
            } else {
                // 剩余的人全部能分配出去。
                matchInGroup(matchGroupQueue);
                return null;
            }
        }
        return null;
    }

    /**
     * 组内匹配，
     * 随机红蓝方，
     * 按照隐藏分红蓝蓝红 分配选手
     * 生成battleServerDispatchRecord
     *
     * @param queue
     * @return
     */
    private void matchInGroup(PriorityQueue<TvtMatchBean> queue) {
        logger.info("tvt matching matchInGroup");
        List<TvtPlayerSignupInfo> team1 = new ArrayList<>();
        List<TvtPlayerSignupInfo> team2 = new ArrayList<>();
        // 分发顺序 ABBA
        int[] index = new int[]{0, 1, 1, 0};
        int counter = 0;
        while (!queue.isEmpty()) {
            TvtMatchBean bean = queue.poll();
            // 根据分发顺序， 决定往那个队伍里头放人
            List<TvtPlayerSignupInfo> team = index[counter++ % 4] == 0 ? team1 : team2;
            team.add(bean.getInfo());
        }
        logger.info("tvt match team1 size:{}, team2 size:{}", team1.size(), team2.size());

        //随机红蓝方
        boolean isRedFirst = RandomUtils.nextBoolean();
        //生成battleServerDispatchRecord;
        Long battleServerId = Long.valueOf(this.canUseBattleServerIds.remove(0));
        long battleStartTime = this.battleStartTime;
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        if (this.battleServerDestroyTime == null) {
            //计算战斗销毁时间 开始时间+准备时间+战场持续时间+10分钟延迟
            long battleTimeInMillis = gvgSettingConfig.getGvgActivityBattleTime() * 1_000L;
            long readyTimeInMillis = gvgSettingConfig.getGvgActivityBattleReadyTime() * 1_000L;
            // 销毁时间 = 开始时间 + 战场准备时间 + 战斗持续时间 + 10分钟；
            this.battleServerDestroyTime = battleStartTime + readyTimeInMillis + battleTimeInMillis + 10 * 60 * 1_000L;
        }

        List<TvtPlayerSimpleInfo> redLineup = new ArrayList<>();
        List<TvtPlayerSimpleInfo> blueLineup = new ArrayList<>();
        List<Long> redRoleIds = new ArrayList<>();
        List<Long> blueRoleIds = new ArrayList<>();
        for (TvtPlayerSignupInfo tpsi : isRedFirst ? team1 : team2) {
            TvtPlayerSignupInfo signupInfo = tvtPlayerSignupInfoDao.findByRoleId(tpsi.getRoleId());
            if (signupInfo == null) {
                ErrorLogUtil.errorLog("tvt match,can't find playerSignUpInfo", "roleId",tpsi.getRoleId());
                continue;
            }
            signupInfo.setSignupStatus(2);
            signupInfo.setTvtSide(1);
            signupInfo.setBattleServerId(battleServerId);
            tvtPlayerSignupInfoDao.save(signupInfo);
            TvtPlayerSimpleInfo simpleInfo = tvtPlayerSimpleInfoDao.findById(tpsi.getRoleId());
            if (simpleInfo == null) {
                simpleInfo = new TvtPlayerSimpleInfo();
                simpleInfo.setServerId(tpsi.getServerId());
                simpleInfo.setRoleId(tpsi.getRoleId());
            }
            redLineup.add(simpleInfo);
            redRoleIds.add(tpsi.getRoleId());
        }

        for (TvtPlayerSignupInfo tpsi : isRedFirst ? team2 : team1) {
            TvtPlayerSignupInfo signupInfo = tvtPlayerSignupInfoDao.findByRoleId(tpsi.getRoleId());
            if (signupInfo == null) {
                ErrorLogUtil.errorLog("tvt match,can't find playerSignUpInfo", "roleId",tpsi.getRoleId());
                continue;
            }
            signupInfo.setSignupStatus(2);
            signupInfo.setTvtSide(2);
            signupInfo.setBattleServerId(battleServerId);
            tvtPlayerSignupInfoDao.save(signupInfo);
            TvtPlayerSimpleInfo simpleInfo = tvtPlayerSimpleInfoDao.findById(tpsi.getRoleId());
            if (simpleInfo == null) {
                simpleInfo = new TvtPlayerSimpleInfo();
                simpleInfo.setServerId(tpsi.getServerId());
                simpleInfo.setRoleId(tpsi.getRoleId());
            }
            blueLineup.add(simpleInfo);
            blueRoleIds.add(tpsi.getRoleId());
        }
        TvtBattleServerDispatchRecord entity = tvtBattleServerDispatchRecordDao.findByBattleServerId(String.valueOf(battleServerId));
        if (entity != null) {
            logger.info("tvt matching result battleServerDispatchRecord battleServerId {} 已存在（应该是内网常驻战斗服的情况）， 删除，再重建。");
            tvtBattleServerDispatchRecordDao.delete(entity);
        }
        tvtBattleServerDispatchRecordDao.create(battleServerId, battleStartTime, battleServerDestroyTime, 0, redLineup, blueLineup);
        logger.info("tvt matching: update lineup in TvtBattleServerDispatchRecord  battleServerId {} ,finish", battleServerId);

        biLogUtil.tvtMatchRecord(null, battleStartTime, battleServerId.intValue(), redRoleIds, blueRoleIds);
    }


    /**
     * 从当日所有时间点中，获取本轮的四个时间点
     * 1-> 0,1,2,3
     * 2-> 4,5,6,7
     * 3-> 8,9,10,11
     * 4-> 12,13,14,15
     * 5->
     * ...
     *
     * @param round
     * @param all
     * @return
     */
    private static List<LocalTime> getActivityTimesByRound(int round, List<LocalTime> all) {
        if (round < 1 || round > all.size() / 4) {
//            logger.error("tvt获取本轮四个时间点错误，给定轮次{} 超出了所有时间点*4", round);
            return null;
        }
        List<LocalTime> res = new ArrayList<>();
        int startIndex = 4 * (round - 1);
        for (int i = 0; i < 4; ++i) {
            res.add(all.get(startIndex + i));
        }
        return res;
    }

    /**
     * 选择tvt有资格参赛的服务器
     *
     */
    private void selectTvtQualifiedServer(Activity activity) {
        //每日重置时候选一次就行，不用每次READY选
        TvtSettingConfig tvtSettingConfig = configService.getConfig(TvtSettingConfig.class);
        ActivityListConfig activityListConfig = configService.getConfig(ActivityListConfig.class);
        ActivityListConfig.ActivityListMeta activityListMeta = activityListConfig.getMetaById(activity.getMetaId());
        // 哪些服务器可以参赛
        Map<Integer, GameServerConfig> gameServers = configCenter.getLsConfig().getGameServers();
        KvkSeasonsConfig kvkSeasons = configCenter.getLsConfig().getKvkSeasons();
        for (GameServerConfig gameServerConfig : gameServers.values()) {
            int gameServerId = gameServerConfig.getGameServerId();
            if (!gameServerConfig.isAlive()) {
                logger.info("服务器{}没启动或已并服", gameServerId);
                continue;
            }
            // TODO 是否对外
            ServerType serverType = ServerConfigManager.getInstance().getServerTypeConfig().getServerType(gameServerId);
            if (serverType != ServerType.GAME && serverType != ServerType.KVK_SEASON) {
                // 非game服跳过
                continue;
            }
            // 区分原始服和赛季服
            switch (serverType) {
                case GAME:
                    handleTvtQualifiedServer(gameServerConfig, tvtSettingConfig, activityListMeta, activity.getActivityContext());
                    break;
                case KVK_SEASON:
                    KvkSeasonServerGroupConfig serverGroupByKServerId = kvkSeasons.getServerGroupByKServerId(gameServerId);
                    if (serverGroupByKServerId != null) {
                        Set<Integer> oServerIds = serverGroupByKServerId.getOServerIds();
                        if (JavaUtils.bool(oServerIds)) {
                            oServerIds.forEach(sid -> {
                                GameServerConfig gameServerConfig1 = gameServers.get(sid);
                                handleTvtQualifiedServer(gameServerConfig1, tvtSettingConfig, activityListMeta, activity.getActivityContext());
                            });
                        }
                    } else {
                        ErrorLogUtil.errorLog("赛季服找不到对应的KvkSeasonServerGroupConfig", "gameServerId",gameServerId);
                    }
                    break;
                default:
                    ErrorLogUtil.errorLog("选择参赛服类型报错", new RuntimeException(),"gameServerId",gameServerId);
                    break;
            }
        }
    }

    /**
     * 选择有资格参加tvt的服务器
     *
     * @param gameServerConfig
     * @param tvtSettingConfig
     * @param activityListMeta
     * @param activityContext
     */
    private void handleTvtQualifiedServer(GameServerConfig gameServerConfig, TvtSettingConfig
            tvtSettingConfig, ActivityListConfig.ActivityListMeta activityListMeta, TvtActivityContext activityContext) {
        int gameServerId = gameServerConfig.getGameServerId();
        TvtQualifiedServer tvtQualifiedServer = tvtQualifiedServerDao.findById((long) gameServerId);
        boolean isBlockServer = activityListMeta.isBlockServer(gameServerId);
        boolean enabledServers = activityListMeta.isEnabledServers(gameServerId);
        if (tvtQualifiedServer == null) {
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            int tvtActivityStartDay = tvtSettingConfig.getTvtGameStartDay();
            long tvtQualifiedTime = calendar.getTimeInMillis() - tvtActivityStartDay * TimeUtil.DAY_MILLIS;
            long openTimeMs = gameServerConfig.getOpenTimeMs();
            if (isBlockServer) {
                logger.info("tvt 服务器{}在黑名单中，不能参加TVT", gameServerId);
                return;
            }
            if (!enabledServers) {
                logger.info("tvt 服务器{}不在白名单中，不能参加TVT", gameServerId);
                return;
            }

            if (openTimeMs > tvtQualifiedTime) {
                logger.info("服务器{}开服时间{}，小于设定时间{}，不能参加TVT", gameServerId, openTimeMs, tvtQualifiedTime);
                return;
            }

            // 超过时间才能参加活动
            tvtQualifiedServerDao.create(gameServerId);
        } else {
            // 之前有资格，但这次可能不在白名单里或者在黑名单里
            if (isBlockServer || !enabledServers) {
                logger.info("服务器{}在黑名单或不在白名单中，从参赛资格中移除", gameServerId);
                tvtQualifiedServerDao.delete(tvtQualifiedServer);
            } else if (!createValidated(activityListMeta,gameServerId)) {
                // ActivityServiceImpl.activityCreateValidated(gameServerId,
                // activity.getEndTime());
                logger.info("服务器{}即将进入赛季排期&&剩余时间不足以参加一轮活动，先不参与TVT了,删除参赛资格", gameServerId);
                tvtQualifiedServerDao.delete(tvtQualifiedServer);
            } else {
                // 该服继续参加
            }
        }
    }

    /**
     * TVT活动每天开启时的处理 等于跨天重置
     *
     * @param activity
     */
    private void onDailyReset(Activity activity) {
        // 通知game重置每日任务数据
        tvtControlBroadcastToGameService.broadcastActivityDailyReset(activity);
        // 。。。。
    }

    /**
     * 查询serverId服 所处赛季 编号
     *
     * @param serverId
     * @return 1, 2, 3...
     */
    private int getSeasonByServerId(int serverId) {
        KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getKvkSeasonServerGroupConfigByOServerId(serverId, System.currentTimeMillis());
        if (kvkSeasonServerGroupConfig == null) {
            //找不到赛季config 说明oserverId处于1赛季 ？ accurate？
            return 1;
        }
        return kvkSeasonServerGroupConfig.getSeason();
    }

    @Override
    public PsActivityInfo getRoleActivityInfo(Role role, ActivityListConfig.ActivityListMeta activityMeta) {
        ActivityVo tvtActivityVo = tvtGameDataManager.findTvtActivityVo();
        if (tvtActivityVo != null) {
            PsActivityInfo info = new PsActivityInfo();
            info.setId(String.valueOf(tvtActivityVo.getId()));
            info.setStartTime(tvtActivityVo.getStartTime());
            info.setEndTime(tvtActivityVo.getEndTime());
            info.setMetaId(activityMeta.getId());
            info.setType(getType().getPsType());
            info.setStatus(tvtActivityVo.getStatus().getPsStatus());
            info.setHasReward(false);
            return info;
        }
        return null;
    }
}
