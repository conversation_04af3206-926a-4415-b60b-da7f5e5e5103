package com.lc.billion.icefire.kvkseason.biz.model.seasonTask;

import java.util.HashMap;
import java.util.Map;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.IAlliancesEntity;

/**
 * 赛季任务 联盟相关的进度记录
 * 
 * <AUTHOR>
 * @date 2021/11/15
 */
public class AllianceSeasonTaskProgress extends AbstractEntity implements IAlliancesEntity {

	@MongoId
	private Long allianceId;
	private Map<Integer, Integer> occupyCityTimes = new HashMap<>();// 占领王城次数
	private Map<Integer, Integer> occupyCity = new HashMap<>();// 当前占领王城状态,限时任务数据，结算后不再更新

	@Override
	public void setPersistKey(Long id) {
		this.allianceId = id;
	}

	@Override
	public Long getPersistKey() {
		return this.allianceId;
	}

	@Override
	public Long getGroupingId() {
		return this.allianceId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	@Override
	public Long getAllianceId() {
		return this.allianceId;
	}

	@Override
	public void setAllianceId(Long allianceId) {
		this.allianceId = allianceId;
	}

	public int getOccupyCityTimes(int castleLevel) {
		if (occupyCityTimes == null)
			return 0;
		return occupyCityTimes.get(castleLevel) == null ? 0 : occupyCityTimes.get(castleLevel);
	}

	public void addOccupyCityTimes(int castleLevel) {
		if (occupyCityTimes == null)
			occupyCityTimes = new HashMap<>();
		occupyCityTimes.compute(castleLevel, (k, v) -> v == null ? 1 : v + 1);
	}

	public void setOccupyCity(Map<Integer, Integer> occupyCity) {
		this.occupyCity = occupyCity;
	}

	public int getOccupyCity(int castleLevel) {
		if (occupyCity == null)
			return 0;
		return occupyCity.get(castleLevel) == null ? 0 : occupyCity.get(castleLevel);
	}
}
