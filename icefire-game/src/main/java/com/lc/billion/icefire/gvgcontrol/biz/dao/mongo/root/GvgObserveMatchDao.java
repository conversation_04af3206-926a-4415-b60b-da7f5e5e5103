package com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root;

import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGQualifiedServer;
import com.lc.billion.icefire.gvgcontrol.biz.model.GvgObserveMatch;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository
public class GvgObserveMatchDao extends RootDao<GvgObserveMatch> {

    private Map<Integer, List<GvgObserveMatch>> dataByWarZoneId = new MyConcurrentMap<>();

    public GvgObserveMatchDao() {
        super(GvgObserveMatch.class, false);
    }

    @Override
    protected void putMemoryIndexes(GvgObserveMatch entity) {
        dataByWarZoneId.compute(entity.getWarZoneId(), (k, v) -> v == null ? new ArrayList<>() : v).add(entity);
    }

    @Override
    protected void removeMemoryIndexes(GvgObserveMatch entity) {
        if (dataByWarZoneId.containsKey(entity.getWarZoneId())) {
            dataByWarZoneId.get(entity.getWarZoneId()).remove(entity);
        }
    }

    @Override
    protected MongoCursor<GvgObserveMatch> doFindAll(int db) {
        return dbFindAllForWorldEntity(db);
    }

    public GvgObserveMatch create(Long battleServerId, int warZoneId) {
        int db = Application.getServerId();
        GvgObserveMatch entity = new GvgObserveMatch();
        entity.setPersistKey(battleServerId);
        entity.setWarZoneId(warZoneId);
        return createEntity(db, entity);
    }

    public void deleteAll() {
        delete(findAll());
    }
}
