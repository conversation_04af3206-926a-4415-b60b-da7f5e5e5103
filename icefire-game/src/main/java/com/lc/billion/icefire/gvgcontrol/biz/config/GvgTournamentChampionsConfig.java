package com.lc.billion.icefire.gvgcontrol.biz.config;

import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.model.AbstractMeta;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * gvg杯赛 淘汰赛冠亚季军数据。
 */
@Config(name = "GvgTournamentChampions", metaClass = GvgTournamentChampionsConfig.GvgTournamentChampionsMeta.class)
public class GvgTournamentChampionsConfig {
    //<warZone,<turn,list>>
    private Map<Integer, Map<Integer, List<GvgTournamentChampionsMeta>>> championsMapper;

    public void init(List<GvgTournamentChampionsMeta> all) {
        championsMapper = new HashMap<>();
        for (GvgTournamentChampionsMeta meta : all) {
            int warZoneId = meta.getWarZoneId();
            int turn = meta.getTurn();
            championsMapper.compute(warZoneId, (k, v) -> v == null ? new HashMap<>() : v).compute(turn, (k, v) -> v == null ? new ArrayList<>() : v).add(meta);
        }
    }

    public List<GvgTournamentChampionsMeta> getChampions(int warZoneId, int turn) {
        if (!championsMapper.containsKey(warZoneId)) {
            return null;
        }
        return championsMapper.get(warZoneId).get(turn);
    }

    public static class GvgTournamentChampionsMeta extends AbstractMeta {
        int warZoneId;
        int turn;
        int ranking;
        Long allianceId;
        String allianceName;
        String allianceAlias;
        String country;

        public int getServerId() {
            return serverId;
        }

        public void setServerId(int serverId) {
            this.serverId = serverId;
        }

        public int getBannerColor() {
            return bannerColor;
        }

        public void setBannerColor(int bannerColor) {
            this.bannerColor = bannerColor;
        }

        public String getBanner() {
            return banner;
        }

        public void setBanner(String banner) {
            this.banner = banner;
        }

        public int getBadgeColor() {
            return badgeColor;
        }

        public void setBadgeColor(int badgeColor) {
            this.badgeColor = badgeColor;
        }

        public String getBadge() {
            return badge;
        }

        public void setBadge(String badge) {
            this.badge = badge;
        }

        int serverId;
        /** 旗帜颜色 */
        int bannerColor;
        /** 旗帜 */
        String banner;
        /** 徽章颜色 */
        int badgeColor;
        /** 徽章 */
        String badge;

        public int getWarZoneId() {
            return warZoneId;
        }

        public void setWarZoneId(int warZoneId) {
            this.warZoneId = warZoneId;
        }

        public int getTurn() {
            return turn;
        }

        public void setTurn(int turn) {
            this.turn = turn;
        }

        public int getRanking() {
            return ranking;
        }

        public void setRanking(int ranking) {
            this.ranking = ranking;
        }

        public Long getAllianceId() {
            return allianceId;
        }

        public void setAllianceId(Long allianceId) {
            this.allianceId = allianceId;
        }

        public String getAllianceName() {
            return allianceName;
        }

        public void setAllianceName(String allianceName) {
            this.allianceName = allianceName;
        }

        public String getAllianceAlias() {
            return allianceAlias;
        }

        public void setAllianceAlias(String allianceAlias) {
            this.allianceAlias = allianceAlias;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }
    }

}
