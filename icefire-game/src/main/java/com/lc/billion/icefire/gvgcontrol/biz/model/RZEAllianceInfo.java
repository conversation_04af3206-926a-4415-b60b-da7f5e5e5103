package com.lc.billion.icefire.gvgcontrol.biz.model;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.rpc.vo.gvg.RZEAllianceInfoVo;
import org.jongo.marshall.jackson.oid.MongoId;

/**
 * RZE 联盟基本信息
 */
public class RZEAllianceInfo extends AbstractEntity {

    private static final long serialVersionUID = 7351905065914808180L;

    @MongoId
    //联盟id
    private Long allianceId;
    //服务器号
    private int serverId;
    //匹配分
    private long rating;
    //s1 势力值
    private long s1Prosperity;
    //联盟名称
    private String name;
    //联盟简称
    private String aliasName;

    public int getServerId() {
        return Long.valueOf(serverId).intValue();
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    public long getRating() {
        return rating;
    }

    public void setRating(long rating) {
        this.rating = rating;
    }

    public long getS1Prosperity() {
        return s1Prosperity;
    }

    public void setS1Prosperity(long s1Prosperity) {
        this.s1Prosperity = s1Prosperity;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAliasName() {
        return aliasName;
    }

    public void setAliasName(String aliasName) {
        this.aliasName = aliasName;
    }

    @Override
    public void setPersistKey(Long id) {
        allianceId = id;
    }

    @Override
    public Long getPersistKey() {
        return allianceId;
    }

    @Override
    public Long getGroupingId() {
        return allianceId;
    }

    @Override
    public int hashCodeImpl() {
        return hashCodeForPersistKey();
    }

    @Override
    public boolean equalsImpl(Object obj) {
        return equalsForPersistKey(obj);
    }

    public RZEAllianceInfoVo toVo() {
        return new RZEAllianceInfoVo(allianceId, serverId, rating, s1Prosperity, name, aliasName);
    }
}
