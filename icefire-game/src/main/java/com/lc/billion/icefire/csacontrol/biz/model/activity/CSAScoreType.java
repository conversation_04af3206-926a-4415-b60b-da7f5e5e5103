package com.lc.billion.icefire.csacontrol.biz.model.activity;

import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public enum CSAScoreType implements IntEnum {

	/**
	 * 服务器总分数
	 */
	TOTAL_SCORE(0) {
		@Override
		public void dealUploadScore(CSAActivityGroupContext csaActivityGroupContext, int serverScore) {
			ErrorLogUtil.errorLog("不应该上传总分", "serverId",csaActivityGroupContext.getSelfServerId());
		}
	},

	/**
	 * 上半场分数
	 */
	FIRST_HALF_SCORE(1) {
		@Override
		public void dealUploadScore(CSAActivityGroupContext csaActivityGroupContext, int serverScore) {
			csaActivityGroupContext.setFirstHalfScore(serverScore);
			csaActivityGroupContext.setServerScore(csaActivityGroupContext.getFirstHalfScore() + csaActivityGroupContext.getSecondHalfScore());
		}
	},

	/**
	 * 下半场分数
	 */
	SECOND_HALF_SCORE(2) {
		@Override
		public void dealUploadScore(CSAActivityGroupContext csaActivityGroupContext, int serverScore) {
			csaActivityGroupContext.setSecondHalfScore(serverScore);
			csaActivityGroupContext.setServerScore(csaActivityGroupContext.getFirstHalfScore() + csaActivityGroupContext.getSecondHalfScore());
		}
	},
	;

	private static final Logger logger = LoggerFactory.getLogger(CSAScoreType.class);

	private static final CSAScoreType[] INDEXES = EnumUtils.toArray(values());

	private int value;

	public abstract void dealUploadScore(CSAActivityGroupContext csaActivityGroupContext, int serverScore);

	CSAScoreType(int value) {
		this.value = value;
	}

	@Override
	public int getId() {
		return this.value;
	}

	public static CSAScoreType findById(int id) {
		if (id < 0 || id >= INDEXES.length) {
			return null;
		}
		return INDEXES[id];
	}

	public int getValue() {
		return value;
	}

	public void setValue(int value) {
		this.value = value;
	}
}
