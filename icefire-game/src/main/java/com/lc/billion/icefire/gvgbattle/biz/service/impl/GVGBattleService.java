package com.lc.billion.icefire.gvgbattle.biz.service.impl;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.config.ItemConfig;
import com.lc.billion.icefire.game.biz.config.SettingConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.activity.GVGAllianceBattleSummaryDao;
import com.lc.billion.icefire.game.biz.dao.mongo.alliances.AllianceMemberDao;
import com.lc.billion.icefire.game.biz.dao.mongo.alliances.AllianceSettingDao;
import com.lc.billion.icefire.game.biz.dao.mongo.alliances.AllianceTechDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.ArmyDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.GVGRoleInfoDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleExtraDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.GVGNpcNodeDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.GVGResNodeDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.RoleServerInfoDao;
import com.lc.billion.icefire.game.biz.internalmsg.login.ClosePlayerReason;
import com.lc.billion.icefire.game.biz.manager.ArmyManager;
import com.lc.billion.icefire.game.biz.manager.RoleCurrencyManager;
import com.lc.billion.icefire.game.biz.manager.RoleItemManager;
import com.lc.billion.icefire.game.biz.manager.SkinManager;
import com.lc.billion.icefire.game.biz.manager.gvg.GVGGameDataVoManager;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceMember;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceSetting;
import com.lc.billion.icefire.game.biz.model.army.ArmyReturnReason;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.gvg.GVGAllianceLineUpInfo;
import com.lc.billion.icefire.game.biz.model.gvg.GVGRoleHistoryInfo;
import com.lc.billion.icefire.game.biz.model.gvg.GVGRoleInfo;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleExtra;
import com.lc.billion.icefire.game.biz.model.role.RoleServerInfo;
import com.lc.billion.icefire.game.biz.model.scene.MapGrid;
import com.lc.billion.icefire.game.biz.model.scene.SceneCoverType;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.util.DateUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.GvgResNode;
import com.lc.billion.icefire.game.biz.model.scene.node.SceneCoverNode;
import com.lc.billion.icefire.game.biz.model.skin.SkinFashionType;
import com.lc.billion.icefire.game.biz.schedule.ScheduleOperation;
import com.lc.billion.icefire.game.biz.service.ScheduleService;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.gvg.GVGGameService;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.login.LoginServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.player.PlayerServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.role.RoleCityServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.exception.ToClientException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgBuildingConfig;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgBuildingConfig.GvgBuildingMeta;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.TvtSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.AllianceBattlePointDao;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.GVGBattleFieldTimeLineDao;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.RoleGVGBattleDao;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.StrongHoldNodeDao;
import com.lc.billion.icefire.gvgbattle.biz.manager.gvg.GVGBattleDataVoManager;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.*;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.GvgResNode;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.StrongHoldNode;
import com.lc.billion.icefire.gvgbattle.biz.service.GVGBattleToGVGControlService;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GVGBattleRecordDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.GVGBattleRecord;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.context.GVGActivityContext;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.constant.PsCloseReason;
import com.lc.billion.icefire.protocol.constant.PsErrorCode;
import com.lc.billion.icefire.protocol.structure.*;
import com.lc.billion.icefire.rpc.vo.gvg.*;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.config.WorldMapConfig;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;
import com.simfun.sgf.utils.JavaUtils;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class GVGBattleService {

    private static final Logger logger = LoggerFactory.getLogger(GVGBattleService.class);

    /**
     * 罗迪说写死,2021-04-21 16:49
     */
    private static final int KICK_DELAY = 30;

    @Autowired
    private ServiceDependency srvDpd;
    @Autowired
    private LoginServiceImpl loginService;
    @Autowired
    private PlayerServiceImpl playerService;
    @Autowired
    private RoleServerInfoDao roleServerInfoDao;
    @Autowired
    private WorldServiceImpl worldService;
    @Autowired
    private SceneServiceImpl sceneService;
    @Autowired
    private GVGBattleToGVGControlService gvgBattleToGVGControlService;
    @Autowired
    private AllianceBattlePointDao allianceBattlePointDao;
    @Autowired
    private GVGStrongHoldService gvgStrongHoldService;
    @Autowired
    private RoleGVGBattleDao roleGVGBattleDao;
    @Autowired
    private StrongHoldNodeDao strongHoldNodeDao;
    @Autowired
    private GVGBattleRecordDao gvgBattleRecordDao;
    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private GVGGameService gvgGameService;
    @Autowired
    private GVGBattleDataVoManager gvgBattleDataVoManager;
    @Autowired
    private GVGGameDataVoManager gvgGameDataVoManager;
    @Autowired
    private GVGBattleFieldTimeLineDao gvgBattleFieldTimeLineDao;
    @Autowired
    private GVGStrongHoldService GVGStrongHoldService;
    @Autowired
    private GVGNpcNodeDao gvgNpcNodeDao;
    @Autowired
    private GVGResNodeDao gvgResNodeDao;
    @Autowired
    private ArmyManager armyManager;
    @Autowired
    private AllianceDao allianceDao;
    @Autowired
    private ArmyDao armyDao;
    @Autowired
    private ArmyServiceImpl armyService;
    @Autowired
    private RoleDao roleDao;
    @Autowired
    private GVGOBService gvgobService;
    @Autowired
    private AllianceMemberDao allianceMemberDao;
    @Autowired
    private RoleExtraDao roleExtraDao;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private SkinManager skinManager;
    @Autowired
    private GVGRoleInfoDao gvgRoleInfoDao;
    @Autowired
    private RoleCityServiceImpl roleCityService;
    @Autowired
    private GVGBattleScoreService gvgBattleScoreService;
    @Autowired
    private RoleItemManager roleItemManager;
    @Autowired
    private RoleCurrencyManager roleCurrencyManager;
    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    protected BiLogUtil biLogUtil;


    private Set<Integer> cmdTypeFilters = new HashSet<>();

    private boolean isGVGBattleServer;

    private boolean gvgStopAndGoBack = false;

    // gvg战斗服锁住
    private boolean gvgBattleServerLock = false;

    // 当前战场服的玩家的服务器id
    @Getter
    private ConcurrentHashMap<Long, Integer> battleServerRoles = new ConcurrentHashMap<>();

    // 战场踢人结束
    @Getter
    private boolean battleKickFinish = false;

    @Autowired
    private GVGAllianceBattleSummaryDao gvgAllianceBattleSummaryDao;

    public void startService() {
        WorldMapConfig worldMapConfig = ServerConfigManager.getInstance().getWorldMapConfig();
        isGVGBattleServer = worldMapConfig.getServerType() == ServerType.GVG_BATTLE;

        if (isGVGBattleServer) {
            int serverId = Application.getServerId();
            logger.info("[GVG] 战斗服{}启服, 取中控服注册", serverId);

            // 1.从中控获取活动信息（活动时间、状态等）
            ActivityVo activityVo = gvgBattleToGVGControlService.findActivity();
            gvgBattleDataVoManager.updateActivityVo(activityVo);

            // 2.注册战斗服信息到中控-->中控再通知game（根据server id，拿到对战双方联盟信息）
            GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo = gvgBattleToGVGControlService.registerGVGBattleServerToGVGControlServer();
            if(gvgBattleServerDispatchRecordVo == null){
                // 中控服那边活动不存在，重启
                throw new AlertException("[GVG] gvgBattleServerDispatchRecordVo == null");
            }

            var time = gvgBattleFieldTimeLineDao.find();
            if(time != null){
                if(time.isEndFlag()){
                    // 战斗服重启，但是战斗已经结束了
                    throw new AlertException("[GVG] time.isEndFlag()");
                }
            }

            gvgBattleDataVoManager.updateGvgBattleServerDispatchRecordVo(gvgBattleServerDispatchRecordVo);

            // 3.从中控获取报名信息（根据server id，拿到对战双方的具体成员的信息）
            List<GVGAllianceSignUpInfoVo> gvgAllianceSignUpInfoVos = gvgBattleToGVGControlService.findGVGAllianceSignUpInfoVo(serverId);
            gvgBattleDataVoManager.updateGvgAllianceSignUpInfoVo(gvgAllianceSignUpInfoVos);

            // 4.初始化据点
            initStrongHoldNodes();

            // 5.初始化战场时间数据
            gvgBattleDataVoManager.initBattleFieldTimeLine();

            // 测试代码
            if (gvgBattleServerDispatchRecordVo == null) {
                // 战斗服启服没有匹配信息，30分钟后销毁
                int delay = 300;//方便GVG杯赛测试， 改成300分钟后销毁。
                ErrorLogUtil.errorLog("[GVG] 战斗服启服没有匹配信息,定时销毁", "delay",delay);
                scheduleService.schedule(new ScheduleOperation() {
                    @Override
                    public void execute() {
                        // 10秒检测一次人是否清干净了
                        boolean checkAllRoleGoBack = checkAllRoleGoBack();
                        if (checkAllRoleGoBack) {
                            // 通知中控服关闭战斗服
                            logger.info("[GVG] 所有人已被踢, 通知中控服销毁");
                            gvgBattleToGVGControlService.noticeControlServerDestroyBattleServer(Application.getServerId());
                        } else {
                            logger.info("[GVG] 战斗服还有人在, 回收失败, 注意关注{}", Application.getServerId());
                        }
                    }
                }, delay * TimeUtil.MINUTE_MILLIS);
            }
        }
    }

    /**
     * 初始化据点
     */
    public void initStrongHoldNodes() {
        // GVG建筑
        Collection<StrongHoldNode> strongHoldNodes = strongHoldNodeDao.findAll();
        GvgBuildingConfig gvgBuildingConfig = configService.getConfig(GvgBuildingConfig.class);
        if (JavaUtils.bool(strongHoldNodes)) {
            for (StrongHoldNode strongHoldNode : strongHoldNodes) {
                GvgBuildingMeta meta = gvgBuildingConfig.get(strongHoldNode.getMetaId());
                logger.info("[GVG] 初始化GVG建筑......, meta:{}", strongHoldNode.getMetaId());
                if (meta != null) {
                    logger.info("[GVG] 初始化GVG建筑, 找到......, meta:{} Type:{}", strongHoldNode.getMetaId(), BuildingType.findById(meta.getBuildingtype()));
                    strongHoldNode.setBuildingType(BuildingType.findById(meta.getBuildingtype()));
                } else {
                    // todo gvg log
                }

                // 乌巢状态保存在WuChaoManager里，不存盘。乌巢的占领状态在node里，存盘
                // 重启把乌巢的占领状态清空
                if(strongHoldNode.getBuildingType() == BuildingType.WuChao) {
                    strongHoldNode.setAllianceId(0L);
                    strongHoldNode.setOccupyState(false);
                }

                sceneService.add(strongHoldNode, true);
                // 多地格处理
                initStrongHoldNodeCoverArea(strongHoldNode);
            }
        } else {
            // 战场开启时间
            long battleStartTime = gvgBattleDataVoManager.getDispatchRecordInfo().getBattleStartTime();
            var gvgBuildingMetas = gvgBuildingConfig.getMetaMap().values();
            if (JavaUtils.bool(gvgBuildingMetas)) {
                for (GvgBuildingMeta gvgBuildingMeta : gvgBuildingMetas) {
                    if(gvgBuildingMeta.getBuildingtype() == BuildingType.ZiYuan.getId()){
                        continue;
                    }
                    long unlockTime = battleStartTime + gvgBuildingMeta.getUnlockTime() * TimeUtil.SECONDS_MILLIS;
                    Point position = gvgBuildingMeta.getBuildingPosition();
                    if (position != null) {
                        StrongHoldNode strongHoldNode = strongHoldNodeDao.create(gvgBuildingMeta.getId(), position, unlockTime);
                        strongHoldNode.setBuildingType(BuildingType.findById(gvgBuildingMeta.getBuildingtype()));

                        if (strongHoldNode.getBuildingType() == BuildingType.DaYing) {
                            Long allianceId = gvgBattleDataVoManager.getPointBelong(position);
                            strongHoldNode.setOccupied(true);
                            strongHoldNode.setAllianceId(allianceId);
                            strongHoldNodeDao.save(strongHoldNode);
                        }


                        sceneService.add(strongHoldNode, true);
                        // 多地格处理
                        initStrongHoldNodeCoverArea(strongHoldNode);
                    }
                }
            }
        }

        // GVG 物资点
        Collection<GvgResNode> gvgResNodes = gvgResNodeDao.findAll();
        if (JavaUtils.bool(gvgResNodes)) {
            for (GvgResNode gvgResNode : gvgResNodes) {
                sceneService.add(gvgResNode, true);
                initGvgResCoverArea(gvgResNode);
                logger.info("[GVG] 战场加载物资:{}", gvgResNode.getMetaId());
            }
        }
    }

    private void initStrongHoldNodeCoverArea(StrongHoldNode strongHoldNode) {
        GvgBuildingConfig gvgBuildingConfig = configService.getConfig(GvgBuildingConfig.class);
        GvgBuildingMeta gvgBuildingMeta = gvgBuildingConfig.getMetaMap().get(strongHoldNode.getMetaId());
        List<Point> gvgBuildsSizePoints = gvgBuildingConfig.getGvgBuildsSizePoints(gvgBuildingMeta);
        if (JavaUtils.bool(gvgBuildsSizePoints)) {
            for (Point position : gvgBuildsSizePoints) {
                SceneCoverNode strongHoldCover = new SceneCoverNode(SceneCoverType.STRONGHOLD, strongHoldNode.getPersistKey());
                strongHoldCover.setCurrentServerId(strongHoldNode.getCurrentServerId());
                strongHoldCover.setPosition(position);
                try {
                    // 永远不广播
                    sceneService.add(strongHoldCover, true);
                } catch (ExpectedException ignored){

                } catch (Exception e) {
                    ErrorLogUtil.exceptionLog("[GVG] 据点多地格处理报错", e);
                }
            }
        }
    }

    public MapGrid getPositionByEnterGVG(Role role) {
        // 判断玩家是否为观战方：
        RoleServerInfo roleServerInfo = roleServerInfoDao.findById(role.getPersistKey());
        if(roleServerInfo != null && roleServerInfo.getGvgSide() == 1){
            return gvgobService.initOBBirthPoint(role);
        }else {
            logger.info("[GVG] 战场获取玩家出生点：{}/{} RoleServerInfo非空：{}", role.getId(), Application.getServerType(), roleServerInfo == null ? 1 : 0);
            Long allianceId = role.getAllianceId();
            if(Application.getServerType() == ServerType.TVT_BATTLE){
                if(roleServerInfo != null) {
                    RoleExtra roleExtra = roleExtraDao.findRoleExtraFromDB(roleServerInfo.getRegisterServerId(), role.getId());
                    if(roleExtra != null){
                        int teamId = roleExtra.getTvtTeamId();
                        allianceId = gvgBattleDataVoManager.getAllianceIdByTeamId(teamId);
                        logger.info("[GVG] 战场获取玩家出生点：{}/{} 队伍:{} 联盟:{}", role.getId(), Application.getServerType(), teamId, allianceId == null ? "" : allianceId);
                    }else {
                        logger.info("[GVG] 战场获取玩家出生点：{}/{} RoleExtra为空", role.getId(), Application.getServerType());
                    }
                }
            }

            if (JavaUtils.bool(allianceId)) {
                // 确定在哪里出生
                GVGBirthArea gvgBirthArea = gvgBattleDataVoManager.findGvgBirthArea(allianceId);
                if (gvgBirthArea != null) {

                    int bornIndex = gvgBirthArea.getColor() - 1;
                    MapGrid mapGrid = getGVGMapGriDByConfig(role.getCurrentServerId(), gvgBirthArea, bornIndex);
                    if (mapGrid == null) {
                        mapGrid = getMapGridOld(role.getCurrentServerId(), gvgBirthArea);
                    }
                    if (mapGrid != null) return mapGrid;
                }
                ErrorLogUtil.errorLog("[GVG] 玩家联盟找不到分配的出生区域", "role",role);
                return null;
            } else {
                ErrorLogUtil.errorLog("[GVG] 玩家没有联盟", "role",role);
                return null;
            }
        }
    }

    private MapGrid getGVGMapGriDByConfig(int currentServerId, GVGBirthArea gvgBirthArea, int bornIndex) {
        int mapHeight = worldService.getMapHeight(currentServerId);
        int mapWidth = worldService.getMapWidth(currentServerId);
        int minX = gvgBirthArea.getMinX() < 0 ? 0 : gvgBirthArea.getMinX();
        int maxX = gvgBirthArea.getMaxX() > mapWidth - 1 ? mapWidth - 1 : gvgBirthArea.getMaxX();
        int minY = gvgBirthArea.getMinY() < 0 ? 0 : gvgBirthArea.getMinY();
        int maxY = gvgBirthArea.getMaxY() > mapHeight - 1 ? mapHeight - 1 : gvgBirthArea.getMaxY();
        List<Point> pointList = configService.getConfig(GvgSettingConfig.class).getBornPointListByIndex(bornIndex);
        if (pointList == null) {
            return null;
        }
        for (int pointIndex = 0; pointIndex < pointList.size(); pointIndex++) {
            Point point = pointList.get(pointIndex);
            if (!checkPointValid(point, minX, maxX, minY, maxY)) {
                continue;
            }
            MapGrid mapGrid = worldService.getGrid(currentServerId, point.getX(), point.getY());
            if (mapGrid == null) {
                continue;
            }
            if (mapGrid.isBlock()) {
                continue;
            }
            Point position = mapGrid.getPosition();
            SceneNode sceneNode = sceneService.getSceneNode(currentServerId, position);
            if (sceneNode == null) {
                return mapGrid;
            }
        }
        return null;
    }

    private boolean checkPointValid(Point point, int minX, int maxX, int minY, int maxY) {
        if (point == null) {
            return false;
        }
        int x = point.getX();
        int y = point.getY();
        return x >= minX && x <= maxX && y >= minY && y <= maxY;
    }

    private MapGrid getMapGridOld(int currentServerId, GVGBirthArea gvgBirthArea) {
        int mapHeight = worldService.getMapHeight(currentServerId);
        int mapWidth = worldService.getMapWidth(currentServerId);
        int minX = gvgBirthArea.getMinX() < 0 ? 0 : gvgBirthArea.getMinX();
        int maxX = gvgBirthArea.getMaxX() > mapWidth - 1 ? mapWidth - 1 : gvgBirthArea.getMaxX();
        int minY = gvgBirthArea.getMinY() < 0 ? 0 : gvgBirthArea.getMinY();
        int maxY = gvgBirthArea.getMaxY() > mapHeight - 1 ? mapHeight - 1 : gvgBirthArea.getMaxY();
        for (int x = minX; x <= maxX; x++) {
            for (int y = minY; y <= maxY; y++) {
                MapGrid mapGrid = worldService.getGrid(currentServerId, x, y);
                if (mapGrid == null) {
                    continue;
                }
                if (mapGrid.isBlock()) {
                    continue;
                }
                Point position = mapGrid.getPosition();
                SceneNode sceneNode = sceneService.getSceneNode(currentServerId, position);
                if (sceneNode == null) {
                    return mapGrid;
                }
                continue;
            }
        }
        return null;
    }

    /**
     * 初始化gvg物资点占地块
     */
    private void initGvgResCoverArea(GvgResNode gvgResNode) {
        GvgBuildingConfig gvgBuildingConfig = configService.getConfig(GvgBuildingConfig.class);
        GvgBuildingMeta meta = gvgBuildingConfig.getBuildingMetaByType(BuildingType.ZiYuan.getId());
        List<Point> resSizePoints = gvgBuildingConfig.getGvgResSizePoints(meta, gvgResNode.getPosition());
        if (JavaUtils.bool(resSizePoints)) {
            for (Point position : resSizePoints) {
                SceneCoverNode gvgResCover = new SceneCoverNode(SceneCoverType.GVG_RES, gvgResNode.getPersistKey());
                gvgResCover.setCurrentServerId(gvgResNode.getCurrentServerId());
                gvgResCover.setPosition(position);
                try {
                    // 永远不广播
                    sceneService.add(gvgResCover, true);
                } catch (ExpectedException ignored){

                } catch (Exception e) {
                    ErrorLogUtil.exceptionLog("[GVG] 物资点,多地格报错", e,
                            "posX",gvgResNode.getPosition().getX(), "posY",gvgResNode.getPosition().getY());
                }
            }
        }
    }

    public void onEnterWorld(Role role) {
        if (role == null) {
            return;
        }
        if (!Application.isBattleServer()) {
            return;
        }

        GcGVGBattlleLogin msg = wrapGcGVGBattleLgin(role);
        role.send(msg);
    }

    private GcGVGBattlleLogin wrapGcGVGBattleLgin(Role role) {
        GcGVGBattlleLogin msg = new GcGVGBattlleLogin();
        msg.setSeason(getBattleServerSeason(role));
        return msg;
    }


    /**
     * 检测指令是否要拦截
     *
     * @param cmdType
     * @return
     */
    public boolean checkCmdTypeFilter(int cmdType) {
        if (isGVGBattleServer) {
            if (cmdTypeFilters.contains(cmdType)) {
                logger.info("[GVG] 指令{}在GVG服务器不允许被执行", cmdType);
                return true;
            }
        }
        return false;
    }

    /**
     * 结算并重算联盟积分速度
     *
     * 据点易主时计算，双方都要计算
     */
    public boolean recountAlliancePoint(Long allianceId, boolean iSend) {
        if (!JavaUtils.bool(allianceId)) {
            ErrorLogUtil.errorLog("[GVG]recountAlliancePoint, allianceId error", "allianceId",allianceId);
            return true;
        }

        AllianceBattlePoint allianceBattlePoint = allianceBattlePointDao.findById(allianceId);
        if (allianceBattlePoint == null) {
            allianceBattlePoint = allianceBattlePointDao.createById(allianceId);
        }

        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        int max = gvgSettingConfig.getGvgWinPoint();
        if (allianceBattlePoint.getValue() >= max) {
            logger.info("[GVG]recountAlliancePoint, alliancePoint exceed max, allianceId: {}, point: {}, max: {}", allianceId, allianceBattlePoint.getValue(), max);
            return false;
        }

        // 1.根据当前速度结算
        double pointSpeed = allianceBattlePoint.getPointSpeed();
        long now = TimeUtil.getNow();
        logger.info("[GVG]recountAlliancePoint, allianceId: {}, speed: {}, now: {}, lastCountTime: {}, elapse: {}, oldPoint: {}", allianceId, pointSpeed, now, allianceBattlePoint.getLastCountTime(), now - allianceBattlePoint.getLastCountTime(), allianceBattlePoint.getValue());
        if (pointSpeed > 0) {
            // 本次理论应该增加的值
            double additionPoint = allianceBattlePoint.additionPoint(now);

            allianceBattlePoint.setLastCountTime(now);
            gvgBattleScoreService.addAlliancePoint(allianceId, (int)additionPoint, AddPointType.OCCUPYING, false);
            allianceBattlePointDao.save(allianceBattlePoint);

            // 上限
            if (allianceBattlePoint.getValue() >= max) {
                logger.info("[GVG]recountAlliancePoint, alliancePoint exceed max2, allianceId: {}, point: {}, max: {}", allianceId, allianceBattlePoint.getValue(), max);
                // 超出上限结束了，设置为最大上限
                allianceBattlePoint.setValue(max);
                // 速度设成0，防止tick继续走
                allianceBattlePoint.setPointSpeed(0);
                allianceBattlePointDao.save(allianceBattlePoint);
                return false;
            }
        } else {
            if (allianceBattlePoint.getLastCountTime() > 0) {
                allianceBattlePoint.setLastCountTime(now);
                allianceBattlePointDao.save(allianceBattlePoint);
            }
        }

        // 广播给战场内所有联盟玩家
        if (iSend) {
            GVGStrongHoldService.pushInformationBothSides(0, 0L);
        }

        return true;
    }

    public void recountAlliancePoint() {
        Collection<AllianceBattlePoint> allianceBattlePoints = allianceBattlePointDao.findAll();
        for (AllianceBattlePoint allianceBattlePoint : allianceBattlePoints) {
            recountAlliancePoint(allianceBattlePoint.getPersistKey(), true);
        }
    }

    /**
     * 更新联盟点数速度
     */
    public void updateAllianceBattlePointSpeed(long allianceId) {
        if (!JavaUtils.bool(allianceId)) {
            return;
        }
        // 结算
        AllianceBattlePoint allianceBattlePoint = allianceBattlePointDao.findById(allianceId);
        if (allianceBattlePoint == null) {
            allianceBattlePoint = allianceBattlePointDao.createById(allianceId);
        }

        // 新速度
        double newPointSpeed = 0;
        // 所有据点
        Collection<StrongHoldNode> strongHoldNodes = strongHoldNodeDao.findAll();
        GvgBuildingConfig gvgBuildingConfig = configService.getConfig(GvgBuildingConfig.class);
        if (JavaUtils.bool(strongHoldNodes)) {
            for (StrongHoldNode strongHoldNode : strongHoldNodes) {
                if (strongHoldNode.isOccupiedByAlliance(allianceId)) {
                    // 这个点每秒钟可加多少个点
                    GvgBuildingMeta meta = gvgBuildingConfig.getMetaMap().get(strongHoldNode.getMetaId());
                    if (meta != null && meta.getBuildingPoints() > 0) {
                        newPointSpeed += meta.getBuildingPoints();
                    }
                }
            }
        }

        var oldPointSpeed = allianceBattlePoint.getPointSpeed();
        // 更新速度
        allianceBattlePoint.setPointSpeed(newPointSpeed);

        // 更新首次计算时间
        var now = TimeUtil.getNow();
        var oldLastCountTime = allianceBattlePoint.getLastCountTime();
        if (newPointSpeed > 0 && allianceBattlePoint.getLastCountTime() == 0) {
            allianceBattlePoint.setLastCountTime(now);
        }
        var newLastCountTime = allianceBattlePoint.getLastCountTime();

        // 重算结束时间
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        long remainValue = gvgSettingConfig.getGvgWinPoint() - allianceBattlePoint.getValue();
        double remainTime = newPointSpeed > 0 ? (remainValue * TimeUtil.SECONDS_MILLIS / newPointSpeed) : (24 * 60 * 60 * TimeUtil.SECONDS_MILLIS);// 如果速度是0，则结束时间设置为1天后
        // 多加一秒防止数值不对重算
        allianceBattlePoint.setEndTime(TimeUtil.getNow() + (long) remainTime + 1);

        logger.info("[GVG]updateAllianceBattlePointSpeed, allianceId: {}, speedChange: {} -> {}, lastCountTime: {} -> {}, remainValue: {}, remainTime: {}",
                allianceId, oldPointSpeed, newPointSpeed, oldLastCountTime, newLastCountTime, remainValue, remainTime);

        allianceBattlePointDao.save(allianceBattlePoint);

        try {
            int round = 0;
            if(Application.getServerType() == ServerType.GVG_BATTLE){
                ActivityVo gvgActivityVo = gvgBattleDataVoManager.findActivityVo();
                GVGActivityContext activityContext = gvgActivityVo.getActivityContext();
                round = activityContext.getRound();
            }

            GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo = gvgBattleDataVoManager.findGvgBattleServerDispatchRecordVo();
            int turn = gvgBattleServerDispatchRecordVo.getBattleTurn();
            long battleStartTime = gvgBattleServerDispatchRecordVo.getBattleStartTime();
            int allianceServerId = 0;
            if (JavaUtils.bool(gvgBattleServerDispatchRecordVo.getAllianceId1()) && allianceId == gvgBattleServerDispatchRecordVo.getAllianceId1()) {
                allianceServerId = gvgBattleServerDispatchRecordVo.getAlliance1ServerId();
            } else if (JavaUtils.bool(gvgBattleServerDispatchRecordVo.getAllianceId2()) && allianceId == gvgBattleServerDispatchRecordVo.getAllianceId2()) {
                allianceServerId = gvgBattleServerDispatchRecordVo.getAlliance2ServerId();
            }

            Alliance alliance = allianceDao.findById(allianceId);
            if (alliance != null) {
                srvDpd.getBiLogUtil().gvgSpeedChange(alliance.getLeaderId(), Application.getServerId(), round, turn, battleStartTime, allianceServerId, allianceId,
                        alliance.getLeaderId(), allianceBattlePoint.getPointSpeed(), allianceBattlePoint.getValue(), gvgBattleServerDispatchRecordVo.getMatchType());
            } else {
                ErrorLogUtil.errorLog("[GVG] 打点, 积分速度变化---联盟为空", "allianceId",allianceId);
            }
        } catch (ExpectedException ignored){

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("[GVG] 打点, 联盟速度变化异常", e,"allianceId",allianceId);
        }
    }

    /**
     * 战场停止-有一方达到上限了
     */
    public void battleStop() {
        logger.info("[GVG] battleStop");
        if (gvgBattleServerLock) {
            // 防多次操作
            ErrorLogUtil.errorLog("[GVG] battleStop reenter");
            return;
        }

        // 打标记
        gvgBattleServerLock = true;

        var timeLine = gvgBattleFieldTimeLineDao.find();
        if(!timeLine.isEndFlag()) {
            timeLine.setEndFlag(true);
            gvgBattleFieldTimeLineDao.save(timeLine);
        }

        // 打标记，不允许登录
        loginService.setLoginSwitch(false);

        // 战斗停止召回所有玩家的行军。
        Collection<Role> allRole = roleDao.findAll();
        for (Role role : allRole) {
            try {
                armyService.returnAllArmy(role, true, ArmyReturnReason.GVG_STOP);
            } catch (Exception e) {
                ErrorLogUtil.errorLog("结算部队报错，", e, "roleId" , role.getId());
            }
            // 结算一次
            GVGStrongHoldService.recountRoleBattlePoint(role, true);
        }
		logger.info("[GVG] 战斗服战斗结束, 清理所有行军 当前armyDao的实体数量：{}, 当前时间戳：{}", armyDao.findAll().size(), TimeUtil.getNow());

        settleGuanDuoccupyTime();

        // 检测是否所有联盟都有数据
        GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo = gvgBattleDataVoManager.findGvgBattleServerDispatchRecordVo();
        checkAllianceBattlePointFinalData(gvgBattleServerDispatchRecordVo.getAllianceId1(), gvgBattleServerDispatchRecordVo.getAllianceId2());

        // 判断是否已经执行过battleStop：发生在战场倒计时结束，但是还没打完情况的特殊判断
        Collection<AllianceBattlePoint> allianceBattlePoints = allianceBattlePointDao.findAll();
//        if (!JavaUtils.bool(allianceBattlePoints)) {
//            logger.info("已经执行过battleStop了");
//            return;
//        }

        // 停止积分产出
        for (AllianceBattlePoint allianceBattlePoint : allianceBattlePoints) {
            allianceBattlePoint.setPointSpeed(0);
            allianceBattlePointDao.save(allianceBattlePoint);
        }

        // 结束后同步一次积分增长
        gvgStrongHoldService.pushInformationBothSides(0, 0L);

        List<AllianceBattlePoint> list = allianceBattlePoints.stream().sorted((a1, a2) -> { return a2.getValue() - a1.getValue(); }).collect(Collectors.toList());
        AllianceBattlePoint failAllianceBattlePoint = list.get(1);
        AllianceBattlePoint victoryAllianceBattlePoint = list.get(0);
        Long failAllianceId = failAllianceBattlePoint.getPersistKey();
        Long victoryAllianceId = victoryAllianceBattlePoint.getPersistKey();

        // 设置成功失败的标志
        victoryAllianceBattlePoint.setResult(0);
        failAllianceBattlePoint.setResult(1);
        allianceBattlePointDao.save(victoryAllianceBattlePoint);
        allianceBattlePointDao.save(failAllianceBattlePoint);

        // 取消据点占领
        Collection<StrongHoldNode> strongHoldNodes = strongHoldNodeDao.findAll();
        for (StrongHoldNode strongHoldNode : strongHoldNodes) {
            strongHoldNode.setAllianceId(0L);
            strongHoldNodeDao.save(strongHoldNode);
        }

        int failAllianceServerId = 0;
        int victoryAllianceServerId = 0;
        if (failAllianceBattlePoint.getPersistKey().longValue() == gvgBattleServerDispatchRecordVo.getAllianceId1().longValue()) {
            failAllianceServerId = gvgBattleServerDispatchRecordVo.getAlliance1ServerId();
            victoryAllianceServerId = gvgBattleServerDispatchRecordVo.getAlliance2ServerId();
        } else {
            failAllianceServerId = gvgBattleServerDispatchRecordVo.getAlliance2ServerId();
            victoryAllianceServerId = gvgBattleServerDispatchRecordVo.getAlliance1ServerId();
        }

        // 防止rpc丢记录本地也存
        int victoryAllianceCount = 0;
        int failAllianceCount = 0;
        long activityId = 0L;
        int activityRound = 0;
        ActivityVo activityVo = gvgBattleDataVoManager.findActivityVo();
        if (activityVo != null) {
            activityId = activityVo.getId();
            if(Application.getServerType() == ServerType.GVG_BATTLE){
                GVGActivityContext gvgActivityContext = activityVo.getActivityContext();
                activityRound = gvgActivityContext.getRound();
            }
        }

        logger.info("[GVG] battleStop createBattleRecord: matchType: {}, win {}(server: {}, score: {}), lost: {}(server: {}, score: {})",
                gvgBattleServerDispatchRecordVo.getMatchType(), victoryAllianceId, victoryAllianceServerId, victoryAllianceBattlePoint.getValue(), failAllianceId, failAllianceServerId, failAllianceBattlePoint.getValue());
        GVGBattleRecord gvgBattleRecord = gvgBattleRecordDao.create(failAllianceId, failAllianceBattlePoint.getValue(), failAllianceServerId, victoryAllianceId,
                victoryAllianceBattlePoint.getValue(), victoryAllianceServerId, activityId, activityRound, gvgBattleServerDispatchRecordVo.getGameRound(),
                gvgBattleServerDispatchRecordVo.getMatchType(), gvgBattleServerDispatchRecordVo.getRound(),gvgBattleServerDispatchRecordVo.getWarZoneId());

        Collection<RoleGVGBattle> roleGVGBattles = roleGVGBattleDao.findAll();

        // mvp结算
        logger.info("[GVG] MVP结算开始");
        List<RoleGVGBattle> collect = roleGVGBattles.stream().sorted(Comparator.comparing(RoleGVGBattle::getBattlePoint).reversed()).toList();
        collect.forEach(roleGVGBattle -> logger.info("[GVG] battleStop roleInfo roleId: {}, allianceId: {}, battlePoint: {}", roleGVGBattle.getRoleId(), roleGVGBattle.getAllianceId(), roleGVGBattle.getBattlePoint()));
        logger.info("[GVG] MVP结算结束");


        // 玩家数据记录
        if (JavaUtils.bool(roleGVGBattles)) {
            for (RoleGVGBattle roleGVGBattle : roleGVGBattles) {
                if (roleGVGBattle.getAllianceId() != null) {
                    if (victoryAllianceId != null && victoryAllianceId.equals(roleGVGBattle.getAllianceId())) {
                        victoryAllianceCount++;
                    } else if (failAllianceId != null && failAllianceId.equals(roleGVGBattle.getAllianceId())) {
                        failAllianceCount++;
                    }
                    RoleGVGBattleVo roleGVGBattleVo = getRoleGVGBattleVo(roleGVGBattle);
                    gvgBattleRecord.addRoleGVGBattleVos(roleGVGBattleVo, roleGVGBattle.getAllianceId());
                } else {
                    ErrorLogUtil.errorLog("[GVG] battleStop roleGVGBattle.getAllianceId() is null", "roleId", roleGVGBattle.getRoleId());
                }
            }
        } else {
            logger.info("[GVG] battleStop roleGVGBattles is empty");
        }
        
        gvgBattleRecordDao.save(gvgBattleRecord);

        // 给客户的发送最终结果
        sendBattleResult(victoryAllianceId, collect.isEmpty() ? null : collect.getFirst(), gvgBattleRecord.getMatchType());

        try{
            // 打点
            biBattleStop(gvgBattleServerDispatchRecordVo, victoryAllianceId, victoryAllianceServerId, failAllianceId, failAllianceServerId,
                victoryAllianceBattlePoint, victoryAllianceCount, failAllianceBattlePoint, failAllianceCount);

            GVGBattleRecordVo gvgBattleRecordVo = new GVGBattleRecordVo(gvgBattleRecord);
            // 填充战场积分数据
            GVGStrongHoldService.addGvgRoleScoreInfo(failAllianceId, victoryAllianceId, gvgBattleRecordVo);
            gvgBattleToGVGControlService.uploadGVGBattleRecord(gvgBattleRecordVo);
            logger.info("[GVG] 战斗服--中控--游戏服：同步战场记录和发奖");
        } catch (ExpectedException ignored){

        } catch (Exception e){
            ErrorLogUtil.exceptionLog("[GVG] 战斗服--中控--游戏服：同步战场记录和发奖,RPC超时导致异常", e);
        }finally {
            // 这样处理的目的:不论战斗服到中控的RPC超时与否，都保证玩家能回去和销毁战斗服.
            GVGStrongHoldService.sendStrongHoldResultInfo(failAllianceId, victoryAllianceId);
            // 战场彻底结束
            gvgBattleServerFinish();
        }
    }

    /**
     * 战场结束，踢人，踢回原服，检测，销毁战场服
     */
    private void gvgBattleServerFinish() {
        // 30秒后开始踢人
        int delay = KICK_DELAY;
        if(Application.getServerType() == ServerType.TVT_BATTLE){
            delay = configService.getConfig(TvtSettingConfig.class).getTvtGameExitTimeSetting();
        }

        int periodMinute = 3;
        // 30秒后踢人
        scheduleService.scheduleWithFixedDelay(new ScheduleOperation() {
            @Override
            public void execute() {
                battleServerEnd();
            }
        }, delay * TimeUtil.SECONDS_MILLIS, periodMinute * TimeUtil.MINUTE_MILLIS);

        ErrorLogUtil.errorLog("[GVG] 最后开始踢人和销毁战斗服了");
    }

    private void settleGuanDuoccupyTime() {
        try {
            List<StrongHoldNode> guanduList = strongHoldNodeDao.findBuildingByType(BuildingType.GuanDu);
            StrongHoldNode guanduNode = guanduList.get(0);
            if (guanduNode.getOccupyState() == StrongHoldNode.SHOccupyState.Occupied && guanduNode.getAllianceId() > 0) {
                gvgStrongHoldService.recountGuanDuOccupyTimeOfOldAlliance(guanduNode, guanduNode.getAllianceId(), guanduNode.getOccupyTime());
            }
        } catch (ExpectedException ignored){

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("[GVG] settleGuanDuoccupyTime error", e);
        }
    }

    private void checkAllianceBattlePointFinalData(Long allianceId1, Long allianceId2) {
        getOneAllianceBattlePoint(allianceId1);
        getOneAllianceBattlePoint(allianceId2);

        List<AllianceBattlePoint> removeList = new ArrayList<>();
        for (AllianceBattlePoint allianceBattlePoint : allianceBattlePointDao.findAll()) {
            if (!Objects.equals(allianceBattlePoint.getPersistKey(), allianceId1)  && !Objects.equals(allianceBattlePoint.getPersistKey(), allianceId2)) {
                removeList.add(allianceBattlePoint);
            }
        }
        if (JavaUtils.bool(removeList)) {
            for (AllianceBattlePoint allianceBattlePoint : removeList) {
                ErrorLogUtil.errorLog("[GVG] checkAllianceBattlePointFinalData",
                        "allianceBattlePointKey",allianceBattlePoint.getPersistKey(),
                        "allianceBattlePointValue",allianceBattlePoint.getValue());
                allianceBattlePointDao.delete(allianceBattlePoint);
            }
        }
    }

    public AllianceBattlePoint getOneAllianceBattlePoint(Long allianceId) {
        if (!JavaUtils.bool(allianceId)) {
            return null;
        }
        AllianceBattlePoint allianceBattlePoint = allianceBattlePointDao.findById(allianceId);
        if (allianceBattlePoint == null) {
            allianceBattlePoint = allianceBattlePointDao.createById(allianceId);
        }
        return allianceBattlePoint;
    }


    public void clearBattleServerData() {
        logger.info("[GVG] clear gvg Data start");
        gvgBattleFieldTimeLineDao.delete();
        allianceBattlePointDao.deleteAll();
        roleGVGBattleDao.deleteAll();
        strongHoldNodeDao.deleteAll();
        gvgResNodeDao.deleteAll();
        gvgNpcNodeDao.deleteAll();
        allianceDao.deleteAll();
        allianceMemberDao.deleteAll();
        logger.info("[GVG] clear gvg Data end");
    }


    private static RoleGVGBattleVo getRoleGVGBattleVo(RoleGVGBattle roleGVGBattle) {
        RoleGVGBattleVo roleGVGBattleVo = new RoleGVGBattleVo();
        roleGVGBattleVo.setRoleId(roleGVGBattle.getRoleId());
        roleGVGBattleVo.setBattlePoint(roleGVGBattle.getBattlePoint());
        roleGVGBattleVo.setResult(roleGVGBattle.getResult());
        roleGVGBattleVo.setScore(roleGVGBattle.getScore());
        roleGVGBattleVo.setChangeScore(roleGVGBattle.getChangeScore());
        roleGVGBattleVo.setRank(roleGVGBattle.getRank());
        roleGVGBattleVo.setChangeRank(roleGVGBattle.getChangeRank());
        roleGVGBattleVo.setKillEnemyScore(roleGVGBattle.getKillEnemyScore());
        roleGVGBattleVo.setKillEnemyNum(roleGVGBattle.getKillEnemyNum());
        roleGVGBattleVo.setKillNpcScore(roleGVGBattle.getKillNpcScore());
        roleGVGBattleVo.setGatherScore(roleGVGBattle.getGatherScore());
        roleGVGBattleVo.setTeamId(roleGVGBattle.getTeamId());
        roleGVGBattleVo.setoServerId(roleGVGBattle.getoServerId());
        roleGVGBattleVo.setBuildScore(
                roleGVGBattle.getBattlePoint() - roleGVGBattle.getGatherScore() - roleGVGBattle.getKillNpcScore() - roleGVGBattle.getKillEnemyScore());
        roleGVGBattleVo.setMvp(roleGVGBattle.getMvp());
        roleGVGBattleVo.setTvtHideScore(roleGVGBattle.getTvtHideScore());
        return roleGVGBattleVo;
    }

    public void updateRoleGameRecord(RoleGVGScoreInfoVo scoreInfoVo, GvgMatchType matchType) {
        if (scoreInfoVo == null) {
            return;
        }
        try {
            GVGRoleInfo gvgRoleInfo = gvgRoleInfoDao.findById(scoreInfoVo.getRoleId());
            if (gvgRoleInfo == null) {
                logger.info("GVG updateRoleGameRecord GVGRoleInfo null, roleId:{},currentServerId:{}", scoreInfoVo.getRoleId(),  Application.getServerId());
                return;
            }

            logger.info("GVG updateRoleGameRecord GVGRoleInfo，roleId:{},currentServerId:{}, score : {}", scoreInfoVo.getRoleId(), Application.getServerId(), scoreInfoVo.getPoint());
            gvgRoleInfo.setRoleHistoryInfo(scoreInfoVo, matchType);
            gvgRoleInfoDao.save(gvgRoleInfo);
        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("GVG updateRoleGameRecord GVGRoleInfo error.", e, "roel", scoreInfoVo.getRoleId());
        }
    }

    /**
     * 打点
     * */
    public void biBattleStop(GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo, Long victoryAllianceId, int victoryAllianceServerId,
            Long failAllianceId, int failAllianceServerId,
            AllianceBattlePoint victoryAllianceBattlePoint, int victoryAllianceCount,
            AllianceBattlePoint failAllianceBattlePoint, int failAllianceCount){
        // 打点联盟总积分
        try {
            int battleTime = (int) ((TimeUtil.getNow() - gvgBattleServerDispatchRecordVo.getBattleStartTime()) / TimeUtil.MINUTE_MILLIS);

            // 联盟向信息打点
            GVGAllianceLineUpInfo victoryLineUp = gvgBattleDataVoManager.findGvgAllianceSignUpInfoVos(victoryAllianceId).getGvgAllianceLineUpInfo();
            GVGAllianceLineUpInfo failLineUp  = gvgBattleDataVoManager.findGvgAllianceSignUpInfoVos(failAllianceId).getGvgAllianceLineUpInfo();
            Collection<Role> allRole = roleDao.findAll();
            int victoryFormalCount = 0;
            int victoryTempCount = 0;
            int failFormalCount = 0;
            int failTempCount = 0;
            for (Role role : allRole) {
                if (Objects.equals(role.getAllianceId(), victoryAllianceId)) {
                    if (victoryLineUp.getFormalMemberIds().contains(role.getRoleId())) {
                        victoryFormalCount++;
                    } else if (victoryLineUp.getTempMemberIds().contains(role.getRoleId())) {
                        victoryTempCount++;
                    }
                }
                if (Objects.equals(role.getAllianceId(), failAllianceId)) {
                    if (failLineUp.getFormalMemberIds().contains(role.getRoleId())) {
                        failFormalCount++;
                    } else if (failLineUp.getTempMemberIds().contains(role.getRoleId())) {
                        failTempCount++;
                    }
                }
            }
            biLogUtil.gvgJoinMember(victoryAllianceId,victoryLineUp.getFormalMemberIds().size(), victoryLineUp.getTempMemberIds().size(), victoryFormalCount, victoryTempCount);
            biLogUtil.gvgJoinMember(failAllianceId, failLineUp.getFormalMemberIds().size(), failLineUp.getTempMemberIds().size(), failFormalCount, failTempCount);

            // 输出结果（新）
            biLogUtil.gvgBattleResultNew(victoryAllianceId, failAllianceId, battleTime,
                    victoryAllianceBattlePoint != null ? victoryAllianceBattlePoint.getValue() : 0, failAllianceBattlePoint != null ? failAllianceBattlePoint.getValue() : 0,
                    victoryAllianceCount, failAllianceCount, gvgBattleServerDispatchRecordVo.getMatchType() == null ? -1 : gvgBattleServerDispatchRecordVo.getMatchType().getId());


            // 个人向信息打点
            Collection<RoleGVGBattle> roleGVGBattles = roleGVGBattleDao.findAll();
            for (RoleGVGBattle roleGVGBattle : roleGVGBattles) {
                Role role = roleDao.findById(roleGVGBattle.getRoleId());
                if (role == null) {
                    continue;
                }
                biLogUtil.gvgRoleResult(role, roleGVGBattle.getBattlePoint(),
                        roleGVGBattle.getGatherScore(), roleGVGBattle.getKillNpcScore(), roleGVGBattle.getKillEnemyScore(),
                        roleGVGBattle.getWuchaoScore(), roleGVGBattle.getOccupyScore(), roleGVGBattle.getFirstOccupyScore(),
                        gvgBattleServerDispatchRecordVo.getMatchType() == null ? -1 : gvgBattleServerDispatchRecordVo.getMatchType().getId());
            }

        } catch (ExpectedException ignored){

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("[GVG] 打点,总点数异常", e,"victoryAllianceId",victoryAllianceId,
                    "failAllianceId",failAllianceId);
        }
    }

    /**
     * 战场结束--踢人下线、踢回原服、通知中控服回收
     */
    public void battleServerEnd() {
        logger.info("[GVG] 战场结束啦 battleServerEnd");
        if (battleKickFinish) {
            logger.info("[GVG] 所有人正常踢回到原服");
            return;
        }

        gvgStopAndGoBack = true;
        kickRole();
        goBackServer();

        // 一分钟检测所有人是否正常踢回。
        checkAllRoleGoBackSchedule();
    }

    private void checkAllRoleGoBackSchedule() {
        scheduleService.schedule(new ScheduleOperation() {
            @Override
            public void execute() {
                //
                checkAllRoleGoBackScheduleFunc();
            }
        }, 60 * TimeUtil.SECONDS_MILLIS);
    }

    /**
     * 踢人检测的处理
     */
    private void checkAllRoleGoBackScheduleFunc() {
        // 踢人结束了
        if (battleKickFinish) {
            return;
        }

        boolean checkAllRoleGoBack = checkAllRoleGoBack();
        if (checkAllRoleGoBack) {
            // 通知中控服关闭战斗服
            logger.info("[GVG] 所有人已被踢, 通知中控服销毁");
            gvgBattleToGVGControlService.noticeControlServerDestroyBattleServer(Application.getServerId());
            battleKickFinish = true;
        } else {
            logger.info("[GVG] 战斗服还有人在, 回收失败, 注意关注{}", Application.getServerId());
        }
    }

    public boolean checkAllRoleGoBack() {

        GVGBattleRecord gvgBattleRecord = getGVGBattleRecord();
        if (gvgBattleRecord == null) {
            ErrorLogUtil.errorLog("GVG官渡 回服检测 没有得到结算数据");
            return true;
        }
        if (!checkSeverRoleGoBack(gvgBattleRecord.getVictoryAllianceServerId(), gvgBattleRecord.getVictoryAllianceId()))
            return false;

        if (!checkSeverRoleGoBack(gvgBattleRecord.getFailAllianceServerId(), gvgBattleRecord.getFailAllianceId()))
            return false;

        return true;
    }

    private boolean checkSeverRoleGoBack(int targetServerId, long allianceId) {
        GVGAllianceSignUpInfoVo gvgAllianceSignUpInfoVos = gvgBattleDataVoManager.findGvgAllianceSignUpInfoVos(allianceId);
        GVGAllianceLineUpInfo gvgAllianceLineUpInfo = gvgAllianceSignUpInfoVos == null ? null : gvgAllianceSignUpInfoVos.getGvgAllianceLineUpInfo();
        if (gvgAllianceLineUpInfo == null) {
            ErrorLogUtil.errorLog("GVG官渡 回服检测异常。 阵容为空", ErrorLogUtil.ALERT_TYPE_KEY, "gvgBackError", "targetServerId", targetServerId, "allianceId", allianceId);
            return true;
        }
        List<Long> signedList = new ArrayList<>();
        signedList.addAll(gvgAllianceLineUpInfo.getTempMemberIds());
        signedList.addAll(gvgAllianceLineUpInfo.getFormalMemberIds());
        int serverId = Application.getServerId();

        for (Long roleId : signedList) {
            RoleServerInfo roleServerInfo = roleServerInfoDao.findRoleServerInfoFromDB(targetServerId, roleId);
            if (roleServerInfo.getMigrateServerId() != serverId) {
                continue;
            }
            return false;
        }
        return true;
    }

    private void kickRole() {
        logger.info("[GVG] 战场结束啦 开始踢人 kickRole");
        try {
            // 踢人下线
            playerService.playerForEach(bean -> {
                try {
                    if (bean == null) {
                        return;
                    }
                    bean.tryClose(new GcCloseClient(PsCloseReason.GVG_STOP), ClosePlayerReason.GVGSERVERACTIVITY);
                } catch (ExpectedException ignored){

                } catch (Exception e) {
                    ErrorLogUtil.exceptionLog(e);
                }
            });
        } catch (ExpectedException ignored){

        } catch(Exception e) {
            ErrorLogUtil.exceptionLog(e);
        } finally {

        }
    }

    private void goBackServer() {
        logger.info("[GVG] 战场结束啦 开始返回 goBackServer");

        try {
            GVGBattleRecord gvgBattleRecord = getGVGBattleRecord();
            if (gvgBattleRecord == null) {
                ErrorLogUtil.errorLog("[GVG] 战场结束啦 没有得到结算数据");
                return;
            }

            // 哪来回哪去
            goBackServer(gvgBattleRecord.getVictoryAllianceServerId(), gvgBattleRecord.getVictoryAllianceId());
            goBackServer(gvgBattleRecord.getFailAllianceServerId(), gvgBattleRecord.getFailAllianceId());
        } catch (ExpectedException ignored){

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog(e);
        }
    }

    /**
     * 战场结束，踢人回原服
     * @param targetServerId
     * @param allianceId
     */
    private void goBackServer(int targetServerId, Long allianceId) {
        logger.info("GVG官渡 结算回服开始  服务器 {} 联盟 {}", targetServerId, allianceId);
        GVGAllianceSignUpInfoVo gvgAllianceSignUpInfoVos = gvgBattleDataVoManager.findGvgAllianceSignUpInfoVos(allianceId);
        GVGAllianceLineUpInfo gvgAllianceLineUpInfo = gvgAllianceSignUpInfoVos == null ? null : gvgAllianceSignUpInfoVos.getGvgAllianceLineUpInfo();
        if (gvgAllianceLineUpInfo == null) {
            ErrorLogUtil.errorLog("GVG官渡 结算回服异常。 阵容为空", ErrorLogUtil.ALERT_TYPE_KEY, "gvgBackError", "targetServerId", targetServerId, "allianceId", allianceId);
            return;
        }
        List<Long> signedList = new ArrayList<>();
        signedList.addAll(gvgAllianceLineUpInfo.getTempMemberIds());
        signedList.addAll(gvgAllianceLineUpInfo.getFormalMemberIds());
        int serverId = Application.getServerId();

        for (Long roleId : signedList) {
            RoleServerInfo roleServerInfo = roleServerInfoDao.findRoleServerInfoFromDB(targetServerId, roleId);
            if (roleServerInfo.getMigrateServerId() != serverId) {
                continue;
            }
            try {
                gvgGameService.goBackServer(roleServerInfo);
                logger.info("GVG官渡 结算回服成功 玩家 {} 服务器 {} 联盟 {}", roleId, targetServerId, allianceId);
            } catch (Exception e) {
                ErrorLogUtil.errorLog("GVG官渡 结算回服异常 回不去。", ErrorLogUtil.ALERT_TYPE_KEY, "gvgBackError", "targetServerId", targetServerId, "allianceId", allianceId);
            }
        }
    }

    public GVGBattleRecord getGVGBattleRecord() {
        Collection<GVGBattleRecord> all = gvgBattleRecordDao.findAll();
        List<GVGBattleRecord> collect = all.stream().sorted(Comparator.comparing(GVGBattleRecord::getRound).reversed()).collect(Collectors.toList());
        if (JavaUtils.bool(collect)) {
            return collect.get(0);
        }
        return null;
    }


    //*********** result show start **************

    /**
     * 结算
     */
    private void sendBattleResult(Long victoryAllianceId, RoleGVGBattle mvp, GvgMatchType gvgMatchType) {
        try {
            logger.info("[GVG] battle sendBattleResult start winner {}", victoryAllianceId);
            Collection<AllianceBattlePoint> allianceBattlePoints = allianceBattlePointDao.findAll();
            List<PsGVGAllianceResultInfo> allianceResultInfoList = getGVGAllianceResult(allianceBattlePoints);
            PsGVGMVPResultInfo gVGMVPResult = wrapGVGMVPResult(mvp);

            GcGVGRankMemberInfo gvgRankMemberInfo = getGvgRankMemberInfo();

            Collection<Role> allRole = roleDao.findAll();
            for (Role role : allRole) {
                if (!role.isOnline()) {
                    continue;
                }
                logger.info("[GVG]  battle result role : {}", role.getRoleId());
                PsGVGRoleResultInfo roleResultInfo = wrapGVGRoleResultInfo(role, gvgMatchType);
                sendBattleResultToRole(role, Objects.equals(victoryAllianceId, role.getAllianceId()) ? 0 : 1, allianceResultInfoList, gVGMVPResult, roleResultInfo);
                if (gvgRankMemberInfo != null) {
                    role.send(gvgRankMemberInfo);
                }
            }
            logger.info("[GVG]  battle sendBattleResult end ");
        } catch (ExpectedException ignored){

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("[GVG] battle sendBattleResult error", e);
        }

    }

    public void sendBattleResultGmTest(Long victoryAllianceId, RoleGVGBattle mvp, Role role, GvgMatchType gvgMatchType) {
        try {
            logger.info("[GVG] battle sendBattleResult start winner " + victoryAllianceId);
            Collection<AllianceBattlePoint> allianceBattlePoints = allianceBattlePointDao.findAll();
            List<PsGVGAllianceResultInfo> allianceResultInfoList = getGVGAllianceResult(allianceBattlePoints);
            PsGVGMVPResultInfo gVGMVPResult = wrapGVGMVPResult(mvp);

            Collection<Role> allRole = roleDao.findAll();

            if (!role.isOnline()) {
                return;
            }
            logger.info("[GVG] send battle result role : " + role.getRoleId());
            PsGVGRoleResultInfo roleResultInfo = wrapGVGRoleResultInfo(role, gvgMatchType);
            sendBattleResultToRole(role, Objects.equals(victoryAllianceId, role.getAllianceId()) ? 0 : 1, allianceResultInfoList, gVGMVPResult, roleResultInfo);

            logger.info("[GVG] battle sendBattleResult end ");
        } catch (ExpectedException ignored){

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("[GVG] battle sendBattleResult error", e);
        }
    }

    /**
     * 个人的信息
     * @param role
     * @return
     */
    private PsGVGRoleResultInfo wrapGVGRoleResultInfo(Role role, GvgMatchType gvgMatchType) {
        PsGVGRoleResultInfo roleResultInfo = new PsGVGRoleResultInfo();
        RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(role.getId());
        if (roleGVGBattle != null) {
            roleResultInfo.setContributeScore(roleGVGBattle.getContributeAllianceScore());
            roleResultInfo.setEnemyKillCount(roleGVGBattle.getKillEnemyNum());
            roleResultInfo.setPersonalScore(roleGVGBattle.getBattlePoint());
            GVGRoleInfo gvgRoleInfo = gvgRoleInfoDao.findById(role.getId());
            GVGRoleHistoryInfo gvgRoleHistoryInfo = gvgRoleInfo == null ? null : gvgRoleInfo.getGvgRoleHistoryInfo(gvgMatchType);
            int joinCount = gvgRoleHistoryInfo == null ? 1 : gvgRoleHistoryInfo.getJoinCount() + 1;

            // 杀敌数
            long enemyKillTotal = gvgRoleHistoryInfo == null ? roleGVGBattle.getKillEnemyNum() : gvgRoleHistoryInfo.getEnemyKillTotal() + roleGVGBattle.getKillEnemyScore();
            long enemyKillMax = Math.max(gvgRoleHistoryInfo == null ? 0L : gvgRoleHistoryInfo.getEnemyKillMax(), roleGVGBattle.getKillEnemyNum());
            roleResultInfo.setEnemyKillCountMax(enemyKillMax);
            roleResultInfo.setEnemyKillCountAve(enemyKillTotal/joinCount);
            // 联盟贡献
            long contributeAllianceScoreTotal = gvgRoleHistoryInfo == null ?
                    roleGVGBattle.getContributeAllianceScore() : gvgRoleHistoryInfo.getAllianceContributeScoreTotal() + roleGVGBattle.getContributeAllianceScore();
            long contributeAllianceScoreMax = Math.max(gvgRoleHistoryInfo == null ? 0L : gvgRoleHistoryInfo.getAllianceContributeScoreMax(), roleGVGBattle.getContributeAllianceScore());
            roleResultInfo.setContributeScoreAve(contributeAllianceScoreTotal/joinCount);
            roleResultInfo.setContributeScoreMax(contributeAllianceScoreMax);
            // 个人积分
            long personScoreTotal = gvgRoleHistoryInfo == null ?
                    roleGVGBattle.getBattlePoint() : gvgRoleHistoryInfo.getPersonalScoreTotal() + roleGVGBattle.getBattlePoint();
            long personScoreMax = Math.max(gvgRoleHistoryInfo == null ? 0L : gvgRoleHistoryInfo.getPersonalScoreMax(), roleGVGBattle.getBattlePoint());
            roleResultInfo.setPersonalScoreAve(personScoreTotal/joinCount);
            roleResultInfo.setPersonalScoreMax(personScoreMax);
        }
        return roleResultInfo;
    }

    /**
     * 发送单个的结果
     * @param role
     * @param result
     * @param allianceResultInfoList
     * @param gVGMVPResult
     */
    private void sendBattleResultToRole(Role role, int result, List<PsGVGAllianceResultInfo> allianceResultInfoList, PsGVGMVPResultInfo gVGMVPResult, PsGVGRoleResultInfo roleResultInfo) {
        GcGVGFinalResultShow showResult = new GcGVGFinalResultShow();
        showResult.setAllianceId(String.valueOf(role.getAllianceId()));
        showResult.setResult(result);
        showResult.setAllianceResults(allianceResultInfoList);
        showResult.setMvpResult(gVGMVPResult);
        showResult.setRoleResult(roleResultInfo);
        role.send(showResult);
    }


    /**
     * mvp结算信息
     */
    private PsGVGMVPResultInfo wrapGVGMVPResult(RoleGVGBattle mvp) {
        PsGVGMVPResultInfo result = new PsGVGMVPResultInfo();
        if (mvp != null) {
            result.setRoleId(mvp.getRoleId());
            result.setName(mvp.getName());
            result.setHead(mvp.getHead());
            Role role = roleDao.findById(mvp.getRoleId());
            if (role != null) {
                result.setLevel(role.getLevel());
                result.setServerId(role.getoServerId());
                result.setNewFashionId(skinManager.getCityActivatedSkinId(role, SkinFashionType.CHENG_BAO));
                result.setNameBrandFashionId(skinManager.getCityActivatedSkinId(role, SkinFashionType.MING_PAI));
            }
            result.setContributeScore(mvp.getContributeAllianceScore());
            result.setPersonalScore(mvp.getBattlePoint());
            result.setEnemyKillCount(mvp.getKillEnemyNum());
            result.setAllianceName(mvp.getAllianceName());
            result.setFrameId(mvp.getHeadFrame());
            result.setAllianceAlias(mvp.getAllianceAliasName());
        } else {
            ErrorLogUtil.errorLog("[GVG] Result mvp is null");
        }
        return result;
    }

    private List<PsGVGAllianceResultInfo> getGVGAllianceResult(Collection<AllianceBattlePoint> allianceBattlePoints) {
        List<PsGVGAllianceResultInfo> allianceResultInfoList = new ArrayList<>();
        for (AllianceBattlePoint allianceBattlePoint : allianceBattlePoints) {
            allianceResultInfoList.add(wrapPsGVGAllianceResultInfo(allianceBattlePoint));
        }
        return allianceResultInfoList;
    }

    /**
     * 联盟结算信息
     * @param allianceBattlePoint
     * @return
     */
    private PsGVGAllianceResultInfo wrapPsGVGAllianceResultInfo(AllianceBattlePoint allianceBattlePoint) {
        PsGVGAllianceResultInfo allianceResult = new PsGVGAllianceResultInfo();
        allianceResult.setAllianceId(allianceBattlePoint.getPersistKey().toString());

        GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo = gvgBattleDataVoManager.findGvgBattleServerDispatchRecordVo();
        int server = Application.getServerId();
        if (Objects.equals(allianceBattlePoint.getPersistKey(), gvgBattleServerDispatchRecordVo.getAllianceId1())) {
            server = gvgBattleServerDispatchRecordVo.getAlliance1ServerId();
        } else if (Objects.equals(allianceBattlePoint.getPersistKey(), gvgBattleServerDispatchRecordVo.getAllianceId2())) {
            server = gvgBattleServerDispatchRecordVo.getAlliance2ServerId();
        }
        allianceResult.setServerId(server);

        Alliance alliance = allianceDao.findById(Long.valueOf(allianceResult.getAllianceId()));
        if (alliance != null) {
            allianceResult.setAllianceName(alliance.getName());
            allianceResult.setAllianceAlias(alliance.getAliasName());
            allianceResult.setAllianceFlag(alliance.toPsAllianceFlagInfo());
        }
        allianceResult.setResult(allianceBattlePoint.getResult());

        allianceResult.setTotalScore(allianceBattlePoint.getValue());
        allianceResult.setWuChaoScore(allianceBattlePoint.getWuchaoScore());
        allianceResult.setGatherScore(allianceBattlePoint.getGatherScore());
        allianceResult.setGuanDuTime(allianceBattlePoint.getGuanduOccupyTime());
        allianceResult.setKillEnemyTotal(allianceBattlePoint.getKillEnemyCount());
        allianceResult.setBuildingScore(allianceBattlePoint.getBuildingScore());

        allianceResult.setMemberCount(allianceMemberDao.findMemberNumByAllianceId(allianceBattlePoint.getPersistKey()));
        try {
            biLogUtil.gvgAllianceResult(allianceResult.getAllianceId(),allianceResult.getTotalScore(), allianceResult.getWuChaoScore(), allianceResult.getBuildingScore(), allianceResult.getGatherScore(), allianceResult.getKillEnemyTotal());
        } catch (Exception e) {

        }

        return allianceResult;
    }


    //*********** result show end **************


    /**
     * GVG 迁城
     * @param role
     * @return
     */
    public boolean isGVGFreeMove(Role role) {
        RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(role.getId());
        if (roleGVGBattle == null) {
            ErrorLogUtil.errorLog("[GVG] isGVGFreeMove roleGVGBattle is null", "roleId",role.getId());
            return false;
        }

        long now = TimeUtil.getNow();
        return roleGVGBattle.getFreeMoveCityTime() == 0 ||
                now >= roleGVGBattle.getFreeMoveCityTime();
    }

    /**
     * gvg 迁城
     * @param role
     */
    public boolean gvgMoveCity(Role role) {
        if (!Application.isGVGBattleServer()) {
            return false;
        }
        RoleGVGBattle roleGVGBattle = roleGVGBattleDao.findById(role.getId());
        if (roleGVGBattle == null) {
            return false;
        }

        int diamondCost = 0;
        int itemCost = 0;
        boolean gvgFreeMove = isGVGFreeMove(role);
        if (gvgFreeMove) {
            roleGVGBattle.setFreeMoveCityTime(TimeUtil.getNow() + configService.getConfig(GvgSettingConfig.class).getFreeMoveCityTime() * TimeUtil.SECONDS_MILLIS);
            roleGVGBattle.setMoveCityItemCostCount(1);
            roleGVGBattleDao.save(roleGVGBattle);
        } else {
            /***
             * 消耗道具
             */
            int moveCityItemCostCount = roleGVGBattle.getMoveCityItemCostCount();

            ItemConfig itemCfg = configService.getConfig(ItemConfig.class);
            SettingConfig settingCfg = configService.getConfig(SettingConfig.class);

            ItemConfig.ItemMeta moveItem = itemCfg.get(settingCfg.getDirectMoveCityItem());
            if (moveItem == null) {
                ErrorLogUtil.errorLog("[GVG] move city item not find,please check ItemConfig");
                return false;
            }
            int itemAmount = roleItemManager.getItemAmount(role, moveItem.getId());
            if (itemAmount >= moveCityItemCostCount) {
                itemCost = moveCityItemCostCount;
                // 道具够
                roleItemManager.removeItem(role, moveItem, itemCost, LogReasons.ItemLogReason.MOVE_CITY);
            } else {
                // 资源补
                int needBuyCount = moveCityItemCostCount - itemAmount;
                diamondCost = Math.max(1, settingCfg.getSeniorMoveCityCost()) * needBuyCount; // 最小 1
                if (!roleCurrencyManager.checkAndCost(role, Currency.DIAMOND, diamondCost, LogReasons.MoneyLogReason.CITY_MOVE)) {
                    ErrorLogUtil.errorLog("[GVG] 迁城金币不足", "roleId",role.getId());
                    return false;
                }
                // 再扣道具
                if (itemAmount > 0) {
                    itemCost = itemAmount;
                    roleItemManager.removeItem(role, moveItem, itemCost, LogReasons.ItemLogReason.MOVE_CITY);
                }
            }

            roleGVGBattle.setMoveCityItemCostCount(roleGVGBattle.getMoveCityItemCostCount() + 1);
            roleGVGBattleDao.save(roleGVGBattle);

        }
        try {
            biLogUtil.gvgMoveCity(role, itemCost, diamondCost);
        } catch (Exception e) {
            ErrorLogUtil.errorLog("[GVG] gvgMoveCity error", e);
        }

        return true;
    }



    public void getHistoryInfo(Role role, GvgMatchType matchType) {
        GcGVGHistoryInfo msg = new GcGVGHistoryInfo();
        msg.setMatchType(matchType.getPsState());

        if (role == null || !JavaUtils.bool(role.getAllianceId() )) {
            var roleId = role != null ? role.getRoleId(): -1;
            var allianceId = (role != null && role.getAllianceId() != null) ? role.getAllianceId() : -1;
            ErrorLogUtil.warnLog("[GVG] getHistoryInfo summaryNull1", "role", roleId, "alliance", allianceId, "matchType", matchType);
            return;
        }

        var gvgAllianceBattleSummary = gvgAllianceBattleSummaryDao.findById(role.getAllianceId());
        if (gvgAllianceBattleSummary == null) {
            ErrorLogUtil.warnLog("[GVG] getHistoryInfo summaryNull1", "role", role.getRoleId(), "alliace", role.getAllianceId(), "matchType", matchType);
            role.send(msg);
            return;
        }

        var summary = gvgAllianceBattleSummary.getBattleSummary(matchType);
        if(summary == null){
            ErrorLogUtil.warnLog("[GVG] getHistoryInfo summaryNull2", "role", role.getRoleId(), "alliace", role.getAllianceId(), "matchType", matchType);
            role.send(msg);
            return;
        }

        List<RoleGVGScoreInfoVo> victoryRoleGvgInfo = summary.getVictoryRoleGvgInfo();
        if (victoryRoleGvgInfo != null) {
            victoryRoleGvgInfo.forEach(value -> putHistoryInfo(msg, value, true));
        }

        List<RoleGVGScoreInfoVo> failRoleGvgInfo = summary.getFailRoleGvgInfo();
        if (failRoleGvgInfo != null) {
            failRoleGvgInfo.forEach(value -> putHistoryInfo(msg, value, false));
        }

        role.send(msg);
    }

    private void putHistoryInfo(GcGVGHistoryInfo msg, RoleGVGScoreInfoVo value, boolean isWin) {
        if (value == null) {
            return;
        }

        PsGvgRankMember member = putGVGScoreInfo(value);
        if (isWin) {
            msg.addToAlliance1RankInfo(member);
        } else {
            msg.addToAlliance2RankInfo(member);
        }
    }


    /**
     * 积分排行
     * @param role
     */
    public void getRankMemberInfo(Role role) {
        GcGVGRankMemberInfo msg = getGvgRankMemberInfo();
        if (msg == null) return;

        role.send(msg);
    }

    public GcGVGRankMemberInfo getGvgRankMemberInfo() {
        GvgBattleServerDispatchRecordVo dispatchRecordInfo = gvgBattleDataVoManager.getDispatchRecordInfo();
        if (dispatchRecordInfo == null) {
            ErrorLogUtil.errorLog("[GVG] getRankMemberInfo dispatchRecordInfo is null");
            return null;
        }

        Long allianceId1 = dispatchRecordInfo.getAllianceId1();
        Long allianceId2 = dispatchRecordInfo.getAllianceId2();
        GcGVGRankMemberInfo msg = new GcGVGRankMemberInfo();
        Collection<RoleGVGBattle> roleGVGBattles = roleGVGBattleDao.findAll();
        if (JavaUtils.bool(roleGVGBattles)) {
            long now = TimeUtil.getNow();
            for (RoleGVGBattle roleGVGBattle : roleGVGBattles) {

                RoleServerInfo roleServerInfo = roleServerInfoDao.findById(roleGVGBattle.getPersistKey());
                if(roleServerInfo != null) {
                    if (roleServerInfo.getGvgSide() == 1) continue;
                }

                if (allianceMemberDao.findById(roleGVGBattle.getRoleId()) == null) {
                    continue;
                }

                Role memberRole = roleDao.findById(roleGVGBattle.getRoleId());
                if (memberRole == null) {
                    continue;
                }

                PsGvgRankMember member = putGVGScoreInfo(roleGVGBattle);
                calBattlePointWithOutSave(roleGVGBattle, now, member);

                if (allianceId1 != null && roleGVGBattle.getAllianceId().equals(allianceId1)) {
                    msg.addToAlliance1RankInfo(member);
                } else if (allianceId2 != null && roleGVGBattle.getAllianceId().equals(allianceId2)) {
                    msg.addToAlliance2RankInfo(member);
                }
            }
        }
        return msg;
    }

    private static void calBattlePointWithOutSave(RoleGVGBattle roleGVGBattle, long now, PsGvgRankMember member) {
        int speed = roleGVGBattle.getSpeed();
        if (speed > 0) {
            // 上次结算时间
            long lastCalTime = roleGVGBattle.getLastCalTime();
            // 当前积累值
            int value = roleGVGBattle.getBattlePoint();
            // 本次理论应该增加的值
            int theoryValue = (int) ((now - lastCalTime) / TimeUtil.SECONDS_MILLIS * speed);
            member.setPoint(theoryValue + value);
        }
    }

    private PsGvgRankMember putGVGScoreInfo(RoleGVGBattle roleGVGBattle) {
        PsGvgRankMember member = new PsGvgRankMember();
        member.setRoleId(roleGVGBattle.getRoleId());
        member.setAllianceId(roleGVGBattle.getAllianceId() == null ? "" : roleGVGBattle.getAllianceId().toString());

        member.setSpeed(roleGVGBattle.getSpeed());
        member.setLastCalTime(roleGVGBattle.getLastCalTime());
        member.setPoint(roleGVGBattle.getBattlePoint());
        member.setName(roleGVGBattle.getName());
        member.setHead(roleGVGBattle.getHead());

        PsRoleInfo psRoleInfo = new PsRoleInfo();
        psRoleInfo.setName(roleGVGBattle.getName());
        psRoleInfo.setAllianceName(roleGVGBattle.getAllianceAliasName());
        psRoleInfo.setAllianceName(roleGVGBattle.getAllianceName());
        psRoleInfo.setHead(roleGVGBattle.getHead());
        psRoleInfo.setHeadFrame(roleGVGBattle.getHeadFrame());
        psRoleInfo.setServerId(roleGVGBattle.getServerId());
        member.setRoleInfo(psRoleInfo);

        return member;
    }

    public PsGvgRankMember putGVGScoreInfo(RoleGVGScoreInfoVo roleGVGScoreInfo) {
        PsGvgRankMember member = new PsGvgRankMember();
        member.setRoleId(roleGVGScoreInfo.getRoleId());
        member.setAllianceId(roleGVGScoreInfo.getAllianceId() == null ? "" : roleGVGScoreInfo.getAllianceId().toString());
        member.setSpeed(0);
        member.setLastCalTime(0L);
        member.setPoint(roleGVGScoreInfo.getPoint());
        member.setName(roleGVGScoreInfo.getName());
        member.setHead(roleGVGScoreInfo.getHead());

        PsRoleInfo psRoleInfo = new PsRoleInfo();
        psRoleInfo.setName(roleGVGScoreInfo.getName());
        psRoleInfo.setAllianceName(roleGVGScoreInfo.getAllianceAliasName());
        psRoleInfo.setAllianceName(roleGVGScoreInfo.getAllianceName());
        psRoleInfo.setHead(roleGVGScoreInfo.getHead());
        psRoleInfo.setHeadFrame(roleGVGScoreInfo.getHeadFrame());
        psRoleInfo.setServerId(roleGVGScoreInfo.getServerId());

        member.setRoleInfo(psRoleInfo);

        return member;
    }

    public boolean itemUseCheck(Role role, ItemConfig.ItemMeta itemMeta) {
        if (role == null || itemMeta == null) {
            return false;
        }
        if (gvgBan(itemMeta.getId())) {
            role.send(new ToClientException(PsErrorCode.GVG_ITE_BAN, "[GVG] gvg item ban " + (itemMeta == null ? "" : itemMeta.getId())).toClientMsg());
            return false;
        }
        return true;
    }

    /**
     * 战场服屏蔽
     * @return
     */
    public boolean gvgBan(String itemId) {
        if (!JavaUtils.bool(itemId)) {
            return false;
        }
        if (!Application.isGVGBattleServer()) {
            return false;
        }
        Set<String> gvgbanItem = configService.getConfig(GvgSettingConfig.class).getGvgbanItem();
        return gvgbanItem != null && gvgbanItem.contains(itemId);
    }

    public boolean isGvgStopAndGoBack() {
        return gvgStopAndGoBack;
    }

    /**
     *
     * @param role
     * @return
     */
    public int getBattleServerSeason(Role role) {
        try {
            if (role == null) {
                return 1;
            }
            KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getKvkSeasonServerGroupConfigByOServerId(role.getoServerId(), System.currentTimeMillis());
            if (kvkSeasonServerGroupConfig != null) {
                return kvkSeasonServerGroupConfig.getSeason();
            }
        } catch (ExpectedException ignored){

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("[GVG] getBattleServerSeason error", e);
        }
        return 1;
    }

    public boolean isGVGBattleEnd() {
        // 战斗结束后，暂用它来做标记
        return gvgBattleServerLock;
    }

    public List<Object> hotFixBackFunc(List<Object> paramList) {
        return null;
    }

    public List<Object> hotFixBackFunc2(List<Object> paramList) {
        return null;
    }

    public void loadAllianceData(Long allianceId, int allianceServerId) {
        if (!JavaUtils.bool(allianceId) || allianceId == 0) {
            ErrorLogUtil.errorLog("官渡加载战场信息错误", "allianceId", allianceId, "allianceServerId", allianceServerId);
            return;
        }

        // 如果是GVG战斗服要加载玩家联盟
        ServerType serverType = Application.getServerType();
        try {
            if (serverType == ServerType.GVG_BATTLE) {
                if (JavaUtils.bool(allianceId)) {
                    Alliance alliance = allianceDao.findById(allianceId);
                    AllianceTechDao allianceTechDao = Application.getBean(AllianceTechDao.class);
                    if (alliance == null) {
                        allianceDao.loadAllianceFromOtherDB(allianceServerId, allianceId);
                        allianceTechDao.loadAllianceTechFromOtherDB(allianceServerId, Arrays.asList(new Long[]{allianceId}));
                    }

                    AllianceSettingDao allianceSettingDao = Application.getBean(AllianceSettingDao.class);
                    AllianceSetting allianceSetting = allianceSettingDao.findById(allianceId);
                    if (allianceSetting == null) {
                        allianceSettingDao.loadAllianceFromOtherDB(allianceServerId, allianceId);
                    }
                } else {
                    ErrorLogUtil.errorLog("玩家进入战斗服没有联盟", "allainceId",allianceId);
                }
            }
        } catch (Exception e) {
            ErrorLogUtil.errorLog("官渡加载战场信息报错", e,"allianceId ",allianceId);
        }

    }
}
