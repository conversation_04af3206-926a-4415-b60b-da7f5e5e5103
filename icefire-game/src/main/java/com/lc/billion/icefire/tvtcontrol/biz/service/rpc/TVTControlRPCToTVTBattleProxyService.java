package com.lc.billion.icefire.tvtcontrol.biz.service.rpc;

import java.util.*;

import com.lc.billion.icefire.rpc.service.tvt.ITvtControlRemoteTvtBattleService;
import org.springframework.stereotype.Service;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.service.AbstractRPCProxyService;
import com.longtech.cod.rpc.client.RpcClient;
import com.longtech.cod.rpc.client.RpcProxyBuilder;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.GameServerConfig;
import com.simfun.sgf.utils.JavaUtils;

/**
 * control和battle之间的连接
 *
 */
@Service
public class TVTControlRPCToTVTBattleProxyService extends AbstractRPCProxyService {

	private Map<Integer, RpcProxyBean<ITvtControlRemoteTvtBattleService>> migrateRemoteTVTBattleServices = new HashMap<>();

	@Override
	protected ServerType[] getSrcServerType() {
		return new ServerType[] { ServerType.TVT_CONTROL };
	}

	@Override
	protected ServerType[] getTargetServerType() {
		return new ServerType[] { ServerType.TVT_BATTLE };
	}

	@Override
	protected boolean createRPCClient(GameServerConfig gameServerConfig) {
		RpcProxyBuilder<ITvtControlRemoteTvtBattleService> rpcProxyBuilder = RpcProxyBuilder.create(ITvtControlRemoteTvtBattleService.class).connect(getSerializer(),
				gameServerConfig.getRpcIp(), gameServerConfig.getRpcPort());
		RpcClient rpcClient = rpcProxyBuilder.createRpcClient();
		ITvtControlRemoteTvtBattleService service = rpcProxyBuilder.buildSync(rpcClient, getTimeOutMills(), getRetryTimes(), createWait());

		RpcProxyBean<ITvtControlRemoteTvtBattleService> rpcProxyBean = new RpcProxyBean<ITvtControlRemoteTvtBattleService>(service, rpcClient);
		migrateRemoteTVTBattleServices.put(gameServerConfig.getGameServerId(), rpcProxyBean);
		logger.info("rpc TVT_CONTROL to TVT_BATTLE {}->{},{}:{}", Application.getServerId(), gameServerConfig.getGameServerId(), gameServerConfig.getRpcIp(),
				gameServerConfig.getRpcPort());
		return true;
	}

	@Override
	protected void rpcIpChanged(GameServerConfig gameServerConfig) {
		RpcProxyBean<ITvtControlRemoteTvtBattleService> rpcProxyBean = migrateRemoteTVTBattleServices.get(gameServerConfig.getGameServerId());
		if (rpcProxyBean != null) {
			RpcClient rpcClient = rpcProxyBean.getRpcClient();
			rpcClient.setStop(true);
		}
		logger.info("rpcIpChanged {}", gameServerConfig.getGameServerId());
		createRPCClient(gameServerConfig);
	}

	@Override
	protected void rpcPortChanged(GameServerConfig gameServerConfig) {
		logger.info("rpcPortChanged {}", gameServerConfig.getGameServerId());
		rpcIpChanged(gameServerConfig);
	}

	@Override
	protected void removeRPCClient(int serverId) {
		RpcProxyBean<ITvtControlRemoteTvtBattleService> rpcProxyBean = migrateRemoteTVTBattleServices.remove(serverId);
		if (rpcProxyBean != null) {
			RpcClient rpcClient = rpcProxyBean.getRpcClient();
			rpcClient.setStop(true);
		}
	}

	@Override
	protected boolean containsRPCClient(int serverId) {
		return migrateRemoteTVTBattleServices.containsKey(serverId);
	}

	@Override
	protected boolean checkInstance() {
		return true;
	}

	@Override
	protected boolean createWait() {
		return false;
	}

	public ITvtControlRemoteTvtBattleService getTVTControlRemoteTVTBattleService(int serverId) {
		RpcProxyBean<ITvtControlRemoteTvtBattleService> rpcProxyBean = migrateRemoteTVTBattleServices.get(serverId);
		if (rpcProxyBean == null) {
			return null;
		}
		return rpcProxyBean.getProxy();
	}

	public Collection<ITvtControlRemoteTvtBattleService> getTVTControlRemoteTVTBattleServices() {
		if (JavaUtils.bool(migrateRemoteTVTBattleServices)) {
			Collection<RpcProxyBean<ITvtControlRemoteTvtBattleService>> values = migrateRemoteTVTBattleServices.values();
			if (JavaUtils.bool(values)) {
				List<ITvtControlRemoteTvtBattleService> ret = new ArrayList<>();
				values.forEach(bean -> ret.add(bean.getProxy()));
				return ret;
			}
		}
		return Collections.emptyList();
	}
}
