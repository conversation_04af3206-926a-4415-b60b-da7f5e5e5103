package com.lc.billion.icefire.tvtcontrol.biz.service.rpc.impl;

import java.util.ArrayList;
import java.util.List;

import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.rpc.service.tvt.ITvtBattleRemoteTvtControlService;
import com.lc.billion.icefire.rpc.vo.gvg.GVGAllianceSignUpInfoVo;
import com.lc.billion.icefire.rpc.vo.gvg.GvgBattleServerDispatchRecordVo;
import com.lc.billion.icefire.tvtcontrol.biz.dao.mongo.root.TvtBattleServerDispatchRecordDao;
import com.lc.billion.icefire.tvtcontrol.biz.model.TvtBattleServerDispatchRecord;
import com.lc.billion.icefire.tvtcontrol.biz.service.TvtControlService;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGBattleRecordVo;

/**
 * TVT战斗服-->远程调用-->TVT中控服--中控服的接口
 *
 */
@Service
public class TVTBattleRemoteTVTControlServiceImpl implements ITvtBattleRemoteTvtControlService {

	private static final Logger logger = LoggerFactory.getLogger(TVTBattleRemoteTVTControlServiceImpl.class);

	@Autowired
	private TvtControlService tvtControlService;
	@Autowired
	private ActivityDao activityDao;
	@Autowired
	private TvtBattleServerDispatchRecordDao tvtBattleServerDispatchRecordDao;

	@Override
	public GvgBattleServerDispatchRecordVo findTVTBattleServerDispatchRecord(int battleServerId) {
		logger.info("战斗服主动拉取对阵信息start，{}", battleServerId);
		// 把中控服对应数据转换为GvgBattleServerDispatchRecordVo，对TVT战斗服透明
		TvtBattleServerDispatchRecord tvtBattleServerDispatchRecord = tvtBattleServerDispatchRecordDao.findByBattleServerId(String.valueOf(battleServerId));
		if(tvtBattleServerDispatchRecord != null){
			return toGVGBattleServerDispatchRecordVo(tvtBattleServerDispatchRecord);
		}

		logger.info("信息空");
		return null;
	}

	@Override
	public String uploadTVTBattleRecord(GVGBattleRecordVo gvgBattleRecordVo) {
		tvtControlService.uploadTVTBattleRecord(gvgBattleRecordVo);
		return null;
	}

	@Override
	public ActivityVo findActivityVo() {
		Activity activity = activityDao.findActivityByActivityType(ActivityType.TVT);
		if (activity == null) {
			return null;
		}

		return new ActivityVo(activity);
	}

	@Override
	public GvgBattleServerDispatchRecordVo registerTVTBattleServerToTVTControlServer(int serverId) {
		// 把中控服对应数据转换为GvgBattleServerDispatchRecordVo，对TVT战斗服透明
		TvtBattleServerDispatchRecord tvtBattleServerDispatchRecord = tvtControlService.updateAndBroadcastTvtBattleServerDispatchRecord(serverId);
		if(tvtBattleServerDispatchRecord != null){
			return toGVGBattleServerDispatchRecordVo(tvtBattleServerDispatchRecord);
		}

		logger.info("注册TVT战斗服到中控 Dispatch信息为空");
		return null;
	}

	@Override
	public List<GVGAllianceSignUpInfoVo> findTVTAllianceSignUpInfoVo(int battleServerId) {
		TvtBattleServerDispatchRecord tvtBattleServerDispatchRecord = tvtBattleServerDispatchRecordDao.findByBattleServerId(String.valueOf(battleServerId));
		if(tvtBattleServerDispatchRecord != null){
			return toGVGAllianceSignUpInfoVos(tvtBattleServerDispatchRecord);
		}else {
			ErrorLogUtil.errorLog("战斗服未分配对阵信息就启动了","battleServerId",battleServerId);
		}

		return new ArrayList<>();
	}

	@Override
	public void noticeDestroyBattleServer(int battleServerId) {
		tvtControlService.updateTVTBattleServerDispatchRecord(battleServerId);
	}

	private GvgBattleServerDispatchRecordVo toGVGBattleServerDispatchRecordVo(TvtBattleServerDispatchRecord tvtBattleServerDispatchRecord){
		GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo = new GvgBattleServerDispatchRecordVo();
		gvgBattleServerDispatchRecordVo.setBattleServerId(tvtBattleServerDispatchRecord.getBattleServerId());
		gvgBattleServerDispatchRecordVo.setBattleStartTime(tvtBattleServerDispatchRecord.getBattleStartTime());
		gvgBattleServerDispatchRecordVo.setBattleDestroyTime(tvtBattleServerDispatchRecord.getBattleDestroyTime());
		gvgBattleServerDispatchRecordVo.setStatus(tvtBattleServerDispatchRecord.getStatus());
		gvgBattleServerDispatchRecordVo.setBattleTurn(tvtBattleServerDispatchRecord.getBattleTurn());

		return gvgBattleServerDispatchRecordVo;
	}

	private List<GVGAllianceSignUpInfoVo> toGVGAllianceSignUpInfoVos(TvtBattleServerDispatchRecord tvtBattleServerDispatchRecord){
		List<GVGAllianceSignUpInfoVo> gvgAllianceSignUpInfoVos = new ArrayList<>();
		if(JavaUtils.bool(tvtBattleServerDispatchRecord.getRedLineup())){
			GVGAllianceSignUpInfoVo gvgAllianceSignUpInfoVo = new GVGAllianceSignUpInfoVo();
			List<Long> formalMemberIds = new ArrayList<>();
			tvtBattleServerDispatchRecord.getRedLineup().forEach( redMember -> formalMemberIds.add(redMember.getRoleId()));
			gvgAllianceSignUpInfoVo.getGvgAllianceLineUpInfo().setFormalMemberIds(formalMemberIds);
			gvgAllianceSignUpInfoVo.setTeamId(1);
			gvgAllianceSignUpInfoVos.add(gvgAllianceSignUpInfoVo);
		}

		if(JavaUtils.bool(tvtBattleServerDispatchRecord.getBlueLineup())){
			GVGAllianceSignUpInfoVo gvgAllianceSignUpInfoVo = new GVGAllianceSignUpInfoVo();
			List<Long> formalMemberIds = new ArrayList<>();
			tvtBattleServerDispatchRecord.getBlueLineup().forEach( blueMember -> formalMemberIds.add(blueMember.getRoleId()));
			gvgAllianceSignUpInfoVo.getGvgAllianceLineUpInfo().setFormalMemberIds(formalMemberIds);
			gvgAllianceSignUpInfoVo.setTeamId(2);
			gvgAllianceSignUpInfoVos.add(gvgAllianceSignUpInfoVo);
		}

		return gvgAllianceSignUpInfoVos;
	}
}
