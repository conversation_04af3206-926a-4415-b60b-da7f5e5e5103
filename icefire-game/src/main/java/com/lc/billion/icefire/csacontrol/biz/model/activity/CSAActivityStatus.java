package com.lc.billion.icefire.csacontrol.biz.model.activity;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.csabattle.biz.config.CrossSeverAttackSetting;
import com.lc.billion.icefire.csacontrol.biz.config.CrossSeverAttackMatchConfig;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.service.impl.csattack.ICrossServerAttackActivityStatusListener;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.protocol.constant.PsCSAActivityStatus;
import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

public enum CSAActivityStatus implements IntEnum {

    /**
     * 预览阶段
     */
    PREHEAT(PsCSAActivityStatus.NONE, PsCSAActivityStatus.WARMUP, false, 0, 0){
        @Override
        public long currStageContinueTime() {
            return 0;
        }

        @Override
        public List<Method> getEventList(CSAActivityGroupContext csaActivityGroupContext) {
            List<Method> listenList = new ArrayList<>();
            try {
                Method csaActivityStart = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivityStart", CSAActivityGroupContext.class);
                listenList.add(csaActivityStart);
                Method csaActivityBattleNoticeEnd = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivityWarmUpEnd", CSAActivityGroupContext.class);
                listenList.add(csaActivityBattleNoticeEnd);
            } catch (NoSuchMethodException e) {
                ErrorLogUtil.exceptionLog("NoSuchMethod", e,"status",this.getCsaActivityStatus().name());
            }
            return listenList;
        }
    },
    /**
     * 预热阶段
     */
    WARMUP(PsCSAActivityStatus.WARMUP, PsCSAActivityStatus.CHOOSE_TIME, false, 0, 1) {
        @Override
        public long currStageContinueTime() {
            ConfigServiceImpl configService = Application.getBean(ConfigServiceImpl.class);
            return configService.getConfig(CrossSeverAttackSetting.class).getCsaPreviewPhaseTime() * TimeUtil.SECONDS_MILLIS;
        }

        @Override
        public List<Method> getEventList(CSAActivityGroupContext csaActivityGroupContext) {
            List<Method> listenList = new ArrayList<>();
            try {
                Method csaActivityStart = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivityStart", CSAActivityGroupContext.class);
                listenList.add(csaActivityStart);
                Method csaActivityBattleNoticeEnd = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivityWarmUpEnd", CSAActivityGroupContext.class);
                listenList.add(csaActivityBattleNoticeEnd);
            } catch (NoSuchMethodException e) {
                ErrorLogUtil.exceptionLog("NoSuchMethod", e,"status",this.getCsaActivityStatus().name());
            }
            return listenList;
        }
    },
    /**
     * 选时间阶段
     */
    CHOOSE_TIME(PsCSAActivityStatus.CHOOSE_TIME, PsCSAActivityStatus.BATTLE_NOTICE, false, 0, 1) {
        @Override
        public long currStageContinueTime() {
            ConfigServiceImpl configService = Application.getBean(ConfigServiceImpl.class);
            return configService.getConfig(CrossSeverAttackSetting.class).getCsaSelectPhaseTime() * TimeUtil.SECONDS_MILLIS;
        }

        @Override
        public List<Method> getEventList(CSAActivityGroupContext csaActivityGroupContext) {
            List<Method> listenList = new ArrayList<>();
            try {
                Method csaActivityChooseTimeStart = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivityChooseTimeStart", CSAActivityGroupContext.class);
                listenList.add(csaActivityChooseTimeStart);
                Method csaActivityChooseTimeEnd = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivityChooseTimeEnd", CSAActivityGroupContext.class);
                listenList.add(csaActivityChooseTimeEnd);
            } catch (NoSuchMethodException e) {
                ErrorLogUtil.exceptionLog("NoSuchMethod", e,"status",this.getCsaActivityStatus().name());
            }
            return listenList;
        }
    },
    /**
     * 战斗前公告
     */
    BATTLE_NOTICE(PsCSAActivityStatus.BATTLE_NOTICE, PsCSAActivityStatus.BATTLE_PREPARE_FIRST_HALF, false, 0, 1) {
        @Override
        public long currStageContinueTime() {
            throw new AlertException("this stage continue time is invalid","stage",this.name());
        }

        @Override
        public List<Method> getEventList(CSAActivityGroupContext csaActivityGroupContext) {
            List<Method> listenList = new ArrayList<>();
            try {
                Method csaActivityBattleNoticeStart = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivityBattleNoticeStart", CSAActivityGroupContext.class);
                listenList.add(csaActivityBattleNoticeStart);
                Method csaActivityBattleNoticeEnd = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivityBattleNoticeEnd", CSAActivityGroupContext.class);
                listenList.add(csaActivityBattleNoticeEnd);
            } catch (NoSuchMethodException e) {
                ErrorLogUtil.exceptionLog("NoSuchMethod", e,"status",this.getCsaActivityStatus().name());
            }
            return listenList;
        }
    },
    /**
     * 上半场战斗 开始前阶段
     */
    BATTLE_PREPARE_FIRST_HALF(PsCSAActivityStatus.BATTLE_PREPARE_FIRST_HALF, PsCSAActivityStatus.BATTLE_FIRST_HALF, true, 1, 1) {
        @Override
        public long currStageContinueTime() {
            ConfigServiceImpl configService = Application.getBean(ConfigServiceImpl.class);
            return configService.getConfig(CrossSeverAttackSetting.class).getBattlePhaseFirstHalfPreparingTime() * TimeUtil.SECONDS_MILLIS;
        }

        @Override
        public List<Method> getEventList(CSAActivityGroupContext csaActivityGroupContext) {
            List<Method> listenList = new ArrayList<>();
            try {
                Method csaActivityFirstHalfStart = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivityFirstHalfStart", CSAActivityGroupContext.class);
                listenList.add(csaActivityFirstHalfStart);
                Method csaActivityFirstHalfPrepareEnd = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivityFirstHalfPrepareEnd", CSAActivityGroupContext.class);
                listenList.add(csaActivityFirstHalfPrepareEnd);
            } catch (NoSuchMethodException e) {
                ErrorLogUtil.exceptionLog("NoSuchMethod", e,"status",this.getCsaActivityStatus().name());
            }
            return listenList;
        }
    },
    /**
     * 上半场战斗 战斗阶段
     */
    BATTLE_FIRST_HALF(PsCSAActivityStatus.BATTLE_FIRST_HALF, PsCSAActivityStatus.BATTLE_FIRST_SETTLEMENT, true, 1, 1) {
        @Override
        public long currStageContinueTime() {
            ConfigServiceImpl configService = Application.getBean(ConfigServiceImpl.class);
            return configService.getConfig(CrossSeverAttackSetting.class).getBattlePhaseFirstHalfBattleTime() * TimeUtil.SECONDS_MILLIS;
        }

        @Override
        public List<Method> getEventList(CSAActivityGroupContext csaActivityGroupContext) {
            List<Method> listenList = new ArrayList<>();
            try {
                Method csaActivityFirstHalfBattleStart = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivityFirstHalfBattleStart", CSAActivityGroupContext.class);
                listenList.add(csaActivityFirstHalfBattleStart);
                Method csaActivityFirstHalfBattleEnd = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivityFirstHalfBattleEnd", CSAActivityGroupContext.class);
                listenList.add(csaActivityFirstHalfBattleEnd);
            } catch (NoSuchMethodException e) {
                ErrorLogUtil.exceptionLog("NoSuchMethod", e,"status",this.getCsaActivityStatus().name());
            }
            return listenList;
        }
    },
    /**
     * 上半场战斗 结算阶段
     */
    BATTLE_FIRST_SETTLEMENT(PsCSAActivityStatus.BATTLE_FIRST_SETTLEMENT, PsCSAActivityStatus.BATTLE_REST, true, 1, 1) {
        @Override
        public long currStageContinueTime() {
            ConfigServiceImpl configService = Application.getBean(ConfigServiceImpl.class);
            return configService.getConfig(CrossSeverAttackSetting.class).getCsaFirstHalfResultTime() * TimeUtil.SECONDS_MILLIS;
        }

        @Override
        public List<Method> getEventList(CSAActivityGroupContext csaActivityGroupContext) {
            List<Method> listenList = new ArrayList<>();
            try {
                Method csaActivityFirstHalfSettlementStart = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivityFirstHalfSettlementStart", CSAActivityGroupContext.class);
                listenList.add(csaActivityFirstHalfSettlementStart);
                Method csaActivityFirstHalfEnd = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivityFirstHalfEnd", CSAActivityGroupContext.class);
                listenList.add(csaActivityFirstHalfEnd);
            } catch (NoSuchMethodException e) {
                ErrorLogUtil.exceptionLog("NoSuchMethod", e,"status",this.getCsaActivityStatus().name());
            }
            return listenList;
        }
    },
    /**
     * 中场休息阶段
     */
    BATTLE_REST(PsCSAActivityStatus.BATTLE_REST, PsCSAActivityStatus.BATTLE_PREPARE_SECOND_HALF, false, 2, 2) {
        @Override
        public long currStageContinueTime() {
            ConfigServiceImpl configService = Application.getBean(ConfigServiceImpl.class);
            return configService.getConfig(CrossSeverAttackSetting.class).getBattlePhaseRestTime() * TimeUtil.SECONDS_MILLIS;
        }

        @Override
        public List<Method> getEventList(CSAActivityGroupContext csaActivityGroupContext) {
            List<Method> listenList = new ArrayList<>();
            try {
                Method csaBattleRestStart = ICrossServerAttackActivityStatusListener.class.getMethod("csaBattleRestStart", CSAActivityGroupContext.class);
                listenList.add(csaBattleRestStart);
                Method csaBattleRestEnd = ICrossServerAttackActivityStatusListener.class.getMethod("csaBattleRestEnd", CSAActivityGroupContext.class);
                listenList.add(csaBattleRestEnd);
            } catch (NoSuchMethodException e) {
                ErrorLogUtil.exceptionLog("NoSuchMethod", e,"status",this.getCsaActivityStatus().name());
            }
            return listenList;
        }
    },
    /**
     * 下半场战斗 开始前阶段
     */
    BATTLE_PREPARE_SECOND_HALF(PsCSAActivityStatus.BATTLE_PREPARE_SECOND_HALF, PsCSAActivityStatus.BATTLE_SECOND_HALF, true, 2, 2) {
        @Override
        public long currStageContinueTime() {
            ConfigServiceImpl configService = Application.getBean(ConfigServiceImpl.class);
            return configService.getConfig(CrossSeverAttackSetting.class).getBattlePhaseSecondHalfPreparingTime() * TimeUtil.SECONDS_MILLIS;
        }

        @Override
        public List<Method> getEventList(CSAActivityGroupContext csaActivityGroupContext) {
            List<Method> listenList = new ArrayList<>();
            try {
                Method csaActivitySecondHalfStart = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivitySecondHalfStart", CSAActivityGroupContext.class);
                listenList.add(csaActivitySecondHalfStart);
                Method csaActivitySecondHalfPrepareEnd = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivitySecondHalfPrepareEnd", CSAActivityGroupContext.class);
                listenList.add(csaActivitySecondHalfPrepareEnd);
            } catch (NoSuchMethodException e) {
                ErrorLogUtil.exceptionLog("NoSuchMethod", e,"status",this.getCsaActivityStatus().name());
            }
            return listenList;
        }
    },
    /**
     * 下半场战斗 战斗阶段
     */
    BATTLE_SECOND_HALF(PsCSAActivityStatus.BATTLE_SECOND_HALF, PsCSAActivityStatus.BATTLE_SECOND_SETTLEMENT, true, 2, 2) {
        @Override
        public long currStageContinueTime() {
            ConfigServiceImpl configService = Application.getBean(ConfigServiceImpl.class);
            return configService.getConfig(CrossSeverAttackSetting.class).getBattlePhaseSecondHalfBattleTime() * TimeUtil.SECONDS_MILLIS;
        }

        @Override
        public List<Method> getEventList(CSAActivityGroupContext csaActivityGroupContext) {
            List<Method> listenList = new ArrayList<>();
            try {
                Method csaActivitySecondHalfBattleStart = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivitySecondHalfBattleStart", CSAActivityGroupContext.class);
                listenList.add(csaActivitySecondHalfBattleStart);
                Method csaActivitySecondHalfBattleEnd = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivitySecondHalfBattleEnd", CSAActivityGroupContext.class);
                listenList.add(csaActivitySecondHalfBattleEnd);
            } catch (NoSuchMethodException e) {
                ErrorLogUtil.exceptionLog("NoSuchMethod", e,"status",this.getCsaActivityStatus().name());
            }
            return listenList;
        }
    },
    /**
     * 下半场战斗 结算阶段
     */
    BATTLE_SECOND_SETTLEMENT(PsCSAActivityStatus.BATTLE_SECOND_SETTLEMENT, PsCSAActivityStatus.OVER, true, 2, 2) {
        @Override
        public long currStageContinueTime() {
            ConfigServiceImpl configService = Application.getBean(ConfigServiceImpl.class);
            return configService.getConfig(CrossSeverAttackSetting.class).getCsaSecondHalfResultTime() * TimeUtil.SECONDS_MILLIS;
        }

        @Override
        public List<Method> getEventList(CSAActivityGroupContext csaActivityGroupContext) {
            List<Method> listenList = new ArrayList<>();
            try {
                Method csaActivitySecondHalfSettlementStart = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivitySecondHalfSettlementStart", CSAActivityGroupContext.class);
                listenList.add(csaActivitySecondHalfSettlementStart);
                Method csaActivitySecondHalfEnd = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivitySecondHalfEnd", CSAActivityGroupContext.class);
                listenList.add(csaActivitySecondHalfEnd);
                Method csaActivitySettleStart = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivitySettleStart", CSAActivityGroupContext.class);
                listenList.add(csaActivitySettleStart);
                Method csaActivitySettleEnd = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivitySettleEnd", CSAActivityGroupContext.class);
                listenList.add(csaActivitySettleEnd);
            } catch (NoSuchMethodException e) {
                ErrorLogUtil.exceptionLog("NoSuchMethod", e,"status",this.getCsaActivityStatus().name());
            }
            return listenList;
        }
    },
    /**
     * 战斗结束阶段
     */
    OVER(PsCSAActivityStatus.OVER, null, false, 0, 2) {
        @Override
        public long currStageContinueTime() {
            throw new AlertException("this stage continue time is invalid","stage",this.name());
        }

        @Override
        public List<Method> getEventList(CSAActivityGroupContext csaActivityGroupContext) {
            List<Method> listenList = new ArrayList<>();
            try {
                Method csaActivityOverStart = ICrossServerAttackActivityStatusListener.class.getMethod("csaActivityOverStart", CSAActivityGroupContext.class);
                listenList.add(csaActivityOverStart);
            } catch (NoSuchMethodException e) {
                ErrorLogUtil.exceptionLog("NoSuchMethod", e,"status",this.getCsaActivityStatus().name());
            }
            return listenList;
        }
    },
    ;

    private static final Logger logger = LoggerFactory.getLogger(CSAActivityStatus.class);

    private static final CSAActivityStatus[] INDEXES = EnumUtils.toArray(values());

    private PsCSAActivityStatus csaActivityStatus;

    private PsCSAActivityStatus nextCsaActivityStatus;

    // 是否是战斗阶段
    private boolean isBattleTime;

    // CSA活动服务器状态 0：普通game服状态 1：攻守转换前 2：攻守转换后
    private int csaServerStatus = 0;

    // CSA客户端展示状态 0: 无防守进攻 1：攻守转换前 2：攻守转换后
    private int csaClientShowStatus = 0;

    abstract public long currStageContinueTime();

    /**
     * 每个阶段必须有也只能有一个开始事件，但可以有零个或多个结束时触发的事件
     *
     * @param csaActivityGroupContext
     */
    abstract public List<Method> getEventList(CSAActivityGroupContext csaActivityGroupContext);

    CSAActivityStatus(PsCSAActivityStatus csaActivityStatus, PsCSAActivityStatus nextCsaActivityStatus) {
        this.csaActivityStatus = csaActivityStatus;
        this.nextCsaActivityStatus = nextCsaActivityStatus;

        this.isBattleTime = false;
        this.csaServerStatus = 0;
        this.csaClientShowStatus = 0;
    }

    CSAActivityStatus(PsCSAActivityStatus csaActivityStatus, PsCSAActivityStatus nextCsaActivityStatus, boolean isBattleTime, int csaServerStatus, int csaClientShowStatus) {
        this.csaActivityStatus = csaActivityStatus;
        this.nextCsaActivityStatus = nextCsaActivityStatus;
        this.isBattleTime = isBattleTime;
        this.csaServerStatus = csaServerStatus;
        this.csaClientShowStatus = csaClientShowStatus;
    }

    @Override
    public int getId() {
        return csaActivityStatus.getValue();
    }

    public static CSAActivityStatus findById(int id) {
        if (id < 0 || id >= INDEXES.length) {
            return null;
        }
        return INDEXES[id];
    }

    public PsCSAActivityStatus getCsaActivityStatus() {
        return csaActivityStatus;
    }

    public void setCsaActivityStatus(PsCSAActivityStatus csaActivityStatus) {
        this.csaActivityStatus = csaActivityStatus;
    }

    public PsCSAActivityStatus getNextCsaActivityStatus() {
        return nextCsaActivityStatus;
    }

    public void setNextCsaActivityStatus(PsCSAActivityStatus nextCsaActivityStatus) {
        this.nextCsaActivityStatus = nextCsaActivityStatus;
    }

    public boolean isBattleTime() {
        return isBattleTime;
    }

    public void setBattleTime(boolean battleTime) {
        isBattleTime = battleTime;
    }

    public int getCsaServerStatus() {
        return csaServerStatus;
    }

    public void setCsaServerStatus(int csaServerStatus) {
        this.csaServerStatus = csaServerStatus;
    }

    public int getCsaClientShowStatus() {
        return csaClientShowStatus;
    }

    public void setCsaClientShowStatus(int csaClientShowStatus) {
        this.csaClientShowStatus = csaClientShowStatus;
    }
}
