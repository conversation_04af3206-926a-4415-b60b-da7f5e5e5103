package com.lc.billion.icefire.tvtcontrol.biz.model;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import org.jongo.marshall.jackson.oid.MongoId;

/**
 * @author: maoqq
 * @Date: 2022/04/11 11:06 AM
 */
public class TvtPlayerSignupInfo extends AbstractEntity {

    private static final long serialVersionUID = -4854676252152129208L;

    @MongoId
    private Long id;
    private int serverId;
    private Long roleId;
    private int hideScore;
    private int signupStatus;
    private Long battleServerId;
    private int tvtSide;


    @Override
    public void setPersistKey(Long id) {
        this.id = id;
    }

    @Override
    public Long getPersistKey() {
        return id;
    }

    @Override
    public Long getGroupingId() {
        return roleId;
    }

    @Override
    public int hashCodeImpl() {
        return hashCodeForPersistKey();
    }

    @Override
    public boolean equalsImpl(Object obj) {
        return equalsForPersistKey(obj);
    }

    public Long getId() {
        return id;
    }


    public int getServerId() {
        return serverId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public int getHideScore() {
        return hideScore;
    }

    public void setHideScore(int hideScore) {
        this.hideScore = hideScore;
    }

    public int getSignupStatus() {
        return signupStatus;
    }

    public void setSignupStatus(int signupStatus) {
        this.signupStatus = signupStatus;
    }

    public Long getBattleServerId() {
        return battleServerId;
    }

    public void setBattleServerId(Long battleServerId) {
        this.battleServerId = battleServerId;
    }

    public int getTvtSide() {
        return tvtSide;
    }

    public void setTvtSide(int tvtSide) {
        this.tvtSide = tvtSide;
    }

    @Override
    public String toString() {
        return "TvtPlayerSignupInfo{" +
                "id=" + id +
                ", serverId=" + serverId +
                ", roleId=" + roleId +
                ", hideScore=" + hideScore +
                ", signupStatus=" + signupStatus +
                ", battleServerId=" + battleServerId +
                ", tvtSide=" + tvtSide +
                '}';
    }
}
