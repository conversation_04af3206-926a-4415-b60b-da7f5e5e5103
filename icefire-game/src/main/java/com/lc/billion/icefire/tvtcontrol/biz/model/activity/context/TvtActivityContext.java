package com.lc.billion.icefire.tvtcontrol.biz.model.activity.context;

import com.lc.billion.icefire.game.biz.model.activity.ActivityContext;
import com.lc.billion.icefire.tvtcontrol.biz.model.activity.TvtActivityStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: maoqq
 * @Date: 2022/04/11 4:18 PM
 */
public class TvtActivityContext extends ActivityContext {

    // tvt活动当前阶段
    private TvtActivityStatus tvtActivityStatus;
    // 下一阶段时间
    private long nextTvtActivityStatusTime;
    // 当前轮次已报名数量
    private int currentSignupCount;
    // 当前轮次允许报名最大数量
    private int signupCapacity;
    // 当日轮次
    private int roundOfDay;

    //本轮是否满员
    private boolean isSignupFull;
    //本轮开战时间
    private long battleStartTime;

    //每日活动所有时间点
    @JsonIgnore
    private List<LocalTime> tvtActivityTimes;

    private List<String> tvtActivityTimesStrs;


    public TvtActivityStatus getTvtActivityStatus() {
        return tvtActivityStatus;
    }

    public void setTvtActivityStatus(TvtActivityStatus tvtActivityStatus) {
        this.tvtActivityStatus = tvtActivityStatus;
    }

    public long getNextTvtActivityStatusTime() {
        return nextTvtActivityStatusTime;
    }

    public void setNextTvtActivityStatusTime(long nextTvtActivityStatusTime) {
        this.nextTvtActivityStatusTime = nextTvtActivityStatusTime;
    }

    public int getCurrentSignupCount() {
        return currentSignupCount;
    }

    public void setCurrentSignupCount(int currentSignupCount) {
        this.currentSignupCount = currentSignupCount;
    }

    public int getSignupCapacity() {
        return signupCapacity;
    }

    public void setSignupCapacity(int signupCapacity) {
        this.signupCapacity = signupCapacity;
    }

    public int getRoundOfDay() {
        return roundOfDay;
    }

    public void addRoundOfDay() {
        this.roundOfDay += 1;
    }

    public void setRoundOfDay(int roundOfDay) {
        this.roundOfDay = roundOfDay;
    }

    public boolean isSignupFull() {
        return isSignupFull;
    }

    public void setSignupFull(boolean signupFull) {
        isSignupFull = signupFull;
    }

    public long getBattleStartTime() {
        return battleStartTime;
    }

    public void setBattleStartTime(long battleStartTime) {
        this.battleStartTime = battleStartTime;
    }

    public List<LocalTime> getTvtActivityTimes() {
        if (this.tvtActivityTimes == null) {
            List<LocalTime> res = new ArrayList<>();
            if (tvtActivityTimesStrs != null) {
                tvtActivityTimesStrs.forEach(str -> {
                    res.add(LocalTime.parse(str));
                });
            }
            this.tvtActivityTimes = res;
        }
        return tvtActivityTimes;
    }

    public void setTvtActivityTimes(List<LocalTime> tvtActivityTimes) {
        this.tvtActivityTimes = tvtActivityTimes;
        List<String> res = new ArrayList<>();
        if (tvtActivityTimes != null) {
            tvtActivityTimes.forEach(lt -> {
                res.add(lt.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
            });
        }
        this.tvtActivityTimesStrs = res;
    }

    public List<String> getTvtActivityTimesStrs() {
        return tvtActivityTimesStrs;
    }

    public void setTvtActivityTimesStrs(List<String> tvtActivityTimesStrs) {
        this.tvtActivityTimesStrs = tvtActivityTimesStrs;
    }
}
