package com.lc.billion.icefire.gvgcontrol.biz.service.gm;


import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.game.biz.schedule.ScheduleOperation;
import com.lc.billion.icefire.game.biz.service.ScheduleService;
import com.lc.billion.icefire.gvgcontrol.biz.dao.mongo.root.GvgBattleServerDispatchRecordDao;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.context.GVGActivityContext;
import com.lc.billion.icefire.gvgcontrol.biz.service.GVGControlBroadcastToGameService;
import com.lc.billion.icefire.protocol.constant.PsGVGActivityStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class ControllerTestGM {
    @Autowired
    private GVGControlBroadcastToGameService gvgControlBroadcastToGameService;
    @Autowired
    private GvgBattleServerDispatchRecordDao gvgBattleServerDispatchRecordDao;
    @Autowired
    private ActivityDao activityDao;
    @Autowired
    private ScheduleService scheduleService;


    public void process(String[] args){
        switch(args[0]) {
            case "nextStage": {
                long addTime = 0;
                try {
                    addTime = Long.parseLong(args[1]) * TimeUtil.SECONDS_MILLIS;
                } catch (Exception ignored) { }

                var startTime = TimeUtil.getNow() + addTime;

                var gvgActivity = activityDao.findActivityByActivityType(ActivityType.GVG);
                if (gvgActivity != null) {
                    GVGActivityContext activityContext = gvgActivity.getActivityContext();
                    activityContext.setNextGvgActivityStatusTime(startTime);
                    activityDao.save(gvgActivity);

                    // 如果即将要进入的阶段是匹配阶段，则在匹配阶段进入后3秒（此时绝大概率已经完成了匹配），修改对阵信息的开始时间（为了让战斗服能马上开始，建筑正常解锁）
                    if(activityContext.getGvgActivityStatus().getNextGvgActivityStatus() == PsGVGActivityStatus.SIGNUP){
                        // 进入
                        scheduleService.schedule(new ScheduleOperation() {
                            @Override
                            public void execute() {
                                var records = gvgBattleServerDispatchRecordDao.findAll();
                                for(var record : records){
                                    record.setBattleStartTime(startTime);
                                    gvgBattleServerDispatchRecordDao.save(record);
                                }
                            }
                        }, TimeUtil.SECONDS_MILLIS * 3);
                    }
                    gvgControlBroadcastToGameService.broadcastGVGActivity(gvgActivity);
                }
                break;
            }
        }
    }
}
