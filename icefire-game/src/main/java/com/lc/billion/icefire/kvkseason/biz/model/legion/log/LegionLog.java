package com.lc.billion.icefire.kvkseason.biz.model.legion.log;

import java.util.ArrayList;
import java.util.List;

import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.annotation.MongoIndex;
import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.protocol.structure.PsLegionLog;

/**
 * <AUTHOR>
 *
 */
public class LegionLog extends AbstractEntity implements Comparable<LegionLog> {

	private static final long serialVersionUID = 8645489961477000256L;

	@MongoId
	private Long id;
	@MongoIndex
	private Long legionId;
	private String metaId;
	private List<String> params = new ArrayList<>();

	@Override
	public int compareTo(LegionLog o) {
		return (int) (getCreateTime() - o.getCreateTime());
	}

	public PsLegionLog toPsLegionLog() {
		PsLegionLog psLegionLog = new PsLegionLog();
		psLegionLog.setCreateTime(createTime);
		psLegionLog.setMetaId(metaId);
		psLegionLog.setParams(params);
		return psLegionLog;
	}

	@Override
	public void setPersistKey(Long id) {
		this.id = id;
	}

	@Override
	public Long getPersistKey() {
		return id;
	}

	@Override
	public Long getGroupingId() {
		return legionId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public Long getLegionId() {
		return legionId;
	}

	public void setLegionId(Long legionId) {
		this.legionId = legionId;
	}

	public String getMetaId() {
		return metaId;
	}

	public void setMetaId(String metaId) {
		this.metaId = metaId;
	}

	public List<String> getParams() {
		return params;
	}

	public void setParams(List<String> params) {
		this.params = params;
	}

}
