package com.lc.billion.icefire.csacontrol.biz.util;

import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivityContext;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivityGroupContext;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivityStatus;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;

public class CSAUtils {

    /**
     * 限中控服使用
     *
     * Game服获取方法见 com.lc.billion.icefire.game.biz.service.impl.csattack.impl.CrossServerAttackServiceImpl#getCsaActivityGroupContext()
     */
    public static Activity getCSAActivity(ActivityDao activityDao, int serverId) {
        Activity activity = activityDao.findActivityByActivityType(ActivityType.CROSS_SERVER_ATTACK);
        if (activity == null) {
            return null;
        }

        CSAActivityContext activityContext = activity.getActivityContext();
        if (activityContext == null || !activityContext.getServerInfoMap().containsKey(serverId)) {
            return null;
        }
        return activity;
    }

    /**
     * 限中控服使用
     *
     * Game服获取方法见 com.lc.billion.icefire.game.biz.service.impl.csattack.impl.CrossServerAttackServiceImpl#getCsaActivityGroupContext()
     */
    public static CSAActivityGroupContext getCsaActivityGroupContext(Activity activity, int serverId) {
        if (activity == null) {
            return null;
        }

        CSAActivityContext activityContext = activity.getActivityContext();
        if (activityContext == null || !activityContext.getServerInfoMap().containsKey(serverId)) {
            return null;
        }

        return activityContext.getServerInfoMap().get(serverId);
    }

    /**
     * 获取对战时长（单位：毫秒）
     */
    public static long getTotalBattleTime() {

        long totalBattleTime = 0;
        for (CSAActivityStatus stage : CSAActivityStatus.values()) {
            if (stage.getCsaServerStatus() != 0) {
                totalBattleTime += stage.currStageContinueTime();
            }
        }

        return totalBattleTime;
    }

    /**
     * CSA个人场次奖励状态
     */
    public class csaRewardStatus {
        // 未完成
        public static final int NOT_FINISH = 0;
        // 未参加
        public static final int CAN_NOT_TAKE = 1;
        // 已完成 未领奖
        public static final int CAN_TAKE = 2;
        // 已领奖
        public static final int ALREADY_TAKE = 3;
    }
}
