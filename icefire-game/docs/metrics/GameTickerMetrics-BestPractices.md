# GameTickerMetrics 最佳实践指南

## 概述

`GameTickerMetrics` 是专门为游戏 Ticker 系统设计的指标记录封装类，提供了统一、高效、异步的监控指标记录接口。

## 设计原则

### 1. **封装性**
- 隐藏底层 `MeterRegistryManager` 的复杂性
- 提供游戏业务专用的指标记录方法
- 统一的命名规范和标签体系

### 2. **性能优化**
- 异步执行所有指标记录操作
- 缓存 Timer 实例，避免重复创建
- 使用虚拟线程提高并发性能

### 3. **业务友好**
- 针对游戏 Ticker 场景的专用方法
- 自动提取 Ticker 类名作为标签
- 支持子步骤性能统计

## 核心功能

### 1. **基本 Ticker 执行时间记录**

```java
// 记录纳秒级执行时间
gameTickerMetrics.recordTickerExecution("WorldTick.PlayerTicker", durationNanos);

// 记录毫秒级执行时间
gameTickerMetrics.recordTickerExecutionMillis("WorldTick.PlayerTicker", durationMillis);
```

### 2. **Timer.Sample 计时方式**

```java
// 开始计时
Timer.Sample sample = gameTickerMetrics.startTiming("WorldTick.PlayerTicker");

try {
    // 执行业务逻辑
    ticker.tick(now);
} finally {
    // 停止计时并记录
    gameTickerMetrics.stopTiming(sample, "WorldTick.PlayerTicker");
}
```

### 3. **子步骤性能记录**

```java
// 记录 Ticker 内部步骤的执行时间
gameTickerMetrics.recordTickerStep("WorldTick.PlayerTicker", "loadPlayers", stepDuration);
gameTickerMetrics.recordTickerStep("WorldTick.PlayerTicker", "processPlayers", stepDuration);
```

### 4. **异常和统计记录**

```java
// 记录异常
gameTickerMetrics.recordTickerException("WorldTick.PlayerTicker", exception);

// 记录处理的实体数量
gameTickerMetrics.recordTickerEntityCount("WorldTick.PlayerTicker", entityCount);
```

### 5. **性能统计记录**

```java
// 基于 Counter 数据记录详细性能统计
gameTickerMetrics.recordTickerPerformanceStats(
    tickerName, stepName, totalTime, avgTime, maxTime, executionTimes);
```

## 使用场景

### 1. **World.tick() 方法改造**

**改造前：**
```java
meterRegistryManager.recordTimer(op, System.nanoTime() - startNanoTime, TimeUnit.NANOSECONDS);
```

**改造后：**
```java
gameTickerMetrics.recordTickerExecution(op, System.nanoTime() - startNanoTime);
```

### 2. **AbstractTicker 子类中的使用**

```java
@Service
public class PlayerTicker extends AbstractTicker<Player> {
    
    @Autowired
    private GameTickerMetricsFactory gameTickerMetricsFactory;
    
    private GameTickerMetrics metrics;
    
    @PostConstruct
    public void init() {
        this.metrics = gameTickerMetricsFactory.create();
    }
    
    @Override
    protected void tick(Player player, long now) {
        Timer.Sample sample = metrics.startTiming("PlayerTicker.processPlayer");
        
        try {
            // 业务逻辑
            processPlayerLogic(player, now);
        } catch (Exception e) {
            metrics.recordTickerException("PlayerTicker", e);
            throw e;
        } finally {
            metrics.stopTiming(sample, "PlayerTicker.processPlayer");
        }
    }
}
```

## 指标命名规范

### 1. **Ticker 执行时间**
- 格式：`WorldTick.{TickerClassName}`
- 示例：`WorldTick.PlayerTicker`、`WorldTick.AllianceTicker`

### 2. **子步骤执行时间**
- 格式：`WorldTick.{TickerClassName}.step.{StepName}`
- 标签：`ticker={TickerClassName}`, `step={StepName}`

### 3. **异常统计**
- 指标名：`game.ticker.exceptions`
- 标签：`ticker={TickerClassName}`, `exception={ExceptionType}`

### 4. **实体数量统计**
- 指标名：`game.ticker.entity.count`
- 标签：`ticker={TickerClassName}`

## 性能优化建议

### 1. **使用缓存**
- `GameTickerMetrics` 内部自动缓存 Timer 实例
- 避免重复创建相同的指标对象

### 2. **异步执行**
- 所有指标记录都是异步执行
- 不会阻塞业务线程

### 3. **合理使用**
- 对于高频调用的场景，优先使用 `recordTickerExecution`
- 对于需要精确控制计时的场景，使用 `Timer.Sample`

## 集成步骤

### 1. **注入依赖**

```java
@Autowired
private GameTickerMetricsFactory gameTickerMetricsFactory;

private GameTickerMetrics gameTickerMetrics;

@PostConstruct
public void init() {
    gameTickerMetrics = gameTickerMetricsFactory.create();
}
```

### 2. **替换现有调用**

将现有的 `meterRegistryManager.recordTimer` 调用替换为 `gameTickerMetrics.recordTickerExecution`。

### 3. **添加详细监控**

根据业务需要，添加子步骤监控、异常监控等。

## 注意事项

1. **线程安全**：`GameTickerMetrics` 是线程安全的
2. **资源管理**：在应用关闭时调用 `clearCache()` 清理缓存
3. **命名一致性**：保持 Ticker 名称的一致性，便于监控和分析
4. **适度使用**：避免过度细化的指标，影响性能

## 监控效果

使用 `GameTickerMetrics` 后，你将获得：

1. **统一的指标命名**：便于 Grafana 等工具展示
2. **丰富的标签信息**：支持多维度分析
3. **更好的性能**：异步执行，缓存优化
4. **业务友好的 API**：专为游戏场景设计

这种封装方式既保持了监控的完整性，又提供了更好的开发体验和性能表现。
