# 服务器ID分配策略与区间划分

## 前言

本文档详细说明冰火SLG游戏中不同类型服务器的ID分配策略、区间划分规则和管理机制。合理的服务器ID规划是保证系统稳定运行和运维管理的重要基础。

## 服务器类型与ID区间

### 整体分配策略

根据 `servertype.json` 配置文件和 `ServerTypeConfig.java` 的定义，系统采用**分段式ID分配**策略：

```
┌─────────────────────────────────────────────────────────────────┐
│                     服务器ID分配全景图                              │
├─────────────────────────────────────────────────────────────────┤
│ 1-8999      │ 普通GAME服务器（含测试服、生产服）                     │
│ 9000-9999   │ 新手服 (Guide Game Servers)                      │
│ 10000-18000 │ GVG战斗服 (GVG Battle Servers)                   │
│ 18001       │ GVG中控服 (GVG Control Server)                   │
│ 18002-19999 │ TVT战斗服 (TVT Battle Servers)                   │
│ 20000-80000 │ KVK赛季服 (KVK Season Servers)                   │
│ 89906       │ TVT中控服 (TVT Control Server)                   │
│ 89908       │ KVK中控服 (KVK Control Server)                   │
│ 89901       │ CSA中控服 (CSA Control Server)                   │
│ 90000+      │ 特殊功能服 (Boss活动等)                             │
└─────────────────────────────────────────────────────────────────┘
```

### 详细区间配置

#### 1. **普通游戏服 (GAME)**
- **ID范围**: `1-8999` (未明确配置的默认范围)
- **服务器类型**: `ServerType.GAME`
- **功能描述**: 
  - 主要游戏逻辑服务器
  - 玩家日常游戏体验
  - 公会管理和社交系统
  - 大世界地图和战斗系统

```java
// 判断逻辑：不在其他特定范围内的服务器ID默认为GAME类型
if (serverId != gvgControlServer && 
    serverId != kvkControlServer && 
    !isInGVGBattleRange(serverId) && 
    !isInKVKSeasonRange(serverId) && 
    serverId != csaControlServer && 
    !isInTVTBattleRange(serverId) && 
    serverId != tvtControlServer) {
    return ServerType.GAME;
}
```

#### 2. **新手服 (Guide Game)**
- **ID范围**: `9000-9999` (1000个ID)
- **配置键**: `guideGameServerIds`
- **功能描述**:
  - 新玩家引导和教学
  - 独立的游戏环境
  - 简化的游戏机制

#### 3. **GVG战斗服 (GVG_BATTLE)**
- **ID范围**: `10000-18000` (8001个ID)
- **配置键**: `gvgBattleServerIds`
- **服务器类型**: `ServerType.GVG_BATTLE`
- **功能描述**:
  - 公会战实时战斗处理
  - 大规模PvP战斗计算
  - 战场地图和据点管理

#### 4. **GVG中控服 (GVG_CONTROL)**
- **ID**: `18001` (单一服务器)
- **配置键**: `gvgControlServer`
- **服务器类型**: `ServerType.GVG_CONTROL`
- **功能描述**:
  - GVG活动全局调度
  - 公会匹配和战斗服分配
  - 跨服数据协调

#### 5. **TVT战斗服 (TVT_BATTLE)**
- **ID范围**: `18002-19999` (1998个ID)
- **配置键**: `tvtBattleServerIds`
- **服务器类型**: `ServerType.TVT_BATTLE`
- **功能描述**:
  - 阵营对抗战斗
  - TVT专用战斗逻辑

#### 6. **KVK赛季服 (KVK_SEASON)**
- **ID范围**: `20000-80000` (60001个ID)
- **配置键**: `kvkSeasonServerIds`
- **服务器类型**: `ServerType.KVK_SEASON`
- **功能描述**:
  - 跨国战赛季系统
  - 临时性战斗服务器
  - 赛季数据管理

#### 7. **中控服务器 (Control Servers)**
- **TVT中控服**: `89906` - `ServerType.TVT_CONTROL`
- **KVK中控服**: `89908` - `ServerType.KVK_CONTROL`
- **CSA中控服**: `89901` - `ServerType.CSA_CONTROL`

## 配置文件结构

### servertype.json 配置示例

```json
{
    "guideGameServerIds": "9000-9999",
    "gvgBattleServerIds": "10000-18000", 
    "gvgControlServer": "18001",
    "tvtBattleServerIds": "18002-19999",
    "tvtControlServer": "89906",
    "kvkSeasonServerIds": "20000-80000",
    "kvkControlServer": "89908",
    "csaControlServer": "89901",
    "aoiTileScopeGame": 10,
    "aoiTileScopeGVGBattle": 120,
    "mapBlockSizeMultipleForGVGBattle": 10,
    "mapBlockSizeMultipleForGame": 10,
    "gameMap": "map.bytes",
    "gVGMap": "mapgvg1.bytes",
    "aoiTileScopeKServer": 10,
    "mapBlockSizeMultipleForKServer": 10,
    "kServerMap": "mapkvk1.bytes"
}
```

## ID分配原则

### 1. **分层设计原则**
- **业务隔离**: 不同类型服务器使用不同ID段，避免功能混淆
- **扩展性**: 预留足够的ID空间供业务扩展
- **管理便利**: 通过ID范围快速识别服务器类型

### 2. **容量规划**
- **GAME服**: 8999个ID，满足大量普通游戏服需求
- **GVG战斗服**: 8001个ID，支持大规模公会战
- **KVK赛季服**: 60001个ID，最大的ID池，支持频繁的赛季活动
- **中控服**: 单点服务器，使用固定ID

### 3. **特殊ID规划**
- **80000以上**: 特殊功能服务器（如Boss活动服90001）
- **8XXXX**: 中控服务器集中在8万段
- **避免冲突**: 确保不同服务器类型ID不重叠

## 服务器类型判断逻辑

### 核心判断方法

```java
public ServerType getServerType(int serverId) {
    // 1. 优先判断中控服
    if (serverId == gvgControlServer) {
        return ServerType.GVG_CONTROL;
    } else if (serverId == kvkControlServer) {
        return ServerType.KVK_CONTROL;
    } else if (serverId == csaControlServer) {
        return ServerType.CSA_CONTROL;
    } else if (serverId == tvtControlServer) {
        return ServerType.TVT_CONTROL;
    }
    
    // 2. 判断范围型服务器
    else if (gvgBattleServerStartId <= serverId && serverId <= gvgBattleServerEndId) {
        return ServerType.GVG_BATTLE;
    } else if (kvkSeasonServerStartId <= serverId && serverId <= kvkSeasonServerEndId) {
        return ServerType.KVK_SEASON;
    } else if (tvtBattleServerStartId <= serverId && serverId <= tvtBattleServerEndId) {
        return ServerType.TVT_BATTLE;
    }
    
    // 3. 默认为GAME服
    else {
        return ServerType.GAME;
    }
}
```

### 辅助判断方法

```java
// 新手服判断
public boolean isGuideGameServer(int serverId) {
    return guideGameServerStartId <= serverId && serverId <= guideGameServerEndId;
}

// 战斗服判断  
public boolean isBattleServer(int serverId) {
    ServerType serverType = getServerType(serverId);
    return serverType == ServerType.GVG_BATTLE || serverType == ServerType.TVT_BATTLE;
}
```

## ID生成机制

### 实体ID组成

系统使用复合ID机制确保全局唯一性：

```java
/**
 * 实体ID = 服务器ID * 100000000000000L + 服务器内ID
 * 
 * 示例：
 * - 服务器ID: 1001
 * - 服务器内ID: 123456789
 * - 最终实体ID: 100100000000123456789
 */
public static Long makeEntityId(int serverId, long idInOneServer) {
    return serverId * serverIdRange + idInOneServer;
}
```

### ID容量计算

- **服务器ID范围**: 最大支持92232个服务器
- **服务器内ID范围**: 每服最大99999999999999个实体
- **使用年限估算**: 按每秒158548个ID消耗，可使用20年

## 运维管理

### 1. **配置部署**
配置文件分布在多个环境：
- `deploy/config/online/game/servertype.json` - 生产环境
- `deploy/config/test*/game/servertype.json` - 测试环境
- `icefire-game/conf/servertype.json` - 开发环境

### 2. **监控告警**
- **ID耗尽监控**: 监控各类型服务器ID使用率
- **类型冲突检测**: 确保服务器类型判断正确
- **配置一致性**: 检查多环境配置同步

### 3. **扩容策略**
- **纵向扩容**: 调整现有ID范围上限
- **横向扩容**: 新增服务器类型和ID段
- **迁移方案**: 支持服务器类型变更

## 最佳实践

### 1. **ID分配建议**
- 新建服务器优先使用连续ID
- 预留测试服ID空间
- 避免跨类型ID复用

### 2. **配置管理**
- 所有环境配置保持一致
- 配置变更需要全面测试
- 建立配置版本控制

### 3. **故障应对**
- 准备ID冲突恢复方案
- 建立服务器类型纠错机制
- 制定紧急扩容流程

## 总结

服务器ID分配策略采用分段式设计，通过合理的区间划分实现了：

✅ **功能隔离** - 不同服务器类型各司其职  
✅ **扩展性强** - 预留充足的ID空间  
✅ **管理便利** - 通过ID快速识别服务器功能  
✅ **运维友好** - 支持灵活的扩容和迁移  

这种设计为游戏的长期稳定运行和业务扩展提供了坚实的基础架构支撑。 