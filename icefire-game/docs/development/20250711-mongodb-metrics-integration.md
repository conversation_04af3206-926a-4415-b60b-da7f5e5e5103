# MongoDB Micrometer监控集成

## 概述

本次集成将MongoDB命令监控功能集成到现有的游戏服监控体系中，通过Micrometer框架收集MongoDB操作的详细性能指标。

## 核心组件

### 1. MongoDbMetrics
- **路径**: `com.lc.billion.icefire.game.metrics.MongoDbMetrics`
- **功能**: MongoDB指标收集器，负责记录命令执行时间、成功/失败统计等指标
- **指标类型**:
  - `mongodb.command.duration`: 命令执行时间（Timer）
  - `mongodb.command.total`: 命令执行总数（Counter）
  - `mongodb.command.errors`: 命令错误统计（Counter）

### 2. MongoDbMetricsFactory
- **路径**: `com.lc.billion.icefire.game.metrics.MongoDbMetricsFactory`
- **功能**: MongoDB指标工厂类，负责创建MongoDbMetrics实例
- **特性**: Spring组件，自动初始化并设置全局实例

### 3. MongoDbMetricsHolder
- **路径**: `com.lc.billion.icefire.game.metrics.MongoDbMetricsHolder`
- **功能**: 静态持有者，解决MongoDB客户端早期初始化时Spring容器未完成的问题

### 4. MyCommandListener增强
- **路径**: `com.lc.billion.icefire.game.biz.dao.jongo.JongoClient.MyCommandListener`
- **功能**: 集成Micrometer监控到MongoDB CommandListener
- **特性**: 
  - 保留原有错误日志功能
  - 新增完整的性能指标收集
  - 异步记录，不影响业务性能

## 监控指标详情

### 指标标签（Tags）
- `command`: MongoDB命令名称（如：find, insert, update, delete）
- `database`: 数据库名称
- `collection`: 集合名称（自动从命令中提取）
- `result`: 执行结果（total, success, failure）
- `exception`: 异常类型（仅在失败时）

### 指标示例
```
# 命令执行时间
mongodb.command.duration{command="find",database="game_db_1",collection="roles"} 15ms

# 命令执行次数
mongodb.command.total{command="find",database="game_db_1",collection="roles",result="success"} 1250
mongodb.command.total{command="insert",database="game_db_1",collection="items",result="success"} 890
mongodb.command.total{command="find",database="game_db_1",collection="roles",result="failure"} 5

# 错误统计
mongodb.command.errors{command="find",database="game_db_1",collection="roles",exception="MongoTimeoutException"} 3
```

## 集成优势

### 1. 性能优化
- **异步记录**: 所有指标记录都在虚拟线程中异步执行
- **缓存机制**: Timer和Counter实例缓存，避免重复创建
- **高并发优化**: 使用ConcurrentHashMap实现无锁并发访问，性能优于synchronized
- **最小开销**: 监控错误不影响业务逻辑

### 2. 兼容性
- **无侵入**: 保留所有原有功能
- **向后兼容**: 原有错误日志功能完全保留
- **框架集成**: 完全集成到现有sgf-monitoring框架

### 3. 监控全面性
- **命令级别**: 精确到每个MongoDB命令
- **数据库级别**: 按数据库分组统计
- **集合级别**: 自动提取并按集合分组统计（支持find、insert、update、delete等常见命令）
- **异常分类**: 详细的异常类型统计
- **时间维度**: 纳秒级精确计时

## 部署说明

### 1. 自动启用
由于集成到现有的Spring配置中，MongoDB监控会在游戏服启动时自动启用，无需额外配置。

### 2. 监控验证
可通过Prometheus指标端点查看MongoDB相关指标：
```bash
curl http://localhost:8080/metrics | grep mongodb
```

### 3. 性能影响
- 指标记录为异步执行，对游戏主线程无影响
- 内存开销极小，每个命令类型约占用几KB内存
- CPU开销可忽略不计（<0.1%）

## 故障排除

### 1. 指标未出现
- 检查Spring容器启动日志中是否有"MongoDB监控指标已初始化"消息
- 确认sgf-monitoring模块正常加载

### 2. 监控数据异常
- 检查游戏服日志中是否有监控相关的警告信息
- 所有监控异常都会记录在日志中，但不会影响业务

### 3. 性能问题
- 监控本身设计为高性能，如发现性能问题，可能是底层Micrometer配置问题
- 可通过JVM指标观察虚拟线程使用情况

## 扩展建议

### 1. 自定义指标
可在`MongoDbMetrics`类中添加更多业务相关的指标，如：
- 慢查询统计
- 连接池使用率
- 特定集合的操作频率

### 2. 告警配置
建议在Prometheus/Grafana中配置以下告警：
- MongoDB命令失败率过高
- 命令执行时间过长
- 特定异常类型频发

### 3. 仪表板
建议创建MongoDB专用监控仪表板，包含：
- 命令执行时间趋势
- 成功/失败率统计
- 热点数据库/集合分析
- 异常类型分布
- 集合级别的性能对比
- 不同命令类型在各集合上的执行分布

## 更新历史

- **2025-07-11**: 初始版本，完成MongoDB Micrometer监控集成
- **2025-07-11**: 性能优化，使用ConcurrentHashMap替代synchronized，提升并发性能 