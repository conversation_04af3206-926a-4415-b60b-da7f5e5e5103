# GVG公会战系统架构分析

## 前言

本文档深入分析SLG游戏中的GVG（Guild vs Guild）公会战系统，详细说明其框架设计、协议体系、服务器间交互流程和活动机制。GVG系统是游戏中最复杂的跨服PvP系统之一，涉及多个服务器类型的协调工作。

## GVG系统概述

GVG公会战是一个大型跨服战斗系统，支持不同服务器的公会进行对战。系统采用分层架构，包含活动调度、匹配系统、战斗服务器、数据同步等多个子系统。

## 核心架构组件

### 1. 服务器类型划分

#### **GAME服务器（游戏服）**
- **职责**: 日常游戏逻辑、公会管理、玩家数据
- **GVG相关功能**:
  - 公会报名和管理
  - 玩家参战资格检查
  - 活动通知和广播
  - 战斗结果处理和奖励发放

#### **GVG_CONTROL服务器（中控服）**
- **职责**: 全局GVG活动调度和匹配
- **核心功能**:
  - 活动时间线管理
  - 公会匹配算法
  - 战斗服务器分配
  - 跨服数据协调

#### **GVG_BATTLE服务器（战斗服）**
- **职责**: 实际战斗逻辑执行
- **主要功能**:
  - 战场地图管理
  - 实时PvP战斗
  - 据点占领机制
  - 积分计算和排行榜

### 2. 架构关系图

```
┌─────────────────────────────────────────────────────────────────┐
│                     GVG系统整体架构                                │
└─────────────────┬───────────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────────┐
│                 GVG_CONTROL (中控服)                            │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ • 活动调度器 (GVGActivityHandler)                           │ │
│ │ • 匹配引擎 (GVGControlService)                             │ │
│ │ │ • 战斗服分配器 (GvgBattleServerDispatchRecord)           │ │
│ │ • 数据管理器 (GVGGameDataVoManager)                        │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────┬───────────────┬───────────────────────────────┘
                  │               │
                  │ RPC通信       │ RPC通信  
                  ▼               ▼
┌─────────────────┴─────────────┐ ┌─────────────────────────────────┐
│       GAME服务器群              │ │      GVG_BATTLE服务器群          │
│ ┌─────────────────────────────┐ │ │ ┌─────────────────────────────┐ │
│ │ • 公会管理                   │ │ │ │ • 战场管理                   │ │
│ │ • 玩家数据                   │ │ │ │ • 实时战斗                   │ │
│ │ • 报名系统                   │ │ │ │ • 据点系统                   │ │
│ │ • 结果处理                   │ │ │ │ • 积分计算                   │ │
│ └─────────────────────────────┘ │ │ └─────────────────────────────┘ │
└─────────────────────────────────┘ └─────────────────────────────────┘
                  │                                   │
                  │ 玩家迁移 (Migration)                │
                  └─────────────────┬─────────────────┘
                                    │
                             ┌─────▼─────┐
                             │  玩家数据   │
                             │  传输机制   │
                             └───────────┘
```

## RPC通信协议体系

### 1. 服务器间RPC接口

#### **GAME → GVG_CONTROL**
```java
interface IGameRemoteGVGControlService {
    // 公会报名
    void uploadAllianceSignUp(GVGAllianceSignUpInfoVo signUpInfo);
    
    // 获取活动信息
    ActivityVo findActivityVo();
    
    // 获取对阵信息
    List<GvgBattleServerDispatchRecordVo> findGvgBattleServerDispatchRecords();
    
    // 查询战斗服状态
    int queryBattleServerStatus(int battleServerId);
    
    // 上传联盟数据
    void uploadAllianceBaseData(AllianceBaseDataVo allianceData);
}
```

#### **GVG_CONTROL → GAME**
```java
interface IGVGControlRemoteGameService {
    // 广播活动信息
    String broadcastGVGActivity(ActivityVo activityVo);
    
    // 广播战斗服分配
    String broadcastGvgBattleServerDispatchRecord(GvgBattleServerDispatchRecordVo record);
    
    // 广播战斗结果
    String broadcastGVGBattleRecord(GVGBattleRecordVo battleRecord);
    
    // 通知选择参战联盟
    Set<AllianceBaseDataVo> broadcastNoticeSelectGVGAlliance(int serverId);
}
```

#### **GVG_CONTROL ↔ GVG_BATTLE**
```java
interface IGVGControlRemoteGVGBattleService {
    // 启动战斗服
    String startBattleServer(GvgBattleServerDispatchRecordVo dispatchRecord);
    
    // 加载联盟数据
    String loadAllianceData(Long allianceId, int serverId);
    
    // 战斗结束通知
    String battleFinish(Long battleServerId);
}

interface IGVGBattleRemoteGVGControlService {
    // 上报战斗结果
    void uploadBattleResult(GVGBattleRecordVo battleRecord);
    
    // 请求销毁战斗服
    void requestDestroyBattleServer(Long battleServerId);
}
```

### 2. 数据传输VO对象

#### **活动信息VO**
```java
public class ActivityVo {
    private Long id;                    // 活动ID
    private ActivityType type;          // 活动类型
    private long startTime;             // 开始时间
    private long endTime;               // 结束时间
    private ActivityStatus status;      // 活动状态
    private GVGActivityContext context; // GVG特定上下文
}
```

#### **战斗服分配记录VO**
```java
public class GvgBattleServerDispatchRecordVo {
    private Long battleServerId;        // 战斗服ID
    private Long allianceId1;           // 参战联盟1
    private int alliance1ServerId;      // 联盟1所在服务器
    private Long allianceId2;           // 参战联盟2  
    private int alliance2ServerId;      // 联盟2所在服务器
    private long battleStartTime;       // 战斗开始时间
    private int status;                 // 状态：0-分配，1-通知，2-启动，3-结束
    private GvgMatchType matchType;     // 匹配类型
    private int gameRound;              // 比赛轮次
}
```

#### **公会报名信息VO**
```java
public class GVGAllianceSignUpInfoVo {
    private Long allianceId;                    // 联盟ID
    private int serverId;                       // 服务器ID
    private String allianceName;                // 联盟名称
    private GvgMatchType matchType;             // 参赛类型
    private GVGAllianceLineUpInfo lineUpInfo;   // 阵容信息
    private int memberCount;                    // 成员数量
    private long totalFightPower;               // 总战力
}
```

## 服务器交互流程

### 1. GVG活动生命周期

```mermaid
sequenceDiagram
    participant GC as GVG_CONTROL
    participant G1 as GAME服1
    participant G2 as GAME服2
    participant GB as GVG_BATTLE

    Note over GC: 活动调度开始
    GC->>GC: GVGActivityHandler.tick()
    GC->>GC: 计算活动阶段转换

    Note over GC,G2: 1. 报名阶段 (REGISTER)
    GC->>G1: broadcastNoticeSelectGVGAlliance()
    GC->>G2: broadcastNoticeSelectGVGAlliance()
    G1->>GC: uploadAllianceSignUp()
    G2->>GC: uploadAllianceSignUp()

    Note over GC: 2. 匹配阶段 (SIGNUP)
    GC->>GC: 执行匹配算法
    GC->>GC: 分配战斗服

    Note over GC,GB: 3. 入场阶段 (ADMITTANCE)
    GC->>G1: broadcastGvgBattleServerDispatchRecord()
    GC->>G2: broadcastGvgBattleServerDispatchRecord()
    GC->>GB: startBattleServer()
    GC->>GB: loadAllianceData()

    Note over G1,GB: 4. 战斗阶段
    G1->>GB: 玩家迁移数据
    G2->>GB: 玩家迁移数据
    GB->>GB: 执行战斗逻辑
 
    Note over GB,GC: 5. 结算阶段
    GB->>GC: uploadBattleResult()
    GC->>G1: broadcastGVGBattleRecord()
    GC->>G2: broadcastGVGBattleRecord()
```

### 2. 玩家参战流程

```mermaid
sequenceDiagram
    participant P as Player
    participant G as GAME
    participant GC as GVG_CONTROL  
    participant GB as GVG_BATTLE

    P->>G: 请求进入GVG
    G->>G: 检查玩家资格
    G->>G: 检查联盟报名状态
    
    G->>GC: 查询战斗服状态
    GC-->>G: 返回战斗服信息
    
    G->>GB: 发起玩家迁移
    Note over G,GB: 迁移玩家数据
    GB->>GB: 创建战场角色数据
    GB-->>P: 进入战场成功

    Note over P,GB: 战斗阶段
    P->>GB: 战斗操作
    GB->>GB: 更新积分排行
    
    Note over GB: 战斗结束
    GB->>G: 返回玩家数据
    G->>P: 发放奖励
```

### 3. 数据同步机制

#### **实时数据同步**
- **战斗积分**: 战斗服实时更新，定期同步到中控服
- **公会状态**: 通过RPC实时广播状态变更
- **活动进度**: 中控服主动推送阶段变更

#### **批量数据同步**
- **玩家数据迁移**: 使用`IMigrateForGVGService`批量传输
- **战斗结果**: 战斗结束后批量上报
- **排行榜数据**: 定期批量更新

## 活动流程和调度

### 1. 活动状态机

```java
public enum GVGActivityStatus {
    READY,              // 准备阶段
    REGISTER,           // 报名阶段  
    SIGNUP,             // 匹配阶段
    ADMITTANCE_ADVANCE, // 提前入场
    ADMITTANCE,         // 正式入场
    BATTLE,             // 战斗阶段
    SETTLEMENT          // 结算阶段
}
```

### 2. 时间线管理

#### **活动调度器 (GVGActivityHandler)**
```java
@Service
public class GVGActivityHandler extends AbstractActivityHandler<Activity> {
    
    @Override
    public void tick(Activity activity, long now) {
        GVGActivityContext context = activity.getActivityContext();
        GVGActivityStatus status = context.getGvgActivityStatus();
        
        if (context.getNextGvgActivityStatusTime() <= now) {
            gvgActivityStatusContinue(activity); // 状态转换
        }
    }
    
    private void gvgActivityStatusContinue(Activity activity) {
        GVGActivityStatus currentStatus = getCurrentStatus();
        switch (currentStatus) {
            case READY:
                handleGvgActivityStatusReady();
                break;
            case REGISTER:
                handleGVGActivityStatusRegister();
                break;
            case SIGNUP:
                handleGvgActivityStatusSignup(activity);
                break;
            // ... 其他状态处理
        }
    }
}
```

#### **战斗服时间线 (GVGBattleFieldTimeLineTicker)**
```java
@Service  
public class GVGBattleFieldTimeLineTicker extends AbstractTicker<GVGBattleFieldTimeLine> {
    
    @Override
    protected void tick(GVGBattleFieldTimeLine timeLine, long now) {
        // 战场开启
        if (!timeLine.isStartFlag() && timeLine.getStartTime() < now) {
            timeLine.setStartFlag(true);
            loadAllianceData(); // 加载联盟数据
            loadOfficeBuff();   // 加载官职buff
        }
        
        // 准备阶段结束
        if (!timeLine.isReadyEndFlag() && timeLine.getReadyEndTime() < now) {
            timeLine.setReadyEndFlag(true);
            gvgReadyFinish(); // 战斗正式开始
        }
        
        // 战斗结束
        if (!timeLine.isEndFlag() && timeLine.getEndTime() < now) {
            battleStop(); // 战斗结束处理
        }
    }
}
```

### 3. 匹配算法

#### **战力匹配系统**
```java
public class GVGControlService {
    
    public void executeMatch() {
        // 1. 获取所有报名联盟
        List<GVGAllianceSignUpInfoVo> alliances = getAllSignUpAlliances();
        
        // 2. 计算匹配分数
        for (GVGAllianceSignUpInfoVo alliance : alliances) {
            long matchScore = calculateMatchScore(alliance);
            alliance.setMatchScore(matchScore);
        }
        
        // 3. 战力匹配
        List<MatchPair> matchPairs = new ArrayList<>();
        while (!alliances.isEmpty()) {
            GVGAllianceSignUpInfoVo alliance1 = alliances.remove(0);
            GVGAllianceSignUpInfoVo alliance2 = findBestMatch(alliance1, alliances);
            
            if (alliance2 != null) {
                alliances.remove(alliance2);
                matchPairs.add(new MatchPair(alliance1, alliance2));
            }
        }
        
        // 4. 分配战斗服
        assignBattleServers(matchPairs);
    }
    
    private long calculateMatchScore(GVGAllianceSignUpInfoVo alliance) {
        // 取战力最高的N名玩家战力之和作为匹配分数
        int selectNum = gvgSettingConfig.getGvgActivityMatchMemberSelectNum();
        return getTopMembersPower(alliance, selectNum);
    }
}
```

## 广播通知系统

### 1. 广播类型

#### **系统跑马灯 (System Marquee)**
```java
@Service
public class NoticeServiceImpl {
    
    // GVG专用跑马灯
    public void marqueeGVGNotice(String langKey, String... params) {
        SystemNotice notice = new SystemNotice(
            TimeUtil.getNow(), 
            langKey, 
            1, 
            TimeUtil.getNow() + 10 * 1000L, 
            PsNoticeType.GVG
        );
        
        GcNoticeInfo msg = NoticeOutput.wrapperMarqueeNotice(notice, speed, params);
        playerSrv.broadcast(msg); // 全服广播
    }
    
    // 按服务器ID广播
    public void systemMarqueeByCurrentServerId(int currentServerId, 
                                             PsSystemMarquee marqueeType, 
                                             Object... params) {
        ScrollMessageConfig config = configService.getConfig(ScrollMessageConfig.class);
        ScrollMessageMeta meta = config.get(marqueeType.getValue());
        
        systemMarquee(0, currentServerId, meta.getContext(), 
                     meta.getCount(), expireTime, meta.getSort(), speed, lvLimit, params);
    }
}
```

#### **GVG事件广播**
```java
// 据点占领广播
private void noticeOccupied(StrongHoldNode node, long allianceId) {
    Alliance alliance = allianceService.getAllianceById(allianceId);
    GvgBuildingMeta meta = configService.getConfig(GvgBuildingConfig.class).get(node.getMetaId());
    
    if (Application.getServerType() == ServerType.TVT_BATTLE) {
        noticeService.systemMarqueeByCurrentServerId(node.getCurrentServerId(),
                PsSystemMarquee.TVT_OCCUPY_BUILD,
                "@" + alliance.getName() + "@",
                NoticeConstants.getKey(meta.getBuildName()));
    } else {
        noticeService.systemMarqueeByCurrentServerId(node.getCurrentServerId(),
                PsSystemMarquee.GVG_OCCUPY_BUILD,
                alliance.getName(),
                alliance.getAliasName(),
                NoticeConstants.getKey(meta.getBuildName()));
    }
}

// 乌巢运输完成广播  
private void noticeWuchaoScoreAdd(Alliance alliance, int addScore) {
    String broadcastTemplate = configService.getConfig(GvgSettingConfig.class)
                                           .getGvgWuChaoTransportCompleteBroadcast();
    
    noticeServiceImpl.marqueeGVGNotice(broadcastTemplate, 
                                      alliance.getName(), 
                                      String.valueOf(addScore));
}
```

### 2. 广播触发时机

#### **活动阶段变更**
- 报名开始/结束
- 匹配完成
- 战斗开始/结束
- 结算完成

#### **战斗事件**
- 据点占领/失守
- 重要战斗胜负
- 积分里程碑达成
- 乌巢运输完成

#### **排行榜更新**
- 个人积分排行
- 公会积分排行
- MVP评选结果

## 数据模型设计

### 1. 核心数据实体

#### **战斗服分配记录**
```java
@Entity
public class GvgBattleServerDispatchRecord {
    private Long battleServerId;         // 战斗服ID
    private Long allianceId1;           // 参战联盟1
    private int alliance1ServerId;      // 联盟1服务器ID
    private Long allianceId2;           // 参战联盟2
    private int alliance2ServerId;      // 联盟2服务器ID
    private long battleStartTime;       // 开始时间
    private int status;                 // 状态
    private GvgMatchType matchType;     // 匹配类型
    private int round;                  // 轮次
    private int warZoneId;             // 战区ID
}
```

#### **GVG玩家战斗数据**
```java
@Entity
public class RoleGVGBattle {
    private Long roleId;                // 玩家ID
    private String name;                // 玩家名称
    private Long allianceId;            // 联盟ID
    private String allianceName;        // 联盟名称
    private int battlePoint;            // 战斗积分
    private int killCount;              // 击杀数
    private int deathCount;             // 死亡数
    private long lastBattleTime;        // 最后战斗时间
}
```

#### **联盟战斗积分**
```java
@Entity
public class AllianceBattlePoint {
    private Long allianceId;            // 联盟ID
    private int totalPoint;             // 总积分
    private int memberCount;            // 参战成员数
    private long lastUpdateTime;        // 最后更新时间
    private Map<String, Integer> buildingPoints; // 建筑积分详情
}
```

### 2. 配置数据

#### **GVG设置配置**
```java
public class GvgSettingConfig {
    // 活动时间配置
    private List<GvgActivityTime> gvgActivityTime;
    
    // 战斗时间选择
    private List<GvgActivityBattleTimeSelect> gvgActivityBattleTimeSelects;
    
    // 匹配参数
    private int gvgActivityMatchMemberSelectNum; // 匹配战力计算成员数
    private double gvgActivityMatchPowerDiffRate; // 战力差异容忍率
    
    // 积分配置
    private Map<String, Integer> buildingPointConfig; // 建筑积分配置
    private int wuChaoCompleteScore; // 乌巢完成积分
    
    // 广播模板
    private String gvgWuChaoTransportCompleteBroadcast;
    private Set<String> gvgbanItem; // 禁用物品列表
}
```

## 技术特性与优化

### 1. 性能优化

#### **数据缓存策略**
- **内存缓存**: 活动数据、配置数据热缓存
- **分布式缓存**: 跨服务器数据同步缓存
- **数据预加载**: 战斗服启动时预加载联盟数据

#### **并发控制**
- **分布式锁**: 防止重复匹配和分配
- **状态同步**: 通过RPC保证状态一致性
- **异步处理**: 非关键路径异步执行

### 2. 容错机制

#### **服务降级**
- **战斗服故障**: 自动重新分配备用服务器
- **中控服故障**: 活动暂停，数据持久化保护
- **网络分区**: 本地缓存保证基本功能

#### **数据恢复**
- **状态恢复**: 服务重启后从持久化数据恢复状态
- **进度续传**: 支持活动中断后续传
- **数据校验**: 定期校验数据一致性

### 3. 监控告警

#### **关键指标监控**
- **服务器负载**: CPU、内存、网络使用率
- **RPC调用**: 成功率、延迟、错误率
- **业务指标**: 参赛公会数、战斗服使用率

#### **异常告警**
- **匹配失败**: 无法找到合适对手
- **服务器故障**: 战斗服无响应
- **数据异常**: 积分计算错误

## 总结

GVG公会战系统是一个高度复杂的分布式游戏系统，其设计充分体现了以下特点：

### **架构优势**
1. **分层解耦**: 通过中控服实现游戏服和战斗服的解耦
2. **弹性扩展**: 支持动态分配战斗服资源
3. **数据一致性**: 通过RPC和状态机保证数据一致性
4. **容错性强**: 多层次的容错和恢复机制

### **技术亮点**
1. **智能匹配**: 基于战力的公平匹配算法
2. **实时同步**: 高效的跨服数据同步机制
3. **活动调度**: 精确的时间线管理和状态转换
4. **广播系统**: 灵活的多类型消息广播

### **运维友好**
1. **状态可视**: 清晰的活动状态和进度展示
2. **操作便捷**: 支持GM命令调试和干预
3. **监控完备**: 全方位的监控和告警机制
4. **扩展性好**: 支持新活动类型和玩法扩展

该系统为SLG游戏的大型跨服PvP活动提供了完整的技术解决方案，具有很强的实用价值和参考意义。 